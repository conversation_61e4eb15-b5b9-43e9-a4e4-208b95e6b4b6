import { DatePipe } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Document } from 'app/core/qinspect/_model/document.model';
import { Status } from 'app/core/qinspect/_model/status.model';
import { QiUser } from 'app/core/qinspect/_model/user.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { ScheduleCommonService } from 'app/modules/schedules/services/schedule-common.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';
import { NgxFileDropEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'import-document',
  templateUrl: './import-document.component.html',
  styleUrls: ['./import-document.component.scss']
})
export class ImportDocumentComponent implements OnInit {


  @ViewChild('fileUpload') fileUpload: any;

  selectedDocument: Document
  userRole: string = '';
  docUid: string

  user: QiUser;
  data: any[] = []

  basicAuth = this.globals.basicAuth;
  public files: NgxFileDropEntry[] = [];
  status: Status = new Status();

  constructor(
    public common: ScheduleCommonService,
    public globals: Globals,
    private http: HttpClient,
    private toastr: ToastrService,
    public _datepipe: DatePipe,
    public _router: Router,
    public route: ActivatedRoute,
    public _invokeService: InvokeService,
    public commonService: CommonService
  ) {
    this.user = this.globals.getUser();
    this.userRole = this.user.role;
    this.docUid = this.route.snapshot.params["uuid"];
  }

  ngOnInit(): void {

  }

  close() {
    this._router.navigate(["documentManagement/documentTree"])
  }

  import() {
    this.uploadFiles()
  }

  public fileOver(event) {
    console.log(event);
  }

  public fileLeave(event) {
    console.log(event);
  }

  public deleteFile(i) {
    this.files.splice(i, 1);
    this.status = new Status()
  }

  public setFiles(file: NgxFileDropEntry[]) {
    this.files = file;
  }
  public displayFileName(item) {
    return item[0].fileEntry.name;
  }


  public uploadFiles(): void {

    for (const file of this.fileUpload.files) {
      const formData = new FormData()
      formData.append('file', file, file.name);
      formData.append("doc", JSON.stringify({ "name": file.name }))
      let headers = new HttpHeaders()
        .set("Authorization", this.basicAuth).set('Client', this.commonService.getTenantUid());
      let baseUrl = environment.apiUrl;
      this.http.post(baseUrl + 'docmgmt/doc/' + this.docUid + '/import', formData, { headers: headers, responseType: 'json' })
        .subscribe(data => {
          this.commonService.success("Imported document successfully");
          this.commonService.navigate("documentManagement/documentTree")
        }, error => {

        })
    }

  }

}

