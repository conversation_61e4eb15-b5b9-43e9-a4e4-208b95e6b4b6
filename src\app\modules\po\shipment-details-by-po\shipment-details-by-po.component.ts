import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { POListStatusCodes, POStatus } from 'app/core/qinspect/_common/AppConstants';
import { Util } from 'app/core/qinspect/_common/util';
import { ROLE } from 'app/enum-constants';
import { ICON_LIST } from 'app/icon-list';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { CustomDateTimeTooltipComponent } from 'app/shared/custom-date-time-tooltip/custom-date-time-tooltip.component';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { ReplaySubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ImportShipmentDetailsByPoComponent } from './import-shipment-details-by-po/import-shipment-details-by-po.component';
import {
  PoShipmentAttachmentDialogComponent,
} from './po-shipment-attachment-dialog/po-shipment-attachment-dialog.component';

@Component({
  selector: 'shipment-details-by-po',
  templateUrl: './shipment-details-by-po.component.html',
  styleUrls: ['./shipment-details-by-po.component.scss']
})
export class ShipmentDetailsByPoComponent extends BaseTableSharedComponent {
  poNumber: string = '';
  poUid: string = '';
  shipmentList: any[] = [];
  shipmentColumnDefs: any[] = [];
  showShipment: boolean = false;
  showGrn: boolean = false;
  showClose: boolean = false;
  userInfo = JSON.parse(localStorage.getItem("user"));
  hdr: string = "Shipment Details";
  poDetails: any;
  poDetailColumnDefs: any[] = [];
  articleWisePoDetails: any[] = [];
  currentActivityName: string = '';
  accessStatus: string = '';
  invoiceNumber: string = '';
  selectedShipment: any[] = [];
  poStatus: any;
  currentActivityStatus: any;
  isShowShipmentDialog: boolean = false;
  tabindex: any = 0;
  allDesignCodes: any[] = [];
  designCodeList: any[] = [];
  articleColumns: any[] = [];
  showDetails: boolean = false;
  allDesignCodesWithActivities: any[] = [];
  shipTo: any;
  buttonActions: any[] = [];
  toolBarActions: any[] = [];
  totalPoQuantity: number = 0;
  totalShippedQty: number = 0;
  isValid: boolean = false;
  isValidClose: boolean = false;
  activityInfoMessage = [{ severity: 'info', detail: 'T&A Not Started yet (OR) Shipment not enabled yet.' }];

  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  public init(): void {
    this.route.queryParams
      .pipe(takeUntil(this.$destroyed))
      .subscribe(params => {
        this.currentActivityName = params['currentActivityName'];
        this.poStatus = params['poStatus'];
        this.currentActivityStatus = params['currentActivityStatus'];
      });
    this.route.paramMap
      .pipe(takeUntil(this.$destroyed))
      .subscribe(paramMap => {
        this.poNumber = paramMap.get('poNumber');
        this.invoiceNumber = paramMap.get('invoiceNumber');
        this.poUid = paramMap.get('uuid');
        this.showDetails = this._commonService.getTenantConfig()?.tenantUid != 'demo';
        this.hdr = this.showDetails ? `Shipment Details For PO Number: ${this.poNumber}` : `Shipment Details For Invoice Number: ${this.invoiceNumber}`;
        this.onRefreshShipments();
      });
    super.init();
  }

  public closeShipment(): void {
    this._commonService.warning("Once the shipment is closed , no further shipments can be made.")
    if (!this.selectedArticle) {
      this._commonService.error("Please select an article");
      return;
    } else {
      const filteredArticles = this.articleWisePoDetails.filter(poDetails =>
        this.selectedArticle?.some(article =>
          article.designCode === poDetails?.designCodeCode
        )
      );
      let isAllDesignCodeSame = filteredArticles.every(item => filteredArticles[0].designCodeCode == item.designCodeCode);
      let payload = isAllDesignCodeSame ? [filteredArticles[0]] : filteredArticles;
      this._commonService.openDialogForConfirmation(`Are you sure you want to close shipment?`).subscribe(confirm => {
        if (confirm) {
          this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.closeShipment, '', '', payload)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                if (response) {
                  if (response.code == 200) {
                    this._commonService.success(response.message);
                    this.selectedShipmentArticleList = [];
                    this.onRefreshShipments();
                  } else {
                    this._commonService.error(response.message)
                  }
                }
              },
              error: error => {
                this._commonService.handleError(error);
              }
            })
        }
      })
    }
  }

  public closeGRN(): void {
    this._commonService.warning("Once the GRN is closed, no further GRN can be processed for this article")
    if (!this.selectedArticle) {
      this._commonService.error("Please select an article");
      return;
    } else {

      const filteredArticles = this.articleWisePoDetails.filter(poDetails =>
        this.selectedArticle?.some(article =>
          article.designCode === poDetails?.designCodeCode
        )
      );
      let isAllDesignCodeSame = filteredArticles.every(item => filteredArticles[0].designCodeCode == item.designCodeCode);
      let payload = isAllDesignCodeSame ? [filteredArticles[0]] : filteredArticles;
      this._commonService.openDialogForConfirmation(`Are you sure you want to close GRN?`).subscribe(confirm => {
        if (confirm) {
          this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.closeGRN, '', '', payload)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                if (response) {
                  if (response.code == 200) {
                    this._commonService.success(response.message);
                    this.selectedGrnArticleList = [];
                    this.onRefreshShipments();
                  } else {
                    this._commonService.error(response.message)
                  }
                }
              },
              error: error => {
                this._commonService.handleError(error);
              }
            })
        }
      })
    }


  }
  public closeArticle(): void {
    let designCodeList = this.allDesignCodes.filter(item => item.processType)[0];
    designCodeList.processType != this._commonService.processType.RAWMATERIAL ? this._commonService.warning('Once article is closed, no further actions will be allowed. Please check before closing if total qty is inwarded') : this._commonService.warning("If article is closed GRN update will not be allowed, Please check before closing if total qty is in warded")
    if (!this.selectedArticle) {
      this._commonService.error("Please select an article");
      return;
    } else {
      const filteredArticles = this.articleWisePoDetails.filter(poDetails =>
        this.selectedArticle?.some(article =>
          article.designCode === poDetails?.designCodeCode
        )
      );

      let isAllDesignCodeSame = filteredArticles.every(item => filteredArticles[0].designCodeCode == item.designCodeCode);
      let payload = isAllDesignCodeSame ? [filteredArticles[0]] : filteredArticles;
      let url;
      if (designCodeList.processType != this._commonService.processType.RAWMATERIAL) {
        url = APP_UI_CONFIG.PO.closeArticle;
        this.closeArticleByProcessType(url, designCodeList, payload)
      } else {
        url = APP_UI_CONFIG.PO.closePo;
        this.closeArticleByProcessType(url, designCodeList, payload)
      }
    }
  }

  private closeArticleByProcessType(url, designCodeList, filteredArticles): void {
    let payload;
    if (designCodeList.processType != this._commonService.processType.RAWMATERIAL) {
      payload = filteredArticles
    } else {
      let payloadObj = {};
      const designCodes = filteredArticles.map(item => item.designCodeUid).join(', ');
      payloadObj['poNo'] = this.poNumber;
      payloadObj['did'] = designCodes;
      payload = payloadObj;
    }
    this._commonService.openDialogForConfirmation(`Are you sure you want to close Article?`).subscribe(confirm => {
      if (confirm) {
        this.invokeService.serviceInvocation(url, '', '', payload).pipe(takeUntil(this.$destroyed))
          .subscribe({
            next: response => {
              if (response) {
                if (response.code == 200) {
                  response.message ? this._commonService.success(response.message) : this._commonService.success("Close Article done successfully");
                  this.selectedClosedArticleList = [];
                  this.onRefreshShipments();
                } else {
                  this._commonService.error(response.message)
                }
              }
            },
            error: error => {
              this._commonService.handleError(error);
            }
          })
      }
    })
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'PO Shipments Template',
        icon: 'add',
        secondary: true,
        isSvg: false,
        show: (this.enableImportAndAddShipment() && this.totalPoQuantity > this.totalShippedQty),
        command: (action) => {
          this.onDownloadPOShipmentsTemplate();
        }
      },
      {
        name: 'Add Shipment',
        icon: 'add',
        primary: true,
        isSvg: false,
        show: (this.enableImportAndAddShipment() && this.totalPoQuantity > this.totalShippedQty),
        command: (action) => {
          this.onAddShipment();
        }
      },

    ];
    this.toolBarActions = [
      {
        name: 'Import',
        svgIcon: ICON_LIST.IMPORT_BLUE,
        show: (this.enableImportAndAddShipment() && this.totalPoQuantity > this.totalShippedQty),
        command: (action) => {
          this.onImportShipment();
        }
      },
      {
        name: 'Export',
        svgIcon: ICON_LIST.EXPORT_BLUE,
        show: this.shipmentList?.length > 0,
        command: (action) => {
          this.onExportShipment();
        }
      }
    ]
  }

  public onRefreshShipments(): void {
    this.getPoGetDetailsByPoNumber();
    this.prepareShipmentColumns();
    this.getArticleWiseDetailsByShipmentNumber();
    this.getArticleWiseDetailsByPoNumber();
  }

  private enableShipment(allDesignCodes, shipmentList): void {
    let designCodeList = allDesignCodes.filter(item => item.processType)[0];

    if (designCodeList.processType != this._commonService.processType.RAWMATERIAL) {
      this.showShipment = true;
      this.showClose = true;

      if (this._commonService.isSourcing()) {
        this.isValidClose = true;
      }
    } else {
      if (this._commonService.isVendor()) {
        this.isValidClose = true;
      }
      const tenantConfig = JSON.parse(sessionStorage.getItem("tenantConfig"));
      let shipmentStaus = JSON.parse(tenantConfig.shipmentStatus)
      const rmActivities = shipmentStaus[0]['rm'].split(',').map(item => item.trim().toLowerCase());
      let currentActivityStatus = this.currentActivity?.some(item => item.status != 2)

      if (this._commonService.getUseRole() == ROLE.ADMIN || (this._commonService.getUseRole() == ROLE.VENDOR && designCodeList.vendorCode == this._commonService.getCurrentUser().code && currentActivityStatus)) {
        this.showShipment = true;
      } else {
        this.showShipment = false;

      }
      if ((this._commonService.isVendor() || this._commonService.iAdmin()) && designCodeList.shipTo != this._commonService.getCurrentUser().code && currentActivityStatus) {
        this.showShipment = true;
      } else if (designCodeList.shipTo == this._commonService.getCurrentUser().code && (this._commonService.isVendor() || this._commonService.iAdmin())) {
        this.showShipment = false;
        this.showClose = true;

      }
      else if (designCodeList.shipTo == this._commonService.getCurrentUser().code && (this._commonService.isVendor() || this._commonService.iAdmin())) {
        this.showClose = true
      }
    }

  }
  getArticleCount: any;
  // 1. Get PO details by Po Number
  private getPoGetDetailsByPoNumber(): void {
    APP_UI_CONFIG.PO.getPODetailsByPoNumber.paramList.poNumber = this.poNumber;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getPODetailsByPoNumber)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.allDesignCodes = response?.map(({ value: value1, ...rest }) => ({ value1, ...rest }));
            this.totalPoQuantity = response?.reduce((a, b) => a + (b.qty ? b.qty : 0), 0);

            this.getArticleCount = Object.values(
              response.reduce((acc, item) => {
                acc[item.designCodeUid] = item;
                return acc;
              }, {})
            );
            this.getDesigncodeActivityByPoNumber();
          }

        }, error: error => {

        }
      })
  }
  currentActivity: any;
  currentActivitName: any;
  // 2. Get Design Code activities by Po Number
  private getDesigncodeActivityByPoNumber(): void {
    APP_UI_CONFIG.PO.getDesignCodeActivityByPoNumber.paramList.poNumber = this.poNumber;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getDesignCodeActivityByPoNumber)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            let activityResponses = response;
            this.currentActivity = response;
            this.mapActivityWithDesignCode(activityResponses);
          }
          if (response.length > 0) {
            let currentActivityDetails = response.filter(item => item.activityCode)[0];
            this.currentActivitName = currentActivityDetails?.activityName;
          }
          else {
            let designCodeList = this.allDesignCodes.filter(item => item.processType)[0];
            if (designCodeList.processType == this._commonService.processType.RAWMATERIAL) {
              this.currentActivitName = 'close article';
            }
          }

        }, error: error => {

        }
      })
  }
  // 3. map Design Code activities with design codes
  designCodeListWithActivities: any[] = [];
  private mapActivityWithDesignCode(activityResponses: any[]) {

    this.designCodeListWithActivities = this.designCodeListWithActivities.map(item => {
      let designCodeActivity = activityResponses.filter(activity => item.designCodeUid == activity.designCodeUid)[0];
      if (designCodeActivity) {
        return ({ activityDetails: designCodeActivity, ...item })
      } else {
        return item;
      }
    })
    this.prepareArticleColumns();

    const designCodeListWithActivities = this.allDesignCodes.map(item => {
      let designCodeActivity = activityResponses.filter(activity => item.designCodeUid == activity.designCodeUid)[0];
      if (designCodeActivity) {
        return { currentActivityName: designCodeActivity.currentActivityName, shipmentBookingQty: item.shippedQty, ...item }
      }
    });
    this.getShipmentListByPoNumber(designCodeListWithActivities);
  }

  //4. Get Shipment List by PO Number
  private getShipmentListByPoNumber(designCodes): void {
    APP_UI_CONFIG.PO.getShipmentListByPONumber.paramList.poNumber = this.poNumber;
    this.subscription.add(this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getShipmentListByPONumber).subscribe({
      next: shipmentList => {
        if (shipmentList && shipmentList.length > 0) {
          this.enableShipment(this.allDesignCodes, shipmentList);
          this.prepareShipmentData(this.groupCommonShipmentToSingleShipment(Util.clone(shipmentList)));
          this.mapShipmentDataWithDesignCode(designCodes, this.groupCommonShipmentByDesignCode(Util.clone(shipmentList)));
        } else {
          this.mapShipmentDataWithDesignCode(designCodes, []);
          this.prepareTableActions();
        }
      }, error: error => {
        this.mapShipmentDataWithDesignCode(designCodes, []);
      }
    }))
  }
  private prepareShipmentData(shipments: any[]): void {
    this.shipmentList = shipments.map(shipment => ({
      deliveryDateText: this._commonService.dateFormat(shipment.deliveryDate),
      actualGRDateText: this._commonService.dateFormat(shipment.actualGRDate),
      ...shipment
    }));
    this.totalShippedQty = this.shipmentList.reduce((a, b) => a + (b.shipmentBookingQty ? b.shipmentBookingQty : 0), 0);
    this.prepareTableActions();
  }

  //5. Group Shipments
  private groupCommonShipmentToSingleShipment(shipmentList: any[]): any[] {
    let groupSignShipmentList: any[] = [];
    shipmentList.forEach(shipment => {
      let findIndex = groupSignShipmentList.findIndex(item => item.shipmentNo == shipment.shipmentNo);
      if (findIndex > -1) {
        if (groupSignShipmentList[findIndex].shipmentBookingQty >= 0) {
          groupSignShipmentList[findIndex].shipmentBookingQty += shipment.shipmentBookingQty ? parseInt(shipment.shipmentBookingQty) : 0
        }
        if (groupSignShipmentList[findIndex].receivedQty >= 0) {
          groupSignShipmentList[findIndex].receivedQty += shipment.receivedQty ? parseInt(shipment.receivedQty) : 0
        } else {
          groupSignShipmentList[findIndex].receivedQty = 0
        }
      } else {
        shipment['receivedQty'] = shipment.receivedQty ? shipment.receivedQty : 0;
        groupSignShipmentList.push(shipment);
      }
    });
    return groupSignShipmentList;
  }
  //5. Group Shipments
  private groupCommonShipmentByDesignCode(shipmentList: any[]): any[] {
    let groupSignShipmentList: any[] = [];
    shipmentList.forEach(shipment => {
      let findIndex = groupSignShipmentList.findIndex(item => item.designCodeUid == shipment.designCodeUid);
      if (findIndex > -1) {
        if (groupSignShipmentList[findIndex].shipmentBookingQty >= 0) {
          groupSignShipmentList[findIndex].shipmentBookingQty += shipment.shipmentBookingQty ? parseInt(shipment.shipmentBookingQty) : 0
        } else {
          groupSignShipmentList[findIndex].shipmentBookingQty = 0
        }
        if (groupSignShipmentList[findIndex].receivedQty >= 0) {
          groupSignShipmentList[findIndex].receivedQty += shipment.receivedQty ? parseInt(shipment.receivedQty) : 0
        } else {
          groupSignShipmentList[findIndex].receivedQty = 0
        }
      } else {
        shipment['receivedQty'] = shipment.receivedQty ? shipment.receivedQty : 0;
        groupSignShipmentList.push(shipment);
      }
    });
    return groupSignShipmentList;
  }


  private mapShipmentDataWithDesignCode(designCodes, shipmentList): void {
    this.allDesignCodesWithActivities = designCodes.map(item => {
      let shipmentDesignCode = shipmentList.find(shipment => shipment?.designCode == item?.designCodeCode);
      if (shipmentDesignCode) {
        return { shipmentBookingQty: shipmentDesignCode.shipmentBookingQty, receivedQty: shipmentDesignCode.receivedQty, ...item };
      } else {
        return item;
      }
    });
  }
  //Download Template
  public onDownloadPOShipmentsTemplate() {
    this.invokeService.downloadFile(APP_UI_CONFIG.PO.downloadPOShipmentTemplate, '', `PO_${this.poNumber}_Shipment_template.csv`, '');
  }

  //Import Data
  public onImportShipment(): void {
    this._commonService.openDialog(ImportShipmentDetailsByPoComponent, '', '', '40%').subscribe(res => {
      if (res) {
        this.init();
      }
    });
  }
  //Export Columns
  public onExportShipment(): void {
    let shipmentNumberList: string[] = [];
    if (this.selectedShipment.length == 0) {
      this._commonService.error('Please Select at-least one Row to export.')
      return;
    }
    this.selectedShipment.forEach(element => {
      shipmentNumberList.push(element.shipmentNo);
    });
    let payload = {
      uids: shipmentNumberList
    }
    this.selectedShipment = [];
    this.invokeService.downloadFile(APP_UI_CONFIG.PO.exportShipment, '', `PO_Shipments_List.csv`, payload);
    this.init();
  }


  //on Add Shipment 
  public onAddShipment() {
    this.isShowShipmentDialog = true;
    this.getShipmentCodeListByPoNumber();
  }
  //Get Shipment Code list PO Number
  private getShipmentCodeListByPoNumber(): void {
    APP_UI_CONFIG.PO.getShipmentDetailsByPonumber.paramList.poNumber = this.poNumber;
    this.subscription.add(this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getShipmentDetailsByPonumber)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.poDetails = response;
          }
        }, error: error => {
        }
      }))

  }


  //Prepare Shipment columns Table Grid Columns
  private prepareShipmentColumns(): void {
    this.shipmentColumnDefs = [
      {
        field: '',
        headerCheckboxSelection: true,
        checkboxSelection: true,
        showDisabledCheckboxes: true,
        maxWidth: 50,
      },
      {
        headerName: "PO Number", field: "purchaseOrderNo", sortable: true, filter: 'agSetColumnFilter', unSortIcon: true
      },
      {
        headerName: "Shipment Number", field: "shipmentNo", sortable: true, filter: 'agSetColumnFilter', unSortIcon: true
      },
      {
        headerName: "Invoice Number", field: "invoiceNumber", sortable: true, filter: 'agSetColumnFilter', unSortIcon: true
      },
      {
        headerName: "LR Details", field: "lrNumber", sortable: true, filter: 'agSetColumnFilter', unSortIcon: true
      },
      {
        headerName: "Shipped Qty", field: "shipmentBookingQty", sortable: true, filter: 'agSetColumnFilter', unSortIcon: true
      },
      {
        headerName: "GRN Qty", field: "receivedQty", sortable: true, filter: 'agSetColumnFilter', unSortIcon: true
      },
      {
        headerName: "Vendor", field: "vendorName", sortable: true, filter: 'agSetColumnFilter', unSortIcon: true
      },
      {
        headerName: "Shipping Date", field: "deliveryDateText",
        minWidth: 220,
      },
      {
        headerName: "GRN Date", field: "actualGRDateText",
        minWidth: 220,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareShipmentGridSActions(params.data), onAction: (action) => this.onAction(action) }
        }
      }
    ];
    this.poDetailColumnDefs = [
      {
        headerName: "PO Number", field: "purchaseOrderNo", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
      },
      {
        headerName: "Vendor Code", field: "vendorCode", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
      },
      {
        headerName: "Order Qty", field: "qty", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
      },
      {
        headerName: "Shipped Qty", field: "shippedQty", sortable: true, filter: 'agSetColumnFilter', unSortIcon: true
      },
      {
        headerName: "Shipment Code", field: "shipmentCode", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareShipmentActionIconButtons(params.data), onAction: (action) => this.onShipmentAction(action) }
        }
      }
    ];
  }

  //Shipment Grid Actions
  public prepareShipmentGridSActions(row): any[] {
    let iconsList: any[] = [];
    iconsList.push({ type: 'view_shipment_po', title: "View Size Wise Shipment Quantity/Update GRN Qty", icon: row.actualGRDate ? 'view_green' : 'view', data: row });
    if (this.poStatus !== POListStatusCodes.PO_CLOSED) {
      iconsList.push({ type: 'add_doc', title: "Attachments", icon: 'view_file_blue', data: row });
    }
    return iconsList;
  }
  //Actions
  public onAction(action: any): void {
    switch (action.type) {
      case 'view_shipment_po':
        let designCodeList = this.allDesignCodes.filter(item => item.processType)[0];
        this._router.navigate([`po/shipment-details/${this.poNumber}/${this.poUid}/update/${action.data.shipmentNo}/${designCodeList.processType}`], { queryParams: { currentActivityName: this.currentActivityName, shipmentCode: action.data.code, poStatus: this.poStatus } })
        break;
      case 'view_doc':
        this.openShipmentAttachmentDialog(action?.data?.shipmentNo);
        break;
      case 'add_doc':
        this.openAddShipmentAttachmentDialog(action?.data);
        break;
      case 'view_shipment_details_article':
        this.navigateToShipmentDetailsByDesignCode(action?.data);
        break;
      default:
        break;
    }
  }

  //Dialog
  public prepareShipmentActionIconButtons(row): any[] {
    let iconsList: any[] = [];
    let toleranceQty = row.qty + Math.round((row.tolerance / 100) * row.qty);
    if (!row.shippedQty || row.shippedQty < toleranceQty) {
      iconsList.push({ type: 'add_shipment', title: "Add Shipment", icon: 'plus-circle_blue', data: row });
    } else {
      iconsList.push({ type: 'view_shipment', title: "Shipment Closed  Please ", icon: 'view_green', data: row });
    }
    return iconsList;
  }

  public onShipmentAction(action: any): void {
    let designCodeList = this.allDesignCodes.filter(item => item.processType)[0];
    if (action.type == 'add_shipment') {
      this._router.navigate([`po/shipment-details/${this.poNumber}/${action.data.shipmentCode}/add/null/${designCodeList.processType}`], { queryParams: { vendorCode: designCodeList.vendorCode, poStatus: this.poStatus, shipmentNumber: designCodeList.processType, shipmentCode: action.data.shipmentCode } })
    } if (action.type == 'view_shipment') {
      this._router.navigate([`po/shipment-details/${this.poNumber}/${action.data.shipmentCode}/view/null/${designCodeList.processType}`], { queryParams: { vendorCode: designCodeList.vendorCode, poStatus: this.poStatus, shipmentNumber: designCodeList.processType, shipmentCode: action.data.shipmentCode } })
      this.isShowShipmentDialog = false;
    }
  }

  public openShipmentAttachmentDialog(shipmentNo: string): void {
    const dialogRef = this.dialog.open(PoShipmentAttachmentDialogComponent, {
      minWidth: '30%',
      data: { data: shipmentNo },
      disableClose: true,
    });
    dialogRef.componentInstance.isShowAddViewAttachments = false;
    dialogRef.componentInstance.isShowOnlyViewAttachments = true;
  }

  private navigateToShipmentDetailsByDesignCode(row): void {
    this._router.navigate([`po/po-shipment/shipment-details-by-designcode/ponumber/${this.poNumber}/designCode/${row.designCode}/poUid/${row.uuid}`], { queryParams: { articleWiseOrderedQty: row.poQty } });
  }
  public openAddShipmentAttachmentDialog(row): void {
    const dialogRef = this.dialog.open(PoShipmentAttachmentDialogComponent, {
      width: '50%',
      data: { data: row.shipmentNo },
      disableClose: true,
    });
    if (this._commonService.isVendor() && this.allDesignCodes[0]?.vendorCode == this._commonService.getCurrentUser().code) {
      dialogRef.componentInstance.isShowAddViewAttachments = true;
      dialogRef.componentInstance.isShowOnlyViewAttachments = false;
    } else {
      dialogRef.componentInstance.isShowAddViewAttachments = false;
      dialogRef.componentInstance.isShowOnlyViewAttachments = true;
    }

  }


  private enableImportAndAddShipment(): boolean {
    let isValid: boolean = false;
    if (!this.showDetails) {
      isValid = true;
    }
    else {
      if (this.poStatus == POListStatusCodes.PO_CLOSED) {
        isValid = false;
      } else {
        const tenantConfig = JSON.parse(sessionStorage.getItem("tenantConfig"));
        let shipmentStaus = JSON.parse(tenantConfig.shipmentStatus)
        const rmActivities = shipmentStaus[0]['rm'].split(',').map(item => item.trim().toLowerCase());
        const fgActivities = shipmentStaus[0]['fg'].split(',').map(item => item.trim().toLowerCase());
        let designCodeList = this.allDesignCodes.filter(item => item.processType)[0];
        if (designCodeList.processType == this._commonService.processType.RAWMATERIAL) {
          let currentActivityStatus = this.currentActivity.some(item => item.status != 2)
          if (this._commonService.getUseRole() == ROLE.ADMIN || (this._commonService.getUseRole() == ROLE.VENDOR && designCodeList.vendorCode == this._commonService.getCurrentUser().code && rmActivities.includes(this.currentActivityName?.toLowerCase())) && currentActivityStatus) {
            isValid = true;
          } else {

            isValid = false;
          }
          if ((this._commonService.isVendor() || this._commonService.iAdmin()) && rmActivities.includes(this.currentActivityName?.toLowerCase()) && designCodeList.shipTo != this._commonService.getCurrentUser().code && currentActivityStatus) {
            isValid = true;
          } else if (designCodeList.shipTo == this._commonService.getCurrentUser().code && (this._commonService.isVendor() || this._commonService.iAdmin()) && rmActivities.includes(this.currentActivityName?.toLowerCase())) {
            isValid = false;
          }
        } else {

          if ((this.currentActivity?.some(item => item.currentActivityName.toLowerCase() == 'confirm shipping complete') && (this._commonService.getUseRole() == ROLE.ADMIN || this._commonService.getUseRole() == ROLE.VENDOR))) {
            isValid = true;
          } else {
            if ((this.currentActivityName.toLowerCase()) === 'acknowledge grn completion') {
              isValid = false;
            }
            isValid = false;
          }
        }
      }
    }
    return isValid;
  }


  //Get All articles shipment details By Po Number
  private getArticleWiseDetailsByShipmentNumber(): void {
    let urlObject = {
      url: `sourcing/po/shipment/articleWise?1=1&po=${this.poNumber}`,
      type: 'GET'
    }
    this.subscription.add(this.invokeService.serviceInvocation(urlObject)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          this.prepareData(res);
        }, error: error => {

        }
      }))
  }

  //Get All articles PO details By Po Number
  private getArticleWiseDetailsByPoNumber(): void {
    let urlObject = {
      url: `sourcing/po/articleWise?1=1&po=${this.poNumber}`,
      type: 'GET'
    }
    this.subscription.add(this.invokeService.serviceInvocation(urlObject)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          this.articleWisePoDetails = res;
        }, error: error => {

        }
      }))
  }





  private prepareData(designCodeListWithActivities): any {
    this.designCodeListWithActivities = designCodeListWithActivities.map(item => {
      return { deliveryDateText: this._commonService.dateFormat(item.deliveryDate), actualGRDateText: this._commonService.dateFormat(item.actualGRDate), ...item }
    });
    this.getDesigncodeActivityByPoNumber()
  }

  private prepareArticleColumns(): void {
    this.articleColumns = [
      {
        field: '',
        headerCheckboxSelection: (this.isAllCheckBoxEnabled()),
        checkboxSelection: params => (this.returnIsSelectionEnabled(params.data)),
        showDisabledCheckboxes: true,
        maxWidth: 50,
      },
      {
        headerName: "Article Number", field: "designCode", sortable: true, unSortIcon: true, filter: 'agSetColumnFilter'
      },
      {
        headerName: "Brand", field: "brand", sortable: true, unSortIcon: true, filter: 'agSetColumnFilter'
      },
      {
        headerName: "Category", field: "category", sortable: true, unSortIcon: true, filter: 'agSetColumnFilter'
      },
      {
        headerName: "Current Activity", field: "activityDetails?.currentActivityName", pinned: 'right', sortable: true, unSortIcon: true, minWidth: 180, filter: 'agSetColumnFilter',
        valueGetter: params => this.mapArticleStatus(params.data)
      },
      {
        headerName: "Ordered Qty", field: "poQty", sortable: true, unSortIcon: true,
      },
      {
        headerName: "Shipped Qty", field: "shipmentBookingQty", sortable: true, unSortIcon: true,
      },
      {
        headerName: "GRN Qty", field: "receivedQty", sortable: true, unSortIcon: true,
      },
      {
        headerName: "Delivery Date", field: "deliveryDateText", sortable: true, unSortIcon: true, minWidth: 180, filter: 'agSetColumnFilter',
      },
      {
        headerName: "Actual GR Date", field: "actualGRDateText", sortable: true, unSortIcon: true, minWidth: 180, filter: 'agSetColumnFilter',
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionArticleIconButtons(params.data), onAction: (action) => this.onAction(action) }
        }
      }

    ];
  }

  private isAllCheckBoxEnabled(): boolean {
    let designCodeList = this.allDesignCodes.filter(item => item.processType)[0];
    if (this._commonService.isSourcing()) {
      return this.designCodeListWithActivities.every(item =>
        item.activityDetails?.activityCode.toLowerCase() === this.designCodeListWithActivities[0].activityDetails?.activityCode.toLowerCase() &&
        ['grn completed', 'close article'].includes(item.activityDetails?.activityCode.toLowerCase())
      );
    }
    if (this._commonService.isVendor() && designCodeList.processType != this._commonService.processType.RAWMATERIAL) {
      return this.designCodeListWithActivities.every(item =>
        item.activityDetails?.activityCode.toLowerCase() === this.designCodeListWithActivities[0].activityDetails?.activityCode.toLowerCase() &&
        ['shipping completed'].includes(item.activityDetails?.activityCode.toLowerCase())
      );
    } else if (this._commonService.isVendor() && designCodeList.processType == this._commonService.processType.RAWMATERIAL && designCodeList.vendorCode == this._commonService.getCurrentUser().code) {
      return this.designCodeListWithActivities.every(item =>
        item.activityDetails?.activityCode.toLowerCase() === this.designCodeListWithActivities[0].activityDetails?.activityCode.toLowerCase() &&
        ['shipping completed'].includes(item.activityDetails?.activityCode.toLowerCase())
      );
    }
  }

  public mapArticleStatus(row): string {
    let designCodeList = this.allDesignCodes.filter(item => item.processType)[0];
    let articleStatus = this.articleWisePoDetails.filter(item => item.designCodeCode == row.designCode)[0].status;
    return row.activityDetails?.currentActivityName ? row.activityDetails?.currentActivityName : designCodeList.processType != this._commonService.processType.RAWMATERIAL ? 'Article Closed' : articleStatus == POStatus.PO_CLOSED ? 'Article Closed' : 'Close Article';
  }

  public prepareActionArticleIconButtons(row): any[] {
    let iconsList: any[] = [];
    iconsList.push({ type: 'view_shipment_details_article', title: "View Shipment Details", icon: 'shipping', data: row });
    return iconsList;
  }
  public onRefreshArticleGrid() {

  }
  selectedShipmentArticleList: any[] = [];
  selectedGrnArticleList: any[] = [];
  selectedClosedArticleList: any[] = [];

  public onSelectionChanged(data: any[]): void {
    this.selectedShipment = data;
  }
  selectedArticle: any;
  public onSelectionArticleChanged(data: any): void {
    this.selectedArticle = data;

    const filteredActivities = this.currentActivity.filter(activity =>
      this.selectedArticle?.some(article =>
        article.designCode === activity.designCodeCode
      )
    );

    this.selectedShipmentArticleList = filteredActivities.filter(item => item.activityCode.toLowerCase() == 'shipping completed');
    this.selectedGrnArticleList = filteredActivities.filter(item => item.activityCode.toLowerCase() == 'grn completed');
    this.selectedClosedArticleList = filteredActivities.filter(item => item.activityCode.toLowerCase() == 'close article');



    if (filteredActivities.some(item => item.activityCode.toLowerCase() == 'grn completed' || item.activityCode.toLowerCase() == 'close article') && this._commonService.isVendor()) {
      this._commonService.error("Shipment closed for selected article");
      this.selectedShipmentArticleList = [];
      return;
    }

    let designCodeList = this.allDesignCodes.filter(item => item.processType)[0];
    if (designCodeList.processType != this._commonService.processType.RAWMATERIAL) {
      if (this.selectedGrnArticleList.length > 0 && this.selectedClosedArticleList.length > 0) {
        this._commonService.error("Articles with different current activity cannot be processed at the same time");
        this.selectedGrnArticleList = [];
        this.selectedClosedArticleList = [];
        return;
      }
    } else {
      if (this._commonService.getUseRole() == ROLE.ADMIN || (this._commonService.getUseRole() == ROLE.VENDOR && designCodeList.vendorCode == this._commonService.getCurrentUser().code)) {
        this.selectedClosedArticleList = filteredActivities.filter(item => item.activityCode.toLowerCase() == 'shipping completed');
      }

      if (filteredActivities.length <= 0 && this.selectedArticle.length > 0 && designCodeList.shipTo == this._commonService.getCurrentUser().code && (this._commonService.isVendor() || this._commonService.iAdmin())) {
        this.selectedClosedArticleList.length = 1
      }
    }


  }

  public returnIsSelectionEnabled(data): boolean {
    let designCodeList = this.allDesignCodes.filter(item => item.processType)[0];
    if (designCodeList.processType == this._commonService.processType.RAWMATERIAL && this.articleWisePoDetails.filter(item => item.designCodeCode == data.designCode && item.status == POStatus.PO_CLOSED).length > 0) {
      return false
    }
    if (!data.activityDetails && designCodeList.processType != this._commonService.processType.RAWMATERIAL) {
      return false
    }
    else if (!data.activityDetails && designCodeList.processType == this._commonService.processType.RAWMATERIAL && designCodeList.shipTo == this._commonService.getCurrentUser().code && (this._commonService.isVendor() || this._commonService.iAdmin())) {
      return true
    } else if (this._commonService.isVendor() && designCodeList.processType != this._commonService.processType.RAWMATERIAL && data.activityDetails.activityCode?.toLowerCase() == 'shipping completed') {
      return true

    }
    else if (designCodeList.processType == this._commonService.processType.RAWMATERIAL && this._commonService.getUseRole() == ROLE.ADMIN || (this._commonService.getUseRole() == ROLE.VENDOR && designCodeList.vendorCode == this._commonService.getCurrentUser().code) && data.activityDetails?.activityCode?.toLowerCase() == 'shipping completed' && this._commonService.isVendor()) {
      return true

    } else if (data?.activityDetails?.activityCode?.toLowerCase() == 'grn completed' && this._commonService.isSourcing()) {
      return true

    } else if (data?.activityDetails?.activityCode?.toLowerCase() == 'close article' && this._commonService.isSourcing()) {
      return true

    }
  }



  public onClose(): void {
    this._commonService.historyBack();
  }
  //Destroy
  public ngOnDestroy(): void {
    this.$destroyed.next(true);
    this.$destroyed.complete();
  }
}
