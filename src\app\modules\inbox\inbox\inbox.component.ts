import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { TimelineInboxItem } from 'app/core/qinspect/_model/TimelineInboxItem.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import moment from 'moment';

@Component({
  selector: 'app-inbox',
  templateUrl: './inbox.component.html',
  styleUrls: ['./inbox.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class InboxComponent implements OnInit {
  filterText: string;
  showDropdown1: boolean = false;
  showDropdown2: boolean = false;
  inboxList: TimelineInboxItem[] = [];
  inboxListTemp: TimelineInboxItem[] = [];
  taskName: string;
  taskDescription: string;
  taskLink: string;
  taskUid: string;
  taskContent: any;
  inboxViewTypes: string[] = ['My Task', 'Assigned by Me', 'Approved Task', 'Reject Task', 'Forwarded Task'];
  filterList: string[] = ['In-Progress', 'Done', 'Delayed'];
  showTaskOperation: boolean = false;
  attachments: any[] = [];
  designcodeActivityDetail: any;
  startDate: Date | undefined;
  endDate: Date | undefined;
  dateRange: Date | undefined;
  userDetails: any;
  public latestTask: any;
  selectedTask: any;
  loading: boolean = true;
  constructor(
    public globals: Globals,
    public dialog: MatDialog,
    public _datepipe: DatePipe,
    public _router: Router,
    public route: ActivatedRoute,
    public _invokeService: InvokeService,
    public commonService: CommonService,
    private _sanitizer: DomSanitizer,
  ) {
    // config.placement = 'bottom-start';
    // // config.autoClose = false;
  }

  ngOnInit(): void {
    this.userDetails = this.commonService.getCurrentUser();
    this.onRefresh();
  }
  public onRefresh(): void {
    this.loading = true;
    this.dateRange = undefined;
    this.endDate = undefined;
    this.startDate = undefined;
    this.filterText = '';
    this.getInboxByUser(true);
  }

  private getInboxByUser(isCalledOnPageLoad: boolean): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.getInboxByUser).subscribe({
      next: res => {
        this.inboxList = res;
        this.inboxListTemp = res;
        this.loading = false;
        this.latestTask = this.inboxListTemp.reduce((latest, task) =>
          !latest || task.createdTime > latest.createdTime ? task : latest, null);
        if (isCalledOnPageLoad && (this.inboxListTemp.length > 0))
          this.onClickTask(this.inboxListTemp[0], false);
        this.filterInboxData(false);
      }, error: error => {
        this.loading = true;
      }
    })
  }

  public getPlannedEndDate(item) {
    if (!!item) {
      item = (typeof (item) == 'number') ? item : Number(item);
      let date: Date = new Date(item);
      return moment(date).format('MM/DD/YYYY');
    } else {
      return '';
    }

  }

  public getTime(item) {
    return moment(item.createdTime).fromNow();
  }

  public onClickTask(task: TimelineInboxItem, isClick: boolean) {
    this.selectedTask = null;
    setTimeout(() => {
      this.selectedTask = task;
      this.taskName = task?.name;
      this.taskDescription = task?.description;
      this.taskLink = task?.customUrl;
      this.showTaskOperation = true;
      this.taskContent = this._sanitizer.bypassSecurityTrustHtml(task.htmlContent);
    }, 100);
    if (task.customParams != null) {
      const params = JSON.parse(task.customParams);
      this.taskUid = params.uuid;
    }
    APP_UI_CONFIG.activity.updateInboxItemStatus.paramList['id'] = task.uuid;
    APP_UI_CONFIG.activity.updateInboxItemStatus.paramList['status'] = 'READ';
    this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.updateInboxItemStatus).subscribe(response => {
      if (response) {
        task = response;
        if (isClick && this.filterText == '' && (!this.startDate || !this.endDate))
          this.getInboxByUser(false);
      }
    });
  }

  public navigate(): void {
    if (this.taskUid != null)
      this.commonService.navigate(`${this.taskLink}/${this.taskUid}`);
    else
      this.commonService.navigate(`${this.taskLink}`);
  }

  public getTile(): string {
    let dCode = this.selectedTask.designCodeUid || '';
    return dCode ? `${dCode}- ${this.selectedTask.name}` : `${this.selectedTask.name}`;
  }

  public onFilter(event): void {
    this.filterText = event?.target?.value?.toLowerCase().trim();
    if (this.filterText == '') {
      this.inboxListTemp = Util.clone(this.inboxList);
    } else {
      let filterInboxListData = this.inboxList.filter(
        item => item.name?.toLowerCase().includes(this.filterText)
      )
      this.inboxListTemp = filterInboxListData;
    }

  }

  public taskBackgroundColor(data: any): string {
    let taskType: string = '';
    switch (data.status?.toLowerCase()) {
      case 'to do':
        taskType = 'toDo';//Green
        break;
      case 'in progress':
        taskType = 'inProgress';//orange
        break;
      case 'completed':
        taskType = 'completed';//orange
        break;
      case 'delayed':
        taskType = 'delayed';//orange
        break;
      default:
        taskType = 'task-row';
        break;
    }
    return taskType;
  }

  public filterInboxData(isFromApplyButton): void {
    this.startDate = this.dateRange?.[0];
    this.endDate = this.dateRange?.[1];
    if (!this.startDate || !this.endDate) {
      return;
    }

    let startDate = new Date(this.startDate);
    let endDate = moment(this.endDate).endOf("day").toDate();
    let filterInboxListData = this.inboxList?.filter(
      item => new Date(item.createdTime) >= startDate && new Date(item.createdTime) <= endDate
    )
    this.inboxListTemp = filterInboxListData;
    if ((this.inboxListTemp.length > 0) && isFromApplyButton) {
      this.onClickTask(this.inboxListTemp[0], false);
    } else {
      this.loading = false;
    }
  }

  public clearFilter(): void {
    this.loading = true;
    this.inboxListTemp = Util.clone(this.inboxList);
    this.onRefresh();
  }

  public viewRequest(item): void {
    const data = JSON.parse(item);
    if (data?.uuid) {
      if (this.commonService.getUseRole().toLowerCase() == 'sourcing') {
        if (item.type == "QUOTATION_REQUEST") {
          this.commonService.navigate(`sourcing/quotation/view-quotation-response/${data.uuid}`);
        }
        else if (item.type == "SOURCING_REQUEST") {
          this.commonService.navigate(`/sourcing/request/create/${data.uuid}`);
        }
      } else {
        this.commonService.navigate(`sourcing/quotation/create-edit-quotation-response/${data.uuid}`)
      }

    } else {
      this.commonService.error("Something went wrong. Please try again later");
    }

  }

  public onCloseDetails(): void {
    this.selectedTask = null;
    this.onRefresh();
  }

}
