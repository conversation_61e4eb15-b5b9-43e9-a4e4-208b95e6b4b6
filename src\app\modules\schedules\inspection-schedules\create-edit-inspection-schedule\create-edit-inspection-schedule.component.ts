import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ThemePalette } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { AuditDesignCode } from 'app/core/qinspect/_model/AuditDesignCode.model';
import { AuditShipmentReq } from 'app/core/qinspect/_model/AuditShipmentReq.model';
import { DesignCodeMaster } from 'app/core/qinspect/_model/DesignCodeMaster.model';
import { InspectionAgency } from 'app/core/qinspect/_model/InspectionAgency.model';
import { AMENDSCH, NEWSCH, QiSchedule } from 'app/core/qinspect/_model/schedule.model';
import { ShipmentDto } from 'app/core/qinspect/_model/ShipmentDto.model';
import { TenantConfig } from 'app/core/qinspect/_model/tenantConfig.model';
import { QiUser } from 'app/core/qinspect/_model/user.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { FORM_CONTROL_TYPES, ROLE, TABLE_ACTION_TYPES } from 'app/enum-constants';
import { BuildFormUtilityService } from 'app/services/build-form-utility.service';
import { DashboardService } from 'app/services/dashboard.service';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { environment, Partners } from 'environments/environment';
import moment from 'moment';
import { ReplaySubject } from 'rxjs';
import { distinctUntilChanged, takeUntil } from 'rxjs/operators';

import { CommonService } from '../../../../services/common.service';
import { InvokeService } from '../../../../services/invoke.service';
import { SaleOrderService } from '../../services/sale-order.service';
import { ScheduleCommonService } from '../../services/schedule-common.service';
import { AuditFlowAlertDialogComponent } from '../audit-flow-alert-dialog/audit-flow-alert-dialog.component';
import { MADURA_SCHEDULE_CREATE_FORM_MODEL } from './schedule-form.mode';

export interface AsinCodeList {
  code: string;
  offeredQty: number;
  orderedQty: number;
}

@Component({
  selector: 'create-edit-inspection-schedule',
  templateUrl: './create-edit-inspection-schedule.component.html',
  styleUrls: ['./create-edit-inspection-schedule.component.scss']
})
export class CreateEditInspectionScheduleComponent implements OnInit {
  @ViewChild('gridDesigncode') gridDesigncode: any;

  selectedDesigncodes: any[] = []
  searchResultDesigncodes: DesignCodeMaster[] = []
  designcodeList: DesignCodeMaster[] = []
  auditDt: any;
  schdMinDt: Date = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate());;
  auditDtText: any = new Date();
  auditTypeList: any[] = [];
  inspectionAgencyList: any[] = [];
  selectedVendor: string = '';
  selectedVendorObject: any;
  selectedFactory: any;
  rec: QiSchedule;
  user: QiUser;
  inspectionAgency: InspectionAgency;
  shipmentDto: ShipmentDto = new ShipmentDto();
  userRole: string = '';
  schedule: QiSchedule;
  hdr: string;
  showShipmentNoTextbox: boolean = true;
  shipmentPayload: AuditShipmentReq;
  submitDisabled: boolean = false;
  @Input() data: QiSchedule;
  @Input() type: string = "create";
  @Input() status: string = "create";
  @Input() mode: string = "audit";
  @Input() fromCalendar: boolean = false;
  @Input() startDate: any;
  @Input() disableSaveButton: boolean = false;
  @Output() closeEvent: EventEmitter<any> = new EventEmitter();
  enableMeridian: any = true;
  public color: ThemePalette = 'primary';
  public startDateControl = new FormControl(new Date());
  factory = 'Factory';
  shipmentNo: string = "";
  purchaseOrderNo: string = "";
  purchaseOrderNoFetchEnable: number = 1;
  asinCodeName: string = '';
  asinCodeOdrQty: number;
  asinCodeOfferQty: number;
  asinCodeList: AsinCodeList[] = [];
  columnDefs: any[] = [];
  columns: any[] = [
    { headerName: 'Inspection Date', field: 'createdTimeText' },
    { headerName: 'Offer Qty', field: 'offerQty' },
    { headerName: 'Order Qty', field: 'orderQty' },
    { headerName: 'Rejected Qty', field: 'rejectedQuantities' },
    { headerName: 'Overall Result', field: 'overAllResultValue' },
    { headerName: 'Re-Inspection Requested?', field: 'reInspectionRequested' },
    { headerName: 'Re-Inspection Comment', field: 'reInspectionRequestedComments' } // increase width
  ];

  disablePoFields: boolean = false
  hidePoFields: boolean = true;
  isDisabled: boolean = false;
  configParam: any;
  SupportingAuditors: any;
  auditDescription: string = '';
  tenantConfig: TenantConfig;
  isByShipment: boolean = false;
  shipmentSelectAuditType: string = '';
  tentUid: string = this.commonService.getTenantUid();
  public selectedDesignCodeColumnsDef: any = [];
  isAuditTypePPM: boolean = false;

  showDynamicForm: boolean = false;
  selectedPo: any;
  showSubmitButton: boolean = true;
  alreadyInspectionCreatedMessage: string = '';
  alreadyInspectionCreated: boolean = false;
  inspectionTableData: any[] = [];
  public scheduleForm: FormGroup;
  private destroyed$: ReplaySubject<boolean> = new ReplaySubject(1);
  public fields: any[] = [];
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  Partners = Partners;

  constructor(
    private _buildFormUtilityService: BuildFormUtilityService,
    public common: ScheduleCommonService,
    public globals: Globals,
    private saleOrderService: SaleOrderService,
    public _invokeService: InvokeService,
    public commonService: CommonService,
    public dashBoardService: DashboardService,
    public dialog: MatDialog,
    private cdRef: ChangeDetectorRef
  ) {
    this.user = this.globals.getUser();
    this.userRole = this.user.role;
  }

  public ngOnInit(): void {
    this.common.clearData();
    this.common.getData();
    if (this.tentUid == Partners.TITAN || this.tentUid == Partners.MADURA) {
      this.buildForm();
      this.showDynamicForm = true;
      this.getPoList();
      this.prepareSelectedDesignCodeColumns();
    }

    this.dashBoardService.getTetantnsConfig(this.commonService.getTenantUid()).subscribe(
      (data) => {
        this.tenantConfig = data;
      },
      (err) => {
        this.tenantConfig = {};
      }
    );

    this.columnDefs = [
      {
        headerName: "ASIN Code",
        field: "code",
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: "Ordered Quantity",
        field: "orderedQty",
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: "Offered Quantity",
        field: "offeredQty",
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: "Actions",
        pinned: "right",
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data) };
        },
      },
    ];


    let today = new Date(); // new Date();
    if (this.commonService.getTenantUid() == "lots") {
      this.factory = "Store";
    }
    if (this.data == null) {
      this.data = new QiSchedule();
    } else {
      // this.mode = "disabled"
    }
    today.setMinutes(0, 0, 0);

    this.shipmentPayload = new AuditShipmentReq();
    //this.auditDt = new Date(today);
    // this.auditDt = new Date();
    this.schedule = this.data;
    if (this.schedule?.auditId) {
      this.rec = this.schedule;
      if (this.status == "view") {
        this.hdr = "View Schedule";
        this.isDisabled = true;
      }
      if (this.status == "assign") {
        this.hdr = "Edit Schedule";
        this.isDisabled = true;
      } else {
        this.hdr = "Edit Schedule";
        this.isDisabled = false;
      }

      this.hidePoFields = false;
      this.disablePoFields = true;
      this.common.labList.selectedItem = this.rec.labUid;
      this.common.agencyList.selectedItem = this.rec.inspectionAgencyUid;
      let dt = new Date(this.rec.auditDate);
      // this.auditDt = dt;//new String(dt.getFullYear+"-"+dt.getMonth()+"-"+dt.getDay());
      this.auditDt = dt;
      this.schdMinDt = new Date(this.rec.auditDate);
      this.auditDtText =
        dt.getFullYear() + "-" + dt.getMonth() + "-" + dt.getDay();
    } else {
      this.rec = <QiSchedule>Util.clone(NEWSCH);
      this.rec.designcodes[0].designcode = "NA";
      this.hdr = "Add Schedule";
      this.rec.shipmentNo = "";
    }

    this.setFormData();
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];
    iconsList.push({
      type: "deleteSech",
      title: "Delete",
      icon: "delete",
      data: row,
    });
    return iconsList;
  }

  public getActions(event): void {
    let actionData: any = event;
    if (actionData.status == "deleteSech") {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data:
          "Are you sure you want to delete the ASIN : " +
          actionData.data?.code +
          "?",
        disableClose: true,
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result) {
          for (let i = 0; i < this.asinCodeList.length; i++) {
            if (this.asinCodeList[i].code == actionData.data?.code) {
              this.asinCodeList.splice(i, 1);
              this.selectedDesigncodes.splice(i, 1);
              this.gridDesigncode.rowData = this.asinCodeList;
              this.gridDesigncode.updateData(this.asinCodeList);
            }
          }
        }
      });
    }
  }

  private setFormData() {
    this.designcodeList = this.common.designcodeList.getData();
    if (this.rec.auditType.uuid != 0) {
      this.common.auditTypeList.setValue(this.rec.auditType.uuid);
    }

    if (this.userRole === "AUDITOR")
      this.common.auditorList.setValue(this.user.uuid);
    else {
      if (this.rec.auditor != null)
        this.common.auditorList.setValue(this.rec.auditor.uuid);
    }
    if (this.userRole === "VENDOR") {
      this.common.vendorList.setValue(this.user.uuid);
      let vendor = this.common.vendorList
        ?.getData()
        ?.find((obj) => obj.uuid == this.user.uuid);
      if (vendor) {
        this.selectedVendor = vendor.code + " - " + vendor.firstName;
        this.selectedVendorObject = vendor;
        this.common.vendorList.setValue(vendor.uuid);
      }
    } else {
      this.common.vendorList.setValue(this.rec.vendor.uuid);
      let vendor = this.common.vendorList
        ?.getData()
        ?.find((obj) => obj.uuid == this.rec.vendor.uuid);
      if (vendor) {
        this.selectedVendor = vendor.code + " - " + vendor.firstName;
        this.selectedVendorObject = vendor;
        this.common.vendorList.setValue(vendor.uuid);
      }
    }
    this.common.factoryList.setValue(this.rec.factory.uuid);
    if (this.rec.seasonAndYear)
      this.common.setSeasonAndYear(this.rec.seasonAndYear);

    this.rec.type = this.rec.auditType.type;
    if (this.rec.category != null)
      this.common.categoryList.setValue(this.rec.category.uuid);
    this.common.brandList.setValue(this.rec.designcodes[0].brandUid);
    this.common.customerList.setValue(this.rec.designcodes[0].customerUid);
    this.common.productTypeList.setValue(
      this.rec.designcodes[0].productTypeUid
    );
  }

  isEditable(sch: QiSchedule) {
    if (sch == null || sch == undefined) return false;
    if (sch.status == 10) return true;
    if (sch.status == 1 || sch.status == 3) return this.userRole === "ADMIN";
    // if (this.userRole === 'VENDOR') return false;
    return sch.statusText != "Deleted";
  }

  public getByPO() {
    this.onClearSelectedItem();
    this.saleOrderService.getShipmentByPo(this.rec.purchaseOrderNo).subscribe(
      (data) => {
        // this.disablePoFields = true
        // this.hidePoFields = false
        if (data.code == "400") {
          this.commonService.error(data.message);
        } else if (data.code == "200") {
          this.shipmentDto = data;
          this.shipmentNo = "";
          if (this.userRole === "AUDITOR")
            this.common.auditorList.setValue(this.user.uuid);
          //      else this.common.auditorList.setValue(this.shipmentDto.auditor.uuid);

          if (this.userRole === "VENDOR")
            this.common.vendorList.setValue(this.user.uuid);
          else {
            if (this.shipmentDto.vendor && this.shipmentDto.vendor.uuid) {
              this.common.vendorList.setValue(this.shipmentDto.vendor.uuid);
              this.selectedVendor =
                this.shipmentDto.vendor.code +
                " - " +
                this.shipmentDto.vendor.firstName;
              this.selectedVendorObject = this.shipmentDto.vendor;
              this.common.vendorList.selectedItem =
                this.shipmentDto.vendor.uuid;
            }
          }

          if (this.shipmentDto.factory && this.shipmentDto.factory.uuid) {
            this.common.factoryList.setValue(
              this.shipmentDto.factory.uuid.toString()
            );
            this.common.actualFactoryList.setValue(
              this.shipmentDto.factory.uuid.toString()
            );
            this.filteredFactoryList = this.common.factoryList.list;
          }

          if (
            this.shipmentDto.productCategory &&
            this.shipmentDto.productCategory.uuid
          )
            this.common.categoryList.setValue(
              this.shipmentDto.productCategory.uuid.toString()
            );

          if (this.shipmentDto.brand && this.shipmentDto.brand.uuid)
            this.common.brandList.setValue(
              this.shipmentDto.brand.uuid.toString()
            );
          //this.common.productTypeList.setValue(this.rec.designcodes[0].productTypeUid);

          if (this.shipmentDto.salesOrder && this.shipmentDto.salesOrder.qty) {
            this.rec.designcodes[0].orderQty = this.shipmentDto.salesOrder.qty;
            this.rec.designcodes[0].offerQty = 0.0;
          }

          if (this.shipmentDto.catalogUid)
            this.rec.designcodes[0].designcode =
              this.shipmentDto.salesOrder?.catalogUid;

          if (
            this.shipmentDto.salesOrder &&
            this.shipmentDto.salesOrder.description
          )
            this.rec.designcodes[0].description =
              this.shipmentDto.salesOrder.description;

          if (
            this.shipmentDto.shipments != null &&
            this.shipmentDto.shipments.length > 1
          ) {
            this.showShipmentNoTextbox = false;
          }

          if (
            this.shipmentDto.customer != null &&
            !!this.shipmentDto.customer
          ) {
            this.common.customerList.setValue(this.shipmentDto.customer.uuid);
          }

          if (!!this.shipmentDto.description) {
            this.auditDescription = this.shipmentDto.salesOrder.description;
          }

          if (!!this.shipmentDto.buyerPo) {
            this.rec.buyerPo = this.shipmentDto.buyerPo || "NA";
          }

          if (!!this.shipmentDto.productType?.name) {
            this.rec.productType = this.shipmentDto.productType?.name;
          }

          if (this.shipmentDto.purchaseOrder?.purchaseOrderItem)
            this.rec.purchaseOrderItem =
              this.shipmentDto.purchaseOrder?.purchaseOrderItem;

          if (this.shipmentDto.purchaseOrder?.salesOrderItem)
            this.rec.salesOrderItem =
              this.shipmentDto.purchaseOrder?.salesOrderItem;

          if (this.shipmentDto.purchaseOrder?.salesOrderNo)
            this.rec.salesOrderNo =
              this.shipmentDto.purchaseOrder?.salesOrderNo;

          //this.rec.designcodes[0].productTypeUid = this.common.productTypeList.selectedItem;
        }
      },
      (err) => {
        this.commonService.error(
          "Purchase Order not found for given PO number, please enter valid PO Number"
        );
      }
    );
  }

  getByShipment() {
    this.onClearSelectedItem();
    this.saleOrderService.getShipment(this.rec.shipmentNo).subscribe((data) => {
      if (data.code == "400") {
        this.commonService.error(data.message);
      } else if (data.code == "200") {
        this.shipmentDto = data;

        if (this.shipmentDto.auditTypes.length == 1) {
          this.shipmentSelectAuditType = this.shipmentDto.auditTypes[0].name;
          this.rec.auditType = this.shipmentDto.auditTypes[0];
        }

        if (
          this.shipmentDto?.shipment &&
          this.shipmentDto.shipment.purchaseOrderNo
        )
          this.rec.purchaseOrderNo = this.shipmentDto.shipment.purchaseOrderNo;

        if (this.userRole === "AUDITOR")
          this.common.auditorList.setValue(this.user.uuid);
        //      else this.common.auditorList.setValue(this.shipmentDto.auditor.uuid);

        if (this.userRole === "VENDOR")
          this.common.vendorList.setValue(this.user.uuid);
        else {
          if (this.shipmentDto.vendor && this.shipmentDto.vendor.uuid) {
            this.common.vendorList.setValue(this.shipmentDto.vendor.uuid);
            this.selectedVendor =
              this.shipmentDto.vendor.code +
              " - " +
              this.shipmentDto.vendor.firstName;
            this.selectedVendorObject = this.shipmentDto.vendor;
            this.common.vendorList.selectedItem = this.shipmentDto.vendor.uuid;
          }
        }

        if (this.shipmentDto.factory && this.shipmentDto.factory.uuid) {
          this.common.factoryList.setValue(
            this.shipmentDto.factory.uuid.toString()
          );
          this.common.actualFactoryList.setValue(
            this.shipmentDto.factory.uuid.toString()
          );
          this.filteredFactoryList = this.common.factoryList.list;
        }

        if (
          this.shipmentDto.productCategory &&
          this.shipmentDto.productCategory.uuid
        )
          this.common.categoryList.setValue(
            this.shipmentDto.productCategory.uuid.toString()
          );

        if (this.shipmentDto.brand && this.shipmentDto.brand.uuid)
          this.common.brandList.setValue(
            this.shipmentDto.brand.uuid.toString()
          );
        //this.common.productTypeList.setValue(this.rec.designcodes[0].productTypeUid);

        if (
          this.shipmentDto.purchaseOrder &&
          this.shipmentDto.purchaseOrder.qty
        ) {
          this.rec.designcodes[0].orderQty = this.shipmentDto.purchaseOrder.qty;
          this.rec.designcodes[0].offerQty =
            this.shipmentDto?.shipment?.shipmentBookingQty || "";
        }

        if (this.shipmentDto.salesOrder?.catalogUid)
          this.rec.designcodes[0].designcode =
            this.shipmentDto.salesOrder?.catalogUid;

        if (
          this.shipmentDto.salesOrder &&
          this.shipmentDto.salesOrder.description
        )
          this.rec.designcodes[0].description =
            this.shipmentDto.salesOrder.description;

        if (
          this.shipmentDto.shipments != null &&
          this.shipmentDto.shipments.length > 1
        ) {
          this.showShipmentNoTextbox = false;
        }

        if (this.shipmentDto.customer != null && !!this.shipmentDto.customer) {
          this.common.customerList.setValue(this.shipmentDto.customer.uuid);
        }

        if (!!this.shipmentDto.description) {
          this.auditDescription = this.shipmentDto.salesOrder.description;
        }

        if (!!this.shipmentDto.buyerPo) {
          this.rec.buyerPo = this.shipmentDto.buyerPo || "NA";
        }

        if (!!this.shipmentDto.productType?.name) {
          this.rec.productType = this.shipmentDto.productType?.name;
        }

        if (this.shipmentDto.purchaseOrder?.purchaseOrderItem)
          this.rec.purchaseOrderItem =
            this.shipmentDto.purchaseOrder?.purchaseOrderItem;

        if (this.shipmentDto.purchaseOrder?.salesOrderItem)
          this.rec.salesOrderItem =
            this.shipmentDto.purchaseOrder?.salesOrderItem;

        if (this.shipmentDto.purchaseOrder?.salesOrderNo)
          this.rec.salesOrderNo = this.shipmentDto.purchaseOrder?.salesOrderNo;
      }
    });
  }

  onAuditTypeSelect(event, value: boolean) {
    if (value) {
      for (let type of this.common.auditTypeList.getData()) {
        if (type.uuid == event.value) {
          this.rec.auditType = type;
          this.purchaseOrderNoFetchEnable =
            this.rec.auditType?.enableSapFetchByPo;
          let id: string = event.value.replace(/\//g, "");
          this.rec.purchaseOrderNo = "";
          this.onClearSelectedItem();
          this.getScheduleFieldConfig(id);
          break;
        }
      }
    } else {
      this.shipmentDto.auditTypes.forEach((element) => {
        if (element.uuid == event.value) {
          this.rec.auditType = element;
        }
      });
    }

    if (this.tentUid == "lots" && this.common.vendorList?.list?.length == 1) {
      let vendor = this.common.vendorList?.list[0]?.raw;

      this.selectedVendor = vendor.code + " - " + vendor.firstName;
      this.selectedVendorObject = vendor;
      this.common.vendorList.setValue(vendor.uuid);
      this.common.vendorList.selectedItem = vendor.uuid;
    }

    if (
      this.tentUid == "lots" &&
      this.common.productTypeList?.list?.length == 1
    ) {
      let pType = this.common.productTypeList?.list[0]?.raw;
      this.common.productTypeList.setValue(pType.uuid);
      this.common.productTypeList.selectedItem = pType.uuid;
    }

    if (
      this.tentUid == "amazon" &&
      this.common.customerList?.list?.length == 1
    ) {
      let pType = this.common.customerList?.list[0]?.raw;
      this.common.customerList.setValue(pType.uuid);
      this.common.customerList.selectedItem = pType.uuid;
    }

    if (this.tentUid == "amazon" && this.common.brandList?.list?.length == 1) {
      let pType = this.common.brandList?.list[0]?.raw;
      this.common.brandList.setValue(pType.uuid);
      this.common.brandList.selectedItem = pType.uuid;
    }
  }

  getScheduleFieldConfig(value) {
    this.saleOrderService.getScheduleFieldConfigById(value).subscribe(
      (data) => {
        this.configParam = data;
      },
      (error) => { }
    );
  }

  getAsinCode() {
    let isAsinCodePresent: boolean = false;
    for (let i = 0; i < this.asinCodeList.length; i++) {
      if (
        this.asinCodeName.toLowerCase() ==
        this.asinCodeList[i].code.toLowerCase()
      ) {
        this.commonService.warning("ASIN Code already added.");
        isAsinCodePresent = true;
      }
    }
    if (!isAsinCodePresent) {
      this.saleOrderService.getAsinCode(this.asinCodeName).subscribe(
        (data) => {
          if (data != null) {
            this.onGetAsinCode(data);
          } else if (data == null) {
            this.commonService.error("Asin Code not found");
          }
        },
        (error) => {
          this.commonService.error(error.msg);
        }
      );
    }
  }

  onGetAsinCode(value) {
    let designcode = new AuditDesignCode();
    this.asinCodeList.push({
      code: value.code,
      offeredQty: this.asinCodeOfferQty,
      orderedQty: this.asinCodeOdrQty,
    });

    designcode.comments = "";
    designcode.description = value.description;
    designcode.designcode = value.code;
    designcode.offerQty = this.asinCodeOfferQty;
    designcode.orderQty = this.asinCodeOdrQty;
    designcode.productTypeUid = value.productTypeUid;
    designcode.tenantUid = value.tenantUid;
    designcode.uuid = value.uuid;
    this.selectedDesigncodes.push(designcode);
    this.gridDesigncode.rowData = this.asinCodeList;
    this.gridDesigncode.updateData(this.asinCodeList);
    this.commonService.success("ASIN Code added successfully.");
    this.asinCodeName = "";
    this.asinCodeOfferQty = undefined;
    this.asinCodeOdrQty = undefined;
  }

  isOptionDisabled(opt: any): boolean {
    return (
      this.SupportingAuditors?.length >= 3 &&
      !this.SupportingAuditors?.includes(opt)
    );
  }

  public onFactorySelect() {
    if (
      this.configParam?.actualFactoryVisible &&
      this.configParam?.actualFactoryVisible == 1 &&
      this.common.actualFactoryList.selectedItem != null &&
      this.common.actualFactoryList.selectedItem != "0"
    ) {
      this.common.actualFactoryList.selectedItem =
        this.common.factoryList.selectedItem;
    }
  }

  public onLotsFactorySelect() {
    let id = this.common.factoryList.selectedItem;
    let selectedFactory = this.common.factoryList
      .getData()
      .find((obj) => obj.uuid.toString() == id);
    let factoryVendor = this.common.vendorList
      .getData()
      .find((obj) => obj.uuid == selectedFactory.vendorUid); //replace 64 with selectedFactory.vendorId
    if (factoryVendor) {
      this.selectedVendor =
        factoryVendor.uuid + " - " + factoryVendor.firstName;
      this.selectedVendorObject = factoryVendor;
      this.common.vendorList.selectedItem = factoryVendor.uuid;
    } else {
      this.selectedVendor = "";
      this.selectedVendorObject = "";
      this.common.vendorList.setValue("");
    }
  }

  public onActualFactorySelect() { }

  filteredFactoryList: any[] = [];
  public onVendorSelect() {
    let id = this.common.vendorList.selectedItem;
    let selectedVendor = this.common.vendorList
      .getData()
      .find((obj) => obj.uuid.toString() == id);
    this.selectedVendorObject = selectedVendor;
    let vendorFactory: any[] = [];

    this.common.factoryList.list.forEach((obj) => {
      if (obj?.raw?.vendorUid == selectedVendor?.uuid) {
        vendorFactory.push(obj);
      }
    });

    if (vendorFactory.length > 0) {
      // this.selectedVendor = selectedVendor;
      this.filteredFactoryList = vendorFactory;
    } else {
      this.filteredFactoryList = this.common.factoryList.list;
    }
  }

  public upperCaseStyleCode(event) {
    if (event.inputType == "deleteContentBackward") return;
    let letters = /^[0-9a-zA-Z,]+$/;
    if (event.inputType == "insertFromPaste") {
      if (this.rec.designcodes[0].designcode.match(letters)) {
        this.rec.designcodes[0].designcode =
          this.rec.designcodes[0].designcode.toUpperCase();
        return;
      } else {
        setTimeout(() => {
          this.rec.designcodes[0].designcode = "";
          return;
        }, 1);
      }
    }
    if (event.inputType == "insertText") {
      if (event.data.match(letters)) {
        this.rec.designcodes[0].designcode =
          this.rec.designcodes[0].designcode.toUpperCase();
      } else {
        setTimeout(() => {
          let str = this.rec.designcodes[0].designcode;
          this.rec.designcodes[0].designcode = str.substring(0, str.length - 1);
        }, 1);
      }
    }
  }

  getFormData() {
    let selAuditType = this.common.auditTypeList
      .getData()
      .filter((item) => item.uuid == this.common.auditTypeList.selectedItem)[0];
    this.rec.auditId = this.rec.auditId || "";
    this.rec.auditType.uuid = selAuditType?.uuid;
    this.rec["auditTypeUid"] = selAuditType?.uuid;
    // this.rec.seasonAndYear = this.common.getSeasonAndYear();
    //this.rec.auditor.uuid = this.common.auditorList.selectedItem;
    this.rec.auditor = this.common.auditorList
      .getData()
      .filter((item) => item.uuid == this.common.auditorList.selectedItem)[0];
    //this.rec.factory.uuid = this.common.factoryList.selectedItem;
    this.rec.factory = this.common.factoryList
      .getData()
      .filter((item) => item.uuid == this.common.factoryList.selectedItem)[0];
    this.rec.vendor = this.selectedVendorObject;

    if (this.rec.category)
      this.rec.category =
        this.common.categoryList
          .getData()
          .filter(
            (item) => item.uuid == this.common.categoryList.selectedItem
          )[0] || null;

    if (
      this.common.brandList.selectedItem != null &&
      this.common.brandList.selectedItem != "0" &&
      this.rec?.designcodes[0]
    ) {
      this.rec.brand = this.common.brandList
        .getData()
        .filter((item) => item.uuid == this.common.brandList.selectedItem)[0];
      this.rec.designcodes[0].brandUid = this.common.brandList.selectedItem;
    } else {
      // this.rec.brand = null;
      if (this.rec?.designcodes[0]) this.rec.designcodes[0].brandUid = null;
    }

    if (
      (this.configParam?.customerMandatory &&
        this.configParam?.customerMandatory == 1) ||
      (this.configParam?.customerVisible &&
        this.configParam?.customerVisible == 1)
    ) {
      if (
        this.common.customerList.selectedItem != null &&
        this.common.customerList.selectedItem != "0"
      ) {
        this.rec.customer = this.common.customerList
          .getData()
          .filter(
            (item) => item.uuid == this.common.customerList.selectedItem
          )[0];
        if (this.rec.designcodes[0]) {
          this.rec.designcodes[0].customerUid =
            this.common.customerList.selectedItem;
        }
      } else {
        this.rec.customer = this.configParam?.defaultCustomer || null;
        if (this.rec?.designcodes[0])
          this.rec.designcodes[0].customerUid = null;
      }
    } else {
      this.rec.customer = this.configParam?.defaultCustomer || null;
      if (this.rec?.designcodes[0]) this.rec.designcodes[0].customerUid = null;
    }

    if (
      this.rec.customer == null &&
      this.tentUid == "amazon" &&
      !!this.common.customerList.selectedItem
    ) {
      this.rec.customer = this.common.customerList
        .getData()
        .filter(
          (item) => item.uuid == this.common.customerList.selectedItem
        )[0];
    }

    // if (this.common.customerList.selectedItem != null && this.common.customerList.selectedItem != "0") {
    //   this.rec.customer = this.common.customerList.getData().filter(item => item.uuid == this.common.customerList.selectedItem)[0];;
    //   if (this.rec.designcodes[0]) {
    //     this.rec.designcodes[0].customerUid = this.common.customerList.selectedItem;
    //   }
    // } else {
    //   this.rec.customer = null;
    //   if (this.rec?.designcodes[0])
    //     this.rec.designcodes[0].customerUid = null;
    // }

    if (this.rec?.designcodes[0])
      this.rec.designcodes[0].productTypeUid =
        this.common.productTypeList.selectedItem;

    if (
      (this.configParam?.productTypeMandatory &&
        this.configParam?.productTypeMandatory == 1) ||
      (this.configParam.productTypeVisible &&
        this.configParam.productTypeVisible == 1)
    ) {
      if (
        !!this.common.productTypeList.selectedItem &&
        this.common.productTypeList.selectedItem != "0" &&
        this.common.productTypeList.selectedItem != ""
      ) {
        this.rec.productTypeObj =
          this.common.productTypeList
            .getData()
            .filter(
              (item) => item.uuid == this.common.productTypeList.selectedItem
            )[0] || null;
      } else {
        this.rec.productTypeObj = this.configParam?.defaultProductType || null;
      }
    }

    this.rec.productTypeUid = this.rec.productTypeObj?.uuid;

    if (this.rec?.designcodes[0])
      this.rec.designcodes[0].goldSeal = this.rec.designcodes[0].designcode;

    this.rec.auditDate = this.auditDt.getTime();
    this.rec.goldSealNo = this.rec?.designcodes[0]?.designcode;
    this.rec["supportingAuditors"] = this.SupportingAuditors?.join(",");
    this.rec["description"] = this.auditDescription;
    this.rec.auditEndTime = this.rec.auditEndTime || 0;
    this.rec.auditStartTime = this.rec.auditStartTime || 0;
    this.rec.childSeqId = 0; //need to check why it is used
    this.rec.offeredQty = this.rec?.designcodes[0]?.offerQty || 0.0;
    this.rec.orderedQty = this.rec?.designcodes[0]?.orderQty || 0.0;

    if (
      this.configParam?.actualFactoryVisible &&
      this.configParam?.actualFactoryVisible == 1 &&
      this.common.actualFactoryList.selectedItem != null &&
      this.common.actualFactoryList.selectedItem != "0"
    ) {
      this.rec.actualFactory =
        this.common.actualFactoryList
          .getData()
          .filter(
            (item) => item.uuid == this.common.actualFactoryList.selectedItem
          )[0] || this.rec.factory;
    } else {
      this.rec.actualFactory =
        this.configParam?.defaultFactory || this.rec.factory;
    }

    if (this.tenantConfig?.enableAgencySupport >= 1)
      this.rec["inspectionAgency"] = this.common.agencyList
        .getData()
        .filter((item) => item.uuid == this.common.agencyList.selectedItem)[0];
  }

  onSelectDesigncode(value) {
    let isNew = true;
    for (var code of this.selectedDesigncodes) {
      if (code.uuid == value.uuid) {
        isNew = false;
        break;
      }
    }
    if (isNew) {
      let designcode = new AuditDesignCode();
      designcode.designcode = value.code;
      designcode.designcodeUid = value.uuid;
      designcode.productTypeUid = value.productTypeUid;
      designcode.productTypeName = value.productTypeName;
      this.selectedDesigncodes.push(designcode);
    }
    this.gridDesigncode.rowData = this.selectedDesigncodes;
    this.gridDesigncode.updateData(this.selectedDesigncodes);
  }

  changeSelectedDesignCodes(): void {
    // console.log("selectedDesigncodes:",this.selectedDesigncodes);
  }

  prepareAuditDesignCode(): void {
    let auditDesignCode: AuditDesignCode = <AuditDesignCode>(
      Util.clone(AMENDSCH.designcodes[0])
    );
    auditDesignCode.poNumber = this.rec.purchaseOrderNo || "";
    auditDesignCode.goldSeal = this.rec.designcodes[0].designcode || "NA";
    auditDesignCode.designcode = this.rec.designcodes[0].designcode || "NA";
    auditDesignCode.orderQty = this.rec.designcodes[0].orderQty || 0;
    auditDesignCode.fabricCode = "";
    auditDesignCode.comments = this.auditDescription || "";
    auditDesignCode.brandUid =
      this.common?.brandList?.selectedItem ||
      this.configParam.defaultBrand?.uuid ||
      "";
    auditDesignCode.productTypeUid =
      this.common?.productTypeList?.selectedItem ||
      this.configParam?.defaultProductType?.uuid ||
      "";
    this.rec._designcode = auditDesignCode;
    this.rec._auditDesignCode = auditDesignCode;
    // this.rec.auditDesignCodes = [auditDesignCode];
    this.rec.designcodes = [auditDesignCode];
  }

  public onClearSelectedItem() {
    this.common.brandList.setValue("");
    this.common.customerList.setValue("");
    this.common.auditorList.setValue("");
    this.common.supportingAuditorList.setValue("");
    this.common.vendorList.setValue("");
    this.common.factoryList.setValue("");
    this.common.categoryList.setValue("");
    this.common.productTypeList.setValue("");
    this.common.productList.setValue("");
    this.common.defectTypeList.setValue("");
    this.common.designcodeList.setValue("");
    this.common.actualFactoryList.setValue("");
    this.SupportingAuditors = [];
    this.selectedVendor = "";
    this.rec.designcodes[0].orderQty = 0.0;
    this.rec.designcodes[0].offerQty = 0.0;
    this.rec.designcodes[0].designcode = "";
    this.rec.designcodes[0].description = "";
    this.auditDescription = "";
    this.shipmentSelectAuditType = "";
    this.selectedDesigncodes = [];
    this.asinCodeList = [];
    this.shipmentDto = new ShipmentDto();
    if (this.userRole === "AUDITOR")
      this.common.auditorList.setValue(this.user.uuid);
  }
  public onSave() {
    if (new Date(this.auditDt) < new Date()) {
      this.commonService.warning("Time must be in future");
      this.submitDisabled = true;
      return;
    }
    this.getFormData();
    if (this.rec.auditType?.uuid == 0) {
      this.commonService.warning("Please select Audit Type");
      this.submitDisabled = true;
      return;
    }
    if (
      this.rec.auditor?.uuid == 0 &&
      this.rec.auditType.assignmentScope == "auditor"
    ) {
      this.commonService.warning("Please select Auditor");
      this.submitDisabled = true;
      return;
    }
    if (this.rec.factory?.uuid == 0) {
      this.commonService.warning("Please select Factory");
      this.submitDisabled = true;
      return;
    }
    if (this.rec.vendor?.uuid == 0) {
      this.commonService.warning("Please select Vendor");
      this.submitDisabled = true;
      return;
    }

    if (
      this.rec.auditType.multiDesignCode &&
      this.rec.auditType.multiDesignCode == 1 &&
      this.asinCodeList.length == 0
    ) {
      this.commonService.warning("Please add some ASIN code");
      this.submitDisabled = true;
      return;
    }

    this.submitDisabled = false;

    // if (this.rec.designcodes[0].productTypeUid == 0) {
    //   this.commonService.warning('Please select Product Type');
    //   this.submitDisabled = false;
    //   return;
    // }
    if (this.rec?.auditId) {
      APP_UI_CONFIG.schedules.update.payload = this.rec;
      APP_UI_CONFIG.schedules.update.paramList["id"] = this.rec.auditId;
      this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.update, null, this.rec.auditId, this.rec)
        .pipe(takeUntil(this.destroyed$))
        .subscribe(
          (res) => {
            if (res.code == 200) {
              this.commonService.success(res.message);
              this.close();
            } else {
              this.commonService.error(res.message);
            }
          },
          (error) => {
            this.commonService.handleError(error);
          }
        );
      this.submitDisabled = false;
    } else {
      if (this.rec.auditor?.uuid == 0) {
        this.rec.auditor = null;
      }
      if (this.rec?.uuid == 0) {
        this.rec.category = null;
      }

      if (this.selectedDesigncodes.length > 0) {
        this.rec.auditDesignCodes = this.selectedDesigncodes;
      }

      this.prepareAuditDesignCode();

      this.rec.labUid = this.common.labList?.selectedItem;
      this.rec.inspectionAgencyUid = this.common.agencyList?.selectedItem;
      this.rec.type = this.rec.auditType.type;

      this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.create, "", "", this.rec)
        .pipe(takeUntil(this.destroyed$))
        .subscribe((res) => {
          if (res.code == 200) {
            this.commonService.success(res.message);
            this.close();
          } else if (res.code == "AUDIT_FLOW_ERROR") {
            this.onAuditFlowAlert(res.message, "audit");
          } else {
            this.commonService.error(res.message);
          }
        });
      this.submitDisabled = false;
    }
  }

  getShipmentFormData(): AuditShipmentReq {
    this.shipmentPayload.actualFactory = this.common.actualFactoryList.getData().filter((item) => item.uuid == this.common.actualFactoryList.selectedItem)[0];
    this.shipmentPayload.auditDate = this.auditDt.getTime();
    this.shipmentPayload.auditType = this.rec.auditType;
    this.shipmentPayload.auditor = this.common.auditorList
      .getData()
      .filter((item) => item.uuid == this.common.auditorList.selectedItem)[0];
    this.shipmentPayload.fromSap = 0;
    let myDate = new Date(this.shipmentPayload.auditDate);
    this.shipmentPayload.selectedAuditDate = myDate.getFullYear() + "-" + ("0" + (myDate.getMonth() + 1)).slice(-2) + "-" + ("0" + myDate.getDate()).slice(-2) + " " + myDate.getHours() + ":" + ("0" + myDate.getMinutes()).slice(-2);
    this.shipmentPayload.shipmentNo = this.rec.shipmentNo;
    return this.shipmentPayload;
  }

  public onShipmentSave() {
    let payload = this.getShipmentFormData();
    if (new Date(this.auditDt) < new Date()) {
      this.commonService.warning("Time must be in future");
      this.submitDisabled = true;
      return;
    }

    if (this.rec.auditType.uuid == 0) {
      this.commonService.warning("Please select Audit Type");
      this.submitDisabled = true;
      return;
    }

    this.submitDisabled = false;

    this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.createShipment, "", "", payload)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((res) => {
        if (res.code == 200) {
          this.commonService.success(res.message);
          this.close();
        } else if (res.code == "AUDIT_FLOW_ERROR") {
          this.onAuditFlowAlert(res.message, "shipment");
        } else {
          this.commonService.error(res.message);
        }
      });
  }

  onAuditFlowAlert(msg: string, type: string) {
    const dialogRef = this.dialog.open(AuditFlowAlertDialogComponent, {
      data: { msg: msg, reason: this.tenantConfig?.auditFlowReasons },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (result.reason != "") {
          if (type == "shipment") {
            this.shipmentPayload.auditFlowReason = result.reason;
            this.shipmentPayload.auditFlowRemarks = result.remarks;
            this.onShipmentSave();
          } else {
            this.rec.auditFlowReason = result.reason;
            this.rec.auditFlowRemarks = result.remarks;
            this.onSave();
          }
        }
      }
    });
  }

  searchDesigncodes(event) {
    this.searchResultDesigncodes = this.designcodeList;
  }

  public close(): void {
    this.closeEvent.emit(true);
  }
  //

  shipmentDestinationList: any[] = [
    { label: "NRFA", value: "NRFA" },
    { label: "SRFA", value: "SRFA" },
    { label: "CWH", value: "CWH" },
  ];
  private prepareSelectedDesignCodeColumns(): void {
    this.selectedDesignCodeColumnsDef = [
      {
        headerName: "SKU",
        field: "code",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Ordered Quantity",
        field: "qty",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Brand",
        field: "brand",
        sortable: true,
        unSortIcon: true,
        minWidth: 100,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Product Type",
        field: "productTypeName",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Category",
        field: "category",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Description",
        field: "description",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Division",
        field: "division",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Color",
        field: "color",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Material",
        field: "material",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Mrp",
        field: "mrp",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Season",
        field: "season",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Lining",
        field: "lining",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Vendor Name",
        field: "vendorName",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
      {
        headerName: "Factory Name",
        field: "factoryName",
        sortable: true,
        unSortIcon: true,
        minWidth: 130,
        filter: "agTextColumnFilter",
      },
    ];
  }
  private prepareFormModel(): any {
    return {
      auditDate: {
        type: FORM_CONTROL_TYPES.DATE,
        fieldStatus: FORM_CONTROL_TYPES.TEXT_SEARCH,
        value: "",
        label: "Audit Date",
        placeholder: "DD/MM/YYYY",
        show: true,
        showTime: true,
        rules: {
          minDate: new Date(),
          required: true,
        },
        onChange: (event) => { }
      },
      auditor: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Auditor",
        placeholder: "Auditor",
        show: true,
        rules: {
          required: true,
        },
        onChange: (item) => { }
      },
      auditType: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Audit Type",
        placeholder: "Audit Type",
        show: true,
        rules: {
          required: true,
        },
        onChange: (item) => this.onChangeAuditType(item.value)
      },
      shipmentDestination: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Shipment Destination",
        placeholder: "Shipment Destination",
        show: true,
        rules: {
          required: true,
        },
        onChange: (item) => { }
      },
      purchaseOrderNo: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Purchase Order No",
        placeholder: "Purchase Order No",
        show: true,
        rules: {
          required: true,
        },
        onChange: (item) => this.onChangepurchaseOrderNo(item.value),
        tooltip: 'Purchase Order Number',
      },
      _designcode: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "SKU",
        placeholder: "SKU",
        show: false,
        // rules: {
        //     required: false,
        // },
        onChange: (item) => this.onChangeDesigncode(item.value),
        tooltip: 'Design Code/Product Code/Article Number',
      },
      designcodes: {
        type: FORM_CONTROL_TYPES.MULTI_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.MULTI_SELECT,
        options: [],
        value: "",
        label: "Design Codes",
        placeholder: "Design Codes",
        show: false,
        onChange: (item) => this.onChangedesigncodes(item.value),
        // rules: {
        //     required: false,
        // },

      }
    }
  }


  private buildForm() {
    if (this.commonService.getTenantUid() != Partners.MADURA) {
      const formGroupFields = this._buildFormUtilityService.getFormControlsFields(this.prepareFormModel());
      this.fields = this._buildFormUtilityService.getFieldsList();
      this.scheduleForm = new FormGroup(formGroupFields);
      this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "auditor", this.common.auditorList.list);
    } else {
      const formGroupFields = this._buildFormUtilityService.getFormControlsFields(MADURA_SCHEDULE_CREATE_FORM_MODEL);
      this.fields = this._buildFormUtilityService.getFieldsList();
      this.scheduleForm = new FormGroup(formGroupFields);
      setTimeout(() => {
        this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "auditor", this.common.auditorList.list);
        this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "auditType", this.common.auditTypeList.list);
        this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "factory", this.common.factoryList.list);
        this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "vendor", this.common.vendorList.list);

        this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "brand", this.common.brandList.list);
        this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "productType", this.common.productTypeList.list);
        this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "category", this.common.categoryList.list);
      }, 1000);


      if (this.commonService.getUseRole().toUpperCase() == ROLE.AUDITOR) {
        this.scheduleForm.get("auditor")?.disable();
      }
      if (this.commonService.getUseRole().toUpperCase() == ROLE.VENDOR) {
        this.scheduleForm.get("vendor")?.disable();
      }
    }



    this.onFormChanges();
  }
  poList: any[] = [];
  private getPoList(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.purchaseOrders.getByGroup)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          this.prepareAndSetPo(response);
        },
        error: (error) => {
          this.commonService.handleError(error);
        },
      });
  }

  private prepareAndSetPo(response): void {
    let options = response.map((item) => ({ label: item.purchaseOrderNo, value: item.purchaseOrderNo, raw: item }));
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "purchaseOrderNo", options);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "auditor", this.common.auditorList.list);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "auditType", this.common.auditTypeList.list);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "shipmentDestination", this.shipmentDestinationList);
  }

  private onChangeAuditType(auditTypeUid) {
    this.scheduleForm.get("purchaseOrderNo")?.setValue("");
    this.scheduleForm.get("_designcode")?.setValue("");
    this.scheduleForm.get("designcodes")?.setValue("");
    this.prepareAndSetDesignCodeDetails([]);
    this.checkInspectionCreatedOrNot(this.scheduleForm.get("auditType")?.value, this.scheduleForm.get("purchaseOrderNo")?.value, this.scheduleForm.get("_designcode")?.value,)
  }
  private onChangepurchaseOrderNo(purchaseOrderNo) {
    this.selectedDesigncodes = [];
    this.scheduleForm.get("_designcode")?.setValue("");
    this.scheduleForm.get("designcodes")?.setValue("");
    if (purchaseOrderNo) {
      this.getDesignCodeByPo(purchaseOrderNo);
    } else {
      this.prepareAndSetDesignCodeDetails([]);
    }
    this.checkInspectionCreatedOrNot(this.scheduleForm.get("auditType")?.value, this.scheduleForm.get("purchaseOrderNo")?.value, this.scheduleForm.get("_designcode")?.value,)
  }
  private onChangeDesigncode(designCodeUid) {
    if (designCodeUid) {
      this.getDesignCodeDetailsByCode(false);
    } else {
      this.prepareAndSetDesignCodeDetails([]);
    }
    this.checkInspectionCreatedOrNot(this.scheduleForm.get("auditType")?.value, this.scheduleForm.get("purchaseOrderNo")?.value, this.scheduleForm.get("_designcode")?.value,)
  }
  private onChangedesigncodes(designcodes) {
    if (designcodes) {
      this.getDesignCodeDetailsByCode(true);
    } else {
      this.prepareAndSetDesignCodeDetails([]);
    }
  }

  private checkInspectionCreatedOrNot(audiTypeUid, ponumber, designcodeUid): void {
    this.alreadyInspectionCreatedMessage = '';
    this.inspectionTableData = [];
    if (audiTypeUid == '7badaf84-016d-4d3f-8bcc-23c787e911e6' && ponumber && designcodeUid) {
      APP_UI_CONFIG.inspection.validateInspection.paramList.audittype = audiTypeUid;
      APP_UI_CONFIG.inspection.validateInspection.paramList.designcode = designcodeUid;
      APP_UI_CONFIG.inspection.validateInspection.paramList.ponumber = ponumber;
      this._invokeService.serviceInvocation(APP_UI_CONFIG.inspection.validateInspection)
        .pipe(takeUntil(this.destroyed$))
        .subscribe({
          next: (response) => {
            let totalOfferQty: number = 0;
            response?.forEach((item) => {
              totalOfferQty = totalOfferQty + item.offerQty;
            })
            if (response?.length > 0) {
              if (response[0].orderQty < totalOfferQty) { this.showSubmitButton = false; } else { this.showSubmitButton = true; }
              this.inspectionTableData = response;
              this.inspectionTableData = this.inspectionTableData.map(item => ({
                createdTimeText: this.commonService.dateShortFormat(item.auditDate),
                ...item
              }))
              this.alreadyInspectionCreatedMessage = `Inspection already created for this Audit type, Order Qty:${response[0].orderQty} and OfferQty:${response[0].offerQty}`;
            } else {
              this.showSubmitButton = true;
            }
          },
          error: (error) => {
            this.commonService.handleError(error);
          },
        });
    } else {
      this.showSubmitButton = true;
      this.alreadyInspectionCreatedMessage = '';
    }
  }
  private onFormChanges(): void {
    if (this.commonService.getTenantUid() == Partners.MADURA) {
      this.scheduleForm.get("factory").valueChanges.pipe(distinctUntilChanged())
        .subscribe((factory) => {
          if (factory) {
            var factoryDetails: any[] = this.common.factoryList.list.filter((item: any) => item.value == this.common.factoryList.selectedItem);
            if (factoryDetails && factoryDetails.length > 0) {
              let facatoryValidDate = new Date(factoryDetails[0].validUpto)
              let factoryLabel = factoryDetails[0].label;
              if (new Date() > facatoryValidDate) {
                this.commonService.error(`Factory is blocked as it’s process audit validity has got expired on ${moment(facatoryValidDate).format('MMMM Do YYYY')}`)
              } else if (new Date() < facatoryValidDate && facatoryValidDate <= new Date(new Date().setMonth(new Date().getMonth() + 1))) {
                this.commonService.warning(` Kindly intimate MFL QA to conduct Process Audit within ${moment(facatoryValidDate).format('MMMM Do YYYY')} else factory will be blocked`)
              }
            }
          }
        });

    }
  }
  private getDesignCodeByPo(purchaseOrderNo): void {
    APP_UI_CONFIG.purchaseOrders.getPoDetailsByPONumber.paramList.poNo = purchaseOrderNo;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.purchaseOrders.getPoDetailsByPONumber)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          this.prepareAndSetPoDetails(response);
        },
        error: (error) => {
          this.commonService.handleError(error);
        },
      });
  }
  private prepareAndSetPoDetails(response): void {
    let designCodes: any[] = [];
    response?.forEach((item) => {
      if (item.designCodes) {
        let designCodesList: any[] = item.designCodes?.map((dCode) => ({
          size: item.sizeCode,
          qty: item.qty,
          ...dCode,
        }));
        if (designCodesList) {
          designCodes = designCodes.concat(designCodesList);
        }
      }
    });
    this.selectedPo = response;
    let poDetails = response[0];
    if (designCodes.length > 0) {
      let designCodesList: any[] = designCodes?.map((item) => ({ label: item.code, value: item.code, raw: item }));
      if (this.scheduleForm.get("auditType").value == "9cbd7fa6-8f17-4cdf-85f7-467cd63b0330") {
        //PPM UID
        this.isAuditTypePPM = true;
        this.fields = this._buildFormUtilityService.showHideField(this.fields, "_designcode", false, this.scheduleForm);
        this.fields = this._buildFormUtilityService.showHideField(this.fields, "designcodes", true, this.scheduleForm);

        this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "designcodes", { required: true });
        this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "_designcode", { required: false });

        this.scheduleForm.get("designcodes").setValidators(Validators.required);
        this.scheduleForm.get("_designcode").clearValidators();
        this.scheduleForm.updateValueAndValidity();

        this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "designcodes", designCodesList);
      } else {
        this.isAuditTypePPM = false;
        this.fields = this._buildFormUtilityService.showHideField(this.fields, "_designcode", true, this.scheduleForm);
        this.fields = this._buildFormUtilityService.showHideField(this.fields, "designcodes", false, this.scheduleForm);

        this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "designcodes", { required: false });
        this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "_designcode", { required: true });
        this.scheduleForm.get("designcodes").clearValidators();
        this.scheduleForm.get("_designcode").setValidators(Validators.required);
        this.scheduleForm.updateValueAndValidity();

        this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, "_designcode", designCodesList);
      }
    } else {
      this.commonService.error(
        `No design codes available for purchase order no:${poDetails[0].purchaseOrderNo}`
      );
    }
  }

  private getDesignCodeDetailsByCode(isMulti) {
    if (!isMulti) {
      let options: any[] = this.fields.find((item) => item.fieldName == "_designcode").options;
      let optionsList = options.map((item) => ({ ...item.raw }));
      let designCodeList: any[] = optionsList.filter(item => item.code == this.scheduleForm.get("_designcode").value);
      this.prepareAndSetDesignCodeDetails(designCodeList);
    } else {
      let designCodeUids: any[] = this.scheduleForm.get("designcodes").value;
      let options: any[] = this.fields.find((item) => item.fieldName == "designcodes").options;
      let optionsList = options.map((item) => ({ ...item.raw }));
      let designCodeList: any[] = optionsList.filter((o) => designCodeUids.some((code) => o.code === code));
      this.prepareAndSetDesignCodeDetails(designCodeList);
    }
  }

  private prepareAndSetDesignCodeDetails(designCodeDetails: any[]): void {
    this.selectedDesigncodes = designCodeDetails.map((item) => ({
      vendorName: this.selectedPo[0].vendorName,
      factoryName: this.selectedPo[0].factoryName,
      ...item,
    }));
  }

  public onCreateSchedule(): void {
    if (this.commonService.getTenantUid() == Partners.MADURA) {
      this.onCreateScheduleForMADURA();
    } else {
      let isValid: boolean = false;
      let designCodeListWithNoProductImage: any[] = this.selectedDesigncodes.filter(designCodeItem => {
        const isProudctImageExist = designCodeItem?.documents?.some(document => document.docType == 'PRODUCT IMAGE');
        if (!isProudctImageExist) {
          return designCodeItem;
        }
      });
      if (this.isAuditTypePPM) {
        isValid = true;
      }
      else if (this.tentUid == Partners.MADURA) {
        isValid = true;
      }

      else {
        if (designCodeListWithNoProductImage.length == 0) {
          isValid = true;
        } else {
          isValid = false;
        }
      }

      if (isValid) {
        this.rec.auditDate = +new Date(this.scheduleForm.get("auditDate").value);
        this.rec.auditor = {};
        let auditor = this.fields.find((item) => item.fieldName == "auditor")?.options?.find((option) => option.value == this.scheduleForm.get("auditor").value)?.raw;
        this.rec.auditor = auditor;
        this.rec.inchargeUid = auditor?.inchargeUid;

        this.rec.auditType = this.fields.find((item) => item.fieldName == "auditType")?.options?.find((option) => option.value == this.scheduleForm.get("auditType").value)?.raw;
        this.rec.vendor = this.common.vendorList.list.find((item) => item.value == this.selectedPo[0].vendorUid)?.raw;
        let selectedFactgory = this.common.factoryList.list.find(item => item?.raw?.vendorUid == this.selectedPo[0]?.vendorUid)?.raw
        if (selectedFactgory) {
          this.rec.factory = selectedFactgory;
        } else {
          selectedFactgory = this.common.factoryList?.list?.find(item => item?.raw?.code == "NA")?.raw || null;
          this.rec.factory = selectedFactgory;
        }

        let designCode: any;
        if (this.isAuditTypePPM) {
          designCode = this.selectedDesigncodes[0];
        } else {
          designCode = this.selectedDesigncodes[0];
        }
        this.rec.purchaseOrderNo = this.selectedPo[0]?.purchaseOrderNo;
        this.rec.purchaseOrderItem = this.selectedPo[0]?.purchaseOrderItem;

        let designCodes = this.selectedDesigncodes.map(item => {
          const { code, uuid, ...newObj } = item;
          return { designcode: item.code, designcodeUid: item.uuid, poNumber: this.selectedPo[0]?.purchaseOrderNo, orderQty: item.qty, ...newObj, id: null }
        });

        this.rec.auditDesignCodes = designCodes;
        this.rec.designcodes = designCodes;

        this.rec.brand = {};
        this.rec.brand["uuid"] = designCode.brandUid;

        this.rec.category = {};
        this.rec.category["uuid"] = designCode.categoryUid;

        this.rec.productTypeUid = designCode.productTypeUid;
        this.rec.shipmentDestination = this.scheduleForm.get("shipmentDestination").value;
        this.commonService.openDialogForConfirmation("Are you sure you want to create the schedule?").subscribe({
          next: confirm => {
            if (confirm) {
              this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.create, '', '', this.rec)
                .pipe(takeUntil(this.destroyed$))
                .subscribe({
                  next: response => {
                    if (response && response.code == 200) {
                      this.commonService.success(response.message);
                      this.close();
                    } else {
                      this.commonService.error(response.message);
                    }
                  }, error: error => {
                    this.commonService.handleError(error)
                  }
                })
            }
          }
        })
      } else {
        this.commonService.error(`Product Image is not available for the SKU:${designCodeListWithNoProductImage.map(item => item.code).toString()}`)
      }
    }
  }


  private onCreateScheduleForMADURA() {
    this.rec=new QiSchedule();
    this.rec.auditDate = +new Date(this.scheduleForm.get("auditDate").value);
    this.rec.auditor = {};
    let auditor = this.common.auditorList.list.find((item) => item.value == this.scheduleForm.get("auditor").value)?.raw;
    console.log("auditor",auditor)
    this.rec.auditor['contactNo'] = "";
     this.rec.auditor['altemail'] = "";
      this.rec.auditor['contactNo'] = "";
    this.rec.auditor = auditor;
    this.rec.inchargeUid = auditor?.inchargeUid;

    this.rec.auditType = this.common.auditTypeList.list.find((item) => item.value == this.scheduleForm.get("auditType").value)?.raw;
    this.rec.vendor = this.common.vendorList.list.find((item) => item.value == this.scheduleForm.get("vendor").value)?.raw;
    let selectedFactgory = this.common.factoryList.list.find(item => item?.value == this.scheduleForm.get("factory").value)?.raw
    if (selectedFactgory) {
      this.rec.factory = selectedFactgory;
    } else {
      selectedFactgory = this.common.factoryList?.list?.find(item => item?.raw?.code == "NA")?.raw || null;
      this.rec.factory = selectedFactgory;
    }

    this.rec.category=this.common.categoryList.list.find(item => item?.value == this.scheduleForm.get("category").value)?.raw;
    this.rec.productTypeObj = this.common.productTypeList.list.find(item => item?.value == this.scheduleForm.get("productType").value)?.raw;
    this.rec.actualFactory = this.common.factoryList.list.find(item => item?.value == this.scheduleForm.get("factory").value)?.raw;

    this.rec.goldSealNo = this.scheduleForm.get("goldSeal").value;
    this.rec.productTypeUid = this.scheduleForm.get("productType").value;
    this.rec.orderedQty = this.scheduleForm.get("orderQty").value;

    this.rec.designcodes = []
    let stylesCodes = {
      "goldSeal": this.scheduleForm.get("goldSeal").value,
      "stylecode": this.scheduleForm.get("designcodes").value,
      "size": this.scheduleForm.get("size").value,
      "poNumber": this.scheduleForm.get("poNumber").value,
      "orderQty": this.scheduleForm.get("orderQty").value,
      "offerQty": this.scheduleForm.get("offerQty").value,
      "offerQty2": this.scheduleForm.get("offerQty2").value,
      "offerQty3": this.scheduleForm.get("offerQty3").value,
      "carton": this.scheduleForm.get("carton").value,
      "channel": this.scheduleForm.get("channel").value,
      "comments": this.scheduleForm.get("comments").value,
      "status": 0,
      "productTypeId": this.scheduleForm.get("productType").value,
      "brandUid": this.scheduleForm.get("brand").value,
      "fabricCode": this.scheduleForm.get("fabricCode").value,
      "designcode": this.scheduleForm.get("designcodes").value,
      "auditId": "",
      "id":''
    }
    this.rec.designcodes.push(stylesCodes);
    this.rec.auditDesignCodes=[];
    this.rec.auditDesignCodes.push(stylesCodes);
    this.rec.offeredQty = Number(this.scheduleForm.get("offerQty").value)+Number(this.scheduleForm.get("offerQty2").value)+Number(this.scheduleForm.get("offerQty3").value);


    console.log("this.rec",this.rec)
    this.commonService.openDialogForConfirmation("Are you sure you want to create the schedule?").subscribe({
      next: confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.create, '', '', this.rec)
            .pipe(takeUntil(this.destroyed$))
            .subscribe({
              next: response => {
                if (response && response.code == 200) {
                  this.commonService.success(response.message);
                  this.close();
                } else {
                  this.commonService.error(response.message);
                }
              }, error: error => {
                this.commonService.handleError(error)
              }
            })
        }
      }

    })
  }


}
