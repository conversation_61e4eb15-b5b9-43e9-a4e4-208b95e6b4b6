import * as am5 from '@amcharts/amcharts5';
import * as am5percent from '@amcharts/amcharts5/percent';
import * as am5plugins_exporting from '@amcharts/amcharts5/plugins/exporting';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import * as am5xy from '@amcharts/amcharts5/xy';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { auditTypeForDashBoard, topDefects } from 'app/core/qinspect/_model/GenericDashBoard.mode';
import { User } from 'app/core/user/user.model';
import { ROLE } from 'app/enum-constants';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import * as _ from 'lodash';
import moment from 'moment';
import { NgxCaptureService } from 'ngx-capture';
import { forkJoin, of, ReplaySubject, Subject } from 'rxjs';
import { catchError, debounceTime, distinctUntilChanged, takeUntil, tap } from 'rxjs/operators';

import { VendorHomeIntroDialogComponent } from '../vendor-home-intro-dialog/vendor-home-intro-dialog.component';

interface homePageConfigurations {
  title: string,
  link: string,
  count: number | string,
  toBeCount?: number
}

interface notificationLIstConf {
  auditTypeName?: string,
  requestUid?: string
  requestUrl?: string
  requestStatus?: string
  name?: string
}

@Component({
  selector: 'tfl-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],

})
export class HomeComponent implements OnInit, OnDestroy {
  @Input() homeConfigurations: homePageConfigurations[];
  @ViewChild('dateRangeCalendar') private dateRangeCalendar: any;
  @ViewChild('screen', { static: true }) screen: any;

  userInfo: User;
  inspectionData = {
    ppm: 43,
    final: 87,
    inline: 56,
    defects: 15
  }
  showFilters: boolean = false;
  isShowTable: boolean = false;
  isShowDefectsTable: boolean = false;
  qualityList: any[] = [];
  columnDefs: any[] = [];
  columnDefsForTopNDefects: any[] = [];
  topNDropDownList: any[] = [{ label: "5", value: 5 }, { label: "10", value: 10 }, { label: "15", value: 15 }, { label: "20", value: 20 }]

  designCodewiseList: any[] = [];
  topNDefects: topDefects[] = [];
  defectsColumnDefs: any[];
  topList: any[] = [{ label: 5, value: 5 }, { label: 10, value: 10 }, { label: 15, value: 15 }, { label: 20, value: 20 }];
  selectedTop: any;
  showMoreFilter: boolean = false;
  categoryList: any[] = [
    { label: 'All Categories', value: 'all' },
    { label: 'PPM', value: 'ppm' },
    { label: 'PPM', value: 'ppm' },
  ]
  timePeriodList: any[] = [
    { label: 'Last 7 days', value: 'week' },
    { label: 'Last 30 days', value: 'month' },
    { label: 'Last 3 Months', value: 'quarterly' },
    { label: 'Yearly', value: 'yearly' },
  ];
  defectsFilterRec = {
    selectedTimeRange: 'all',
    selectedCategory: 'yearly'
  };
  showChart: boolean = true;
  filtersListData: any[] = [];
  hideShowFilter: boolean = false;
  filterIconUrl: string = './assets/svg/filter_icon.svg';
  toggleGraphAndTable: boolean = true;  //here for true value graph view is enable
  timeDurationSelectedItem: any = { label: 'Last 3 Months', value: 'quarterly' };
  currentTabIndex: number = 0;
  defectGraphRoot: any;
  auditTypeStackedChartRoot: any;
  qualityPassFailRoot: any;
  qualityFirstTimePassRoot: any;
  defectsPassFailRoot: any;
  poOfferedQtyRoot: any;
  dateRange: Date | undefined;
  fromDate: Date | undefined;
  toDate: Date | undefined;
  auditTypeListToShowInTop: any[] = [];
  auditTypeList: any[] = [];
  currentIndex = 0;
  itemsPerPage = 5;
  inputFilterChange = new Subject<string>();
  noOfFilterApplied: number = 0;
  noOfNotification: number = 6;
  showDefectGraph: boolean = true;
  auditTypeDataForQualityGraph: any[] = [];
  poVSOfferForQualityGraphData: any[] = [];
  passFailDataForQualityGraphData: any[] = [];
  passFailReInspectionDataForQualityGraphData: any[] = [];
  passFailDataForDefectGraphData: any;
  defectGraphData: any[] = [];
  overAllDefectPercentage: number;
  selectedFilters: { [key: string]: any } = {};
  defectTotalCriticalCount: number = -1;
  defectTotalMajorCount: number = -1;
  defectTotalMinorCount: number = -1;
  selectAuditTypeName: string = '';

  defectTypesCountAsPassFailData: { [key: string]: any } = {};
  defectTypesAsPassFailGraphData: any[] = [];

  pieChartRows: any[][] = [[]];
  chartCount: number = 0;
  tenantUid: string = this._commonService.getTenantUid();

  defectpieChartRows: any[][] = [[]];
  // defectpieChartRowsCAPA: any[][] = [[]]; //TODO : For Capa screen
  defectpieChartCount: number = 0;
  // defectpieChartCountCAPA: number = 0; //TODO : For Capa screen
  selectedNValueForTopNDefects: any = { label: "5", value: 5 };
  private charts: any[] = [];
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  imgBase64 = '';
  displayDialog: boolean = false;
  dialogHdr: string = 'Notification';
  notificationList: notificationLIstConf[] = [{ name: 'Notification-1' }, { name: 'Notification-2' }, { name: 'Notification-3' }, { name: 'Notification-4' }, { name: 'Notification-5' }];
  colorListItem: string[] = ['3px solid #abc3e5', '3px solid #a9eba9', '3px solid #e3cab6', '3px solid #b6d3e3', '3px solid #e3b6bd']
  hasReInspection: boolean;
  hasRejectQty: any;

  constructor(
    private _authService: AuthService,
    public _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialog: MatDialog,
    private _router: Router,
    private http: HttpClient,
    private captureService: NgxCaptureService
  ) {
    this.inputFilterChange.pipe(
      debounceTime(1000),
      distinctUntilChanged())
      .subscribe(value => {
        this.filterValueChange();
      });
  }

  public ngOnInit(): void {
    setTimeout(() => {
      const div = document.getElementById('q-body-card');
      if (div) {
        div.style.setProperty('padding-left', '0', 'important');
        div.style.setProperty('padding-right', '0', 'important');
      }
    }, 100);

    if (this.isDashBoardVisible()) {
      this.prepareTableColumns();
      // this.getNotificationList();
      this.refreshDashBoard();
    } else {
      this.userInfo = this._commonService.getCurrentUser();
      if (!this.homeConfigurations) {
        if (this.userInfo.tenantUid.toLowerCase() == 'godrej' || this.userInfo.tenantUid.toLowerCase() == 'titan') {
          APP_UI_CONFIG.administration.homePageCount.get.paramList.page = 'home';
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
            if (result != null && result != undefined) {
              if (this.userInfo.tenantUid.toLowerCase() == 'godrej') {
                this.homeConfigurations = [
                  { title: 'Inspection', link: 'schedules/inspectionSchedules/audit', count: result.inspectionCount },
                ];
              } else {
                this.homeConfigurations = [
                  { title: 'PPM', link: 'schedules/inspectionSchedules/audit', count: result.ppmCount },
                  { title: 'Inline', link: 'schedules/inspectionSchedules/audit', count: result.inlineCount },
                  { title: 'Final Inspection', link: 'schedules/inspectionSchedules/audit', count: result.finalCount },

                ];
              }

            }
          });
        }
        else if (this.userInfo.tenantUid.toLowerCase() == 'twills') {
          this._commonService.navigate("npd/home");
        }
        else if (this.userInfo.tenantUid.toLowerCase() == 'norlanka') {
          this._commonService.navigate("dashboard/qualityDashboard");
        }
        else {
          if (this._authService.hasLoggedUserPermitted(ROLE.ADMIN)) {
            APP_UI_CONFIG.administration.homePageCount.get.paramList.page = 'home';
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
              if (result != null && result != undefined) {
                this.homeConfigurations = [
                  { title: 'Audit', link: 'home/audit-home', count: result.auditCount },
                  { title: 'Inspection', link: 'inspection/inspection-home', count: result.inspectionCount },
                  // { title: 'Sourcing', link: '', count: 0 },
                  // { title: 'Testing', link: 'testing/testing-home', count: result.trfCount }
                ];
              }
            });
          }
          else if (this._authService.hasLoggedUserPermitted(ROLE.AGENCY)) {//TODO
            if (this.userInfo.type?.toLowerCase()?.split(',')?.includes('audit')) {
              this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
                if (result != null && result != undefined) {
                  this.homeConfigurations = [
                    { title: 'Planning', link: 'master/auditPlanning', count: result.planningCount, toBeCount: result.planningActionCount },
                    { title: 'Review', link: 'schedules/reviewProcess/fqa', count: result.reviewCount, toBeCount: result.reviewActionCount },
                    { title: 'CAPA', link: 'capa/capa/fqa', count: result.capaCount, toBeCount: result.capaActionCount },
                    { title: 'Audits', link: 'schedules/inspectionSchedules/fqa', count: result.auditCount }
                  ];
                }
              });
            } else if (this.userInfo.type?.toLowerCase()?.split(',')?.includes('inspection')) {
              this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
                if (result != null && result != undefined) {
                  this.homeConfigurations = [
                    { title: 'Request', link: 'master/inspectionRequest', count: result.requestCount, toBeCount: result.requestActionCount },
                    { title: 'CAPA', link: 'capa/capa/inspection', count: result.capaCount, toBeCount: result.capaActionCount },
                    { title: 'Inspections', link: 'schedules/inspectionSchedules/inspection', count: result.inspectionCount }
                  ];
                }
              });
            }
          }
          else if (this._authService.hasLoggedUserPermitted(ROLE.OPERATIONS)) {//TODO
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
              if (result != null && result != undefined) {
                this.homeConfigurations = [
                  { title: 'Planning', link: 'master/auditPlanning', count: result.planningCount, toBeCount: result.planningActionCount },
                  { title: 'Review', link: 'schedules/reviewProcess/fqa', count: result.reviewCount, toBeCount: result.reviewActionCount },
                  { title: 'CAPA', link: 'capa/capa/fqa', count: result.capaCount, toBeCount: result.capaActionCount },
                  { title: 'Audits', link: 'schedules/inspectionSchedules/fqa', count: result.auditCount }
                ];
              }
            });
          }
          else if (this._authService.hasLoggedUserPermitted(ROLE.REVIEWER)) {//TODO
            if (this.userInfo.type?.toLowerCase()?.split(',')?.includes('audit')) {
              this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
                if (result != null && result != undefined) {
                  this.homeConfigurations = [
                    { title: 'Review', link: 'schedules/reviewProcess/fqa', count: result.reviewCount, toBeCount: result.reviewActionCount }

                  ];
                }
              });
            } else if (this.userInfo.type?.toLowerCase()?.split(',')?.includes('inspection')) {
              this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
                if (result != null && result != undefined) {
                  this.homeConfigurations = [
                    { title: 'Review', link: 'schedules/reviewProcess/inspection', count: result.reviewCount, toBeCount: result.reviewActionCount }
                  ];
                }
              });
            }
          }
          else if (this._authService.hasLoggedUserPermitted(ROLE.SELLER)) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
              if (result != null && result != undefined) {
                this.homeConfigurations = [
                  { title: 'Inspection', link: 'master/inspectionRequest', count: result.requestCount }, ////TODO
                  // { title: 'Testing', link: 'testing/testRequisition', count: result.trfCount } //TODO
                ];
              }
            });
          }
          else if (this._authService.hasLoggedUserPermitted(ROLE.AUDITOR)) {
            if (this.userInfo.type?.toLowerCase()?.split(',')?.includes('audit')) {
              this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
                if (result != null && result != undefined) {
                  this.homeConfigurations = [
                    { title: 'Review', link: 'schedules/reviewProcess/fqa', count: result.reviewCount, toBeCount: result.reviewActionCount },
                    { title: 'My Audits', link: 'schedules/inspectionSchedules/fqa', count: result.auditCount },
                  ];
                }
              });
            } else if (this.userInfo.type?.toLowerCase()?.split(',')?.includes('inspection')) {
              this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
                if (result != null && result != undefined) {
                  this.homeConfigurations = [
                    { title: 'Review', link: 'schedules/reviewProcess/inspection', count: result.reviewCount, toBeCount: result.reviewActionCount },
                    { title: 'My Inspections', link: 'schedules/inspectionSchedules/inspection', count: result.inspectionCount },
                  ];
                }
              });
            }
          }
          else if (this._authService.hasLoggedUserPermitted(ROLE.LAB)) {//TODO
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
              if (result != null && result != undefined) {
                this.homeConfigurations = [
                  { title: 'Request', link: 'testing/testRequisition', count: result.trfCount },//TODO
                  { title: 'CAPA', link: 'capa/capa/testing', count: result.capaCount },
                ];
              }
            });
          }
          else if (this._authService.hasLoggedUserPermitted(ROLE.VENDOR)) { //TODO
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
              if (result != null && result != undefined) {
                this.homeConfigurations = [
                  { title: 'On Boarding Status', link: '', count: result.onBoardingStatus },
                  // { title: 'FQA Score', link: '', count: result.fqaScore },
                  { title: 'CAPA', link: 'capa/capa/all', count: result.capaCount, toBeCount: result.capaActionCount }
                ];
                if (result?.onBoardingStatus == "REQUEST RAISED") {
                  const dialogRef = this._dialog.open(VendorHomeIntroDialogComponent, {
                    width: '35%',
                    data: {},
                    disableClose: true
                  });

                  dialogRef.afterClosed().subscribe(result => {
                    if (result) {
                      this._router.navigate(['/vendor/training']);
                    }
                  });
                }
              }
            });
          }
          else if (this._authService.hasLoggedUserPermitted(ROLE.FACTORY)) {//TODO
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.homePageCount.get).subscribe(result => {
              if (result != null && result != undefined) {
                this.homeConfigurations = [
                  { title: 'SAQ Score', link: 'vendor/saqForm', count: `${result.saqScore} %` },
                  { title: 'FQA Score', link: '', count: `${result.fqaScore} %` },
                ];
              }
            });
          }
        }
      }
    }

  }

  public isDashBoardVisible(): boolean {
    return (this._commonService.isGenericDashBoardVisible() && (this._router.url == '/home'));
  }

  private getStatsByAllAuditType(): void {
    this.auditTypeDataForQualityGraph = [];
    let payload: any = {};
    payload = Util.clone(this.selectedFilters);
    delete payload['auditTypeUid'];

    this._invokeService.serviceInvocation(APP_UI_CONFIG.qualityDashBoard.getStatsByAllAuditType, null, null, payload)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.auditTypeDataForQualityGraph = response;
            this.prepareAuditTypeListForTopInQualityPage();
            setTimeout(() => {
              this.prepareAuditTypeWiseData(response);
            }, 200);
          }
        }, error: error => {
          this._commonService.handleError(error);
        }

      })
  }

  private getDesignCodeWiseDataForTable(): void {
    this.designCodewiseList = [];
    this._invokeService.serviceInvocation(APP_UI_CONFIG.qualityDashBoard.getDesignCodeWiseDataForTable, null, null, this.selectedFilters)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.designCodewiseList = response;
          }
        }, error: error => {
          this._commonService.handleError(error);
        }

      })
  }

  public getTopNDefects(): void {
    if (this.currentTabIndex == 1) {
      this.topNDefects = [];
      let payload = Util.clone(this.selectedFilters);
      payload['topNoOfDefects'] = this.selectedNValueForTopNDefects.value;

      this._invokeService.serviceInvocation(APP_UI_CONFIG.qualityDashBoard.getTopNDefects, null, null, payload)
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            if (response) {
              this.sno = 0;
              this.topNDefects = response;
            }
          }, error: error => {
            this._commonService.handleError(error);
          }

        })
    }
  }


  private getStatsByAuditType(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.qualityDashBoard.getStatsByAuditType, null, null, this.selectedFilters)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.transformStatsByAuditTypeData(response);
          }
        }, error: error => {
          this._commonService.handleError(error);
          this.transformStatsByAuditTypeData({})
        }

      })
  }

  private getDefectCount(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.qualityDashBoard.getDefectCount, null, null, this.selectedFilters)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.transformDefectCountData(response);
          }
        }, error: error => {
          this._commonService.handleError(error);
          this.transformDefectCountData({});
        }

      })
  }

  private getCheckpointSummary(): void {
    this.passFailDataForDefectGraphData = {};
    this._invokeService.serviceInvocation(APP_UI_CONFIG.qualityDashBoard.getCheckpointSummary, null, null, this.selectedFilters)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.passFailDataForDefectGraphData = response;
            this.prepareDefectsPassFailData();
            this.getDefectTypeCount();
          }
        }, error: error => {
          this._commonService.handleError(error);
        }

      })
  }

  private getObjectFirstPropertyValue(obj): any {
    const [prop] = Object.keys(obj)
    return obj[prop];
  }

  //this id for middle section of defect for pie chart
  private getDefectTypeCount(): void {
    this.defectTypesCountAsPassFailData = this.getObjectFirstPropertyValue(this.passFailDataForDefectGraphData);
    // this.passFailDataForDefectGraphData[this.selectAuditTypeName]
    this.prepareDefectTypeCountPassfail();
  }

  private getDashBoardFilterList(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.qualityDashBoard.getDashBoardFilterList, null, null, null)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.setFilterList(response);
          }
        }, error: error => {
          this._commonService.handleError(error);
        }

      })
  }

  //this api will give  option list for Division,SubCategory,Origin and Collection filters
  private getOptionListForDivisionSubCategoryOriginCollection(): void {
    let payload = {
      to: this.selectedFilters.to,
      from: this.selectedFilters.from,
      tenantUid: this.selectedFilters.tenantUid
    }
    this._invokeService.serviceInvocation(APP_UI_CONFIG.qualityDashBoard.getOptionListForFilters, null, null, payload)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.transformOptionListForFilters(response);
          }
        }, error: error => {
          this._commonService.handleError(error);
        }

      })
  }

  private findMaxSumDefect(array): any {
    return array.reduce((maxDefect, current) => {
      const currentSum = current.critical + current.major + current.minor;
      const maxSum = maxDefect.critical + maxDefect.major + maxDefect.minor;

      return currentSum > maxSum ? current : maxDefect;
    });
  }

  private transformDefectCountData(data): void {
    this.defectTotalCriticalCount = -1;
    this.defectTotalMajorCount = -1;
    this.defectTotalMinorCount = -1;
    this.overAllDefectPercentage = -1;
    this.defectGraphData = [];

    let finalData: auditTypeForDashBoard = this.getObjectFirstPropertyValue(data)
    //data[this.selectAuditTypeName];
    this.defectTotalCriticalCount = finalData?.totalCritical;
    this.defectTotalMajorCount = finalData?.totalMajor;
    this.defectTotalMinorCount = finalData?.totalMinor;
    this.overAllDefectPercentage = finalData?.defectPercentage;

    if (finalData?.severity) {
      const maxDefect = this.findMaxSumDefect(finalData?.severity);
      const maxSum = maxDefect.critical + maxDefect.major + maxDefect.minor;
      const minValue = (maxSum + (maxSum / 3)) / 12;

      finalData?.severity.forEach(element => {
        this.defectGraphData.push({
          "type": element.defectTypeCategoryName,
          "critical": (element.critical >= minValue) ? element.critical : (element.critical > 0 ? minValue : undefined),
          "major": (element.major >= minValue) ? element.major : (element.major > 0 ? minValue : undefined),
          "minor": (element.minor >= minValue) ? element.minor : (element.minor > 0 ? minValue : undefined),
          "criticalValue": element.critical > 0 ? element.critical : '',
          "majorValue": element.major > 0 ? element.major : '',
          "minorValue": element.minor > 0 ? element.minor : '',
        })
      });
    } else {

    }
    this.prepareDefectsStackedData();
  }

  public prepareDefectsStackedData(): void {
    let passFailSeries: any[] = [
      { label: 'Critical', value: 'critical', actualValue: 'criticalValue', color: '#82000c' },
      { label: 'Major', value: 'major', actualValue: 'majorValue', color: '#dc7ba4' },
      { label: 'Minor', value: 'minor', actualValue: 'minorValue', color: '#f0bc42' },
    ];
    if (this.showDefectGraph)
      this.renderStackedChart('defectsStackedChart', this.defectGraphData, passFailSeries);
  }

  private transformStatsByAuditTypeData(data): void {
    this.poVSOfferForQualityGraphData = [];
    this.passFailDataForQualityGraphData = [];
    this.passFailReInspectionDataForQualityGraphData = [];

    let finalData: auditTypeForDashBoard = this.getObjectFirstPropertyValue(data)
    //data[this.selectAuditTypeName];
    let data1: any[] = [];
    finalData?.passFail?.forEach(item => {
      data1.push({
        "type": item.brandName,
        "pass": item.passPercentage,
        "fail": item.failPercentage,
      })
    })
    const maxDefect = this.findMaxValue(data1);

    this.hasReInspection = finalData?.passFail?.some(item =>
      'reInspectionCount' in item && item.reInspectionCount !== undefined
    );

    finalData?.passFail?.forEach(item => {
      const baseData = {
        "type": item.brandName || "",
        "pass": item.passPercentage,
        "fail": item.failPercentage,
        "passCount": item.passCount ? `(${item.passCount})` : '',
        "noCountValue":  '',
        "failCount": item.failCount ? `(${item.failCount})` : '',
        "barHeightpass": item.passPercentage > (maxDefect / 6) ? item.passPercentage : (item.passPercentage > 0 ? (maxDefect / 6) : undefined),
        "barHeightfail": item.failPercentage > (maxDefect / 6) ? item.failPercentage : (item.failPercentage > 0 ? (maxDefect / 6) : undefined)
      };

      if (this.hasReInspection) {
        Object.assign(baseData, {
          "reInspection": item.reInspectionPercentage,
          "reInspectionCount": item.reInspectionCount ? `(${item.reInspectionCount})` : '',
          "barHeightreInspection": item.reInspectionPercentage > (maxDefect / 6) ? item.reInspectionPercentage : (item.reInspectionPercentage > 0 ? (maxDefect / 6) : undefined)
        });
      }
      this.passFailDataForQualityGraphData.push(baseData);
    });

    this.hasReInspection ?
      this.prepareQualityPassFailReInspectionData() :
      this.prepareQualityPassFailData();

    this.prepareQualityFirstTimePassData();

    this.hasRejectQty = finalData?.poVSOffer?.some(item =>
      'rejectQty' in item && item.rejectQty !== undefined
    );

    finalData?.poVSOffer?.forEach(item => {
      const baseData = {
        "type": item.brandName || "",
        "po": item.orderQty ? `(${item.orderQty})` : '',
        "offered": item.offerQty ? `(${item.offerQty})` : '',
        "poPer": item.orderQty ? 100 : 0,
        "offeredPer": item.poVsOfferPercentage,
        "barHeightofferedPer": item.poVsOfferPercentage > 33 ? item.poVsOfferPercentage : (item.poVsOfferPercentage > 0 ? 33 : undefined),
        "barHeightpoPer": item.orderQty ? 100 : undefined,
      };

      if (this.hasRejectQty) {
        Object.assign(baseData, {
          "reject": item.rejectQty ? `(${item.rejectQty})` : '',
          "rejectPer": item.poVsRejectedPercentage,
          "barHeightrejectPer": item.poVsRejectedPercentage > 33 ? item.poVsRejectedPercentage : (item.poVsRejectedPercentage > 0 ? 33 : undefined)
        });
      }

      this.poVSOfferForQualityGraphData.push(baseData);
    });

    this.hasRejectQty ?
      this.preparePoPassFailRejectData() :
      this.preparePoPassFailData();

  }

  isOpen = false;
  public openSidebar(): void {
    this.isOpen = true;
  }

  public closeSidebar(): void {
    this.isOpen = false;
  }

  public refreshDashBoard(): void {
    this.clearFilter();
    this.getDashBoardFilterList();
  }

  public onClickCount(item: homePageConfigurations): void {
    if (item?.link) {
      this._commonService.navigate(item.link);
    }
  }

  private prepareQualityData(): void {
    setTimeout(() => {
      this.prepareAuditTypeWiseData(this.auditTypeDataForQualityGraph);
      this.prepareQualityFirstTimePassData();
      this.hasReInspection ? this.prepareQualityPassFailReInspectionData() : this.prepareQualityPassFailData();
      this.hasRejectQty ? this.preparePoPassFailRejectData() : this.preparePoPassFailData();
    }, 100);
  }

  public prepareDefectsStackedDataChart(): void {
    setTimeout(() => {
      this.prepareDefectsStackedData();
    }, 100)
  }

  public prepareGraphs(): void {
    setTimeout(() => {
      if (this.currentTabIndex == 1 && this.toggleGraphAndTable) {
        if (this.showDefectGraph)
          this.prepareDefectsStackedData();
        this.prepareDefectsPassFailData();
        this.prepareDefectTypeCountPassfail();
      } else if (this.currentTabIndex == 0 && this.toggleGraphAndTable) {
        this.prepareQualityData();
      }
    }, 100)

  }

  public onChangeTabView(event): void {
    switch (event.index) {
      case 0:
        this.currentTabIndex = 0;
        this.isShowTable = false;
        this.callDashBoardApi();
        break;
      case 1:
        this.currentTabIndex = 1;
        this.isShowDefectsTable = false;
        this.callDashBoardApi();
        setTimeout(() => {
          this.prepareDefectsPassFailData();
        }, 100)
        break;
      default:
        break;
    }
  }

  public onFilterMore(): void {
    this.showMoreFilter = false;
  }

  private prepareAuditTypeListForTopInQualityPage(): void {
    this.auditTypeListToShowInTop = [];
    for (const property in this.auditTypeDataForQualityGraph) {
      let obj = { name: property, value: this.auditTypeDataForQualityGraph[property].total };
      this.auditTypeListToShowInTop.push(obj);
    }
  }

  auditTypeMainTopbar: any = [];
  private prepareAuditTypeWiseData(data): void {
    if (this.toggleGraphAndTable) {
      let auditTypeData: any[] = [];

      for (const property in data) {
        const stats = data[property].stats[0];
        let obj = {
          type: property,
          schedule: stats.schedule > 0 ? stats.schedule : 0,
          started: stats.started > 0 ? stats.started : 0,
          completed: stats.completed > 0 ? stats.completed : 0,
        }
        if (obj.schedule != 0 || obj.started != 0 || obj.completed != 0)
          auditTypeData.push(obj)
      }
      this.chartCount = 0;
      this.pieChartRows = [[]];

      let auditTypeSeries: any[] = [
        { label: 'Scheduled', value: 'schedule', color: '#fad232' },
        { label: 'Started', value: 'started', color: '#f37b1d' },
        { label: 'Completed', value: 'completed', color: '#5eb95e' }
      ]

      am5.array.each(am5.registry.rootElements, function (root) {
        if (root?.dom?.id?.includes('qualityPassFailPercentChartDiv')) {
          root.dispose();
        }
      })
      this.auditTypeMainTopbar = auditTypeData;
      if (this.currentTabIndex == 0) {
        auditTypeData?.forEach(item => {
          this.addPieChartsforQualityScreen(item, auditTypeSeries, 'qualityPassFailPercentChartDiv')
        })
      }
    }
  }

  private prepareQualityPassFailData(): void {
    let passFailSeries: any[] = [
      { label: 'Pass', value: 'pass', countValue: 'passCount', color: '#5eb95e', barHeight: 'barHeightpass' },
      { label: 'Fail', value: 'fail', countValue: 'failCount', color: '#dd514c', barHeight: 'barHeightfail' },
    ]

    this.renderBarChart('qualityPassFail', this.passFailDataForQualityGraphData, passFailSeries)
  }

  private prepareQualityFirstTimePassData(): void {
    let passFailSeries: any[] = [
      { label: 'Pass', value: 'pass', countValue: 'noCountValue', color: '#5eb95e', barHeight: 'barHeightpass' },
    ]

    this.renderBarChart('qualityFirstTimePass', this.passFailDataForQualityGraphData, passFailSeries)
  }


  private prepareQualityPassFailReInspectionData(): void {
    let passFailReInspectionSeries: any[] = [
      { label: 'Pass', value: 'pass', countValue: 'passCount', color: '#5eb95e', barHeight: 'barHeightpass' },
      { label: 'Fail', value: 'fail', countValue: 'failCount', color: '#dd514c', barHeight: 'barHeightfail' },
      { label: 'Re-Inspection', value: 'reInspection', countValue: 'reInspectionCount', color: '#f37b1d', barHeight: 'barHeightreInspection' },
    ]
    this.renderBarChart('qualityPassFail', this.passFailDataForQualityGraphData, passFailReInspectionSeries)
  }

  private prepareDefectsPassFailData(): void {
    if (this.toggleGraphAndTable) {
      let passFailData: any[] = [];
      const result = {};

      let data1: any[] = [];
      this.getObjectFirstPropertyValue(this.passFailDataForDefectGraphData)?.forEach(obj => {
        if (obj.defect == false) {
          data1.push({
            "type": obj.checkpoint,
            "pass": obj.passPercentage,
            "fail": obj.failPercentage,
          })
        }
      });
      const maxDefect = this.findMaxValue(data1);

      //  this.passFailDataForDefectGraphData[this.selectAuditTypeName]?.forEach(obj => {
      this.getObjectFirstPropertyValue(this.passFailDataForDefectGraphData)?.forEach(obj => {
        if (obj.defect == false) {
          passFailData.push({
            "type": obj.checkpoint,
            "pass": obj.passPercentage,
            "fail": obj.failPercentage,
            "passCount": obj.passCount ? '(' + obj.passCount + ')' : '',
            "failCount": obj.failCount ? '(' + obj.failCount + ')' : '',
            "barHeightpass": obj.passPercentage > (maxDefect / 6) ? obj.passPercentage : (obj.passPercentage > 0 ? (maxDefect / 6) : undefined),
            "barHeightfail": obj.failPercentage > (maxDefect / 6) ? obj.failPercentage : (obj.failPercentage > 0 ? (maxDefect / 6) : undefined)
          })
        }
      });

      let passFailSeries: any[] = [
        { label: 'Pass', value: 'pass', countValue: 'passCount', color: '#5eb95e', barHeight: 'barHeightpass' },
        { label: 'Fail', value: 'fail', countValue: 'failCount', color: '#dd514c', barHeight: 'barHeightfail' },
      ]

      this.renderBarChart('defectPassFail', passFailData, passFailSeries);
      //this.renderBarChart('defectPassFailCapa', passFailData, passFailSeries); //TODO : For Capa screen
    }
  }

  private prepareDefectTypeCountPassfail(): void {
    if (this.toggleGraphAndTable) {
      this.defectTypesAsPassFailGraphData = [];
      //this.passFailDataForDefectGraphData[this.selectAuditTypeName]?.forEach(obj => {
      this.getObjectFirstPropertyValue(this.passFailDataForDefectGraphData)?.forEach(obj => {
        if (obj.defect == true) {
          this.defectTypesAsPassFailGraphData.push({
            "type": obj.checkpoint,
            "metaDataPass": obj.passCount,
            "metaDataFail": obj.failCount,
            "pass": obj.passPercentage,
            "fail": obj.failPercentage,
          })
        }
      });
      this.defectpieChartCount = 0;
      //this.defectpieChartCountCAPA = 0; //TODO : For Capa screen
      this.defectpieChartRows = [[]];
      //this.defectpieChartRowsCAPA = [[]]; //TODO : For Capa screen

      let series: any[] = [
        { label: 'Pass', color: '#6cacf5' },
        { label: 'Fail', color: '#ffd75f' },
      ]

      am5.array.each(am5.registry.rootElements, function (root) {
        if (root?.dom?.id?.includes('prepareDefectTypeCountPassfailChartDiv')) {
          root.dispose();
        }
        //TODO : For Capa screen
        // if (root?.dom?.id?.includes('prepareDefectTypeCountPassfailChartDivCapa')) {
        //   root.dispose();
        // }
      })

      this.defectTypesAsPassFailGraphData?.forEach(item => {
        this.addDefectPieChart(item, series, 'prepareDefectTypeCountPassfailChartDiv');
      })
      //TODO : For Capa screen
      // this.defectTypesAsPassFailGraphData?.forEach(item => {
      //   this.addDefectPieChartCAPA(item, series, 'prepareDefectTypeCountPassfailChartDivCapa')
      // });
    }

  }

  private preparePoPassFailData(): void {
    let poVsOffredSeries: any[] = [
      { label: 'PO', value: 'poPer', countValue: 'po', color: '#1f8dd6', barHeight: 'barHeightpoPer' },
      { label: 'OFFERED', value: 'offeredPer', countValue: 'offered', color: '#8058a5', barHeight: 'barHeightofferedPer' }
    ];

    this.renderBarChart('poOfferedQty', this.poVSOfferForQualityGraphData, poVsOffredSeries)
  }

  private preparePoPassFailRejectData(): void {
    let poVsOffredRejectSeries: any[] = [
      { label: 'PO', value: 'poPer', countValue: 'po', color: '#1f8dd6', barHeight: 'barHeightpoPer' },
      { label: 'OFFERED', value: 'offeredPer', countValue: 'offered', color: '#8058a5', barHeight: 'barHeightofferedPer' },
      { label: 'REJECT', value: 'rejectPer', countValue: 'reject', color: '#dd514c', barHeight: 'barHeightrejectPer' }
    ];

    this.renderBarChart('poOfferedQty', this.poVSOfferForQualityGraphData, poVsOffredRejectSeries);
  }

  private renderStackedChart(id, data: any[], columnSeries: any[]): void {
    if (this.toggleGraphAndTable) {
      this.renderStackedChartView(id, data, columnSeries)
    }
  }

  private renderStackedChartView(id, data: any[], columnSeries: any[]): void {
    if (data.length == 0) {
      document.getElementById(id)?.classList.add("chart_no_data");

      if (document.getElementById(id))
        document.getElementById(id).innerHTML = `<div>No data found for the provided selection</div>`;

      if (document.getElementById("chartExport"))
        document.getElementById("chartExport").style.display = 'none';
    } else {
      document.getElementById(id)?.classList.remove("chart_no_data");

      if (document.getElementById(id))
        document.getElementById(id).innerHTML = '';

      if (document.getElementById("chartExport"))
        document.getElementById("chartExport").style.display = 'block';
      this.clearChartRoot(id);
      let root = am5.Root.new(id);
      const myTheme = am5.Theme.new(root);
      root.setThemes([
        am5themes_Animated.new(root),
        myTheme
      ]);

      root._logo.dispose();

      let chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "panX",
        wheelY: "zoomX",
        paddingLeft: 0,
        layout: root.verticalLayout
      }));

      // Create axes

      let xRenderer: any = am5xy.AxisRendererX.new(root, {
        minorGridEnabled: true
      });
      let xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        categoryField: "type",
        renderer: xRenderer,
        tooltip: am5.Tooltip.new(root, {})
      }));

      xRenderer.grid.template.setAll({
        location: 1,
        stroke: am5.color(0xFFFFFF),
        strokeWidth: 2
      })

      if (data.length < 7) {
        xAxis.get("renderer").labels.template.setAll({
          fill: am5.color('#7b8699'),
          paddingTop: 15,
          paddingBottom: 40,
        });
      } else {
        xAxis.get("renderer").labels.template.setAll({
          fill: am5.color('#7b8699'),
          rotation: -90,
          centerX: 100,
          centerY: am5.p0,
          marginBottom: 25,
          paddingTop: 0
        });
      }

      xAxis.data.setAll(data);

      let yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        min: 0,
        renderer: am5xy.AxisRendererY.new(root, {
          strokeOpacity: 0.1,
        })
      }));

      yAxis.get("renderer").labels.template.setAll({
        fill: am5.color('#7b8699'),
      });

      // Add legend
      let legend = chart.children.push(am5.Legend.new(root, {
        centerX: am5.p50,
        x: am5.p50
      }));


      const makeSeries = (name, fieldName, color, actualValue) => {
        let series = chart.series.push(am5xy.ColumnSeries.new(root, {
          name: name,
          stacked: true,
          xAxis: xAxis,
          yAxis: yAxis,
          valueYField: fieldName,
          categoryXField: "type",
        }));

        series.columns.template.setAll({
          tooltipText: "{categoryX},{name}: " + `{${actualValue}}`,
          tooltipY: am5.percent(10),
        });
        // Rounded corners for columns
        series.columns.template.setAll({
          maxWidth: 100,
        });

        series.set("fill", am5.color(color));
        series.set("stroke", am5.color(color));
        series.data.setAll(data);

        // Make stuff animate on load
        series.appear();

        series.bullets.push(function () {
          return am5.Bullet.new(root, {
            sprite: am5.Label.new(root, {
              text: `{${actualValue}}`,
              fill: root.interfaceColors.get("alternativeText"),
              centerY: am5.p50,
              centerX: am5.p50,
              populateText: true,
              fontWeight: 'bold',
              marginTop: 5,
              marginBottom: 5
            })
          });
        });

        legend.data.push(series);
      }

      columnSeries.forEach(column => {
        makeSeries(column.label, column.value, column.color, column.actualValue);
      })

      if (id == 'defectsStackedChart') {
        this.defectGraphRoot = root;
      } else if (id == 'auditTypeStackedChart') {
        this.auditTypeStackedChartRoot = root;
      }

      chart.appear(1000, 100);
    }

  }

  private renderBarChart(id, data: any[], columnSeries: any[]): void {
    if (this.toggleGraphAndTable) {
      this.renderBarChartView(id, data, columnSeries)
    }
  }

  private renderBarChartView(id, data: any[], columnSeries: any[]): void {
    if (data.length == 0) {
      document.getElementById(id)?.classList.add("chart_no_data");
      if (document.getElementById(id))
        document.getElementById(id).innerHTML = `<div>No data found for the provided selection</div>`;
      if (document.getElementById("chartExport"))
        document.getElementById("chartExport").style.display = 'none';
    } else {
      document.getElementById(id)?.classList.remove("chart_no_data");
      if (document.getElementById(id))
        document.getElementById(id).innerHTML = '';
      if (document.getElementById("chartExport"))
        document.getElementById("chartExport").style.display = 'block';
      this.clearChartRoot(id);
      let root = am5.Root.new(id);
      const myTheme = am5.Theme.new(root);
      root.setThemes([
        am5themes_Animated.new(root),
        myTheme
      ]);
      root._logo.dispose();
      // Create chart
      let chart = root.container.children.push(am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        paddingLeft: 0,
        wheelX: "panX",
        wheelY: "zoomX",
        layout: root.verticalLayout,
      }));


      // Add legend
      let legend = chart.children.push(
        am5.Legend.new(root, {
          centerX: am5.p50,
          x: am5.p50
        })
      );
      // Create axes
      let xRenderer: any = am5xy.AxisRendererX.new(root, {
        cellStartLocation: 0.1,
        cellEndLocation: 0.9,
        minorGridEnabled: true
      })

      let xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
        categoryField: "type",
        renderer: xRenderer,
        tooltip: am5.Tooltip.new(root, {})
      }));

      if ((data.length > 4 && id != 'defectPassFail') || (data.length > 7 && id == 'defectPassFail')) {
        xAxis.get("renderer").labels.template.setAll({
          fill: am5.color('#7b8699'),
          paddingTop: 15,
          paddingBottom: 40,
          rotation: -90,
          centerY: am5.p50
        });
      } else {
        xRenderer.grid.template.setAll({
          location: 1,
          stroke: am5.color(0xFFFFFF),
          strokeWidth: 2
        })

      }

      xAxis.get("renderer").labels.template.setAll({
        fill: am5.color('#7b8699'),
        paddingTop: 15,
        paddingBottom: 40,
      });

      xAxis.data.setAll(data);

      let yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
        renderer: am5xy.AxisRendererY.new(root, {
          strokeOpacity: 0.1,
        }),
      }));

      yAxis.get("renderer").labels.template.setAll({
        fill: am5.color('#7b8699'),
      });

      // Add series
      const makeSeries = (name, fieldName, color, countValue, actualValue) => {
        let series = chart.series.push(am5xy.ColumnSeries.new(root, {
          name: name,
          xAxis: xAxis,
          yAxis: yAxis,
          valueYField: fieldName,
          categoryXField: "type"
        }));

        series.columns.template.setAll({
          tooltipText: "{categoryX},{name}:" + `{${actualValue}}%` + ', ' + `{${countValue}}`,
          width: am5.percent(90),
          tooltipY: 0,
          strokeOpacity: 0,
        });
        // Rounded corners for columns
        series.columns.template.setAll({
          maxWidth: 100,
        });
        // Make each column to be of a different color
        series.set("fill", am5.color(color));

        series.data.setAll(data);

        // Make stuff animate on load
        series.appear();

        let maxValue: number;
        maxValue = this.findMaxValue(data);
        yAxis.set("max", maxValue * 1.1);
        yAxis.set("min", 0);
        let symbol: string = '';
        if (id == 'qualityPassFail') {
          this.qualityPassFailRoot = root;
          symbol = '%'
        } else if (id == 'poOfferedQty') {
          symbol = '%'
          this.poOfferedQtyRoot = root;
        } else if (id == 'defectPassFail') {
          symbol = '%'
          this.defectsPassFailRoot = root;
        } else if (id == 'qualityFirstTimePass') {
          symbol = '%'
          this.qualityFirstTimePassRoot = root;
        }
        // Add Label bullet
        series.bullets.push(function () {
          return am5.Bullet.new(root, {
            locationY: 1,
            sprite: am5.Label.new(root, {
              text: `{${actualValue}}` + symbol,
              centerY: 30,
              fontWeight: "bold",
              centerX: am5.p50,
              populateText: true,
              marginTop: 20

            })
          });
        });
        series.bullets.push(function () {
          return am5.Bullet.new(root, {
            locationY: 1,
            sprite: am5.Label.new(root, {
              centerX: am5.p50,
              centerY: 0,
              text: `{${countValue}}`,
              populateText: true
            })
          });
        });
        legend.data.push(series);
      }

      columnSeries.forEach(column => {
        makeSeries(column.label, column.barHeight, column.color, column.countValue, column.value);
      });
      // Make stuff animate on load
      chart.appear(1000, 100);
    }
  }

  private findMaxValue(array): any {
    return array.reduce((max, obj) => {
      // Get all numeric values from the current object
      const currentMax = Math.max(
        ...Object.values(obj).map(value => {
          // Convert to number and check if it's finite
          const num = Number(value);
          return isFinite(num) ? num : -Infinity; // Only consider finite numbers
        })
      );
      // Compare with the global maximum
      return Math.max(max, currentMax);
    }, -Infinity); // Start with the smallest possible number
  }

  public addPieChartsforQualityScreen(data, columnSeries, chartId) {
    const newChartId = this.chartCount++;
    const newChartData = this.getChartData(newChartId, data, columnSeries);

    const lastRow = this.pieChartRows[this.pieChartRows.length - 1];
    lastRow.push({ id: newChartId, data: newChartData });

    setTimeout(() => {
      const chart = this.createChart(newChartId, newChartData, chartId, 'quality');
      this.charts.push(chart); // Store the chart instance for exporting
    });

  }

  public addDefectPieChart(data, columnSeries, chartId) {
    const newChartId = this.defectpieChartCount++;
    const newChartData = this.getChartData(newChartId, data, columnSeries);

    const lastRow = this.defectpieChartRows[this.defectpieChartRows.length - 1];
    lastRow.push({ id: newChartId, data: newChartData });

    setTimeout(() => {
      const chart = this.createChart(newChartId, newChartData, chartId, 'defect');
      this.charts.push(chart); // Store the chart instance for exporting
    });

  }

  //TODO : For Capa screen
  // public addDefectPieChartCAPA(data, columnSeries, chartId) {
  //   const newChartId = this.defectpieChartCountCAPA++;
  //   const newChartData = this.getChartData(newChartId, data, columnSeries);

  //   const lastRow = this.defectpieChartRowsCAPA[this.defectpieChartRowsCAPA.length - 1];
  //   lastRow.push({ id: newChartId, data: newChartData });

  //   setTimeout(() => {
  //     const chart = this.createChart(newChartId, newChartData, chartId, 'capa');
  //     this.charts.push(chart); // Store the chart instance for exporting
  //   });

  // }

  private getChartData(chartId: number, data, columnSeries) {
    let dataForPieGraph: any[] = [];

    let keyIndex = 0;
    for (let key in data) {
      if (key !== 'type' && !key.includes('metaData')) {
        dataForPieGraph.push({
          category: columnSeries[keyIndex].label,
          value: data[key],
          type: data.type,
          sliceSettings: {
            fill: am5.color(columnSeries[keyIndex].color),
            stroke: am5.color(columnSeries[keyIndex].color)
          }
        })
        keyIndex++;
      }
    }
    return dataForPieGraph;

  }

  private createChart(chartId: number, data: any[], chartDivId, type) {
    const chartDiv = document.getElementById(chartDivId + '' + chartId);
    if (!chartDiv) return;

    const root = am5.Root.new(chartDiv);
    const chart = root.container.children.push(am5percent.PieChart.new(root, {
      radius: am5.percent(90),
      innerRadius: type == 'quality' ? am5.percent(70) : 0,
      layout: root.verticalLayout
    }));
    const series = chart.series.push(am5percent.PieSeries.new(root, {
      valueField: "value",
      categoryField: "category",
      legendLabelText: "{category}",
      legendValueText: "",
      paddingTop: 10
    }));

    // Set colors for each slice
    series.slices.template.setAll({
      templateField: "sliceSettings"
    });

    series.labels.template.setAll({
      fontSize: 18,
      text: type == 'quality' ? "{value}" : "{value}" + '%',
      textType: "adjusted",
      radius: 10,
      fontWeight: 'bold'
    })

    series.slices.template.events.on("click", (ev) => {
      let data: any = ev.target?.dataItem?.component?.data?.values;
      let obj = { type: data[0]?.type };
      data?.forEach((item: any) => {
        obj[item.category] = item.value;
      })
    })

    series.data.setAll(data);

    root._logo.dispose();
    return chart; // Return the chart instance
  }



  public onToggleTable(): void {
    this.isShowTable = !this.isShowTable;
    if (!this.isShowTable) {
      this.prepareQualityData();
    } else {
      this.prepareTableColumns();
    }
  }

  private convertNullToEmptyString(data): any {
    return (data == 'null' || data == 'undefined') ? '' : data;
  }
  sno: number = 0;
  private prepareTableColumns(): void {
    this.sno = 0;
    this.columnDefs = [
      {
        headerName: "PO No", field: "poNumber", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.poNumber)
        }
      },
      {
        headerName: "Brand", field: "brandName", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.brandName)
        }
      },
      {
        headerName: "Category", field: "categoryName", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.categoryName)
        }
      },
      {
        headerName: "Sub-Category", field: "subCategory", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.subCategory)
        }
      },
      {
        headerName: "Article", field: "designCode", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.designCode)
        }
      },
      {
        headerName: "Vendor", field: "vendorName", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.vendorName)
        }
      },
      {
        headerName: "Qty Offered", field: "qtyOffered", sortable: true, unSortIcon: false,
        width: 120, minWidth: 120,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.qtyOffered)
        }
      },
      {
        headerName: "Qty Ordered", field: "qtyOrdered", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.qtyOrdered)
        }
      },
      {
        headerName: "PO Vs Offered %", field: "poVsOfferedPercentage", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.poVsOfferedPercentage)
        }
      },
      {
        headerName: "Audit Result", field: "auditResult", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          let value = this.convertNullToEmptyString(params.data.auditResult);
          return value?.toLowerCase() == 'fail' ? this._commonService.prepareStatusText(params.data.auditResult, '#dd514c') : this._commonService.prepareStatusText(params.data.auditResult, '#5eb95e');
        }
      },
      // { headerName: "1st Time Pass", field: "", sortable: true, unSortIcon: true },
      // { headerName: "2nd Time Pass", field: "", sortable: true, unSortIcon: true },
      // {
      //   headerName: "Qty Rejected", field: "qtyReject", sortable: true, unSortIcon: false,
      //   width: 120, minWidth: 120,
      //   cellRenderer: (params) => {
      //     return this.convertNullToEmptyString(params.data.qtyReject)
      //   }
      // },
      // {
      //   headerName: "No Of inline Performed", field: "noOfInlineCount", sortable: true, unSortIcon: false,
      //   cellRenderer: (params) => {
      //     return this.convertNullToEmptyString(params.data.noOfInlineCount)
      //   }
      // },
      // {
      //   headerName: "No Of Final Inspection", field: "noOfFinalCount", sortable: true, unSortIcon: false,
      //   cellRenderer: (params) => {
      //     return this.convertNullToEmptyString(params.data.noOfFinalCount)
      //   }
      // },
    ];

    this.columnDefsForTopNDefects = [
      {
        headerName: "Top", field: "", sortable: true, unSortIcon: false,
        width: 100, minWidth: 80, maxWidth: 80,
        cellRenderer: (params) => {
          this.sno++;
          return this.sno;
        }
      },
      {
        headerName: "Headers", field: "defectTypeCategoryName", sortable: true, unSortIcon: false,
        width: 200, minWidth: 200, maxWidth: 200,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.defectTypeCategoryName)
        }
      },
      {
        headerName: "Defect Type", field: "defectTypeName", sortable: true, unSortIcon: false,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.defectTypeName)
        }
      },
      {
        headerName: "Critical", field: "critical", sortable: true, unSortIcon: false,
        width: 100, minWidth: 100, maxWidth: 100,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.critical)
        }
      },
      {
        headerName: "Major", field: "major", sortable: true, unSortIcon: false,
        width: 100, minWidth: 100, maxWidth: 100,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.major)
        }
      },
      {
        headerName: "Minor", field: "minor", sortable: true, unSortIcon: false,
        width: 100, minWidth: 100, maxWidth: 100,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.minor)
        }
      },
      {
        headerName: "Total", field: "totalCount", sortable: true, unSortIcon: false,
        width: 100, minWidth: 100, maxWidth: 100,
        cellRenderer: (params) => {
          return this.convertNullToEmptyString(params.data.totalCount)
        }
      },
    ]

  }

  public getNotificationList() {
    APP_UI_CONFIG.qualityDashBoard.getNotificationList.paramList.userId = this._commonService.getUseUid()
    this._invokeService.serviceInvocation(APP_UI_CONFIG.qualityDashBoard.getNotificationList)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.notificationList = response;
          }
        }, error: error => {
          this._commonService.handleError(error);
        }

      })
  }

  //Defects
  public onToggleDefectsTable(): void {
    this.isShowDefectsTable = !this.isShowDefectsTable;
    this.prepareDefectsTableColumns();
    this.prepareDefectsOverAllData();
  }

  private prepareDefectsOverAllData(): void {
    this.prepareDefectsStackedData();
    this.prepareDefectsPassFailData();
  }



  private prepareDefectsTableColumns(): void {
    this.defectsColumnDefs = [
      { headerName: "Top", field: "code", sortable: true, unSortIcon: true },
      { headerName: "Headers", field: "name", sortable: true, unSortIcon: true },
      { headerName: "Defect Type", field: "description", sortable: true, unSortIcon: true },
      { headerName: "Critical", field: "description", sortable: true, unSortIcon: true },
      { headerName: "Major", field: "description", sortable: true, unSortIcon: true },
      { headerName: "Minor", field: "description", sortable: true, unSortIcon: true }
    ]
  }

  private clearChartRoot(divId): void {
    am5.array.each(am5.registry.rootElements, function (root) {
      if (root?.dom?.id == divId) {
        root.dispose();
      }
    })
  }

  private callApisForGettingOptionListOfEachFilterItem() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());
    let baseUrl = environment.apiUrl;
    //apiResponse = this.http.get(url, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    const requests = this.filtersListData.filter((item) => item.endPoint && (item.type == 'dropdown' || item.type == 'dropdownSingle') && (!item.endPoint.includes(APP_UI_CONFIG.qualityDashBoard.getOptionListForFilters.url))).map(item =>
      this.http.get(baseUrl + item.endPoint, { headers: headers }).pipe(
        catchError(error => {
          return of({}); // Return null or any placeholder on error
        })
      )
    );

    forkJoin(requests).subscribe(responses => {
      const combinedResponse = this.filtersListData.filter((item) => item.endPoint && (item.type == 'dropdown' || item.type == 'dropdownSingle') && (!item.endPoint.includes(APP_UI_CONFIG.qualityDashBoard.getOptionListForFilters.url))).reduce((acc, item, index) => {
        acc[item.code] = responses[index];
        return acc;
      }, {});

      this.handleResponses(combinedResponse);
    });
  }

  handleResponses(combinedResponse: any) {
    // Process the combined responses as needed
    for (const key in combinedResponse) {
      if (Object.keys(combinedResponse[key]).length > 0) {
        this.transformedOptionListObj[key] = [];
        combinedResponse[key]?.forEach(value => {
          if (value.name || value.firstName || value.fullName) {
            this.transformedOptionListObj[key].push({
              name: value.name || value.firstName || value.fullName,
              value: value.uuid,
            });
          }
        });
      }
    }
    this.getOptionListForDivisionSubCategoryOriginCollection();
  }

  transformedOptionListObj: any = {};
  private transformOptionListForFilters(data): void {
    for (const key in data) {
      this.transformedOptionListObj[key] = [];
      data[key]?.forEach(value => {
        this.transformedOptionListObj[key].push({
          name: value,
          value: value
        });
      });
    }
    this.setOptionListToRespectiveFiltersItem();

  }

  private setOptionListToRespectiveFiltersItem(): void {
    this.filtersListData.forEach(item => {
      item.list = this.transformedOptionListObj[item.code]?.length > 0 ? this.transformedOptionListObj[item.code] : [];
      if (item.label.toLowerCase().includes('audittype') || item.label.toLowerCase().includes('audit type')) {
        let selectedAudits = _.find(item.list, function (o) {
          return o.value == item.uidValue
        });

        if (selectedAudits !== undefined) {
          item.value = selectedAudits;
          this.selectedFilters[item.code] = selectedAudits?.value;
          this.selectAuditTypeName = selectedAudits?.name;
        }
        else {
          item.value = item.list[0];
          this.selectedFilters[item.code] = item?.list[0]?.value;
          this.selectAuditTypeName = item?.list[0]?.name;
        }

      }
      if (this._commonService.getUseRole() === ROLE.SELLER && item.label.toLowerCase().includes('seller')) {
        let sellerUuid = this._commonService.getUseUid();
        let selectedSeller = _.find(item.list, function (o) {
          return o.value == sellerUuid;
        });
        if (selectedSeller) {
          item.value = selectedSeller;
          this.selectedFilters[item.code] = selectedSeller?.value;
        }
      }
    })
    this.getFilterCount();
    this.onTimeDurationSelectedItem(false);
    this.callDashBoardApi();
  }

  lastInputIndex: number = -1;

  public setFilterList(filterDataList: any[]): void {

    let filterList = filterDataList;

    filterList.sort((a, b) => {
      if (a.ordinal < b.ordinal) return -1;
      if (a.ordinal > b.ordinal) return 1;
      return 0; // they are equal
    });
    this.filtersListData = [];
    filterList?.forEach((item, i) => {
      if (item.tenantUid == this._commonService.getTenantUid() && item.active) {
        let obj = {
          show: true,
          label: item.filterName,
          type: (item.type == 'dropdown' && !item.multi) ? 'dropdownSingle' : item.type,
          selectedValue: '',
          showLabelName: 'name',
          value: '',
          list: [],
          endPoint: item.endPoint,
          code: item.code,
          uidValue: item.uidValue || ''
        }
        this.filtersListData.push(obj)
      }
    })
    this.transformedOptionListObj = {};
    this.callApisForGettingOptionListOfEachFilterItem();
  }

  public callDashBoardApi(): void {
    if (this.currentTabIndex == 1) {
      this.getTopNDefects();
      this.getDefectCount();
    }
    if (this.toggleGraphAndTable) {
      // this.getDesignCodeWiseDataForTable();
      this.getStatsByAllAuditType();
      if (this.currentTabIndex == 0) {
        this.getStatsByAuditType();
      } else if (this.currentTabIndex == 1) {
        this.getCheckpointSummary();
      }
    }
  }

  public onClickHideShowFilter(): void {
    this.hideShowFilter = !this.hideShowFilter;
    if (this.hideShowFilter) {
      this.filterIconUrl = './assets/svg/filter_blue_icon.svg';
    } else {
      this.filterIconUrl = './assets/svg/filter_icon.svg';
    }
  }

  public exportDefectScreenTableData(): void {

  }

  public exportTopNTableData(): void {

  }

  public downloadChartData(id, fileName: string, type: any): void {
    let root: any;
    switch (id) {
      case 'auditTypeStackedChart':
        root = this.auditTypeStackedChartRoot;
        break;
      case 'qualityPassFail':
        root = this.qualityPassFailRoot;
        break;
      case 'qualityFirstTimePass':
        root = this.qualityFirstTimePassRoot;
        break;
      case 'poOfferedQty':
        root = this.poOfferedQtyRoot;
        break;
      case 'defectsStackedChart':
        root = this.defectGraphRoot;
        break;
      case 'defectPassFail':
        root = this.defectsPassFailRoot;
        break;
      default:
        break;
    }

    let exportingData = am5plugins_exporting.Exporting.new(root, {
      title: `${fileName}`,
      filePrefix: `${fileName}`,
      pdfOptions: {
        addURL: false
      }
    })
    let downloadType = type;
    exportingData.download(downloadType);
  }

  public onDateRangeSelect(): void {
    this.timeDurationSelectedItem = '';
    let startDate = this.dateRange?.[0];
    let endDate = this.dateRange?.[1];
    if (!startDate || !endDate) {
      return;
    }

    this.fromDate = new Date(startDate);
    this.toDate = moment(endDate).endOf("day").toDate();
    this.dateRangeCalendar.hideOverlay();
    this.onFilterOrDateChanges();

  }

  public filterValueChange(): void {
    this.onFilterOrDateChanges();
  }

  public clearFilter(): void {
    this.selectedFilters = {};
    this.fromDate = undefined;
    this.toDate = undefined;
    this.dateRange = undefined;
    this.timeDurationSelectedItem = { label: 'Last 3 Months', value: 'quarterly' };
    this.filtersListData.forEach(item => item.value = '');
    this.onFilterOrDateChanges();
  }

  private getFilterCount(): void {
    this.noOfFilterApplied = 0;
    this.filtersListData.forEach(item => {
      if ((item.value?.length > 0) || (item.value && (item.type != 'dropdown'))) {
        this.noOfFilterApplied++;
      }
    })
  }


  private onFilterOrDateChanges(): void {
    let filterObj: any = {}
    if (this.fromDate && this.toDate) {
      filterObj['from'] = moment(this.fromDate).valueOf();
      filterObj['to'] = moment(this.toDate).valueOf();
    }
    this.filtersListData.forEach(item => {
      if (item.value?.length > 0 || item.value) {
        if (item.type == 'dropdown')
          filterObj[item.code] = item.value.map(obj => obj.value).join(',').split(',') || '';
        else if (item.type == 'dropdownSingle')
          filterObj[item.code] = item.value.value;
        else if (item.type == 'input')
          filterObj[item.code] = item.value;
      }
      if (item.code == 'auditTypeUid')
        this.selectAuditTypeName = item.value?.name;
    });
    if (Object.keys(filterObj).length !== 0) {
      this.selectedFilters = this.filterObjectEmptyProperties(filterObj);
      this.selectedFilters['tenantUid'] = this._commonService.getTenantUid();
      this.callDashBoardApi();
      if (!this.toggleGraphAndTable)
        this.getDesignCodeWiseDataForTable();
    }
    this.getFilterCount();
  }

  private filterObjectEmptyProperties(obj: Record<string, any>): Record<string, any> {
    return Object.keys(obj).reduce((acc, key) => {
      if ((obj[key] !== undefined && obj[key] !== null && obj[key] !== '')) {
        if ((obj[key]?.length > 0 && obj[key][0] == '')) {

        } else
          acc[key] = obj[key];
      }
      return acc;
    }, {} as Record<string, any>);
  }

  public onTimeDurationSelectedItem(callDashboardApis: boolean): void {
    this.dateRange = undefined;
    const today = new Date();
    this.toDate = new Date(today.setHours(23, 59, 59, 999));
    const startDay = new Date(today);
    switch (this.timeDurationSelectedItem.value) {
      case 'month':
        startDay.setDate(today.getDate() - 30);
        break;
      case 'week':
        startDay.setDate(today.getDate() - 7);
        break;
      case 'quarterly':
        startDay.setMonth(today.getMonth() - 3);
        break;
      case 'yearly':
        startDay.setFullYear(today.getFullYear() - 1);
        break;
      default:
        break;
    }
    this.fromDate = new Date(startDay.setHours(0, 0, 0, 0));
    this.selectedFilters['from'] = moment(this.fromDate).valueOf();
    this.selectedFilters['to'] = moment(this.toDate).valueOf();
    this.selectedFilters['tenantUid'] = this._commonService.getTenantUid();
    if (callDashboardApis)
      this.callDashBoardApi();
    if (!this.toggleGraphAndTable)
      this.getDesignCodeWiseDataForTable();
  }

  public downloadContent(id: string, fileName: string, type: string) {
    const element = document.getElementById(id);

    if (element) {
      // Capture the div as a PNG
      html2canvas(element).then((canvas) => {
        // Create PNG from canvas
        const pngUrl = canvas.toDataURL('image/png');

        // Create a link to download PNG
        if (type == 'png') {
          const link = document.createElement('a');
          link.href = pngUrl;
          link.download = fileName + '.png';
          link.click();
        } else {

          // Create a PDF using jsPDF
          const pdf = new jsPDF();
          const imgData = pngUrl;
          const imgWidth = 190; // Adjust width
          const pageHeight = pdf.internal.pageSize.height;
          const imgHeight = (canvas.height * imgWidth) / canvas.width;
          let heightLeft = imgHeight;

          let position = 0;

          // Add image to PDF
          pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
          heightLeft -= pageHeight;

          // If the image is larger than a page, add new pages
          while (heightLeft >= 0) {
            position = heightLeft;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
          }

          // Save the PDF
          pdf.save(fileName + '.pdf');
        }
      });
    }
  }

  public capture(id, name): void {
    this.captureService
      .getImage(document.getElementById(id), true)
      .pipe(
        tap(img => {
          this.imgBase64 = img;
          this.downloadJson(name);
        })
      )
      .subscribe();
  }

  private downloadJson(filename): void {
    var element = document.createElement('a');
    element.setAttribute('href', this.imgBase64);
    element.setAttribute('download', filename + '.png');
    element.style.display = 'none';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  }

  public onClickNotification(item: notificationLIstConf): void {
    if (item?.requestUrl) {
      this._commonService.navigate(item.requestUrl);
    }
  }


  //on Destroy
  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }

}
