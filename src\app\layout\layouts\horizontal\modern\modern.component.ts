import { Component, OnD<PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseNavigationService } from '@fuse/components/navigation';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { InitialData } from 'app/app.types';
import { Tenant } from 'app/core/qinspect/_model/Tenant.model';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'modern-layout',
    templateUrl: './modern.component.html',
    encapsulation: ViewEncapsulation.None
})
export class ModernLayoutComponent implements OnInit, OnDestroy {
    data: InitialData;
    isScreenSmall: boolean;
    tenant: Tenant;
    tenantUid: string;
    headerLogo: any;
    headerStickyLogo: any;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    /**
     * Constructor
     */
    constructor(
        private _activatedRoute: ActivatedRoute,
        private _router: Router,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _fuseNavigationService: FuseNavigationService
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Getter for current year
     */
    get currentYear(): number {
        return new Date().getFullYear();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Subscribe to the resolved route mock-api
        this.data = this._fuseNavigationService.menuConfigationService();
        sessionStorage.setItem('navigation', JSON.stringify(this.data.navigation.navigation))
        // Subscribe to media changes
        this._fuseMediaWatcherService.onMediaChange$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(({ matchingAliases }) => {
                // Check if the screen is small
                this.isScreenSmall = !matchingAliases.includes('md');
            });

        this.tenant = JSON.parse(localStorage.getItem("tenant")) as Tenant;
        if (!this.tenant) {
            this.tenant = { tenantUid: JSON.parse(localStorage.getItem("user"))?.tenantUid } as Tenant;
        }
        
        const defaultLogo = './assets/logos/3frames.png';
        this.headerLogo = defaultLogo;
        this.headerStickyLogo = defaultLogo;
        if (this.tenant?.tenantUid) {
            this.tenantUid = this.tenant.tenantUid;
            const setLogos = (logoPath) => {
                this.headerLogo = logoPath;
                this.headerStickyLogo = logoPath;
            };
            const tenantLogoPath = `./assets/logos/${this.tenant.tenantUid}.png`;
            const img = new Image();
            img.onload = () => {
                setLogos(tenantLogoPath);
            };
            
            img.onerror = () => {
                setLogos(defaultLogo); 
            };
            img.src = tenantLogoPath;
        }
    }
    private checkImage(imageSrc, good, bad) {
        var img = new Image();
        img.onload = good;
        img.onerror = bad;
        img.src = imageSrc;
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Toggle navigation
     *
     * @param name
     */
    toggleNavigation(name: string): void {
        // Get the navigation
        const navigation = this._fuseNavigationService.getComponent(name);

        if (navigation) {
            // Toggle the opened status
            navigation.toggle();
        }
    }
}
