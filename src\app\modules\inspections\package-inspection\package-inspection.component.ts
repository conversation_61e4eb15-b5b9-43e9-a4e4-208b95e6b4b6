import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ChangeDetectorRef, Component, On<PERSON>estroy, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { CadAppBundle } from 'app/core/qinspect/_model/CadAppBundle.model';
import { CadDimension } from 'app/core/qinspect/_model/CadDimension.model';
import { CadDocument } from 'app/core/qinspect/_model/CadDocument.model';
import { CadDocumentContent } from 'app/core/qinspect/_model/CadDocumentContent.model';
import { headersConfigurations } from 'app/core/qinspect/_model/headersConfigurations.model';
import { MtcReferenceData } from 'app/core/qinspect/_model/MtcReferenceData.model';
import { Status } from 'app/core/qinspect/_model/status.model';
import { InvokeService } from 'app/services/invoke.service';
import { MTCHeadersConfigurationsService } from 'app/services/mtcHeadersConfig.service';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Subscription } from 'rxjs';

import { CommonService } from '../../../services/common.service';


// import { Message } from 'primeng/primeng';
// import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-package-inspection',
  templateUrl: './package-inspection.component.html',
  styleUrls: ['./package-inspection.component.scss']
})
export class PackageInspectionComponent implements OnInit, OnDestroy {
  refDataList: MtcReferenceData[] = [];
  selectedRefData: MtcReferenceData;
  stepIndex: any = 0;
  showFileUploadErrorMessage: boolean = false;
  public files: NgxFileDropEntry[] = [];
  status: Status = new Status();
  subscription: Subscription = new Subscription();
  supplierOrigin: string = ''
  supplierOrigins: any[] = [];
  mtcImageUid: string

  imagePath: any

  showUploadCad: boolean = false;

  cadDocumentUid: string
  imgPreview: false
  cadImage: any
  showSpinner: boolean = false;
  displayImageinView: boolean = false;
  sendCurrentDataToImageCrop: BehaviorSubject<any> = new BehaviorSubject(null);
  isImageDataSaved: BehaviorSubject<any> = new BehaviorSubject(null);

  rec: CadDocument;
  dimensions: CadDimension[] = [];
  selectedBundle: CadAppBundle
  bundles: CadAppBundle[] = [];
  images: CadDocument[] = [];
  bacRows: CadDocument[] = [];
  content: CadDocumentContent[] = [];

  subscriptions: Subscription[] = [];
  buttonsConfigArray: headersConfigurations[] = [];
  miscButtonsConfigArray: headersConfigurations[] = [];


  constructor(private _router: Router,
    private _invokeService: InvokeService,
    private http: HttpClient,
    private _sanitizer: DomSanitizer,
    private toaster: ToastrService,
    private _MTCHeadersConfigurationsService: MTCHeadersConfigurationsService,
    private cdRef: ChangeDetectorRef,
    private _commonService: CommonService) {
    this.supplierOrigins = [
      { name: 'World except Europe', code: '' },
      { name: 'Europe', code: '.' },
    ]
  }

  ngOnInit(): void {

  }

  public goto(): void {

  }

  onRefDataChange(event) {

  }

  public refresh() {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.inspection.getPkgInspection).subscribe(data => {
      this.imagePath = this._sanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'
        + data.entityUid);
    }, error => {
      // this.toaster.error(error.message);
    });
  }

  public onFileChanged(event: any): void {
    this.files = event;
  }

  public uploadCadFile(): void {
    for (const droppedFile of this.files) {
      this.showSpinner = true;
      if (droppedFile.fileEntry.isFile) {

        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          console.log(droppedFile.relativePath, file);
          let fileType = file.type;
          console.log("fileType:", fileType);
          const formData = new FormData()
          formData.append('file', file, droppedFile.relativePath)
          if (this.selectedBundle != null) {
            this.rec.code = this.selectedBundle.code
            this.rec.name = this.selectedBundle.name
            this.rec.appBundleUid = this.selectedBundle.uuid
          } else {
            this.rec = new CadDocument()
          }
          const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
          formData.append('img', JSON.stringify(this.rec))
          let headers = new HttpHeaders()
            .set("Authorization", this._commonService.getBasicAuth())
            .set("Client", tenantUid);

          this.http.post(environment.apiUrl + 'pkgInspection/img', formData,
            { headers: headers, responseType: 'json' })
            .subscribe(data => {
              this.showSpinner = false;
              this.status = data as Status
              this.toaster.success('Uploaded Image for processing');
              this.cadDocumentUid = this.status.entityUid
              if (this.status.code != '500') {
                this.showUploadCad = false;
                this.files = []
              } else {
                this.status.message = "";
              }
            })
        });
      } else {
        // It was a directory (empty directories are added, otherwise only files)
        this.showSpinner = false;
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
        console.log(droppedFile.relativePath, fileEntry);
      }
    }
  }


  onBundleChange(event) {
    //this.selectedBundle = event
  }

  public ngOnDestroy(): void {
    this.subscription.unsubscribe();
    this.subscriptions.forEach((subscription) => subscription.unsubscribe())
  }

}
