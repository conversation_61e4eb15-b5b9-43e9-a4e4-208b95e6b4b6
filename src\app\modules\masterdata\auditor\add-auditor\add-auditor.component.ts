import { ChangeDetectorRef, Component, EventEmitter, Inject, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CountryList } from 'app/core/qinspect/_common/country';
import { GenericList } from 'app/core/qinspect/_common/list';
import { Util } from 'app/core/qinspect/_common/util';
import { QiAuditor } from 'app/core/qinspect/_model/auditor.model';
import { QiAuditorIncharge } from 'app/core/qinspect/_model/auditorincharge.model';
import { countryList, FORM_CONTROL_TYPES, HTTP_STATUS, ROLE, TABLE_ACTION_TYPES } from 'app/enum-constants';
import { BuildFormUtilityService } from 'app/services/build-form-utility.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';
import * as sha512 from 'js-sha512';
import { forkJoin, ReplaySubject } from 'rxjs';
import { distinctUntilChanged, takeUntil } from 'rxjs/operators';

import { APP_UI_CONFIG } from '../../../../app-config.constants';
import { AUDITOR_FORM_MODEL } from './auditor-form-model';

@Component({
  selector: 'add-auditor',
  templateUrl: './add-auditor.component.html',
  styleUrls: ['./add-auditor.component.scss']
})
export class AddAuditorComponent implements OnInit, OnDestroy {
  rec: QiAuditor = new QiAuditor();
  status: any;
  hdr: string = 'Add Auditor';
  aicList: GenericList<QiAuditorIncharge>;
  allAuditors: QiAuditor[] = [];
  auditorList: QiAuditor[] = [];
  allAuditorInchageList: QiAuditorIncharge[] = [];
  auditorInchargeList: any[] = [];
  resetPwdValue: any = "random";
  resetPasswordList: any[] = [{ label: "Set password same as username", value: "same" },
    { label: "Create new password", value: "new" }
  ];
  filteredresetPasswordList: any[];
  tenantUid = this._commonService.getTenantUid();
  roleList: any[] = [{ label: "AUDITOR", value: "AUDITOR" },{ label: "DQA", value: "DQA" }];
  filteredRoleList: any[];
  selectedAuditorForDQA: any;
  isMultiRole: boolean = true;

  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  auditorForm: FormGroup;
  public fields: any[] = [];
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  countryList: any[] = countryList;
  buttonLabel: string;
  auditorsList: any[] = [];
  inspectionInchargeList: any[] = [];
  timeoutIdForScroll:any;

  constructor(private invokeService: InvokeService,
    private cdRef: ChangeDetectorRef,
    private _commonService: CommonService,
    private _buildFormUtilityService: BuildFormUtilityService,
    public dialogRef: MatDialogRef<AddAuditorComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any) { }

  public ngOnInit(): void {
    this.status = this.data.status;
    this.aicList = new GenericList<QiAuditorIncharge>();
    this.buildForm();
    this.getRequiredData();
  }
  private buildForm(): void {
    if (this.tenantUid != 'norlanka') {
      this.filteredRoleList = this.roleList.filter(item => item.value !== "DQA");
    } else {
      this.filteredRoleList = this.roleList;
    }
   
    this.filteredresetPasswordList = this.resetPasswordList;
    let formFields = this._buildFormUtilityService.getFormControlsFields(AUDITOR_FORM_MODEL);
    this.fields = this._buildFormUtilityService.getFieldsList();
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'country', this.countryList);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'role', this.filteredRoleList);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'password', this.filteredresetPasswordList);
    this.dropDownConfiguration();
    this.auditorForm = new FormGroup(formFields);
  }

  private dropDownConfiguration(): void {
    if (this._commonService.getTenantConfig().tenantUid == 'titan') {
      this.fields = this._buildFormUtilityService.setFieldStatus(this.fields, 'role', FORM_CONTROL_TYPES.SINGLE_SELECT);
      this.fields = this._buildFormUtilityService.setTypeStatus(this.fields, 'role', FORM_CONTROL_TYPES.SINGLE_SELECT);
    }
  }
  private getRequiredData(): void {
    let auditorDetails$ = this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.auditor.getallauditors);
    let auditorIncharge$ = this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.auditorincharge.get)
    forkJoin([auditorDetails$, auditorIncharge$])
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.allAuditors = response[0] as QiAuditor[];
          this.allAuditorInchageList = response[1] as QiAuditorIncharge[];
          this.parepareAuditorList();
          this.parepareAuditorIncharge();
          this.configureForm();
        },
        error: error => {
          this._commonService.handleError(error);
        }
      })
  }
  private parepareAuditorList(): void {
    this.allAuditors.forEach(a => {
      if (a.inchargeUid) {
        a.inChargeName = this.auditorList.find(ai => ai.uuid == a.inchargeUid)?.fullName;
      }
    });
    let auditors = this.allAuditors.filter(item => item.role == 'AUDITOR');
    this.auditorsList = auditors.map(item => ({ label: item.firstName, value: item.uuid, raw: item }));
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'inchargeUid', this.auditorsList);
    this.cdRef.detectChanges();
  }

  private parepareAuditorIncharge(): void {
    this.aicList.setList2(this.allAuditorInchageList, 'firstName', 'lastName', 'userName', 'uuid');
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'inchargeUid', this.aicList.list);
    this.cdRef.detectChanges();
  }

  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        this.hdr = "Add Auditor";
        if(this.data?.data?.inspectionAgencyUid){
          this.hdr = "Add Auditor (Inspection Agency : "+this.data?.data?.inspectionAgencyName+')';
        }
        this.auditorForm.enable();
        if(this.tenantUid == 'norlanka'){
          this.auditorForm.get('role').enable();
        }else{
          this.auditorForm.get('role').setValue(ROLE.AUDITOR);
          this.auditorForm.get('role').disable();
        }
        this.auditorForm.get('setPasswordAsUserName').setValue(true);
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Auditor";
        this.auditorForm.get('role').disable();
        this.auditorForm.get('code').disable();
        this.setDataForm();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = 'View Auditor';
        this.setDataForm();
        this.auditorForm.disable();
        break;
    }
    this.onFormChanges();
  }

  private setDataForm(): void {
    this.rec = this.data?.data;
    this.rec.resetPassword = this.auditorForm.get('resetPassword').setValue(false);
    this.auditorForm = this._buildFormUtilityService.setFormControlsValues(this.auditorForm, this.rec, this.fields);
    if (this.rec.role.includes(ROLE.DQA)) {
      this.auditorForm.get('role').setValue(ROLE.DQA);
    } else {
      this.auditorForm.get('role').setValue(ROLE.AUDITOR);
    }

    if (this.hasDQARole()) {
      this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'inchargeUid', this.auditorsList);
      this.auditorForm.get("inchargeUid").setValue(this.rec.inchargeUid);
    } else {
      this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'inchargeUid', this.aicList.list);
      this.selectedAuditorForDQA = this.auditorsList.find(item => item.value == this.rec.inchargeUid);
      this.auditorForm.get("inchargeUid").setValue(this.rec.inchargeUid);
    }

    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'setPasswordAsUserName', false, this.auditorForm);
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'resetPassword', true, this.auditorForm);
  }

  private onFormChanges(): void {
    this.auditorForm.get('role').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          if (response) {
            if (response?.includes("DQA")) {
              this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'inchargeUid', this.auditorsList);
            } else {
              this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'inchargeUid', this.aicList.list);
            }
          }
        }
      })

    if (this.tenantUid == 'godrej') {
      this.auditorForm.get('setPasswordAsUserName').setValue(true);
      this.auditorForm.get('setPasswordAsUserName').disable();
    } else {
      //this.auditorForm.get('setPasswordAsUserName').setValue(false);
    }

    this.auditorForm.get('resetPassword').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          if (response) {
            this.fields = this._buildFormUtilityService.showHideField(this.fields, 'password', true, this.auditorForm);
          } else {
            this.fields = this._buildFormUtilityService.showHideField(this.fields, 'password', false, this.auditorForm);
            this.hideNewPasswordField();
          }
        }
      });

    this.auditorForm.get('password').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          if (response) {
            this.rec.resetPassword = true;
              if (response === 'Create new password') {
                this.rec.setPasswordAsUserName = this.auditorForm.get('setPasswordAsUserName').setValue(false);
                this.showNewPasswordField();
              }
              else {
                this.rec.setPasswordAsUserName = this.auditorForm.get('setPasswordAsUserName').setValue(true);
                this.hideNewPasswordField();
              }
          }
        }
      });
  }

  private hideNewPasswordField():void{
    this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "newPassword", { required: false });
    this.auditorForm.get("newPassword").clearValidators();
    this.auditorForm.get("newPassword").setValue('');
    this.auditorForm.updateValueAndValidity();
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'newPassword', false, this.auditorForm);
    this.cdRef.detectChanges();
  }

  private showNewPasswordField():void{
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'newPassword', true, this.auditorForm);
    this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "newPassword", { required: true });
    this.auditorForm.get("newPassword").setValidators(Validators.required);
    this.auditorForm.updateValueAndValidity(); 
    let el = document.getElementById('lastEle');
    this.timeoutIdForScroll = setTimeout(() => {
      el.scrollIntoView();
    }, 10)
    this.cdRef.detectChanges();
  }

  findInvalidControls(f: FormGroup) {
    const invalid = [];
    const controls = f.controls;
    for (const name in controls) {
      if (controls[name].invalid) {
        invalid.push(name);
      }
    }
    return invalid;
  }

  public onSave(): void {
    this.rec.code = this.auditorForm.get('code').value;
    this.rec.firstName = this.auditorForm.get('firstName').value;
    this.rec.lastName = this.auditorForm.get('lastName').value;
    if (this.rec.role = this.auditorForm.get('role').value.includes(ROLE.DQA)) {
      this.rec.role = `${ROLE.AUDITOR},${ROLE.DQA}`;
    } else {
      this.rec.role = this.auditorForm.get('role').value
    }

    this.rec.inchargeUid = this.auditorForm.get('inchargeUid').value;
    this.rec.designation = this.auditorForm.get('designation').value;
    this.rec.contactNo = this.auditorForm.get('contactNo').value;
    this.rec.email = this.auditorForm.get('email').value;
    this.rec.addressLine1 = this.auditorForm.get('addressLine1').value;
    this.rec.altemail = this.auditorForm.get('altemail').value;
    this.rec.city = this._commonService.trimEdgeSpaces(this.auditorForm.get('city').value);
    this.rec.state = this._commonService.trimEdgeSpaces(this.auditorForm.get('state').value);
    this.rec.pincode = this.auditorForm.get('pincode').value;
    this.rec.country = this.auditorForm.get('country').value;
    this.rec.setPasswordAsUserName = this.auditorForm.get('setPasswordAsUserName').value;
    if (this.data.data && this.data.data.uuid) {
      let password:string = this.auditorForm.get('newPassword').value;
      if(password){
        this.rec.password = this.tenantUid == 'godrej'? sha512.sha512(password) : password;
      }else
      this.rec.password = sha512.sha512(this.rec.userName);
    } else {
      this.rec.password = sha512.sha512(this.rec.code);
    }
    if (this.rec.uuid) {
      this._commonService.openDialogForConfirmation(`Are you sure you want to update auditor : ${this.rec.userName} ?`).subscribe({
        next: response => {
          if (response) {
            APP_UI_CONFIG.masterdata.auditor.update.paramList['id'] = this.rec.uuid;
            this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.auditor.update, null, this.rec.uuid, Util.clone(this.rec)).subscribe({
              next: response => {
                if (response && response.code == HTTP_STATUS.SUCCESS) {
                  this._commonService.success(`${this.rec.userName} updated successfully.`);
                  this.dialogRef.close(true);
                } else {
                  this._commonService.error(`Failed to Update ${this.rec.userName}.`);
                }
              }, error: error => {
                this._commonService.handleError(error);
              }
            })
          }
        }
      })
    } else {
      this._commonService.openDialogForConfirmation(`Are you sure you want to create Auditor : ${this.rec.firstName} ?`).subscribe({
        next: response => {
          this.data?.data?.inspectionAgencyUid ? this.rec.inspectionAgencyUid = this.data.data.inspectionAgencyUid : '';
          if (response) {
            this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.auditor.add, '', '', Util.clone(this.rec)).subscribe({
              next: response => {
                if (response && response.code == HTTP_STATUS.SUCCESS) {
                  this._commonService.success(`${this.rec.firstName} created successfully.`);
                  this.dialogRef.close(true);
                } else {
                  this._commonService.handleError(response);
                }
              }, error: error => {
                this._commonService.handleError(error);
              }
            })
          }
        }
      })
    }

  }

  public close(): void {
    this.dialogRef.close(true);
  }

  processResponse(data) {
    if (data.code == "200") {
      this.dialogRef.close(true);
      // this.getAuditors();
      // this.closeAddAuditor();
    }
    else {
      this._commonService.toaster.error(data.message);
    }
  }

  setToggleValue(e) {
    let val = 0
    if (e.checked) {
      val = 1
    }
    this.rec.setPasswordAsUserName = val;
  }

  setResetPassword(e) {
    let val = 0
    if (e.target.checked) {
      val = 1
    }
    this.rec.resetPassword = val;
  }
  changeResetPwdValue(e) {
    if (e.value == "same") {
      this.rec.setPasswordAsUserName = true;
    } else {
      this.rec.setPasswordAsUserName = false;
    }
  }

  public hasDQARole(): boolean {
    if (this.rec.role?.includes("DQA")) {
      return true;
    } else {
      return false;
    }
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(true);
    this.$destroyed.complete();
    clearTimeout(this.timeoutIdForScroll);
  }
}
