// ::ng-deep .mat-dialog-content {
//     display: block;
//     margin: 0 -24px;
//     padding: 0 24px;
//     max-height: 100% !important;
//     -webkit-overflow-scrolling: touch;
// }
mat-hint {
    font-size: 12px;
}

.file-link {
    color: #3366FF;
}

.mbb-10 {
    margin-bottom: -10px;
}

.item-basline {
    align-items: baseline;
}

.bshadow {
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
}

.questionBackgrd {
    width: 100%;
    background-color: aliceblue;
    padding: 10px;
    border-radius: 7px;
    letter-spacing: 0.5px;
    border-left: 4px solid #bddffd;

    background-color: aliceblue;
    padding: 10px;
    border-radius: 7px;
    letter-spacing: 0.5px;
    border-left: 7px solid #bddffd;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 16px;
}

.marginTopminus {
    margin-top: -20px;
}

.filesnameAsLink {
    text-decoration: underline;
    color: var(--bs-blue);
    margin-left: 0.3rem;
}

.width90 {
    width: 90% !important;
}

.colorAqs {
    color: #6c757d;
}

.capaAttachmentWrapper {
    padding: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border-radius: 4px;
    background-color: #f0f8ff;
    background: conic-gradient(from 22.75deg at 66.04% 84.5%, #d9eaf9 0deg, #f0f8ff 360deg);
    box-shadow: 0px 13px 20px 0px #E7EAF080;
    width: auto;
}

.attachmentTextWrapper {
    position: relative;
    top: 11px;
    left: 8px;
    letter-spacing: 1px;
    background-color: transparent;
    width: fit-content;
}

.te {
    margin-top: -5px;
    margin-left: -7px;
    font-weight: bold;
    text-decoration: underline;
    letter-spacing: 1px;
    color: #517ebc;
}

.fileName {
    color: #6c757d;
    font-weight: bold;
    letter-spacing: 0.5px;
    overflow-wrap: anywhere;

}