import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { ROLE } from 'app/enum-constants';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import { ImgIconComponent } from 'app/shared/tfl-table-search-grid/grid-supporting-components/img-icon/img-icon.component';
import { environment } from 'environments/environment';
import JSZip from 'jszip';
import moment from 'moment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';

class CAPADetails {
  severity?: String
  correctiveAction?: String
  preventiveAction?: String
  plannedCompletionTime?: any
  cause?: any
  remarksL1?: any
  followupRemarks?: String
  capClosureComments?: any
  capClosureFeedback?: String
  status?: any
}
@Component({
  selector: 'tps-edit-capa-details',
  templateUrl: './edit-capa-details.component.html',
  styleUrls: ['./edit-capa-details.component.scss']
})
export class EditCapaDetailsComponent implements OnInit {
  hdr: String = 'Update CAPA Details';
  @Input() formData: any;
  @Output() emitSaveOrSubmitEvent: EventEmitter<any> = new EventEmitter();
  @Output() onCopyDetails: EventEmitter<any> = new EventEmitter();
  @Output() refreshPage: EventEmitter<any> = new EventEmitter();
  capTitle: String = "";
  @ViewChild('f') public userFrm: NgForm;
  today: Date = new Date();
  newDate: Date = new Date()
  maxOneMonth: Date = new Date(this.newDate.setDate(this.newDate.getDate() + 30));
  severityDropdown = ['MINOR', 'MAJOR', 'CRITICAL'];
  user: any;
  l1l2LabelText = "L1"
  rec: CAPADetails = new CAPADetails();
  isAuditor: boolean = false;
  isAdmin: boolean = false;
  isVendor: boolean = false;
  isFactory: boolean = false;
  isSeller: boolean = false;
  isLab: boolean = false;
  isAgency: boolean = false;
  isOps: boolean = false;
  editableFields = {
    isSeverityDisabled: true,
    isSeverityShow: false,
    isCorrectiveActionDisabled: true,
    isCorrectiveActionShow: false,
    isPreventiveActionDisabled: true,
    isPreventiveActionShow: false,
    isPlannedCompletionTimeDisabled: true,
    isPlannedCompletionTimeShow: false,
    isCauseDisabled: true,
    isCauseShow: false,
    isRemarksL1Disabled: true,
    isRemarksL1Show: false,
    isFollowup_remarksDisabled: true,
    isFollowup_remarksShow: false,
    isCapClosureCommentsDisabled: true,
    isCapClosureCommentsShow: false,
    isCapClosureFeedbackDisabled: true,
    isCapClosureFeedbackShow: false,
    isCapAttachment: false,
  };
  data: any;
  showAttachmentUrl: any;
  isFileUploaded: Boolean = false;
  plannedCompletionDate: any;
  agencyCloserComments: any[] = [
    { uuid: 'CAPA Satisfactory', name: 'CAPA Satisfactory' },
    { uuid: 'Action Not effective', name: 'Action Not effective' },
    { uuid: 'Evidence Not effective', name: 'Evidence Not effective' },
    { uuid: 'Need More Information', name: 'Need More Information' }
  ]

  agencyCloserRequired: any[] = [
    { uuid: 'Physical Re-Audit Required', name: 'Physical Re-Audit Required' },
    { uuid: 'Virtual Re-Audit Required', name: 'Virtual Re-Audit Required' },
    { uuid: 'Desktop CAPA Closure', name: 'Desktop CAPA Closure' },

  ]

  columnDefs: any[] = [
    {
      headerName: "Type", field: "fileEntry.name", width: 70, maxWidth: 70,
      cellRendererFramework: ImgIconComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data
    },
    { headerName: "Name", field: "fileEntry.name", sortable: true, unSortIcon: true, minWidth: 100 },
    {
      headerName: "Attachment Type", field: "docType", sortable: true, unSortIcon: true, minWidth: 100,
      cellRenderer: params => {
        return params.data.docType ? params.data.docType : 'N/A'
      }
    },
    {
      headerName: "Created On/Modified On", field: "modified", sortable: true, unSortIcon: true, minWidth: 100,
      cellRendererFramework: CustomDateTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data
    },
    {
      headerName: "File Size", field: "size", sortable: true, unSortIcon: true,
    },
  ]

  code: any;
  isTestUser: boolean = false;
  public files: NgxFileDropEntry[] = [];

  showCopyTo: boolean = false;
  copyToDetails = {
    from: '',
    toList: []
  };
  toCapaItemList: any[] = [];
  @Input() capaItemList: any[] = [];
  constructor(
    public _authService: AuthService,
    public _invokeService: InvokeService,
    public _commonService: CommonService,) { }

  ngOnInit(): void {
    if (this._authService.hasLoggedUserPermitted(ROLE.AUDITOR))
      this.isAuditor = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.VENDOR))
      this.isVendor = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.FACTORY))
      this.isFactory = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.AGENCY))
      this.isAgency = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.LAB))
      this.isLab = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.ADMIN))
      this.isAdmin = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.SELLER))
      this.isSeller = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.OPERATIONS))
      this.isOps = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.TEST_USER)) {
      this.isTestUser = true;
    }
    this.data = this.formData.data;
    this.rec = this.formData.data;
    this.onCapClosureCommentsChange();
    if (this.data.plannedCompletionTime && this.data.plannedCompletionTime > 0) {
      this.plannedCompletionDate = new Date(this.data.plannedCompletionTime);
    }
    this.editableFieldsConfiguration();
    if (this.formData.status == 'view') {
      this.hdr = 'View CAPA Details';
    }
    if (this.data.comments != undefined) {
      this.capTitle =this.data.comments;
    }
    
    if (!this.data.comments && this.data.description != undefined) {
      this.capTitle = this.data.description
    }
  }

  public onCapClosureCommentsChange() {
    if (this.rec.capClosureComments == "CAPA Satisfactory") {
      this.editableFields.isCapClosureFeedbackShow = true;
      this.editableFields.isCapClosureFeedbackDisabled = false;
    }
    else {
      this.editableFields.isCapClosureFeedbackShow = false;
      this.editableFields.isCapClosureFeedbackDisabled = true;
      this.rec.capClosureFeedback = '';
    }
  }

  private editableFieldsConfiguration(): void {
    if (this.isAdmin || this.isSeller || this.isOps || this.isTestUser) {
      this.editableFields.isSeverityShow = true;
      this.editableFields.isCauseShow = true;
      this.editableFields.isCorrectiveActionShow = true;
      this.editableFields.isPreventiveActionShow = true;
      this.editableFields.isPlannedCompletionTimeShow = true;
      this.editableFields.isRemarksL1Show = true;
      if (this.formData.auditType == "INSPECTION") {
        this.editableFields.isRemarksL1Disabled = false;
        this.l1l2LabelText = "L2"
      }
      this.editableFields.isFollowup_remarksShow = true;
      this.editableFields.isCapClosureCommentsShow = true;
      this.editableFields.isCapClosureFeedbackShow = true;
      this.editableFields.isCapClosureFeedbackDisabled = true;
      this.editableFields.isCapClosureCommentsDisabled = false;
    }
    else if (this.isVendor || this.isFactory) {
      this.editableFields.isSeverityShow = true;
      this.editableFields.isCauseShow = true;
      this.editableFields.isCorrectiveActionShow = true;
      this.editableFields.isPreventiveActionShow = true;
      this.editableFields.isPlannedCompletionTimeShow = true;
      if (this.rec.status == 2) {
        this.editableFields.isSeverityDisabled = true;
        this.editableFields.isCauseDisabled = false;
        this.editableFields.isCorrectiveActionDisabled = false;
        this.editableFields.isPreventiveActionDisabled = false;
        this.editableFields.isPlannedCompletionTimeDisabled = false;
        this.editableFields.isCapAttachment = true;
        this.code = 3;
      }

      if (this.rec.status == 4 && !this.isFactory) {
        this.editableFields.isFollowup_remarksShow = true;
        this.editableFields.isRemarksL1Show = true;
        this.editableFields.isRemarksL1Disabled = true;
        this.editableFields.isCauseDisabled = true;
        this.editableFields.isCapAttachment = false;
        this.editableFields.isFollowup_remarksDisabled = false;
        this.editableFields.isPlannedCompletionTimeDisabled = true;
        this.editableFields.isPreventiveActionDisabled = true;
        this.editableFields.isCorrectiveActionDisabled = true;
        this.code = 5;
      }
      if (this.rec.status == 5 && !this.isFactory) {
        this.editableFields.isFollowup_remarksShow = true;
        this.editableFields.isRemarksL1Show = true;
        this.editableFields.isCapAttachment = false;
        this.code = 5;
      }
      if (this.rec.status == 6 && !this.isFactory) {
        this.editableFields.isRemarksL1Show = true;
        this.editableFields.isFollowup_remarksShow = true;
        this.editableFields.isFollowup_remarksDisabled = false;
        this.editableFields.isCapClosureCommentsShow = true;
        this.editableFields.isCapClosureFeedbackShow = true;
        this.editableFields.isCapAttachment = true;
        this.code = 5;
      }
      if (this.formData.auditType == "INSPECTION") {
        this.l1l2LabelText = "L2"
      }
    }
    else if (this.isAgency || this.isLab || this.isAuditor) {
      if (this.data.auditType == "INSPECTION") return;
      this.editableFields.isCauseShow = true;
      this.editableFields.isSeverityShow = true;
      this.editableFields.isCorrectiveActionShow = true;
      this.editableFields.isPreventiveActionShow = true;
      this.editableFields.isPlannedCompletionTimeShow = true;
      this.editableFields.isRemarksL1Show = true;
      if (this.rec.status == 3) {
        this.editableFields.isRemarksL1Disabled = false;
        this.editableFields.isCapClosureCommentsShow = true;
        this.editableFields.isCapClosureCommentsDisabled = false;
        this.code = 6; //Revised CAPA flow doesn't not have Follow ups
      }
      if (this.rec.status == 5) {
        this.editableFields.isFollowup_remarksShow = true;
        this.editableFields.isCapClosureCommentsShow = true;
        this.editableFields.isCapClosureFeedbackShow = true;
        this.editableFields.isCapClosureCommentsDisabled = false;
        this.editableFields.isCapClosureFeedbackDisabled = false;
        this.code = 6;
      }
      if (this.rec.status == 6) {//view only
        this.editableFields.isFollowup_remarksShow = true;
        this.editableFields.isCapClosureCommentsShow = true;
        this.editableFields.isCapClosureFeedbackShow = true;
        this.code = 6;
      }
    }
    else if (this.isAuditor) {
      // this.editableFields.isSeverityShow = true;
      // if (!this.rec.status || this.rec.status == 1)
      //   this.editableFields.isSeverityDisabled = false;
      // this.code = 2;
    }
  }

  public onSaveOrSubmit(isFromSave): void {
    if (this.isVendor) {
      if (this.files.length > 0) {
        if (isFromSave) {
          this.onSave()
        } else {
          this.onSubmit()
        }
      } else {
        this._commonService.openDialogForConfirmation('No document evidence is added for this CAPA, are you sure want to proceed ?').subscribe(response => {
          if (response) {
            if (isFromSave) {
              this.onSave()
            } else {
              this.onSubmit()
            }
          }
        })
      }
    }
    else {
      if (isFromSave) {
        this.onSave()
      } else {
        this.onSubmit()
      }
    }
  }

  public onSave() {
    if (this.isVendor && this.data.status == 2) {
      if (this.plannedCompletionDate) {
        this.rec['plannedCompletionTime'] = moment(this.plannedCompletionDate).valueOf();
        this.emitSaveOrSubmitEvent.emit({ rec: this.rec, isAttachment: this.files.length > 0 ? true : false, files: this.files, code: 2, isSaving: true , isSavingMessage:true})
        // this.dialogRef.close({ rec: this.rec, isAttachment: this.files.length > 0 ? true : false, files: this.files, code: 2, isSaving: true });
      } else {
        this._commonService.warning('Please select Estimation Completion Date');
      }
    } else {
      this.emitSaveOrSubmitEvent.emit({ rec: this.rec, isAttachment: this.files.length > 0 ? true : false, files: this.files, code: this.rec.status, isSaving: false,isSavingMessage:true })
      //    this.dialogRef.close({ rec: this.rec, isAttachment: this.files.length > 0 ? true : false, files: this.files, code: this.code,isSaving: false });
    }

  }

  public onSubmit() {
    if (this.isVendor && this.data.status == 2) {
      if (this.plannedCompletionDate) {
        this.rec['plannedCompletionTime'] = moment(this.plannedCompletionDate).valueOf();
        this.emitSaveOrSubmitEvent.emit({ rec: this.rec, isAttachment: this.files.length > 0 ? true : false, files: this.files, code: this.code, isSaving: false, isSavingMessage:false })
        //          this.dialogRef.close({ rec: this.rec, isAttachment: this.files.length > 0 ? true : false, files: this.files, code: this.code,isSaving: false });
      } else {
        this._commonService.warning('Please select Estimation Completion Date');
      }
    } else {
      if (this.isSeller || this.isOps || this.isAdmin) {
        this.code = 6;
      }
      this.emitSaveOrSubmitEvent.emit({ rec: this.rec, isAttachment: this.files.length > 0 ? true : false, files: this.files, code: this.code, isSaving: false , isSavingMessage:false})
      //         this.dialogRef.close({ rec: this.rec, isAttachment: this.files.length > 0 ? true : false, files: this.files, code: this.code,isSaving: false });
    }

  }

  public onFileChanged(event: any): void {
    var zip = new JSZip();
    var count = 0;
    var zipFilename = "capaAttachment.zip";
    this.files = event;

    this.files.forEach(droppedFile => {
      const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
      fileEntry.file((file: File) => {
        zip.file(droppedFile.relativePath, file);
      });
      count++;
    });

    if (count == this.files.length) {
      zip.generateAsync({ type: 'blob' }).then((content) => {
        const fd = new FormData();
        fd.append("file", content, zipFilename);
        this.uploadCapAttachment(fd, this.data);
        //APP_UI_CONFIG.masterdata.inspectionAgency.addFiles.paramList.id = uuid;
        // this._invokeService.uploadAttachments(APP_UI_CONFIG.masterdata.inspectionAgency.addFiles, fd).subscribe({
        //   next: res => {
        //   },
        //   error: error => {
        //     this._commonService.error('Files uploading failed, please check.');
        //   }
        // })
        // this._saleOrderService.uploadProductImagesZipForCheckpoint(this.data.auditId,this.data.checkpoint.uuid, fd).subscribe((result) => {
        // },(err: any) => {
        //   this._commonService.error('Files uploading failed, please check.');
        // });
      });
    }


    // for (const droppedFile of this.files) {
    //   if (droppedFile.fileEntry.isFile) {
    //     const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
    //     fileEntry.file((file: File) => {
    //       const formData = new FormData()
    //       formData.append('file', file);
    //       this.uploadCapAttachment(formData, this.data);
    //     });
    //   } else {
    //     const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
    //   }
    // }
  }

  private uploadCapAttachment(formData: any, data): void {
    APP_UI_CONFIG.capa.updateCapaDetailsAttachment.paramList.auditUid = data.auditUid;
    APP_UI_CONFIG.capa.updateCapaDetailsAttachment.paramList.capUid = data.uuid;
    APP_UI_CONFIG.capa.updateCapaDetailsAttachment.payload = formData;
    this._invokeService.uploadImage(APP_UI_CONFIG.capa.updateCapaDetailsAttachment).subscribe(res => {
      if (res.code == 200) {
        this._commonService.success(res.message ? res.message : 'CAPA Attachment has been uploaded successfully');
        this.isFileUploaded = true;
      } else {
        this._commonService.error(res.message ? res.message : 'Failed to update the CAPA Attachment');
      }
    }, error => {
      this._commonService.handleError(error);
    })
  }

  public getCapAttachment(fileName): void {
    APP_UI_CONFIG.capa.getCapaDetailsAttachment.paramList.auditUid = this.data.auditUid;
    APP_UI_CONFIG.capa.getCapaDetailsAttachment.paramList.capUid = this.data.uuid;
    let fileUid = this.isFileUploaded ? this.files[0].fileEntry?.name : this.data.capaAttachment;
    APP_UI_CONFIG.capa.getCapaDetailsAttachment.paramList.fileUid = fileUid;

    let url = environment.apiUrl + `audit/${this.data.auditUid}/capa/${this.data.uuid}/${fileName}`
    window.open(url, '_blank');
  }

  public deleteCapAttachment(fileName): void {
    this._commonService.openDialogForConfirmation('Are you sure want to delete this document ?').subscribe(response => {
      if (response) {
        APP_UI_CONFIG.capa.deleteCapAttachment.paramList.auditUid = this.data.auditUid;
        APP_UI_CONFIG.capa.deleteCapAttachment.paramList.capUid = this.data.uuid;
        APP_UI_CONFIG.capa.deleteCapAttachment.paramList.fileUid = fileName;

        this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.deleteCapAttachment).subscribe(res => {
          if (res.code == 200) {
            this._commonService.success(res.message ? res.message : 'CAPA Attachment has been deleted successfully');
            this.isFileUploaded = true;
            this.refreshPage.emit();
          } else {
            this._commonService.error(res.message ? res.message : 'Failed to delete the CAPA Attachment');
          }
        }, error => {
          this._commonService.handleError(error);
        })
      }
    })
  }

  fromItem;
  public onCopy(capaItem): void {
    this.fromItem = capaItem;
    this.showCopyTo = true;
    this.toCapaItemList = this.capaItemList.filter(item => (item.uuid != capaItem?.uuid && item.status==3)).map(item => ({ label: `${item.checkpointCategoryName}-${item.designCodeName}-${item.severity}`, value: item.uuid }))
    this.copyToDetails.from = `${capaItem.checkpointCategoryName}-${capaItem.designCodeName}-${capaItem.severity}`;
    this.copyToDetails.toList = [];
  }


  public saveCopiedCapaItems(): void {
    let copiedCapaDetailsList: any[] = [];
    let selectedCapaItemsList: any[] = this.capaItemList.filter(item => this.copyToDetails.toList?.find(itemUid => itemUid == item.uuid));
    copiedCapaDetailsList = selectedCapaItemsList?.map(capaItem => ({
      ...capaItem,
      capClosureComments: this.fromItem.capClosureComments,
      capClosureFeedback: this.fromItem.capClosureFeedback,
      remarksL1: this.fromItem.remarksL1,
      code: this.code,
    }));
    this.onCopyDetails.emit(copiedCapaDetailsList);
    this.showCopyTo = false;
    this._commonService.success("Details Copied Successfully");
  }

  public closeViewCapa(): void {
    this.emitSaveOrSubmitEvent.emit(false);
  }

}
