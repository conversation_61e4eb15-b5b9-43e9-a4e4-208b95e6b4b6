import { Pattern } from '@amcharts/amcharts5';
import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES } from 'app/enum-constants';

export const AUDITOR_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Code',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 255
        },
    },
    firstName: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "First Name",
        placeholder: 'First Name',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 255
        },
    },
    lastName: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Last Name",
        placeholder: 'Last Name',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 255
        },
    },
    role: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Role",
        placeholder: 'Select Role',
        show: true,
        rules: {
            required: true,
        },
    },
    inchargeUid: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Reports To",
        placeholder: 'Select an Incharge',
        show: true,
        rules: {
            required: true,
        },
    },
    designation: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Designation",
        placeholder: 'Designation',
        show: true,
        rules: {
            maxLength: 255
        },
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact Number",
        placeholder: 'Contact Number',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: FORM_FIELDS_CONSTANTS_VALUES.PHONE_NUMBER_LENGTH_MAX_LENGTH,
            minLength: FORM_FIELDS_CONSTANTS_VALUES.PHONE_NUMBER_LENGTH_MIN_LENGTH
        },
        topGroupTitle: true,
        topGroupTitleLabel: 'Contact Details',
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Email",
        placeholder: 'Email',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 255
        },
    },
    altemail: {
        type: FORM_CONTROL_TYPES.TEXTAREA,
        fieldStatus: FORM_CONTROL_TYPES.TEXTAREA,
        value: "",
        label: "Alternate Email",
        placeholder: 'Alternate Email',
        show: true,
        rules: {
            maxLength: 1000,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.COMMA_SEPARATED_EMAIL_PATTERN
        },
    },
    addressLine1: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Address",
        placeholder: 'Address',
        show: true,
        rules: {
            maxLength: 255
        },
        topGroupTitle: true,
        topGroupTitleLabel: 'Address Details',
    },
    city: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "City",
        placeholder: 'City',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN_WITH_SPACE,
            maxLength: 255
        },
    },
    state: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "State",
        placeholder: 'State',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN_WITH_SPACE,
            maxLength: 255
        },
    },
    pincode: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "PIN",
        placeholder: 'PIN',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.UNIVERSAL_PIN_CODE_PATTERN,
            minLength: FORM_FIELDS_CONSTANTS_VALUES.PIN_CODE_LENGTH_MIN_LENGTH,
            maxLength: FORM_FIELDS_CONSTANTS_VALUES.PIN_CODE_LENGTH_MAX_LENGTH
        },
    },
    country: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Country",
        placeholder: 'Country',
        show: true,
        rules: {
            maxLength: 255
        },
        rightSideGap:true,
    },
    setPasswordAsUserName: {
        type: FORM_CONTROL_TYPES.CHECKBOX,
        fieldStatus: FORM_CONTROL_TYPES.CHECKBOX,
        options: [],
        value: "",
        label: "Password same as UserName",
        placeholder: 'Password same as UserName',
        show: true,
        
    },
    resetPassword: {
        type: FORM_CONTROL_TYPES.CHECKBOX,
        fieldStatus: FORM_CONTROL_TYPES.CHECKBOX,
        options: [],
        value: "",
        label: "Reset Password",
        placeholder: 'Reset Password',
        show: false,
        rightSideGap: true
    },
    password: {
        type: FORM_CONTROL_TYPES.RADIO,
        fieldStatus: FORM_CONTROL_TYPES.RADIO,
        options: [],
        value: "",
        label: "",
        placeholder: "Password",
        show: false,
        rightSideGap: true
    },
    newPassword: {
        type: FORM_CONTROL_TYPES.PASSWORD,
        fieldStatus: FORM_CONTROL_TYPES.PASSWORD,
        value: "",
        label: "New Password",
        placeholder: 'New Password',
        show: false,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_NO_SPACE,
            maxLength: 50,
            minLength: 4,
            required: false
        },
    },
}