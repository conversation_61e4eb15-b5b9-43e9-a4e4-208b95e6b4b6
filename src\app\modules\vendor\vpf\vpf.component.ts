import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatAccordion } from '@angular/material/expansion';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { CountryList } from 'app/core/qinspect/_common/country';
import { Util } from 'app/core/qinspect/_common/util';
import { QiDiscussionHistory } from 'app/core/qinspect/_model/DiscussionHistory.model';
import {
  customers,
  facProductTypes,
  factories,
  inhouseTestings,
  processes,
  productTypes,
  qualityTeams,
  vendorGenForm,
} from 'app/core/qinspect/_model/VendorGenral.model';
import { ROLE } from 'app/enum-constants';
import { SaleOrderService } from 'app/modules/schedules/services/sale-order.service';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { DashboardService } from 'app/services/dashboard.service';
import { MessageService } from 'app/services/message.service';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { WarningDialogBoxComponent } from 'app/shared/warning-dialog-box/warning-dialog-box.component';
import { environment } from 'environments/environment';
import { unwatchFile } from 'fs';
import JSZip from 'jszip';
import { forEach } from 'lodash';
import moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, tap } from 'rxjs/operators';

import { FactorySaqDialogComponent } from './factory-saq-dailog/factory-saq-dailog.component';
import { VpfInfo } from './vpf-form-details';


export interface imageUrlType {
  factoryIndex: number;
  imageIndex: number;
  imageUrl: string;
}
@Component({
  selector: 'vpf-form',
  templateUrl: './vpf.component.html',
  styleUrls: ['./vpf.component.scss'],
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { showError: true },
    },
  ],

})
export class VPFComponent implements OnInit, OnDestroy {
  @ViewChildren("employeeForm", { read: ElementRef }) renderedEmployee: QueryList<ElementRef>;

  vendorGenForm: vendorGenForm = new vendorGenForm();
  vendorFormData: vendorGenForm = {};

  @ViewChild(MatAccordion) accordion: MatAccordion;
  @ViewChild('fileInput') el: ElementRef;
  imageUrl: imageUrlType[] = [];
  editFile: boolean = true;
  removeUpload: boolean = false;
  subscription: Subscription = new Subscription();

  vgiFormGroup: FormGroup;
  vpsFormGroup: FormGroup;
  empForm: FormGroup;
  vpsFormDataArray: FormArray;
  vpsBestSellingFormGroup: FormGroup;
  vpsBestSellingFormDataArray: FormArray;
  timeoutIdForNewEmployee: any;
  toogle = new FormControl('', []);
  toogle2 = new FormControl('YES', []);
  companyName = "Amazon";
  urlRegex = /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+$/;
  cityRegex = /^[a-zA-Z]+(?:[\s-][a-zA-Z]+)*$/;
  stateRegex = /^[a-zA-Z]+(?:[\s-][a-zA-Z]+)*$/;
  nameRegex = "^[a-zA-Z]{1}[a-zA-Z0-9.'\\s]*$";
  maxDate: any;

  yseNo: string[] = ['YES', 'NO'];
  facLicyseNo: string[] = ['YES', 'NO'];
  countryList: any;
  companyType: string[] = ['Direct Manufacturer - Foreign Invested', 'Direct Manufacturer - Private or Government Owned Enterprise',
    'Trader/Manufacturer - Uses several subcontracts but owns at least one production facility', 'Importer/Trader with license or brand', 'Importer/Trader', 'Other'];
  ownershipType: string[] = ['Sole Proprietorship', 'Private Limited', 'PartnerShip', 'Limited PartnerShip', 'Limited Liability Company (LLC)', 'Public Listed', 'Other'];

  lastFinancialYear: string;
  currentFinancialYear: string;
  factoryList: any[] = [1];
  hdr: string = 'Vendor Profile Form'
  tenantConfig: any;
  isEdit = false;
  isTrainingCompleted = false;
  @Input() status: string;
  @Input() data: any;
  @Output() close: EventEmitter<any> = new EventEmitter();

  employees1: FormArray;
  revisionData: String = "";
  isAdmin: boolean = false;
  isVendor: boolean = false;
  isSourcing: boolean = false;
  vendorUid = ""
  vendorApprovalStatus: boolean = false;
  discussionHistories: QiDiscussionHistory[]
  alreadyFilledForm: boolean = false;
  allCategories: any[] = [];
  allProductTypes: any[] = [];
  isApproveBtn: boolean = false;
  isApproved: boolean = false;
  productImages: File[] = [];
  factoryProductImages: any[][] = [,];
  private _isExpanded = false;
  isVpsApproved: boolean = false;
  auditTypeUidForFactoryScore: string = "8046cade-1521-4898-af52-608ea39a490f";
  factoryScoreList: any[] = [];
  isAnyFactoryHavingScore: boolean = false;
  inHouseProcessControlTypeList: string[] = ['IN-HOUSE', 'OUTSOURCED'];
  disabledSubmitButton: boolean = false;
  isViewMode: boolean = false;
  floatInfoSectionData: any;
  processNameList: string[] = ['Material Selection and Preparation', 'Forming or, Shaping or, Cutting', ' Stitching, Joining & Fastening', 'Assembly', 'Surface Treatment',
    'In-process Quality Control', 'Final Inspections', ' Packaging & Shipping', 'Component Fabrication', 'Electronics Manufacturing Services', 'Injection Molding',
    'Tooling & Mold making', 'Chemical Processing', 'Printing & Embroidery', 'Textile manufacturing', 'Wood Working', 'Machining', 'Lab Testing', 'Other'];

  indexOfStepper:number=0;
  isTestUser:boolean=false;

  constructor(
    private _activatedRoute: ActivatedRoute,
    private _formBuilder: FormBuilder,
    private cd: ChangeDetectorRef,
    private toaster: ToastrService,
    private _commonService: CommonService,
    public _dashboardService: DashboardService,
    private _saleOrderService: SaleOrderService,
    private _router: Router,
    private _sanitizer: DomSanitizer,
    private _authService: AuthService,
    public dialog: MatDialog,
    private _messageService: MessageService) { }

  ngOnInit(): void {
    if(ROLE.TEST_USER == this._commonService.getUseRole()){
      this.isTestUser = true;
    }
    this.disabledSubmitButton = false
    this.maxDate = new Date();
    this.isVpsApproved = false;
    const param_uuid = this._activatedRoute.snapshot.queryParams['uuid'];
    const param_status = this._activatedRoute.snapshot.queryParams['status'];
    if (param_status == 'view') {
      this.isViewMode = true;
      this.isVpsApproved = true;
    }
    this.tenantConfig = JSON.parse(localStorage.getItem('user'));
    this.isAdmin = this.tenantConfig.role === 'ADMIN' || this.tenantConfig.role.includes("OPERATIONS") || this._commonService.isSourcing();
    this.isVendor = this.tenantConfig.role === 'VENDOR';
    this.isSourcing = this._commonService.isSourcing();
    //let storedTrainingCheck = localStorage.getItem(`${this.tenantConfig.uuid}-trainingStatus`);
    let storedTrainingCheck = "COMPLETED"; //TODO - Remove with API integration
    if (this.isAdmin) {
      storedTrainingCheck = "COMPLETED";
    }
    this.isTrainingCompleted = true;
    this.vendorApprovalStatus = this.tenantConfig.vendorApprovalStatus;

    let uuid = "";//param_uuid ? param_uuid : (this.tenantConfig.role == 'VENDOR' && this.tenantConfig.vendorApprovalStatus == 0) ? this.tenantConfig?.uuid : null
    if (!(param_uuid == null)) {
      uuid = param_uuid;
      this.vendorUid = uuid;
    }
    else {
      if (this.tenantConfig.role == 'VENDOR') {
        uuid = this.tenantConfig?.uuid;
        this.vendorUid = uuid;
      }
    }

    //Get Product Type and Categories
    let getAllCategory$ = this._dashboardService.getAllCategory();
    let getAllProductTypes$ = this._dashboardService.getAllProductTypes();

    forkJoin([getAllCategory$, getAllProductTypes$]).subscribe(results => {

      this.allCategories = results[0];
      this.allProductTypes = results[1];

      if (uuid) {
        this.fetchVendorFormData(uuid);
        this.fetchDiscussionHistory(uuid);
      }
      else {
        this.initlizeForms();
      }
    }), (err: any) => {
      this._commonService.handleError(err);
    };
  }

  public onClick($event: MouseEvent): void {
    $event.stopPropagation();
  }

  public initlizeForms() {
    this.countryList = new CountryList();
    this.vendorGenInfoFormInti();
    this.vpsformIntilization();
    this.vpsBestSellingformIntilization();
    this.empForm = this._formBuilder.group({
      employees: this._formBuilder.array([])
    });
    if (this.isViewMode) {
      let factoryForm = this.empForm.get('employees') as FormArray;
      factoryForm.disable();
    }
    this.addEmployee();
    if (Object.keys(this.vendorFormData).length != 0) {
      this.toogle.valueChanges.subscribe(newToogleValue => {
        this.populateCommonVendorFactoryFields(newToogleValue);
      });
    }
    this.checkForFormValuesChanges();
  }

  public get isExpanded() {
    return this._isExpanded;
  }

  public set isExpanded(value: boolean) {
    this._isExpanded = value;
  }

  public isVendorFormData(): boolean {
    return Object.keys(this.vendorFormData).length != 0;
  }

  onStepChange(event: any): void {
    //use event.selectedIndex to know which step your user in.
  }
  //^[a-zA-Z0-9_.-]*$
  public vendorGenInfoFormInti() {
    //vgi stands for Vendor General Information
    this.vgiFormGroup = this._formBuilder.group({
      vgiUpdatedOn: [''],
      vgiEcomBiz: [''],
      vgiIsAmazVendor: [''],
      vgiVendorId: [''],
      vgiAmazonSeller: [''],
      vgiRegEmail: ['', [Validators.email]],

      vgiVendorNameEng: [this.vendorFormData.name || '', [Validators.required, Validators.pattern(".*[^ ].*"), Validators.maxLength(255)]],
      // vgiVendorNamelocal: ['', Validators.required],
      // vgiVendorAddressEng: [this.vendorFormData.addressLine1 || '', Validators.required],
      // vgiVendorAddresslocal: ['', Validators.required],

      vgiVendorFullAddressEng: this._formBuilder.group({
        vgiVendorAddressEng: [this.vendorFormData.addressLine1 || '', [Validators.required, Validators.pattern(".*[^ ].*"), Validators.maxLength(255)]],
        city: [this.vendorFormData.city || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
        state: [this.vendorFormData.state || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
        pincode: [this.vendorFormData.pincode || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(10)]],
        country: [this.vendorFormData.country || '', [Validators.required]],
        otherCountry: [this.vendorFormData.otherCountry || '', [Validators.maxLength(200)]],
      }),


      vgiChiefDeciMaker: this._formBuilder.group({
        name: [this.vendorFormData.decisionMakerName || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(200)]],
        title: [this.vendorFormData.decisionMakerTitle || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        telephone: [this.vendorFormData.decisionMakerTelephone || '', [Validators.required, Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        cellphone: [this.vendorFormData.decisionMakerMobile || '', [Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        email: [this.vendorFormData.decisionMakerEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      vgiMainContactPerson: this._formBuilder.group({
        name: [this.vendorFormData.mainContactName || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(200)]],
        title: [this.vendorFormData.mainContactTitle || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        telephone: [this.vendorFormData.mainContactTelephone || '', [Validators.required, Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        cellphone: [this.vendorFormData.mainContactMobile || '', [Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        email: [this.vendorFormData.mainContactEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      vgiQualityContactPerson: this._formBuilder.group({
        name: [this.vendorFormData.qualityContactName || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(200)]],
        title: [this.vendorFormData.qualityContactTitle || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        telephone: [this.vendorFormData.qualityContactTelephone || '', [Validators.required, Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        cellphone: [this.vendorFormData.qualityContactMobile || '', [Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        email: [this.vendorFormData.qualityContactEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),


      vgiWebsite: [this.vendorFormData.website || '', [Validators.pattern(this.urlRegex), Validators.maxLength(200)]],
      vgiRiskLevel: [this.vendorFormData.riskLevel || ''],
      vgiBusinessModel: [this.vendorFormData.businessModel || ''],
      vgiBusinessLine: [this.vendorFormData.businessLine || ''],
      vgiCategory: [this.vendorFormData.categories || []],
      vgiTypeOfCompany: [this.vendorFormData.otherCompanyType ? 'Other' : (this.vendorFormData.companyType || ''), [Validators.required, Validators.maxLength(200)]],
      vgiTypeOfCompanyOther: [this.vendorFormData.otherCompanyType || '', [Validators.maxLength(200)]],
      vgiTradeMark: [this.vendorFormData.trademark?.length > 0 ? this.vendorFormData.trademark : 'NO' || '', [Validators.required, Validators.maxLength(200)]],
      vgiTradeMarkName: [this.vendorFormData.tradeMarkDetails || '', [Validators.maxLength(200)]],
      vgiDateofEstablish: [this.vendorFormData.establishedDate || '', Validators.required],

      vgiTypeOfOwnerShip: [this.vendorFormData.otherOwnerShipType ? 'Other' : (this.vendorFormData.ownershipType || ''), Validators.required],
      vgiTypeOfOwnerShipOther: [this.vendorFormData.otherOwnerShipType || '', [Validators.maxLength(200)]],
      vgiFactoryLic: [this.vendorFormData.licenseNo?.length > 0 ? this.vendorFormData.licenseNo : 'NO' || '', Validators.required],
      // vgiRestriction: [this.vendorFormData.anyRestrictionOtherVendor ? this.vendorFormData.anyRestrictionOtherVendor : 'NO' || '', Validators.required],
      vgiFactoryLicName: [this.vendorFormData.licenseNoDetails || ''],
      vgiFactoryLicValidDate: [this.vendorFormData.licenseValidDate > 0 ? new Date(Number(this.vendorFormData.licenseValidDate)) : 0 || ''],
      vgiLastFinancialYear: [this.vendorFormData.lastYearSalesTurnover || null],
      vgiCurrentYearExpectedSalesTurnover: [this.vendorFormData.currentYearExpectedSalesTurnover || null],

    });

    if (this.vgiFormGroup.get("vgiFactoryLic").value == 'NO') {
      this.vgiFormGroup.get('vgiFactoryLicName').disable();
      this.vgiFormGroup.get('vgiFactoryLicValidDate').disable();
    }
    if (this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'country']).value != 'Other')
      this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).disable();

    this.vgiFormGroup.get("vgiFactoryLic")?.valueChanges
      .pipe(distinctUntilChanged())
      .subscribe(response => {
        if (response == "YES") {
          this.vgiFormGroup.get('vgiFactoryLicName').enable();
          this.vgiFormGroup.get('vgiFactoryLicName').setValidators([Validators.required]);
          this.vgiFormGroup.get('vgiFactoryLicName').updateValueAndValidity();

          this.vgiFormGroup.get('vgiFactoryLicValidDate').enable();
          this.vgiFormGroup.get('vgiFactoryLicValidDate').setValidators([Validators.required]);
          this.vgiFormGroup.get('vgiFactoryLicValidDate').updateValueAndValidity();
        } else {
          this.vgiFormGroup.get('vgiFactoryLicName').disable();
          this.vgiFormGroup.get('vgiFactoryLicName').setValue('');
          this.vgiFormGroup.get('vgiFactoryLicName').clearValidators();
          this.vgiFormGroup.get('vgiFactoryLicName').updateValueAndValidity();

          this.vgiFormGroup.get('vgiFactoryLicValidDate').disable();
          this.vgiFormGroup.get('vgiFactoryLicValidDate').setValue('');
          this.vgiFormGroup.get('vgiFactoryLicValidDate').clearValidators();
          this.vgiFormGroup.get('vgiFactoryLicValidDate').updateValueAndValidity();
        }
      })

    this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'country'])?.valueChanges
      .pipe(distinctUntilChanged())
      .subscribe(response => {
        if (response == "Other") {
          this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).enable();
          this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).setValidators([Validators.required, Validators.pattern('.*[^ ].*')]);
          this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).updateValueAndValidity();
        } else {
          this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).disable();
          this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).setValue('');
          this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).clearValidators();
          this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).updateValueAndValidity();
        }
      })

    if (this.isAdmin) {
      //this.vgiFormGroup.controls.vgiRiskLevel.setValidators([Validators.required]);
    }

    //Set values for few fields
    // if(this.alreadyFilledForm)
    // {
    //this.vgiFormGroup.controls.vgiRiskLevel.setValue(this.vendorFormData.riskLevel?.toString());
    this.vgiFormGroup.controls.vgiBusinessModel.setValue(this.vendorFormData.businessModel?.toString());
    this.vgiFormGroup.controls.vgiBusinessLine.setValue(this.vendorFormData.businessLine?.toString());

    if (this.isVpsApproved) {
      this.vgiFormGroup.disable();
      this.vgiFormGroup.get('vgiChiefDeciMaker').enable();
      this.vgiFormGroup.get('vgiMainContactPerson').enable();
      this.vgiFormGroup.get('vgiQualityContactPerson').enable();
    }
    //}
    if (this.isViewMode) {
      this.vgiFormGroup.disable();
    }
  }

  private vpsformIntilization(): void {
    this.vpsFormGroup = this._formBuilder.group({
      vpsFormDataArray: this._formBuilder.array([])
    });
    this.addNewRow();
    if (this.isVpsApproved) {
      this.vpsFormGroup.disable();
    }
    if (this.isViewMode) {
      this.vpsFormGroup.disable();
    }
  }

  private vpsBestSellingformIntilization(): void {
    this.vpsBestSellingFormGroup = this._formBuilder.group({
      vpsBestSellingFormDataArray: this._formBuilder.array([])
    });
    this.addNewRow2();
    if (this.isVpsApproved) {
      this.vpsBestSellingFormGroup.disable();
    }
    if (this.isViewMode) {
      this.vpsBestSellingFormGroup.disable();
    }
  }

  public getDynamicFormControls(name): FormArray {
    return this.vpsFormGroup.get(name) as FormArray;
  }

  public getDynamicFormControls2(name): FormArray {
    return this.vpsBestSellingFormGroup.get(name) as FormArray;
  }

  private createVpsformIntilizationFormRow(): any {
    let formSetup = this._formBuilder.group({
      vpsProductCategManuf: [{ value: '', disabled: false }, [Validators.required]],
      //vpsProductTypes: [{ value: '', disabled: false }],
      vpsProductIncInEachCateg: [{ value: '', disabled: false }, [Validators.required, Validators.pattern(".*[^ ].*"), Validators.maxLength(500)]],
      isSelected: [{ value: false, disabled: false }, [Validators.required]],
      vpsYearExp: [{ value: '', disabled: false }, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
      vpsPercentageOfYourBuss: [{ value: '', disabled: false }, [Validators.required, Validators.pattern("^[0-9.]*$"), Validators.min(1), Validators.max(99)]],
      isNewFormRow: [{ value: true, disabled: false }],
    });

    // if (this.isAdmin) {
    //   formSetup.get('vpsProductTypes').setValidators([Validators.required]);
    // }
    return formSetup;
  }

  private createVpsformIntilizationFormRowWithData(data): any {
    let formSetup = this._formBuilder.group({
      vpsProductCategManuf: [{ value: data.productTypeCategoryName, disabled: false }, [Validators.required]],
      //vpsProductTypes: [{ value: data.productTypeName, disabled: false }],
      vpsProductIncInEachCateg: [{ value: data.productIncludedInCategory, disabled: false }, [Validators.required, Validators.pattern(".*[^ ].*"), Validators.maxLength(500)]],
      isSelected: [{ value: (data.plannedForSourcing == '1' ? true : false), disabled: false }],
      vpsYearExp: [{ value: data.experienceInYrs, disabled: false }, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
      vpsPercentageOfYourBuss: [{ value: data.percentOfBusiness, disabled: false }, [Validators.required, Validators.pattern("^[0-9.]*$"), Validators.min(1), Validators.max(99)]],
      isNewFormRow: [{ value: true, disabled: false }],
      idValue: [{ value: data.uuid, disabled: false }]
    });

    let categoryObj = this.allCategories.find(e => e.uuid == data.productTypeCategoryUid);
    formSetup.get('vpsProductCategManuf').setValue(categoryObj);
    return formSetup;
  }

  private createVpsbestSellingformIntilizationFormRow(): any {
    return this._formBuilder.group({
      vpsMainCust: [{ value: '', disabled: false }, [Validators.maxLength(200)]],
      vpsProductDesc: [{ value: '', disabled: false }, [Validators.maxLength(200)]],
      vpsCountry: [{ value: '', disabled: false }, [Validators.maxLength(40)]],
      vpsQtyShipped: [{ value: '', disabled: false }, [Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(999999)]],
      isNewFormRow: [{ value: true, disabled: false }]
    });
  }

  private createVpsbestSellingformIntilizationFormRowWithData(data): any {
    return this._formBuilder.group({
      vpsMainCust: [{ value: data.customerName, disabled: false }, [Validators.maxLength(200)]],
      vpsProductDesc: [{ value: data.productDescription, disabled: false }, [Validators.maxLength(200)]],
      vpsCountry: [{ value: data.country, disabled: false }, [Validators.maxLength(40)]],
      vpsQtyShipped: [{ value: data.qtyShipped, disabled: false }, [Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(999999)]],
      isNewFormRow: [{ value: true, disabled: false }],
      idValue: [{ value: data.uuid, disabled: false }]
    });
  }

  public addNewRow(isUserAdded: boolean = false): void {
    this.vpsFormDataArray = this.vpsFormGroup.get('vpsFormDataArray') as FormArray;
    if (isUserAdded) {
      this.vpsFormDataArray.push(this.createVpsformIntilizationFormRow());
    }
    else {
      if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.productTypes) {
        this.vendorFormData.productTypes.forEach(row => {
          this.vpsFormDataArray.push(this.createVpsformIntilizationFormRowWithData(row));
        });
      }
      else {
        this.vpsFormDataArray.push(this.createVpsformIntilizationFormRow());
      }
    }
  }

  public removeRowFromVpsform(index: number) {
    this.vpsFormDataArray.removeAt(index);
  }

  public addNewRow2(isUserAdded: boolean = false): void {
    this.vpsBestSellingFormDataArray = this.vpsBestSellingFormGroup.get('vpsBestSellingFormDataArray') as FormArray;
    if (isUserAdded) {
      this.vpsBestSellingFormDataArray.push(this.createVpsbestSellingformIntilizationFormRow());
    }
    else {
      if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.customers) {
        this.vendorFormData.customers.forEach(row => {
          this.vpsBestSellingFormDataArray.push(this.createVpsbestSellingformIntilizationFormRowWithData(row));
        })
      }
      else {
        this.vpsBestSellingFormDataArray.push(this.createVpsbestSellingformIntilizationFormRow());
      }
    }

  }

  public removeRowFromVpsBestSelling(index: number) {
    this.vpsBestSellingFormDataArray.removeAt(index);
  }

  // private findIfNewFieldExist(): any {
  //   let newFormGroup: any[] = this.getDynamicFormControls().controls;
  //   let findNewFormIndex = newFormGroup.findIndex(item => {
  //     return (item.controls.isNewFormRow.value == true)
  //   });
  //   return findNewFormIndex;
  // }

  public employees(): FormArray {
    return this.empForm.get('employees') as FormArray;
  }

  public newEmployee(): FormGroup {

    return this._formBuilder.group({
      fgiFactoryOwnership: ['', [Validators.required]],
      fgiFactoryNameEng: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
      fgiFactoryAddressEng: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
      fgiRiskLevel: [''],

      fgiFactoryFullAddressEng: this._formBuilder.group({
        city: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
        state: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
        pincode: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(10)]],
        country: ['', [Validators.required]],
        otherCountry: [''],
      }),

      fgiChiefDeciMaker: this._formBuilder.group({
        name: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(200)]],
        title: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        telephone: ['', [Validators.required, Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        cellphone: ['', [Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        email: ['', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiBusinessContactPerson: this._formBuilder.group({
        name: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(200)]],
        title: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        telephone: ['', [Validators.required, Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        cellphone: ['', [Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        email: ['', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiAuditContactPerson: this._formBuilder.group({
        name: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(200)]],
        title: ['', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        telephone: ['', [Validators.required, Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        cellphone: ['', [Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        email: ['', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiIsoCertificate: ['', [Validators.required, Validators.maxLength(200)]],
      fgiIsoDateOfCertificate: ['', [Validators.maxLength(20)]],
      fgiFactoryCapacityperMonth: ['', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiFactoryAvailVolume: ['', [Validators.pattern("^[0-9.]*$"), Validators.min(1), Validators.max(99)]],
      fgiFactoryworkerCount: ['', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiFactorycomments: ['', [Validators.maxLength(255)]],
      fgiDepForQualityControl: ['', [Validators.required]],
      fgiDepForProductDev: ['', [Validators.required]],
      fgiProductTestingMaintaince: ['', [Validators.required]],

      fgiQualityTeamEngineerPerson: [, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiQualityTeamEngineerYearOfExp: [, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
      fgiQualityTeamIQCPerson: [, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiQualityTeamIQCYearOfExp: [, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
      fgiQualityTeamInLineIQCPerson: [, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiQualityTeamInLineIQCYearOfExp: [, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
      fgiQualityTeamFinalQCPerson: [, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiQualityTeamFinalQCYearOfExp: [, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],

      processControll: this._formBuilder.array([]),
      processControllForOutSourced: this._formBuilder.array([]),
      qualityTeams: this._formBuilder.array([]),
      inHouseTesting: this._formBuilder.array([]),
      productImage: this._formBuilder.array([]),
    });

  }

  public addFactoryByUser() {
    this.employees().push(this.newEmployee());
    this.addfactoryProcessControll(this.employees().length - 1, true);
    this.addfactoryProcessControllForOutSourced(this.employees().length - 1, true);
    this.addfactoryinHouseTesting(this.employees().length - 1, true);
    this.addnewProductImage(this.employees().length - 1, true);
    this._commonService.success('New factory information section added successfully, please fill your details in newly added factory');
    this.timeoutIdForNewEmployee = setTimeout(() => {
      const newEmployeeToScrollOn = this.renderedEmployee.toArray();
      newEmployeeToScrollOn[this.employees().length - 1].nativeElement.scrollIntoView({
        behavior: 'smooth'
      });
    }, 500)

  }



  public addEmployee() {
    let name = [
      'Quality Engineer',
      'IQC (Incoming material checking)',
      'In-Line QC',
      'Final QC'
    ]
    let isThere: boolean = false;
    if (this.vendorFormData?.factories) {
      this.vendorFormData.factories.forEach((row) => {
        this.employees().push(this.populateFactoryFormWithData(row));
        this.addfactoryProcessControll(this.employees().length - 1);
        this.addfactoryProcessControllForOutSourced(this.employees().length - 1);
        this.addfactoryinHouseTesting(this.employees().length - 1);
        this.addnewProductImage(this.employees().length - 1);

        if (row.qualityTeams) {
          row.qualityTeams.forEach(element => {
            if (name.includes((element.qualityTeamName)) == false) {

            }
            else {
              isThere = true;
            }
          });
        }
        if (isThere) {

          this.addQualityTeamControll(this.employees().length - 1);
        }

      })
    }
    else {
      this.employees().push(this.newEmployee());
      this.addfactoryProcessControll(this.employees().length - 1);
      this.addfactoryProcessControllForOutSourced(this.employees().length - 1);
      this.addfactoryinHouseTesting(this.employees().length - 1);
      this.addnewProductImage(this.employees().length - 1);
    }
  }

  public removeEmployee(empIndex: number) {
    //write code to delete image also
    // this.imageUrl.splice(processIndex, 1);
    this.employees().removeAt(empIndex);
  }


  // these functions are to add and remove Process Controlled table inside Capabilty fields of each factory
  public factoryProcessControll(index: number): FormArray {
    return this.employees()
      .at(index)
      .get('processControll') as FormArray;
  }

  public factoryProcessControllForOutSourced(index: number): FormArray {
    return this.employees()
      .at(index)
      .get('processControllForOutSourced') as FormArray;
  }

  public newProcessControll(): FormGroup {
    return this._formBuilder.group({
      processType: ['IN-HOUSE', [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseProcessName: ['', [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseProcessNameOther: ['', [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseKeyOperation: ['', [Validators.maxLength(200), Validators.pattern(".*[^ ].*")]],
    });
  }

  public newProcessControllForOutSourced(): FormGroup {
    return this._formBuilder.group({
      processType: ['OUTSOURCED', [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseProcessName: ['', [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseProcessNameOther: ['', [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseKeyOperation: ['', [Validators.maxLength(200), Validators.pattern(".*[^ ].*")]],
    });
  }

  public newProcessControllWithData(data): FormGroup {
    return this._formBuilder.group({
      processType: [{ value: data.processType || '', disabled: this.isVpsApproved }, [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseProcessName: [{ value: data.processName || '', disabled: this.isVpsApproved }, [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseProcessNameOther: [{ value: data.processNameOther || '', disabled: this.isVpsApproved }, [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseKeyOperation: [{ value: data.keyOperations || '', disabled: this.isVpsApproved }, [Validators.maxLength(200), Validators.pattern(".*[^ ].*")]],
      uuid: data.uuid,
    });
  }

  public newProcessControllForOutSourcedWithData(data): FormGroup {
    return this._formBuilder.group({
      processType: [{ value: data.processType || '', disabled: this.isVpsApproved }, [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseProcessName: [{ value: data.processName || '', disabled: this.isVpsApproved }, [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseProcessNameOther: [{ value: data.processNameOther || '', disabled: this.isVpsApproved }, [Validators.maxLength(255), Validators.pattern(".*[^ ].*")]],
      inHouseKeyOperation: [{ value: data.keyOperations || '', disabled: this.isVpsApproved }, [Validators.maxLength(200), Validators.pattern(".*[^ ].*")]],
      uuid: data.uuid,
    });
  }

  public addfactoryProcessControll(index: number, isNew?: boolean) {
    if (isNew) {
      this.factoryProcessControll(index).push(this.newProcessControll());
    } else {
      if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.factories) {
        let dataArray: any[] = this.vendorFormData?.factories[index]?.processes?.filter((item) => item.processType == 'IN-HOUSE');
        dataArray?.forEach((element) => {
          this.factoryProcessControll(index).push(this.newProcessControllWithData(element));
        });
        if (dataArray?.length == 0 && !this.isVpsApproved) {
          this.factoryProcessControll(index).push(this.newProcessControll());
        } else if (dataArray?.length == 0 && this.isVpsApproved) {
          this.factoryProcessControll(index).push(this.newProcessControllWithData({}));
        }
      } else
        this.factoryProcessControll(index).push(this.newProcessControll());
    }
  }

  public addfactoryProcessControllForOutSourced(index: number, isNew?: boolean) {
    if (isNew) {
      this.factoryProcessControllForOutSourced(index).push(this.newProcessControllForOutSourced());
    } else {
      if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.factories) {
        let dataArray: any[] = this.vendorFormData?.factories[index]?.processes.filter((item) => item.processType == 'OUTSOURCED');
        dataArray?.forEach((element) => {
          this.factoryProcessControllForOutSourced(index).push(this.newProcessControllForOutSourcedWithData(element));
        });
        if (dataArray?.length == 0 && !this.isVpsApproved) {
          this.factoryProcessControllForOutSourced(index).push(this.newProcessControllForOutSourced());
        } else if (dataArray?.length == 0 && this.isVpsApproved) {
          this.factoryProcessControllForOutSourced(index).push(this.newProcessControllForOutSourcedWithData({}));
        }
      } else
        this.factoryProcessControllForOutSourced(index).push(this.newProcessControllForOutSourced());
    }
  }

  public removefactoryProcessControll(index: number, processIndex: number) {
    this.factoryProcessControll(index).removeAt(processIndex);
  }

  public removefactoryProcessControllForOutSourced(index: number, processIndex: number) {
    this.factoryProcessControllForOutSourced(index).removeAt(processIndex);
  }

  public onFactoryCountryValueChanges(index: number) {
    if (this.employees().at(index).get(['fgiFactoryFullAddressEng', 'country']).value == "Other") {
      this.employees().at(index).get(['fgiFactoryFullAddressEng', 'otherCountry']).enable();
      this.employees().at(index).get(['fgiFactoryFullAddressEng', 'otherCountry']).setValidators([Validators.required, Validators.pattern('.*[^ ].*')]);
      this.employees().at(index).get(['fgiFactoryFullAddressEng', 'otherCountry']).updateValueAndValidity();
    } else {
      this.employees().at(index).get(['fgiFactoryFullAddressEng', 'otherCountry']).disable();
      this.employees().at(index).get(['fgiFactoryFullAddressEng', 'otherCountry']).setValue('');
      this.employees().at(index).get(['fgiFactoryFullAddressEng', 'otherCountry']).clearValidators();
      this.employees().at(index).get(['fgiFactoryFullAddressEng', 'otherCountry']).updateValueAndValidity();
    }

    //this.employees().at(index).get('country');
  }
  // these functions are to add and remove Process Controlled table inside Capabilty fields of each factory

  // these functions are to add and remove qualityTeams table of each factory
  public factoryqualityTeamsControll(index: number): FormArray {
    return this.employees()
      .at(index)
      .get('qualityTeams') as FormArray;
  }

  public newQualityTeamControll(): FormGroup {
    return this._formBuilder.group({
      qualityTeamName: ['', [Validators.maxLength(40), Validators.pattern(".*[^ ].*"), Validators.required]],
      personCount: ['', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      avgExperienceInYrs: ['', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
    });
  }

  public newQualityTeamControllWithData(data): FormGroup {
    return this._formBuilder.group({
      qualityTeamName: [{ value: data.qualityTeamName || '', disabled: this.isVpsApproved }, [Validators.maxLength(40), Validators.pattern(".*[^ ].*"), Validators.required]],
      personCount: [{ value: data.personCount || '', disabled: this.isVpsApproved }, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      avgExperienceInYrs: [{ value: data.avgExperienceInYrs || '', disabled: this.isVpsApproved }, [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
      uuid: data.uuid,
    });
  }

  public addQualityTeamControll(index: number, isNew?: boolean) {
    let name = [
      'Quality Engineer',
      'IQC (Incoming material checking)',
      'In-Line QC',
      'Final QC'
    ]
    if (isNew) {
      //console.log('new quality---1', index)
      this.factoryqualityTeamsControll(index).push(this.newQualityTeamControll());
    } else {
      if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.factories && this.vendorFormData?.factories[index]?.qualityTeams) {
        this.vendorFormData?.factories[index]?.qualityTeams.forEach(element => {
          if (name.includes((element.qualityTeamName))) {
            //console.log('new quality1', index)
          } else {
            // console.log('new quality2', index)
            this.factoryqualityTeamsControll(index).push(this.newQualityTeamControllWithData(element));
          }
        });
      } else {
        //console.log('new quality3', index)
        this.factoryqualityTeamsControll(index).push(this.newQualityTeamControll());
      }
    }

  }

  public removeQualityTeamControll(index: number, processIndex: number) {
    this.factoryqualityTeamsControll(index).removeAt(processIndex);
  }
  // these functions are to add and remove qualityTeams table  of each factory


  // these functions are to add and remove  FACTORY IN-HOUSE TESTING CAPABILITY (LAB/ON-SITE TESTING)table inside Capabilty fields of each factory
  public factoryinHouseTesting(index: number): FormArray {
    return this.employees()
      .at(index)
      .get('inHouseTesting') as FormArray;
  }

  public newinHouseTesting(): FormGroup {
    return this._formBuilder.group({
      typeOfTest: ['Not Applicable', [Validators.maxLength(255), Validators.pattern(".*[^ ].*"), Validators.required]],
      // scales: ['', [Validators.maxLength(50), Validators.pattern(".*[^ ].*"), Validators.required]],
      //equipment: ['', [Validators.maxLength(200), Validators.pattern(".*[^ ].*"), Validators.required]],
      testOfStandard: ['Not Applicable', [Validators.maxLength(255), Validators.pattern(".*[^ ].*"), Validators.required]],
    });
  }

  public newinHouseTestingWithData(data): FormGroup {
    return this._formBuilder.group({
      typeOfTest: [{ value: data.testName || '', disabled: this.isVpsApproved }, [Validators.maxLength(255), Validators.pattern(".*[^ ].*"), Validators.required]],
      // scales: [{ value: data.scales || '', disabled: this.isVpsApproved }, [Validators.maxLength(50), Validators.pattern(".*[^ ].*"), Validators.required]],
      // equipment: [{ value: data.equipment || '', disabled: this.isVpsApproved }, [Validators.maxLength(200), Validators.pattern(".*[^ ].*"), Validators.required]],
      testOfStandard: [{ value: data.standardTest || '', disabled: this.isVpsApproved }, [Validators.maxLength(255), Validators.pattern(".*[^ ].*"), Validators.required]],
      uuid: data.uuid,
    });
  }

  public addfactoryinHouseTesting(index: number, isNew?: boolean) {
    if (isNew) {
      this.factoryinHouseTesting(index).push(this.newinHouseTesting());
    } else {
      if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.factories) {
        let dataArray: any[] = this.vendorFormData?.factories[index]?.inhouseTestings;
        dataArray?.forEach((element) => {
          this.factoryinHouseTesting(index).push(this.newinHouseTestingWithData(element));
        });
        if (dataArray?.length == 0) {
          this.factoryinHouseTesting(index).push(this.newinHouseTesting())
        }
      } else
        this.factoryinHouseTesting(index).push(this.newinHouseTesting());
    }
  }

  public removefactoryinHouseTesting(index: number, processIndex: number) {
    this.factoryinHouseTesting(index).removeAt(processIndex);
  }
  // these functions are to add and remove  FACTORY IN-HOUSE TESTING CAPABILITY (LAB/ON-SITE TESTING) table inside Capabilty fields of each factory



  // these functions are to add and remove  product image  inside Capabilty fields of each factory
  public factoryProductImage(index: number): FormArray {
    return this.employees()
      .at(index)
      .get('productImage') as FormArray;
  }

  public newProductImage(): FormGroup {
    return this._formBuilder.group({
      name: ['', [Validators.maxLength(400), Validators.pattern(".*[^ ].*"), Validators.required]],
      factoryProductType: [''],
      factoryProductCategory: ['', [Validators.required]],
      isFactoryProductSourced: '',
      desc: ['', [Validators.maxLength(400), Validators.pattern(".*[^ ].*"), Validators.required]],
      imageFile: [null],
      imageName: null
    });
  }

  public newProductImageWithData(data): FormGroup {
    let formObj = this._formBuilder.group({
      name: [{ value: data.productName || '', disabled: this.isVpsApproved }, [Validators.maxLength(400), Validators.pattern(".*[^ ].*"), Validators.required]],
      desc: [{ value: data.description || '', disabled: this.isVpsApproved }, [Validators.maxLength(400), Validators.pattern(".*[^ ].*"), Validators.required]],
      factoryProductType: [{ value: '', disabled: this.isVpsApproved }, []],
      factoryProductCategory: [{ value: '', disabled: this.isVpsApproved }],
      isFactoryProductSourced: [{ value: (data.plannedForSourcing == '1' ? true : false), disabled: this.isVpsApproved }],
      imageFile: this.fetchPictureUrl(data.factoryUid, data.productImageUrl) || '',
      imageName: data.productImageUrl || '',
      uuid: data.uuid,
      factoryUid: data.factoryUid
    });

    let productTypeObj = this.allProductTypes.find(e => e.uuid == data.productTypeUid);
    formObj.get('factoryProductType').setValue(productTypeObj);
    let categoryObj = this.allCategories.find(e => e.uuid == data.productTypeCategoryUid);
    formObj.get('factoryProductCategory').setValue(categoryObj);

    return formObj;
  }

  public addnewProductImage(index: number, isNew?: boolean) {
    if (isNew) {
      this.factoryProductImage(index).push(this.newProductImage());
    } else {
      if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.factories) {
        let dataArray: any[] = this.vendorFormData?.factories[index]?.productTypes;
        dataArray?.forEach((element) => {
          this.factoryProductImage(index).push(this.newProductImageWithData(element));
        });
        if (dataArray?.length == 0) {
          this.factoryProductImage(index).push(this.newProductImage());
        }
      } else
        this.factoryProductImage(index).push(this.newProductImage());
    }
  }

  public removeProductImage(index: number, processIndex: number) {
    // this.imageUrl.splice(processIndex, 1);
    this.factoryProductImage(index).removeAt(processIndex);
  }

  /*########################## File Upload ########################*/


  uploadFile(event, i, empIndex) {
    let reader = new FileReader(); // HTML5 FileReader API
    let file = event.target.files[0];
    this.productImages.push(file);
    if (event.target.files && event.target.files[0]) {
      reader.readAsDataURL(file);

      // When file uploads set it to file formcontrol
      reader.onload = () => {
        this.imageUrl.push({
          factoryIndex: empIndex,
          imageIndex: i,
          imageUrl: reader.result + ''
        })

        this.factoryProductImage(empIndex).controls[i]['controls'].imageFile?.setValue(reader.result + '');
        this.factoryProductImage(empIndex).controls[i]['controls'].imageName?.setValue(file.name);

        this.editFile = false;
        this.removeUpload = true;
      }
      // ChangeDetectorRef since file is loading outside the zone
      this.cd.markForCheck();
    }

  }

  public fetchImage(factIndex, imgIndex): string {
    let url = '';
    url = this.factoryProductImage(factIndex).controls[imgIndex]['controls'].imageFile?.value;
    return url;
  }

  public populateCommonVendorFactoryFields(value: boolean) {
    if (value) {
      this.employees().patchValue([{
        'fgiFactoryNameEng': this.vgiFormGroup.get('vgiVendorNameEng').value,
        'fgiFactoryAddressEng': this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'vgiVendorAddressEng']).value,
        'fgiFactoryFullAddressEng': {
          city: this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'city']).value,
          state: this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'state']).value,
          pincode: this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'pincode']).value,
          country: this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'country']).value,
          otherCountry: this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).value,
        },
        'fgiChiefDeciMaker': {
          name: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'name']).value,
          title: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'title']).value,
          telephone: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'telephone']).value,
          cellphone: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'cellphone']).value,
          email: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'email']).value,
        },
        'fgiBusinessContactPerson': {
          name: this.vgiFormGroup.get(['vgiMainContactPerson', 'name']).value,
          title: this.vgiFormGroup.get(['vgiMainContactPerson', 'title']).value,
          telephone: this.vgiFormGroup.get(['vgiMainContactPerson', 'telephone']).value,
          cellphone: this.vgiFormGroup.get(['vgiMainContactPerson', 'cellphone']).value,
          email: this.vgiFormGroup.get(['vgiMainContactPerson', 'email']).value,
        },
        'fgiAuditContactPerson': {
          name: this.vgiFormGroup.get(['vgiQualityContactPerson', 'name']).value,
          title: this.vgiFormGroup.get(['vgiQualityContactPerson', 'title']).value,
          telephone: this.vgiFormGroup.get(['vgiQualityContactPerson', 'telephone']).value,
          cellphone: this.vgiFormGroup.get(['vgiQualityContactPerson', 'cellphone']).value,
          email: this.vgiFormGroup.get(['vgiQualityContactPerson', 'email']).value,
        }
      },
      { selfOnly: true }]);
    } else {
      this.employees().patchValue([{}, { selfOnly: true }]);
    }

  }

  public populateFactoryFormWithData(data): any {
    let factoryFormWithData = this._formBuilder.group({
      fgiFactoryOwnership: [data.ownership || '', Validators.required],
      fgiFactoryNameEng: [data.name || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
      fgiFactoryAddressEng: [data.addressLine1 || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
      fgiRiskLevel: [data.riskLevel || ''],
      fgiUid: [data.uuid || ''],
      fgiFactoryFullAddressEng: this._formBuilder.group({
        city: [data.city || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
        state: [data.state || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(255)]],
        pincode: [data.pincode || '', [Validators.required, Validators.pattern(".*[^ ].*"), Validators.maxLength(10)]],
        country: [data.country || '', [Validators.required]],
        otherCountry: [data.otherCountry || ''],
      }),

      fgiChiefDeciMaker: this._formBuilder.group({
        name: [data.decisionMakerName || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(200)]],
        title: [data.decisionMakerTitle || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        telephone: [data.decisionMakerTelephone || '', [Validators.required, Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        cellphone: [data.decisionMakerMobile || '', [Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        email: [data.decisionMakerEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiBusinessContactPerson: this._formBuilder.group({
        name: [data.mainContactName || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        title: [data.mainContactTitle || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        telephone: [data.mainContactTelephone || '', [Validators.required, Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        cellphone: [data.mainContactMobile || '', [Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        email: [data.mainContactEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiAuditContactPerson: this._formBuilder.group({
        name: [data.qualityContactName || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        title: [data.qualityContactTitle || '', [Validators.required, Validators.pattern('.*[^ ].*'), Validators.maxLength(100)]],
        telephone: [data.qualityContactTelephone || '', [Validators.required, Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        cellphone: [data.qualityContactMobile || '', [Validators.pattern("^[0-9]{10,12}$"), Validators.maxLength(12)]],
        email: [data.qualityContactEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiIsoCertificate: [data.isoCertificationDate ? 'YES' : 'NO', [Validators.required, Validators.maxLength(200)]],
      fgiIsoDateOfCertificate: [data.isoCertificationDate > 0 ? new Date(Number(data.isoCertificationDate)) : 0 || '', Validators.maxLength(30)],
      fgiFactoryCapacityperMonth: [data.capacityPerMonth || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiFactoryAvailVolume: [data.availableCapacityPerMonth || '', [Validators.pattern("^[0-9.]*$"), Validators.min(1), Validators.max(99)]],
      fgiFactoryworkerCount: [data.workerCount || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiFactorycomments: [data.comments || '', [Validators.maxLength(255)]],
      fgiDepForQualityControl: [data.hasDedicatedQualityTeam == 1 ? 'YES' : 'NO', [Validators.required]],
      fgiDepForProductDev: [data.hasDedicatedProductDevTeam == 1 ? 'YES' : 'NO', [Validators.required]],
      fgiProductTestingMaintaince: [data.productQualityAudit == 1 ? 'YES' : 'NO', [Validators.required]],

      fgiQualityTeamEngineerPerson: [this.personCount(data.qualityTeams, 'Quality Engineer') || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiQualityTeamEngineerYearOfExp: [this.avgExperienceInYrs(data.qualityTeams, 'Quality Engineer') || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
      fgiQualityTeamIQCPerson: [this.personCount(data.qualityTeams, 'IQC (Incoming material checking)') || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiQualityTeamIQCYearOfExp: [this.avgExperienceInYrs(data.qualityTeams, 'IQC (Incoming material checking)') || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
      fgiQualityTeamInLineIQCPerson: [this.personCount(data.qualityTeams, 'In-Line QC') || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiQualityTeamInLineIQCYearOfExp: [this.avgExperienceInYrs(data.qualityTeams, 'In-Line QC') || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],
      fgiQualityTeamFinalQCPerson: [this.personCount(data.qualityTeams, 'Final QC') || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99999)]],
      fgiQualityTeamFinalQCYearOfExp: [this.avgExperienceInYrs(data.qualityTeams, 'Final QC') || '', [Validators.required, Validators.pattern("^[0-9]*$"), Validators.min(1), Validators.max(99)]],

      processControll: this._formBuilder.array([]),
      processControllForOutSourced: this._formBuilder.array([]),
      qualityTeams: this._formBuilder.array([]),
      inHouseTesting: this._formBuilder.array([]),
      productImage: this._formBuilder.array([]),
    });

    if (this.isAdmin) {
      //factoryFormWithData.controls.fgiRiskLevel.enable();
      //factoryFormWithData.controls.fgiRiskLevel.setValidators([Validators.required]);
    }

    factoryFormWithData.controls.fgiRiskLevel.setValue(data.riskLevel?.toString());
    if (this.isVpsApproved) {
      factoryFormWithData.disable();
      factoryFormWithData.get('fgiChiefDeciMaker').enable();
      factoryFormWithData.get('fgiBusinessContactPerson').enable();
      factoryFormWithData.get('fgiAuditContactPerson').enable();
    }
    return factoryFormWithData;
  }

  getRiskForfactory(index, event) {
    const productImageList = this.employees()
      .at(index).get('productImage');
    let isHighLevel: boolean = false;
    let isAllProductshavefactoryProductType: boolean = true;
    productImageList?.value?.forEach(element => {
      if (!element?.factoryProductType && element?.isFactoryProductSourced) {
        isAllProductshavefactoryProductType = false;
        return;
      }
      else if (!!element?.factoryProductType?.riskLevel && element?.factoryProductType?.riskLevel == 1) {
        isHighLevel = true;
      }
    });

    if (isAllProductshavefactoryProductType) {
      if (isHighLevel) {
        this.employees().at(index).get('fgiRiskLevel').patchValue('1');
      } else {
        this.employees().at(index).get('fgiRiskLevel').patchValue('0');
      }
    }

  }

  public personCount(data: any[], name: string): any {
    let ret = undefined;
    data?.forEach(element => {
      if ((element.qualityTeamName + '').trim().toLowerCase() == (name + '').trim().toLowerCase()) {
        ret = element.personCount;
      }
    });
    return ret;
  }
  public avgExperienceInYrs(data: any[], name: string): any {
    let ret = undefined;
    data?.forEach(element => {
      if ((element.qualityTeamName + '').trim().toLowerCase() == (name + '').trim().toLowerCase()) {
        ret = element.avgExperienceInYrs;
      }
    });
    return ret;
  }

  public addNewFactory() {
    this.factoryList.push(1);
  }

  public isPrevProductImage(): boolean {
    return true;
  }

  onSubmitForm(saveDataToServer) {
    let isAllRequiredData: boolean = true;
    this.vendorGenForm = new vendorGenForm();
    //this.vendorGenForm.code = 'abc';
    this.vendorGenForm.uuid = this.vendorFormData.uuid;
    this.vendorGenForm.name = this.vgiFormGroup.controls['vgiVendorNameEng'].value;
    this.vendorGenForm.addressLine1 = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'vgiVendorAddressEng']).value;
    // this.vendorGenForm.addressLine2 = this.vgiFormGroup.controls['vgiVendorAddresslocal'].value;
    this.vendorGenForm.city = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'city']).value;
    this.vendorGenForm.pincode = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'pincode']).value;
    this.vendorGenForm.country = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'country']).value;
    if (this.vendorGenForm.country == 'Other') {
      this.vendorGenForm.otherCountry = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'otherCountry']).value;
    }
    this.vendorGenForm.state = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'state']).value;

    this.vendorGenForm.decisionMakerName = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'name']).value;
    this.vendorGenForm.decisionMakerTitle = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'title']).value;
    this.vendorGenForm.decisionMakerTelephone = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'telephone']).value;
    this.vendorGenForm.decisionMakerMobile = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'cellphone']).value;
    this.vendorGenForm.decisionMakerEmail = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'email']).value;

    this.vendorGenForm.mainContactName = this.vgiFormGroup.get(['vgiMainContactPerson', 'name']).value;
    this.vendorGenForm.mainContactTitle = this.vgiFormGroup.get(['vgiMainContactPerson', 'title']).value;
    this.vendorGenForm.mainContactTelephone = this.vgiFormGroup.get(['vgiMainContactPerson', 'telephone']).value;
    this.vendorGenForm.mainContactMobile = this.vgiFormGroup.get(['vgiMainContactPerson', 'cellphone']).value;
    this.vendorGenForm.mainContactEmail = this.vgiFormGroup.get(['vgiMainContactPerson', 'email']).value;

    this.vendorGenForm.qualityContactName = this.vgiFormGroup.get(['vgiQualityContactPerson', 'name']).value;
    this.vendorGenForm.qualityContactTitle = this.vgiFormGroup.get(['vgiQualityContactPerson', 'title']).value;
    this.vendorGenForm.qualityContactTelephone = this.vgiFormGroup.get(['vgiQualityContactPerson', 'telephone']).value;
    this.vendorGenForm.qualityContactMobile = this.vgiFormGroup.get(['vgiQualityContactPerson', 'cellphone']).value;
    this.vendorGenForm.qualityContactEmail = this.vgiFormGroup.get(['vgiQualityContactPerson', 'email']).value;
    this.vendorGenForm.website = this.vgiFormGroup.get('vgiWebsite').value;
    this.vendorGenForm.riskLevel = this.vgiFormGroup.get('vgiRiskLevel').value;
    this.vendorGenForm.businessModel = this.vgiFormGroup.get('vgiBusinessModel').value;
    this.vendorGenForm.businessLine = this.vgiFormGroup.get('vgiBusinessLine').value;
    this.vendorGenForm.categories = this.vgiFormGroup.get('vgiCategory').value;
    this.vendorGenForm.companyType = this.vgiFormGroup.get('vgiTypeOfCompany').value == 'Other' ? this.vgiFormGroup.get('vgiTypeOfCompanyOther').value : this.vgiFormGroup.get('vgiTypeOfCompany').value;
    this.vendorGenForm.otherCompanyType = this.vgiFormGroup.get('vgiTypeOfCompany').value == 'Other' ? this.vgiFormGroup.get('vgiTypeOfCompanyOther').value : "";
    this.vendorGenForm.ownershipType = this.vgiFormGroup.get('vgiTypeOfOwnerShip').value == 'Other' ? this.vgiFormGroup.get('vgiTypeOfOwnerShipOther').value : this.vgiFormGroup.get('vgiTypeOfOwnerShip').value;
    this.vendorGenForm.otherOwnerShipType = this.vgiFormGroup.get('vgiTypeOfOwnerShip').value == 'Other' ? this.vgiFormGroup.get('vgiTypeOfOwnerShipOther').value : "";
    this.vendorGenForm.trademark = this.vgiFormGroup.get('vgiTradeMark').value;
    this.vendorGenForm.tradeMarkDetails = this.vgiFormGroup.get('vgiTradeMarkName').value;
    this.vendorGenForm.licenseNo = this.vgiFormGroup.get('vgiFactoryLic').value;
    this.vendorGenForm.licenseNoDetails = this.vgiFormGroup.get('vgiFactoryLicName').value;
    this.vendorGenForm.establishedDate = this.vgiFormGroup.get('vgiDateofEstablish').value;
    this.vendorGenForm.licenseValidDate = moment(this.vgiFormGroup.get('vgiFactoryLicValidDate').value).valueOf() || '';
    // this.vendorGenForm.anyRestrictionOtherVendor = this.vgiFormGroup.get('vgiRestriction').value;
    this.vendorGenForm.lastYearSalesTurnover = this.vgiFormGroup.get('vgiLastFinancialYear').value;
    this.vendorGenForm.currentYearExpectedSalesTurnover = this.vgiFormGroup.get('vgiCurrentYearExpectedSalesTurnover').value;
    this.vendorGenForm.vendorBoardingStatus = this.vendorFormData.vendorBoardingStatus;
    this.vendorGenForm.trainingStatus = this.vendorFormData.trainingStatus;

    let items: any[] = this.vpsFormGroup.value.vpsFormDataArray;
    let exists = items.some(a => a.vpsProductCategManuf ? true : false);

    if (exists) {
      let vpsFormGroupItems: productTypes[] = [];
      items.forEach(item => {
        if (item.vpsProductCategManuf) {
          let obj: productTypes = {
            productTypeCategoryName: item.vpsProductCategManuf.category,
            productTypeCategoryUid: item.vpsProductCategManuf.uuid,
            productIncludedInCategory: item.vpsProductIncInEachCateg,
            plannedForSourcing: item.isSelected == true ? 1 : 0,
            experienceInYrs: item.vpsYearExp,
            percentOfBusiness: item.vpsPercentageOfYourBuss,
            uuid: item.idValue
          }
          vpsFormGroupItems.push(obj);
        }
      });
      this.vendorGenForm.productTypes = vpsFormGroupItems;
    }


    let itemsBestSelling: any[] = this.vpsBestSellingFormGroup.value.vpsBestSellingFormDataArray;
    let existsBestSelling = itemsBestSelling.some(a => a.vpsMainCust ? true : false);

    if (existsBestSelling) {
      let vpsBestSellingItems: customers[] = [];
      itemsBestSelling.forEach(item => {
        if (item.vpsMainCust) {
          let obj: customers = {
            customerName: item.vpsMainCust,
            productDescription: item.vpsProductDesc,
            country: item.vpsCountry,
            qtyShipped: item.vpsQtyShipped,
            uuid: item.idValue
          }
          vpsBestSellingItems.push(obj);
        }
      });
      this.vendorGenForm.customers = vpsBestSellingItems;
    }

    let itemsFact: any[] = this.empForm.value.employees;
    let existsFact = itemsFact.some(a => a.fgiFactoryNameEng ? true : false);

    if (existsFact) {
      let empFormItems: factories[] = [];
      itemsFact.forEach(item => {
        if (item.fgiFactoryNameEng) {
          const factoryName = item.fgiFactoryNameEng;
          // for process realted data in factory form
          // processControllForOutSourced
          let itemsprocesses: any[] = item.processControll.concat(item.processControllForOutSourced);
          let factoryprocessesItems: processes[] = [];

          itemsprocesses.forEach(item => {
            if (saveDataToServer && isAllRequiredData && item.inHouseProcessName == 'Other' && item.inHouseProcessNameOther == '') {
              isAllRequiredData = false;
              this._commonService.warning('Please fill Process Name for factory : ' + factoryName);
              return;
            }

            let obj: processes = {
              processType: item.processType,
              processName: item.inHouseProcessName,
              processNameOther: item.inHouseProcessNameOther,
              keyOperations: item.inHouseKeyOperation,
              uuid: item.uuid
            }
            if (!!obj.processName || !!obj.keyOperations) {
              factoryprocessesItems.push(obj);
            }

            let obj2: processes = {
              processName: item.outSourcedProcessName,
              keyOperations: item.outSourcedKeyOperation,
            }
            if (obj2.processName) {
              factoryprocessesItems.push(obj2);
            }
          });
          if (factoryprocessesItems?.length == 0 && saveDataToServer && isAllRequiredData) {
            isAllRequiredData = false;
            const dialogRef = this.dialog.open(WarningDialogBoxComponent, {
              width: '50%',
              data: { message:'Please add at-least one Process Controlled Details(either In-House or Outsourced) for factory : ' + factoryName ,header:'Warning' },

            });
          }
          if (saveDataToServer && isAllRequiredData) {
            factoryprocessesItems.forEach((objItem) => {
              if ((!objItem.keyOperations || !objItem.processName) && isAllRequiredData) {
                isAllRequiredData = false;
                const dialogRef = this.dialog.open(WarningDialogBoxComponent, {
                  width: '50%',
                  data: { message:'Please fill all required fields in Process Controlled Details section for factory : ' + factoryName ,header:'Warning' },
    
                });
                return;
              }
            })
          }
          // for process realted data in factory form


          // for quqlityTeam realted data in factory form
          let itemsQualityTeam: any[] = item.qualityTeams;
          let factoryQulaityTeamItems: qualityTeams[] = [];
          let obj1: qualityTeams = {
            qualityTeamName: "Quality Engineer",
            personCount: item.fgiQualityTeamEngineerPerson,
            avgExperienceInYrs: item.fgiQualityTeamEngineerYearOfExp
          }
          if (obj1.personCount) {
            factoryQulaityTeamItems.push(obj1);
          }

          let obj2: qualityTeams = {
            qualityTeamName: "IQC (Incoming material checking)",
            personCount: item.fgiQualityTeamIQCPerson,
            avgExperienceInYrs: item.fgiQualityTeamIQCYearOfExp
          }
          if (obj2.personCount) {
            factoryQulaityTeamItems.push(obj2);
          }

          let obj3: qualityTeams = {
            qualityTeamName: "In-Line QC",
            personCount: item.fgiQualityTeamInLineIQCPerson,
            avgExperienceInYrs: item.fgiQualityTeamInLineIQCYearOfExp
          }
          if (obj3.personCount) {
            factoryQulaityTeamItems.push(obj3);
          }

          let obj4: qualityTeams = {
            qualityTeamName: "Final QC",
            personCount: item.fgiQualityTeamFinalQCPerson,
            avgExperienceInYrs: item.fgiQualityTeamFinalQCYearOfExp
          }
          if (obj4.personCount) {
            factoryQulaityTeamItems.push(obj4);
          }

          itemsQualityTeam.forEach(item => {
            let obj: qualityTeams = {
              qualityTeamName: item.qualityTeamName,
              personCount: item.personCount,
              avgExperienceInYrs: item.avgExperienceInYrs
            }
            if (obj.personCount) {
              factoryQulaityTeamItems.push(obj);
            }
          });
          // for quqlityTeam realted data in factory form


          // for inhouseTestings realted data in factory form
          let itemsinhouseTestings: any[] = item.inHouseTesting;
          let factoryinhouseTestingsItems: inhouseTestings[] = [];
          itemsinhouseTestings.forEach(item => {
            let obj: inhouseTestings = {
              testName: item.typeOfTest,
              // scales: item.scales,
              // equipment: item.equipment,
              standardTest: item.testOfStandard,
              uuid: item.uuid
            }
            if (obj.testName)
              factoryinhouseTestingsItems.push(obj);
          });
          // for inhouseTestings realted data in factory form

          // for productTypes realted data in factory form
          let itemsproductTypes: any[] = item.productImage;
          let factoryproductTypesItems: facProductTypes[] = [];
          itemsproductTypes.forEach(item => {
            let obj: facProductTypes = {
              productName: item.name,
              productTypeUid: item.factoryProductType?.uuid,
              productTypeName: item.factoryProductType?.name,
              productTypeCategoryUid: item.factoryProductCategory.uuid,
              productTypeCategoryName: item.factoryProductCategory.category,
              description: item.desc,
              plannedForSourcing: item.isFactoryProductSourced == true ? 1 : 0,

              productImageUrl: item.imageName,
              uuid: item.uuid,
              factoryUid: item.factoryUid

            }
            if (obj.productName)
              factoryproductTypesItems.push(obj);
          });
          // for productTypes realted data in factory form

          let obj: factories = {
            // productTypeCategoryName: item.vpsProductCategManuf.trim(),
            name: item.fgiFactoryNameEng,
            ownership: item.fgiFactoryOwnership,
            addressLine1: item.fgiFactoryAddressEng,
            city: item.fgiFactoryFullAddressEng.city,
            country: item.fgiFactoryFullAddressEng.country,
            otherCountry: item.fgiFactoryFullAddressEng.country == 'Other' ? item.fgiFactoryFullAddressEng.otherCountry : '',
            pincode: item.fgiFactoryFullAddressEng.pincode,
            state: item.fgiFactoryFullAddressEng.state,
            riskLevel: (item.fgiRiskLevel == 1 || item.fgiRiskLevel == '1' || item.fgiRiskLevel == 0 || item.fgiRiskLevel == '0') ? item.fgiRiskLevel : undefined,
            riskLevelText: (item.fgiRiskLevel == 1 || item.fgiRiskLevel == '1') ? "HIGH" : (item.fgiRiskLevel == 0 || item.fgiRiskLevel == '0' ? 'LOW' : undefined),

            uuid: item.fgiUid,

            decisionMakerName: item.fgiChiefDeciMaker.name,
            decisionMakerTitle: item.fgiChiefDeciMaker.title,
            decisionMakerTelephone: item.fgiChiefDeciMaker.telephone,
            decisionMakerMobile: item.fgiChiefDeciMaker.cellphone,
            decisionMakerEmail: item.fgiChiefDeciMaker.email,

            mainContactName: item.fgiBusinessContactPerson.name,
            mainContactTitle: item.fgiBusinessContactPerson.title,
            mainContactTelephone: item.fgiBusinessContactPerson.telephone,
            mainContactMobile: item.fgiBusinessContactPerson.cellphone,
            mainContactEmail: item.fgiBusinessContactPerson.email,

            qualityContactName: item.fgiAuditContactPerson.name,
            qualityContactTitle: item.fgiAuditContactPerson.title,
            qualityContactTelephone: item.fgiAuditContactPerson.telephone,
            qualityContactMobile: item.fgiAuditContactPerson.cellphone,
            qualityContactEmail: item.fgiAuditContactPerson.email,

            isoCertificationNumber: item.fgiIsoCertificate,
            isoCertificationDate: moment(item.fgiIsoDateOfCertificate).valueOf(),
            capacityPerMonth: item.fgiFactoryCapacityperMonth,
            availableCapacityPerMonth: item.fgiFactoryAvailVolume,
            workerCount: item.fgiFactoryworkerCount,
            comments: item.fgiFactorycomments,
            hasDedicatedQualityTeam: item.fgiDepForQualityControl == "YES" ? 1 : 0,
            hasDedicatedProductDevTeam: item.fgiDepForProductDev == "YES" ? 1 : 0,
            productQualityAudit: item.fgiProductTestingMaintaince == "YES" ? 1 : 0,

            processes: factoryprocessesItems,
            inhouseTestings: factoryinhouseTestingsItems,
            productTypes: factoryproductTypesItems,
            qualityTeams: factoryQulaityTeamItems,
            email: item.fgiAuditContactPerson.email,
            altemail: item.fgiBusinessContactPerson.email
          }
          empFormItems.push(obj);
        }
      });

      this.vendorGenForm.factories = empFormItems;
    }

    let vpfFormData = Util.clone(this.vendorGenForm);
    vpfFormData['loggedInUserUuid'] = this._authService.getLoggedInUser().uuid;
    vpfFormData['setTime'] = new Date().getTime();
    
    localStorage.setItem('vpf-form-data', JSON.stringify(vpfFormData));
    if (!isAllRequiredData) {
      return;
    }
    if (saveDataToServer) {
        this._dashboardService.submitVendorForm(this.vendorGenForm).subscribe(res => {
          this.processResponse(res);
          if (this.isApproveBtn) {
            this._dashboardService.approveVendorRequest(this.vendorFormData.uuid).subscribe(res => {
              if (res.code == 200) {
                this.toaster.success(res.message);
              } else {
                this.toaster.warning(res.message)
              }
            });
          }
          //this._router.navigate(['/home']);
        });
    }

  }

  
  private createZip(factoryUid: string): void {
    var zip = new JSZip();
    var count = 0;
    var zipFilename = "products.zip";
    const parentFolder = zip.folder("products");
    this.productImages.forEach(droppedFile => {
      parentFolder.file(droppedFile.name, droppedFile);
      count++;
      if (count == this.productImages.length) {
        zip.generateAsync({ type: 'blob' }).then((content) => {
          const fd = new FormData();
          fd.append("file", content, zipFilename);
          this._saleOrderService.uploadProductImagesZip(factoryUid, fd).subscribe((result) => {
            if (result?.code?.toLowerCase() != "success")
             {}
          });
        });
      }
    });
  }

  public approveRejectFactoryStatus(factoryData: any, status: string) {
    if (factoryData?.factoryScores?.overallScore === undefined) {
      this.toaster.warning("Please complete Self Assessment.")
      return;
    }

    this._dashboardService.approveRejectFactoryRequest(factoryData.uuid, status).subscribe(res => {
      if (res.code == 200) {
        this.toaster.success(res.message);
      } else {
        this.toaster.warning(res.message)
      }
    }), (err: any) => {
      this._commonService.handleError(err);
    }
  }

  public checkForFormValuesChanges(): void {
    this.subscription.add(this.vgiFormGroup.valueChanges
      .pipe(
        // tap((c) => console.log('vgiFormGroup data changes')),
        debounceTime(1000),
        distinctUntilChanged(),
      )
      .subscribe(() => {
        this.onSubmitForm(false);
      }));

    this.subscription.add(this.vpsFormGroup.valueChanges
      .pipe(
        // tap((c) => console.log('vpsFormGroup data changes')),
        debounceTime(1000),
        distinctUntilChanged(),
      )
      .subscribe(() => {
        this.onSubmitForm(false);
      }));

    this.subscription.add(this.empForm.valueChanges
      .pipe(
        // tap((c) => console.log('empForm data changes')),
        debounceTime(1000),
        distinctUntilChanged(),
      )
      .subscribe(() => {
        this.onSubmitForm(false);
      }));

    this.subscription.add(this.vpsBestSellingFormGroup.valueChanges
      .pipe(
        // tap((c) => console.log('vpsBestSellingFormGroup data changes')),
        debounceTime(1000),
        distinctUntilChanged(),
      )
      .subscribe(() => {
        this.onSubmitForm(false);
      }));
  }

  public fetchVendorFormData(uuid) {
    let localStorageVpfData = JSON.parse(localStorage.getItem('vpf-form-data'));
    if (localStorageVpfData?.loggedInUserUuid && this._authService.getLoggedInUser().uuid != localStorageVpfData?.loggedInUserUuid) {
      //console.log('clear ho gaya data');
      localStorage.removeItem('vpf-form-data');
      localStorageVpfData = {};
    }
    this._dashboardService.getVendorFormData(uuid).subscribe(res => {
      let vendorFormDataResponse = res;
      this.floatInfoSectionData = {
        createdBy: res.createdBy,
        createdTime: res.createdTime,
        modifiedBy: res.modifiedBy,
        modifiedTime: res.modifiedTime
      }
      if (Object.keys(vendorFormDataResponse).length != 0) {

        if (localStorageVpfData == null || localStorageVpfData == undefined || Object.keys(localStorageVpfData).length == 0 ) {

          // console.log('backend have data and local empty')
          localStorage.removeItem('vpf-form-data');
          let vpfFormData = Util.clone(vendorFormDataResponse);
          vpfFormData['loggedInUserUuid'] = this._authService.getLoggedInUser().uuid;
          vpfFormData['setTime'] = vendorFormDataResponse.modifiedTime;
          localStorage.setItem('vpf-form-data', JSON.stringify(vpfFormData));

        } else if (localStorageVpfData && Object.keys(localStorageVpfData).length != 0) {
          if (localStorageVpfData.setTime && localStorageVpfData.setTime > vendorFormDataResponse.modifiedTime) {
            //console.log('backend have data and local not empty, but local have recent time')
            vendorFormDataResponse = localStorageVpfData;
          } else {

            // console.log('backend have data and local not empty, but local have old time')
            localStorage.removeItem('vpf-form-data');
            let vpfFormData = Util.clone(vendorFormDataResponse);
            vpfFormData['loggedInUserUuid'] = this._authService.getLoggedInUser().uuid;
            vpfFormData['setTime'] = vendorFormDataResponse.modifiedTime
            localStorage.setItem('vpf-form-data', JSON.stringify(vpfFormData));

          }
        }

      } else if (Object.keys(vendorFormDataResponse).length == 0 && localStorageVpfData && Object.keys(localStorageVpfData).length != 0) {
        //console.log('backend have no data but local have')
        vendorFormDataResponse = localStorageVpfData;
      }
      //this.fetchCategoriesProductTypes();
      if (this.isVendor && vendorFormDataResponse.trainingStatus?.toLowerCase() != 'completed') {
        this.isTrainingCompleted = false;
        this.toaster.error("Please complete Training to access Vendor Profile Form.")
      } else {
        this.vendorFormData = vendorFormDataResponse;
        if (res.factories?.length > 0) {
          this.processFactoryScores(res.factories);
        }
        if (this.vendorFormData?.vendorBoardingStatus
          && (this.vendorFormData?.vendorBoardingStatus == 'ACTIVE'
            || this.vendorFormData?.vendorBoardingStatus == 'PRE-PRODUCTION'
            || this.vendorFormData?.vendorBoardingStatus == 'VENDOR APPROVED')) {
          this.isVpsApproved = true;
          this.vendorApprovalStatus = true;
          if (this.vendorFormData?.vendorBoardingStatus == 'VENDOR APPROVED')
            this.isApproved = true
        }

        if (this.vendorFormData?.name != null) {
          this.alreadyFilledForm = true;
        }
        this.initlizeForms();
      }
    }, (err: any) => {
      this._commonService.handleError(err);
      this.initlizeForms();
    });
  }

  private processFactoryScores(factoryList: factories[]): void {
    this.factoryScoreList = [];
    factoryList.forEach((factory) => {
      let scoresData = factory?.factoryScores?.find((item) => item.auditTypeUid == this.auditTypeUidForFactoryScore && item.factoryUid == factory.uuid)
      if (scoresData != undefined) {
        scoresData['sectionScores'] = JSON.parse(scoresData.sectionScores);
        scoresData['factoryName'] = factory.name;
        scoresData['checkPointCategoryScores'] = scoresData?.sectionScores?.checkPointCategoryScores;
        this.factoryScoreList.push(scoresData);
      } else if (scoresData == undefined) {
        this.factoryScoreList.push({
          factoryName: factory.name,
          factoryUid: factory.uuid,
          checkPointCategoryScores: []
        });
      }

    })
    if (this.factoryScoreList.some((x) => x.checkPointCategoryScores.length > 0)) {
      this.isAnyFactoryHavingScore = true;
    } else
      this.isAnyFactoryHavingScore = false;
  }

  processResponse(data) {

    if (data.uuid != null) {
      data.factories.forEach(item => {
        this.createZip(item.uuid);
      });
      //this.toaster.success(data.message);
      this.toaster.success("Vendor Created Successfully.");
      this._dashboardService.getVendorFormData(this.vendorUid).subscribe(res => {
        if (res.factories?.length > 0) {
          this.disabledSubmitButton = true;
          this.processFactoryScores(res.factories);

          localStorage.removeItem('vpf-form-data');
          let vpfFormData = Util.clone(res);
          vpfFormData['loggedInUserUuid'] = this._authService.getLoggedInUser().uuid;
          vpfFormData['setTime'] = res.modifiedTime;
          vpfFormData['hello'] = 'lll';
          
          localStorage.setItem('vpf-form-data', JSON.stringify(vpfFormData));
          this.vendorFormData= Util.clone(vpfFormData)
          
          this.openFactoryListSaqDialog(res);
        }
      })

    }
    else {
      this.toaster.warning(data.message);
    }

  }

  public openFactoryListSaqDialog(res: any): void {
    console.log("res.factories",res.factories)
    let isAllFactoryHaveScore:any=true;
    res.factories.forEach(element => {
       if(element.factoryScores==undefined || element.factoryScores==null){
       
        isAllFactoryHaveScore=false;
       }
    });
    if(isAllFactoryHaveScore==false && this.isVendor){
      console.log('fgf');
    this.indexOfStepper=3;
    }else{
      console.log('fgffffff');
      this._router.navigate(['/home']);
    }
    // const dialogRef = this.dialog.open(FactorySaqDialogComponent, {
    //   data: { vendorFormData: res, factoryScoreList: this.factoryScoreList },
    //   disableClose: true,
    // });

    // dialogRef.afterClosed().subscribe(result => {
    //   if (result) {
    //     this.navigationOnCloseVendor();
    //     this.cd.detectChanges();
    //   }
    // });
  }

  public closeAddVendor(removeLocalVpfData: boolean): void {
    if (removeLocalVpfData) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'Any entered data will be lost, are you sure to proceed?',
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          localStorage.removeItem('vpf-form-data');
          this.navigationOnCloseVendor();
        }
      });
    } else {
      this.navigationOnCloseVendor();
    }
  }

  public navigationOnCloseVendor(): void {
    if ((this.tenantConfig.role == 'ADMIN' || this.tenantConfig.role == 'SOURCING' || this.tenantConfig.role.includes("OPERATIONS") || this.isTestUser) && this._commonService.getTenantUid() == 'amazon') {
      if (this.isViewMode) {
        this._router.navigate(['master/vendor-list']);
      } else {
        this._router.navigate(['master/vendor-request']);
      }

    }
    if (this.tenantConfig.role == 'VENDOR' && this._commonService.getTenantUid() == 'amazon') {
      this._router.navigate(['/home']);
    }
  }

  // public addRevisionData(event): void {
  //   const target = event.target || event.srcElement || event.currentTarget;
  //   if((target.nodeName == 'LABEL' && target.htmlFor == 'cd') || target.nodeName == 'TH') {
  //     if(this.revisionData.indexOf(target.innerText) === -1) {
  //       this.revisionData += '\n' + target.innerText;
  //     }
  //     this.revisionData = this.revisionData.trimStart()
  //   }
  // }

  public submitReview(): void {
    const discussionRequest: QiDiscussionHistory = {
      discussion: this.revisionData,
      recordUid: this.vendorFormData.uuid,
      pageName: 'VENDOR'
    }
    this._dashboardService.submitDiscussion(discussionRequest).subscribe(res => {
      if (res.uuid != null) {
        this.toaster.success(res.message);
        this.revisionData = ''
        this.fetchDiscussionHistory(this.vendorFormData.uuid);
      } else {
        this.toaster.warning(res.message);
      }
    }), (err: any) => {
      this._commonService.handleError(err);
    }
  }

  private fetchDiscussionHistory(recordUid): void {
    this._dashboardService.getAllDiscussion(recordUid).subscribe(res => {
      if (res.length > 0) {
        this.discussionHistories = res;
      }
    })
  }

  public initiateFQA(): void {
    this._dashboardService.initiateFQA(this.vendorFormData.uuid).subscribe(res => {
      if (res.code == 200) {
        this.toaster.success(res.message);
        this._router.navigate(['/home']);
      } else {
        this.toaster.warning(res.message)
      }
    });
  }

  public approve(): void {
    let isFormComplete = true;
    let factoryCompleteness = false;
    let factoryCount = 0
    this.isApproveBtn = true;
    this.vendorFormData.factories?.forEach(f => {
      if (f?.factoryScores != undefined) {
        if (f?.factoryScores[0]?.overallScore != null) {
          factoryCount++;
        }
      }
    });
    if (this.vendorFormData.factories?.length == factoryCount) factoryCompleteness = true;

    //Every factory
    let factoryForm = this.empForm.get('employees') as FormArray;

    factoryForm.controls.forEach(element => {
      let singleForm = element as FormGroup;
      if (isFormComplete && singleForm.controls.fgiRiskLevel.value != undefined && singleForm.controls.fgiRiskLevel.value != '') {
        isFormComplete = true;
      }
      else {
        isFormComplete = false;
        if (!isFormComplete) {
          this.toaster.warning("Factory(s) Risk Level is not defined.Please choose risk level for each factory.");

        }
        return;
      }
    });

    if (!isFormComplete) {
      return;
    }

    if (this.vendorFormData.vendorBoardingStatus == "REQUEST RAISED") {
      this.toaster.warning("Vendor needs to fill VPF to Approve.");
      return;
    }

    if (!factoryCompleteness) {
      this.toaster.warning("Please Complete Factory(s) Self Assessment Questionaries (SAQ).");
      return;
    }

    this.onSubmitForm(true);


    // if(this.vpsFormGroup.valid){
    //   this.vpsFormGroup.controls.vpsFormDataArray.value.forEach(element => {
    //     if(element.vpsProductTypes != ""){
    //       isFormComplete = true;
    //     }
    //   });
    // }
    //if(this.vgiFormGroup.controls.vgiRiskLevel.value == undefined) isFormComplete = false;


  }

  public ngOnDestroy(): void {
    clearTimeout(this.timeoutIdForNewEmployee);
    this.subscription.unsubscribe();
  }

  public fetchPictureUrl(factoryUid: string, imageName: string): any {
    if (!!factoryUid && !!imageName) {
      this._saleOrderService.getProductImage(factoryUid, imageName).subscribe(data => {
        const urlCreator = window.URL;
        if (this.factoryProductImages[factoryUid] == null)
          this.factoryProductImages[factoryUid] = [];
        this.factoryProductImages[factoryUid][imageName] = data ? this._sanitizer.bypassSecurityTrustUrl(urlCreator.createObjectURL(data)) : '';
      }, (err: any) => {
        this._commonService.handleError(err);
      });
    }
  }

  public fetchImageByName(factoryIndex: number, productIndex: number): any {
    let factoryForm = this.empForm.get('employees') as FormArray;
    const factoryUid = factoryForm.controls[factoryIndex]['controls']['fgiUid']?.value;
    const imageName = this.factoryProductImage(factoryIndex).controls[productIndex]['controls'].imageName?.value;
    if (this.factoryProductImages[factoryUid])
      return this.factoryProductImages[factoryUid][imageName];
    else
      return null;
  }

  public showSectionInfo(sectionName: string): void {
    const dialogRef = this.dialog.open(WarningDialogBoxComponent, {
      width: '50%',
      data: { message:VpfInfo[sectionName]?.title || 'Hi!!' ,header:VpfInfo[sectionName]?.Section || 'Info' },

    });
  }
}
