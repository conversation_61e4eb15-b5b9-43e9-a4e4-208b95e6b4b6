import { DatePipe } from '@angular/common';
import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { FilterList } from 'app/core/qinspect/_model/filterList.model';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { CustomDateTimeTooltipComponent } from 'app/shared/custom-date-time-tooltip/custom-date-time-tooltip.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { environment } from 'environments/environment';

import { ScheduleHistoryComponent } from './schedule-history/schedule-history.component';

export enum scheduleStatus {
  DELETED = -1,
  SCHEDULED = 0,
  SUBMITTED = 1,
  ABORTED = 2,
  REVOKED = 3,
  FAILED = 4,
  DRAFT = 5,
  DRAFT_COMPLETED = 6,
  DRAFT_FROM_SAP = 7,
  EXPIRED = 8,
  SAVE_OR_UPDATE = 9,
  PREVIEW = 10,
  STARTED = 11,
  CONFIRMED = 12,
  REVIEWED = 13,
  PARTIALLY_SUBMITTED = 14,
  EDITED = 15,//for Godrej done post Submit
  DIVERSION = 16, //for Godrej done post Submit
  L1_REVIEW = 17, // for audit review process
  L1_REVIEWED = 18,// for audit review process
  L2_REVIEW = 19, // for audit review process
  L2_REVIEWED = 20, // for audit review process
  AUDITOR_SUBMITTED = 23, // for audit review process
}

@Component({
  selector: 'app-inspection-schedules',
  templateUrl: './inspection-schedules.component.html',
  styleUrls: ['./inspection-schedules.component.scss']
})
export class InspectionSchedulesComponent extends BaseTableSharedComponent {
  //declarations:
  scheduleList: any[] = [];
  columnDefs: any[] = [
    // {
    //   field: '',
    //   // headerCheckboxSelection: true,
    //   checkboxSelection: true,
    //   showDisabledCheckboxes: true,
    //   width: 52,
    //   minWidth: 52,
    // },
    {
      headerName: "Date", field: "auditDateText", sortable: true, unSortIcon: true, minWidth: 130,
      width: 130,
      valueGetter: (data) => {
        // this._datepipe.transform( data?.data?.auditDateText , 'd-MM-yyyy , hh:mm a')
        return data?.data?.auditDateText

      },
    },

    {
      headerName: "Test Engineer", field: "auditor.fullName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150
    },
    {
      headerName: "Result", field: "result", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150
    },
    {
      headerName: "Status", field: "statusText", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150
    },
    {
      headerName: "Submitted Date",
      field: "submittedDateText",
      sortable: true, unSortIcon: true,
      minWidth: 165,
      width: 165,
    },
    {
      headerName: "Audit ID", field: "auditId", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 130,
    },
    // {
    //   headerName: "Design Code", field: "designcodes", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150,
    //   valueGetter: (data) => {
    //     if (data?.data?.designcodes && data?.data?.designcodes.length > 0)
    //       return data?.data?.designcodes[0]?.designcode;
    //   },
    //   getQuickFilterText: params => {
    //     if (params?.data?.designcodes && params?.data?.designcodes.length > 0)
    //       return params?.data?.designcodes[0]?.designcode;
    //   }
    // },

    {
      headerName: "Factory", field: "factory.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 200, width: 200
    },
    {
      headerName: "Vendor", field: "vendor.firstName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 200, width: 200
    },

    // {
    //   headerName: "PO No.", field: "purchaseOrderNo", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
    // },
    // {
    //   headerName: "PO Line Item", field: "purchaseOrderItem", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 300, width: 300,

    // },
    // {
    //   headerName: "Source ID", field: "sourceScheduleUid", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 300, width: 300,
    //   hide: this.shouldHide()
    // },


    {
      headerName: 'Actions',
      pinned: 'right',
      minWidth: 200,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: (params) => {
        return { actions: this.prepareActionIconButtons(params.data) }
      }
    }
  ];
  filtersList: FilterList[] = [
    { id: "fromDate", type: 'date', label: 'From', defaultValue: this.defaultfromDate, value: this.defaultfromDate },
    { id: "toDate", type: 'date', label: 'To', defaultValue: this.defaultToDate, value: this.defaultToDate },
  ];
  title = "Inspection Schedules"
  type: string = "create";
  status: string = 'create';
  showSubmitAudit: boolean = false;
  submitTypeFromPopup: number;
  showAdd: boolean = true;
  public init(): void {
    if (this._commonService.getTenantUid() == 'amazon') {
      this.showAdd = false;
    }
    this.defaultfromDate.setDate(this.defaultToDate.getDate() - 2);
    this.setDates(this.defaultfromDate, this.defaultToDate);
    this.selectedData = {};
    this.showTable = true;
    this.showAddEdit = false;
    this.showSubmitAudit = false;
    this.updateView();
    this.route.paramMap.subscribe(
      (routeParam) => {
        this.type = routeParam.get("type");
        this.serviceInvocation();
        this.updateView()
        this.changeDetectorRef.detectChanges();
      },
      (err) => { }
    );

    //this.serviceInvocation();
    // if (environment.tenantUid == 'lots') {
    //   this.columnDefs.splice(3, 0, {
    //     headerName: "Audit Id", field: "auditId", sortable: true, unSortIcon:true, filter: 'agTextColumnFilter', 
    //     autoHeight: false,
    //   });

    //   this.columnDefs.splice(4, 1);

    //   this.columnDefs.splice(4, 0, {
    //     headerName: "Store", field: "factory.name", sortable: true, unSortIcon:true, filter: 'agTextColumnFilter', 
    //     autoHeight: false,
    //   });

    // } else {
    //   this.columnDefs.splice(2, 0,
    //     {
    //       headerName: "Style code",
    //       field: "_designcode.designcode",
    //       sortable: true, unSortIcon:true,
    //       autoHeight: false,
    //     },

    //   );
    //   this.columnDefs.splice(5, 0,
    //     {
    //       headerName: "Result",
    //       field: "result",
    //       sortable: true, unSortIcon:true,
    //       autoHeight: false,
    //     },

    //   )
    // }
    if (this._commonService.getTenantUid() == 'godrej') {
      this.columnDefs = [
        // {
        //   field: '',
        //   // headerCheckboxSelection: true,
        //   checkboxSelection: true,
        //   showDisabledCheckboxes: true,
        //   width: 52,
        //   minWidth: 52,
        // },
        {
          headerName: "Audit Date", field: "auditDateText", sortable: true, unSortIcon: true, minWidth: 180,
          width: 165,
        },
        {
          headerName: "Status", field: "statusText", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150
        },
        {
          headerName: "Submitted Date",
          field: "submittedDateText",
          sortable: true, unSortIcon: true,
          minWidth: 165,
          width: 165,
        },
        {
          headerName: "Auditor", field: "auditor.fullName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150
        },
        {
          headerName: "S.O.No", field: "extraSchedulesFiledList", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
          autoHeight: false,
          cellRenderer: (data) => {
            let object = data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('s.o'));
            return object[0]?.optionValue || '-';
          },
          getQuickFilterText: params => {
            let object = params.data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('s.o'));
            return object[0]?.optionValue || '-';
          }
        },
        {
          headerName: "Project No", field: "extraSchedulesFiledList.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
          autoHeight: false,
          cellRenderer: (data) => {
            let object = data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('project'));
            return object[0]?.optionValue || '-';
          },
          getQuickFilterText: params => {
            let object = params.data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('project'));
            return object[0]?.optionValue || '-';
          }
        },
        {
          headerName: "Customer Name", field: "extraSchedulesFiledList.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
          autoHeight: false,
          cellRenderer: (data) => {
            let object = data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('customers'));
            return object[0]?.optionValue || '-';
          },
          getQuickFilterText: params => {
            let object = params.data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('customers'));
            return object[0]?.optionValue || '-';
          }
        },
        {
          headerName: "Body Sr. No.", field: "extraSchedulesFiledList.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
          autoHeight: false,
          cellRenderer: (data) => {
            let object = data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('body sr'));
            return object[0]?.optionValue || '-';
          },
          getQuickFilterText: params => {
            let object = params.data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('body sr'));
            return object[0]?.optionValue || '-';
          }
        },
        {
          headerName: "Model", field: "extraSchedulesFiledList.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
          autoHeight: false,
          cellRenderer: (data) => {
            let object = data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('model'));
            return object[0]?.optionValue || '-';
          },
          getQuickFilterText: params => {
            let object = params.data.extraScheduleFieldsList?.filter(item => item.shortQuestion?.toLowerCase().includes('model'));
            return object[0]?.optionValue || '-';
          }
        },
        {
          headerName: "Audit Type", field: "auditType.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
        },
        {
          headerName: "Audit ID", field: "auditId", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 300, width: 300,
        },
        {
          headerName: "Child ID", field: "childSeqId", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.shouldHide()
        },
        {
          headerName: "Parent ID", field: "parentScheduleUid", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 300, width: 300,
          hide: this.shouldHide()
        },
        {
          headerName: "Source ID", field: "sourceScheduleUid", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 300, width: 300,
          hide: this.shouldHide()
        },
        {
          headerName: "Design Code", field: "designcodes", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150,
          valueGetter: (data) => {
            if (data?.data?.designcodes && data?.data?.designcodes.length > 0)
              return data?.data?.designcodes[0]?.designcode;
          },
          getQuickFilterText: params => {
            if (params?.data?.designcodes && params?.data?.designcodes.length > 0)
              return params?.data?.designcodes[0]?.designcode;
          }
        },
        {
          headerName: "Factory", field: "factory.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 200, width: 200
        },
        {
          headerName: "Vendor", field: "vendor.firstName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 200, width: 200
        },
        {
          headerName: 'Actions',
          pinned: 'right',
          minWidth: 200,
          cellRendererFramework: CellActionComponent,
          cellRendererParams: (params) => {
            return { actions: this.prepareActionIconButtons(params.data) }
          }
        }
      ]
    }
    if (this._commonService.getTenantUid() == 'titan') {
      this.prepareTableColumns();
    }
  }

  shouldHide() {
    if (this._commonService.getTenantUid() == 'godrej') {
      return false;
    } else {
      return true;
    }
  }

  private updateView() {
    if (this.type == "testing") {
      this.title = "Lab Schedules"
    } else if (this.type == "fqa") {
      this.title = "FQA Schedules"
    } else if (this.type == "inspection") {
      this.title = "Inspection Schedules"
    }
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Audit Date", field: "auditDate", sortable: true, unSortIcon: true, minWidth: 100, filter: 'agTextColumnFilter',
        cellRendererFramework: CustomDateTimeTooltipComponent,
        cellRendererParams: (params: ICellRendererParams) => params.data,
      },
      {
        headerName: "PO Number", field: "purchaseOrderNo", sortable: true, unSortIcon: true, minWidth: 100, filter: 'agTextColumnFilter',
      },
      {
        headerName: "SKU", field: "designCode", sortable: true, unSortIcon: true, minWidth: 250, filter: 'agTextColumnFilter',
        valueGetter: params => params.data?.auditDesignCodes?.map(item => item.designcode).toString(),
      },
      {
        headerName: "Ordered Qty", field: "vendor.firstName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', maxWidth: 150,

        valueGetter: params => params.data?.auditDesignCodes?.reduce((a, b) => a + (b.orderQty ? b.orderQty : 0), 0)
      },
      {
        headerName: "Vendor", field: "vendor.firstName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', maxWidth: 150,
      },
      {
        headerName: "Brand", field: "brand.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', maxWidth: 150,
      },
      {
        headerName: "Factory", field: "factory.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', maxWidth: 150,
      },
      {
        headerName: "Audit Type", field: "auditType.name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150,
      },
      {
        headerName: "Auditor", field: "auditor.fullName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', maxWidth: 150,
      },
      {
        headerName: "Status", field: "statusText", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', maxWidth: 150,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 100,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data) }
        }
      }
    ]
  }

  private serviceInvocation(): void {
    APP_UI_CONFIG.schedules.getSchedules.params = this.prepareParameters();
    this.subscription.add(
      this.invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getSchedules, this.prepareParameters(), null, this.prepareParameters())
        .subscribe({
          next: response => {
            if (this.user.tenantUid == 'amazon') {
              this.scheduleList = response?.filter((item) => item.type?.toLowerCase() == this.type);
            } else {
              this.scheduleList = Util.clone(response)
            }
          },
          error: error => {
            this._commonService.handleError(error);
          }
        }))
  }

  public getFilterList(event: any): void {
    let filterData: FilterList[] = event;
    filterData.forEach(item => {
      if (item.id == "fromDate") {
        this.FromToDate.from = item.value;
      }
      if (item.id == "toDate") {
        this.FromToDate.to = item.value;
      }
    })
    this.serviceInvocation();
  }

  public getOnGetDate(event: any): void {
    this.FromToDate.from = event.from;
    this.FromToDate.to = event.to;
    this.serviceInvocation();
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];
    iconsList.push({ type: 'history', title: "History", icon: 'history', data: row });
    // if(row.statusText =='Deleted' || row.statusText =='Aborted'){
    //   icons = icons + this._commonService.createIcon('viewAuditor', 'View', 'visibility');
    // }else{
    if (row.status == scheduleStatus.DRAFT_COMPLETED || row.status == scheduleStatus.SUBMITTED || row.status == scheduleStatus.PARTIALLY_SUBMITTED ) {
      iconsList.push({ type: 'download', title: "Download Report", icon: 'download_blue', data: row });
    }
    //For now Commented this functionality for godrej tenenat
    // if (this._commonService.getTenantUid() == 'godrej' && (row.status == 1 || row.status == 6 || row.status == 14)) {
    //   icons = icons + this._commonService.createIcon('download2nd', 'Download 2nd Report', 'download_for_offline');
    // }

    // if(row.status==1 || row.statusText == 'Draft Completed' && (row.auditType?.type == "FQA" || row.auditType?.type == "INSPECTION") ){
    //   icons = icons + this._commonService.createIcon('submit', 'Submit Audit', 'edit_note');
    // }

    if (row.auditType?.portalSupport == 1 && (row.status == scheduleStatus.SUBMITTED || row.status == scheduleStatus.EDITED || row.status == scheduleStatus.DIVERSION) && this._commonService.getTenantUid() == 'godrej') {
      iconsList.push({ type: 'submit', title: "Update Audit", icon: 'edit', data: row });
    }
    if (row.statusText == 'Completed') {
      iconsList.push({ type: 'mail', title: "Email Report", icon: 'email_blue', data: row });
    }
    if (this.type == 'testing' && row.statusText != 'Scheduled') {
      iconsList.push({ type: 'edit', title: "Testing", icon: 'play', data: row });
    }
    //else if(row.statusText!='Deleted' && row.statusText!='Aborted'){
    //   if(this.mode == 'lab' ){
    //     icons = icons + this._commonService.createIcon('updateLab', 'Edit', 'edit');  
    //   }else{
    //     icons = icons + this._commonService.createIcon('updateAuditor', 'Edit', 'edit');  
    //   }
    // }    
    // if (row.statusText != 'Started' && row.statusText!='Deleted' && row.statusText!='Aborted'){
    // icons = icons + this._commonService.createIcon('viewAuditor', 'View', 'visibility');
    //   if(row.statusText != 'Completed'){
    //   icons = icons + this._commonService.createIcon('assign', 'Assign Auditor', 'assignment_ind');
    //   }
    // }
    //}


    return iconsList;
  }

  public getActions(event): void {
    let actionData: any = event;
    this.selectedData = actionData.data;
    if (actionData.status == 'download') {
      this.downloadReport(actionData.data)
    }

    if (actionData.status == 'download2nd') {
      this.downloadReport2nd(actionData.data)
    }

    if (actionData.status == 'edit') {
      this._commonService.navigate('testing/testingProcessByCategory/' + actionData.data.auditId);
    }

    if (actionData.status == 'mail') {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'Confirm sending email ?',
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          APP_UI_CONFIG.schedules.sendReport.paramList['id'] = actionData.data.auditId
          this.invokeService.serviceInvocation(APP_UI_CONFIG.schedules.sendReport).subscribe(res => {
            this._commonService.success('Report sent successfully.');
          });
        }
      });

    }
    if (actionData.status == 'viewAuditor') {
      this.status = 'view';
      this.showTable = false;
      this.showAddEdit = true;
    }
    if (actionData.status == 'updateAuditor') {
      this.status = 'edit';
      this.selectedData = event.data
      this.showTable = false;
      this.showAddEdit = true;
    }
    if (actionData.status == 'updateLab') {
      this.status = 'edit';
      this.selectedData = event.data
      this.showTable = false;
      this.showAddEdit = true;
    }
    if (actionData.status == 'assign') {
      this.status = 'assign';
      this.selectedData = event.data
      this.showTable = false;
      this.showAddEdit = true;
    }

    if (actionData.status == 'submit') {

      APP_UI_CONFIG.schedules.canUpdateReport.paramList['id'] = actionData.data.auditId;
      this.invokeService.serviceInvocation(APP_UI_CONFIG.schedules.canUpdateReport, null, actionData.data.auditId, null).subscribe(res => {
        if (event.data?.auditType?.postSubmitFlow == 1 && res.code == 200) {
          const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            data: 'How do you want to update the audit ? fromInspectionPage',
          });

          dialogRef.afterClosed().subscribe(result => {
            if (result) {
              this.submitTypeFromPopup = result;
              this.showSubmitComp(event);
            }
          });
        } else if (res.code == 400) {
          this._commonService.error(res.message);
        }
      },
        (error) => {
          this._commonService.error('Something went wrong, Please try again later.');
        },
        () => {

        });
    }
    if (actionData.status == 'history') {
      this.openScheduleHistoryDialog(this.selectedData.auditId);
    }

    // if (actionData.status == 'download') {
    // 	this.downloadReport(actionData.data)
    // }
    // if (actionData.status == 'download') {
    // 	this.downloadReport(actionData.data)
    // }

  }

  showSubmitComp(event) {
    this.status = 'submit';
    this.selectedData = event.data
    this.showTable = false;
    this.showAddEdit = false;
    this.showSubmitAudit = true;
  }

  downloadReport(row) {
    let reportUrl = URL_CONSTANTS.BASE_URL + URL_CONSTANTS.AUDIT_GETREPORT + '/' + row.auditId + '.pdf';
    window.open(reportUrl, '_blank');
    //return reportUrl;
  }

  downloadReport2nd(row) {
    let reportUrl = URL_CONSTANTS.BASE_URL + 'audit/getsecondreport' + '/' + row.auditId + '.pdf';
    window.open(reportUrl, '_blank');
    //return reportUrl;
  }

  public getcloseEvent(event): void {
    this.showSubmitAudit = false;
    this.serviceInvocation();
    super.closeAddEdit();
  }

  private openScheduleHistoryDialog(uuid): void {
    const dialogRef = this.dialog.open(ScheduleHistoryComponent, {
      data: { uuid: uuid },
      width: '98%',
      maxWidth: '98vw',
      height: 'auto'
    });

    dialogRef.afterClosed().subscribe(result => {

    });
  }


}