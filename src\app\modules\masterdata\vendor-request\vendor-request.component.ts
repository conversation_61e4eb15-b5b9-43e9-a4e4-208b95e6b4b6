import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { FilterList } from 'app/core/qinspect/_model/filterList.model';
import { QiVendor } from 'app/core/qinspect/_model/vendor.model';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';
import { ROLE } from 'app/enum-constants';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { environment } from 'environments/environment';
import * as _ from 'lodash';

import {
  DeleteConfirmationDialogComponent,
} from '../../../shared/delete-confirmation-dialog/delete-confirmation-dialog.component';

@Component({
  selector: 'app-vendor-request',
  templateUrl: './vendor-request.component.html',
  styleUrls: ['./vendor-request.component.scss']
})
export class VendorRequestComponent extends BaseTableSharedComponent {

  //declarations:
  tenantUid = this._commonService.getTenantUid();
  vendorList:any[]=[];
  allVendorList:any[]=[];
  tenantConfig: any;
  isTestUser: boolean = false;

  columnDefs: any[] = [
    // {
    //       field: '',
    //         // headerCheckboxSelection: true,
    //         checkboxSelection: true,
    //         showDisabledCheckboxes: true,
    //         width: 30,
    //         minWidth: 30,
    // },
    {
      headerName: "Requested Date", field: "createdTime", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', sort: 'desc',
      cellRendererFramework: CustomDateTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
    },
    {
      headerName: "Name", field: "name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
    },
    { headerName: "GL Category", field: "categoryNames", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', },
    { headerName: "Business Line", field: "businessLine", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', },
    {
      headerName: "Business Model", field: "businessModel", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
      cellRenderer: function (params) {
        switch (params.data.businessModel) {
          case '1P': return "Amazon Owned Brands"
          case '3P': return "Licensed Brands"
          case 'Amazon Owned Brands': return params.data.businessModel.toString()
          case 'Licensed Brands': return params.data.businessModel.toString()
          default: params.data.businessModel;
            break;
        }
      }
    },

    { headerName: "City", field: "city", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', },
    {
      headerName: "Status",
      field: "vendorBoardingStatus",
      sortable: true, unSortIcon: true,
      filter: 'agTextColumnFilter',
    },
    // 
    {
      headerName: 'Actions',
      pinned: 'right',
      maxWidth: 180,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: (params) => {
        return { actions: this.prepareActionIconButtons(params.data) }
      }
    }

  ];

  selectedStatus: string = '';
  statusList: any[] = [];
  filtersList: FilterList[] = [];

  private setIntialFliterList(): void {
    this.selectedStatus = '';
    this.statusList = [];
    this.filtersList = [{ id: "status", type: 'dropdown', label: 'Select Status', showLabelName: 'name', list: this.statusList, defaultValue: this.selectedStatus }]
  }

  public init(): void {
    this.setIntialFliterList();
    if (ROLE.TEST_USER == this._commonService.getUseRole()) {
      this.isTestUser = true;
    }
    this.tenantConfig = JSON.parse(localStorage.getItem('user'));
    localStorage.removeItem('vpf-form-data');
    this.getVendorList();
    // super.setBasUrl(APP_UI_CONFIG.masterdata.vendor.allVendors);
    super.init();
  }

  private getVendorList(): void {
    this.subscription.add(this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.vendor.allVendors)
      .subscribe({
        next: response => {
          this.allVendorList = response;
          this.vendorList = _.filter(response, (r) => {
            return r.vendorBoardingStatus?.toLowerCase() != 'active';
          });
          const uniqueStatuses = [...new Set(this.vendorList.map(item => item.vendorBoardingStatus))];
          this.statusList = uniqueStatuses.map(status => ({ name: status.toUpperCase(), value: status }));
          this.filtersList[0].list = this.statusList;
        },
        error: error => {
          this._commonService.handleError(error);
        }
      }))
  }

  public prepareActionIconButtons(row: any): any {
    this.status = null;
    let iconsList: any[] = [];
    if (row.deleted == 0) {
      iconsList.push({ type: 'view', title: "Quick View", icon: 'view', data: row });
    }
    if (row.deleted == 0) {
      iconsList.push({ type: 'update', title: "View", icon: 'edit_document_blue', data: row });
    }
    if ((row.vendorBoardingStatus == "VENDOR APPROVED" || row.vendorBoardingStatus == "PRE-PRODUCTION") && this.tenantConfig.role.includes("OPERATIONS")) {
      iconsList.push({ type: 'editable', title: "Make Editable", icon: 'edit', data: row });
    }
    return iconsList;

  }

  public getActions(event): void {
    let actionData: any = event;
    this.selectedData = actionData.data;
    let name = (actionData.data.fullName || actionData.data.code) || ' ';

    if (actionData.status == 'update') {
      this._router.navigate(['/vendor/vpfForm'],
        {
          queryParams: { uuid: this.selectedData.uuid }
        });
    }
    if (actionData.status == 'editable') {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'This will make VPF editable, are you sure?',
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          console.log(result);
          this._dashboardService.makeVPFEditable(this.selectedData.uuid).subscribe(res => {
            if (res.code == 200) {
              this._router.navigate(['/vendor/vpfForm'],
                {
                  queryParams: { uuid: this.selectedData.uuid }
                });
            }
          });
        }
      });
    }
    if (actionData.status == 'view') {
      this.status = 'update';
      this.showAddEdit = true;
      this.showTable = false;
    }
    if (actionData.status == 'activate') {
      this.onActivate(this.selectedData);
    }

    if (actionData.status == 'deactivate') {
      this.onDeActivate(this.selectedData);
    }
    if (actionData.status == 'delete') {
      this.onDelete(actionData.data);
    }
  }


  private onActivate(data): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: 'Are you sure you want to activate vendor ' + ((data.fullName || data.code) || '') + ' ?',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        APP_UI_CONFIG.masterdata.vendor.activate.paramList['id'] = data.uuid;
        this.subscription.add(this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.vendor.activate, null, data.uuid, data)
          .subscribe({
            next: response => {
              if (response.code == 400) {
                this._commonService.error('Unable to activate vendor');
              } else {
                this._commonService.success('Vendor activated successfully');
                this.onRefresh();
              }
            }, error: error => {
              this._commonService.handleError(error);
            }
          })
        );
      }
    });
  }

  private onDeActivate(data): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: 'Are you sure you want to deactivate vendor ' + ((data.fullName || data.code) || '') + ' ?',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        APP_UI_CONFIG.masterdata.vendor.deactivate.paramList['id'] = data.uuid;
        this.subscription.add(this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.vendor.deactivate.paramList['id'], null, data.uuid, data)
          .subscribe({
            next: response => {
              if (response.code == 400) {
                this._commonService.error('Unable to deactivate vendor');
              } else {
                this._commonService.success('Vendor deactivated successfully');
                this.onRefresh();
              }
            }, error: error => {
              this._commonService.handleError(error);
            }
          })
        );
      }
    });

  }

  public onDelete(row): void {
    let rec = <QiVendor>Util.clone(row);

    let url = URL_CONSTANTS.VENDOR + '/' + row.uuid;
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '50%',
      data: { apiData: { url: url, data: row } },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        setTimeout(() => {
          this.init();
        }, 1000);
      }
    });
  }

  public getFilterList(event: any): void {
    let filterData: FilterList[] = event;
    this.selectedStatus = '';
    filterData.forEach(item => {
      console.log(item)
      if (item.id == "status") {
        this.selectedStatus = item.value?.value || null;
      }
    });
    const gridData = Util.clone(this.allVendorList);
    this.vendorList = _.filter(gridData, (r) => {
      const isNotActive = r.vendorBoardingStatus?.toLowerCase() !== 'active';
      const matchesStatus = this.selectedStatus ? r.vendorBoardingStatus === this.selectedStatus : true;

      return isNotActive && matchesStatus;
    });
    //this.init();
  }

  public getCloseEvent() {
    super.closeAddEdit();
    this.init();
  }

  public onRefresh(): void {
    this.init();
  }

}
