import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Util } from 'app/core/qinspect/_common/util';
import { CadAppBundle } from 'app/core/qinspect/_model/CadAppBundle.model';
import { MtcCertificateType } from 'app/core/qinspect/_model/MTCCertificateConfig.model';
import { MtcCertificateTypeConfig } from 'app/core/qinspect/_model/MtcCertificateTypeConfig.model';
import { Status } from 'app/core/qinspect/_model/status.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-add-edit-cad-appbundle',
  templateUrl: './add-edit-cad-appbundle.component.html',
  styleUrls: ['./add-edit-cad-appbundle.component.scss']
})
export class AddEditCadAppbundleComponent implements OnInit {
  hdr: string = 'Add Product';
  editRec: boolean = false;
  rec: CadAppBundle;
  public files: NgxFileDropEntry[] = [];
  status: Status = new Status();
  certTypeConfigs: MtcCertificateTypeConfig[] = [];
  basicAuth = this.globals.basicAuth;
  
  constructor( public invoke: InvokeService,
    public toastr: ToastrService,
    private _commonService: CommonService,
    private http: HttpClient,
    private globals: Globals,
    public dialogRef: MatDialogRef<AddEditCadAppbundleComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,) { }

  ngOnInit(): void {
    if (this.data?.data?.id) {
      this.hdr = "Update Data Extractor";
      this.certTypeConfigs = this.data.data.configs;
      this.editRec = false;
      this.rec = <MtcCertificateType>Util.clone(this.data.data);
    } else {
      this.hdr = "Add Data Extractor";
      this.editRec = false;
      this.rec = new MtcCertificateType();
    }
  }

  public setFiles(files: NgxFileDropEntry[]) {
    this.files = files
  }

  deleteFile(i) {
    this.files.splice(i, 1)
    this.status = new Status()
  }

  public fileOver(event) {
    //console.log(event);
  }

  public fileLeave(event) {
    //console.log(event);
  }
  

  importBundle() {
    for (const droppedFile of this.files) {
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          const formData = new FormData()
          formData.append('file', file, droppedFile.relativePath)
          formData.append('payload', JSON.stringify(this.rec))
          let headers = new HttpHeaders()
            .set("Authorization", this.basicAuth).set('Client', this._commonService.getTenantUid())
          this.http.post(environment.apiUrl + 'config/appBundle', formData, { headers: headers, responseType: 'json' })
            .subscribe(data => {
              this.status = data as Status
              if (this.status.code != '500') {
                this.toastr.success(this.data.message ? this.data.message : 'Record Saved');
                this.files = [];
                this.dialogRef.close(true);
              } else {
                this.status.message = "";
                this.toastr.warning(this.data.message ? this.data.message : 'Unable to save the record');
              }
            }, error => {
              this.toastr.error(error.message ? error.message : 'Unable to save the record');
            })

        });
      } else {
        this.toastr.warning('Please select file')
        // It was a directory (empty directories are added, otherwise only files)
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;

      }
    }
  }

}
