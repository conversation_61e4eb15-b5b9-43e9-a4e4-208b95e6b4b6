import { HttpHeaders } from '@angular/common/http';
import { Component, ViewChild } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { FilterList } from 'app/core/qinspect/_model/filterList.model';
import { QiUser } from 'app/core/qinspect/_model/user.model';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { environment } from 'environments/environment';

import { UploadFileComponent } from '../../../shared/upload-file/upload-file.component';
import { ViewPoDetailsComponent } from './view-po-details/view-po-details.component';

@Component({
  selector: 'app-purchase-orders',
  templateUrl: './purchase-orders.component.html',
  styleUrls: ['./purchase-orders.component.scss']
})
export class PurchaseOrdersComponent extends BaseTableSharedComponent {
  //declarations:
  poDataList: any[] = [];
  @ViewChild('gridPo') gridPo: any;
  showPurchaseOrderTemplate: boolean = true;
  basicAuth = this.globals.basicAuth;
  user: QiUser;
  poData: any[] = [];
  statusList: any[] = [
    { "label": "All", value: "-2" },
    { "label": "Pending", value: "0" },
    { "label": "Submitted", value: "2" },
    { "label": "Accepted", value: "3" },
    { "label": "Rejected", value: "-1" },
    { "label": "PO Uploaded", value: "4" },
    { "label": "PO Accepted", value: "5" },
    { "label": "PO Rejected", value: "6" },
    { "label": "PO Closed", value: "7" },
  ]
  columnDefs: any[] = [];
  selectedStatusCode = "-2";
  // columnDefs: any[] = [
  //   { headerName: "PO Number", field: "purchaseOrderNo", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', maxWidth: 160 },
  //   { headerName: "PO Item", field: "purchaseOrderItem", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.hideColumsnForTitan() },
  //   { headerName: "SO Number", field: "salesOrderNo", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.hideColumsnForTitan() },
  //   { headerName: "SO Item", field: "salesOrderItem", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.hideColumsnForTitan() },
  //   { headerName: "PO Qty'", field: "qty", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.hideColumsnForTitan() },
  //   { headerName: "Vendor Name", field: "vendorName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.hideColumsnForTitan() },
  //   { headerName: "Shipment No", field: "shipmentNo", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.hideColumsnForTitan() },
  //   { headerName: "Shipment Qty", field: "shipmentQty", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.hideColumsnForTitan() },
  //   { headerName: "Factory", field: "factoryName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
  //   {
  //     headerName: "Delivery Date", field: "deliveryDate", sortable: true, unSortIcon: true,
  //     cellRendererFramework: CustomDateTooltipComponent,
  //     cellRendererParams: (params: ICellRendererParams) => params.data,
  //   },
  //   {
  //     headerName: "Status", field: "status", sortable: true, unSortIcon: true, hide: this.hideColumsnForTitan(),
  //     cellRenderer: (data) => {
  //       return this.prepareStatus(data.data);
  //     },
  //   },
  //   {
  //     headerName: "PO Date", field: "poCreationDate",
  //     sortable: true, unSortIcon: true,
  //     cellRendererFramework: CustomDateTooltipComponent,
  //     cellRendererParams: (params: ICellRendererParams) => params.data,
  //     filter: 'agTextColumnFilter',
  //     hide: !this.hideColumsnForTitan()

  //   },



  //   { headerName: "Qty", field: "totalQty", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: !this.hideColumsnForTitan() },


  //   {
  //     headerName: 'Actions',
  //     pinned: 'right',
  //     cellRendererFramework: CellActionComponent,
  //     maxWidth: this.hideColumsnForTitan() ? 100 : 200,
  //     hide: environment.tenantUid == 'norlanka',
  //     cellRendererParams: (params) => {
  //       return { actions: [] }
  //     }
  //   }
  // ];



  private prepareColumnsForTitan(): void {
    this.columnDefs = [
      { headerName: "PO Number", field: "purchaseOrderNo", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', maxWidth: 160 },
      {
        headerName: "PO Date", field: "poCreationDateText",
        sortable: true, unSortIcon: true,
        filter: 'agTextColumnFilter',
      },
      { headerName: "Brand", field: "brandName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
      // { headerName: "SKU", field: "designCodeCode", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', },
      { headerName: "Supplier", field: "vendorName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', },
      { headerName: "Factory", field: "factoryName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
      { headerName: "Qty", field: "totalQty", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', },
      {
        headerName: "Delivery Date", field: "deliveryDateText", sortable: true, unSortIcon: true
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        cellRendererFramework: CellActionComponent,
        maxWidth: 100,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data) }
        }
      }
    ]
  }
  private prepareColumns(): void {
    this.columnDefs = [
      { headerName: "PO Number", field: "purchaseOrderNo", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', maxWidth: 160 },
      { headerName: "PO Item", field: "purchaseOrderItem", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
      { headerName: "SO Number", field: "salesOrderNo", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
      { headerName: "SO Item", field: "salesOrderItem", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
      { headerName: "PO Qty", field: "qty", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
      { headerName: "Vendor Name", field: "vendorName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
      //   { headerName: "Shipment No", field: "shipmentNo", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.hideColumsnForTitan() },
      //   { headerName: "Shipment Qty", field: "shipmentQty", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', hide: this.hideColumsnForTitan() },
      //   { headerName: "Factory", field: "factoryName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
      //   {
      //     headerName: "Delivery Date", field: "deliveryDate", sortable: true, unSortIcon: true,
      //     cellRendererFramework: CustomDateTooltipComponent,
      //     cellRendererParams: (params: ICellRendererParams) => params.data,
      //   },
      {
        headerName: "Status", field: "status", sortable: true, unSortIcon: true,
        cellRenderer: (data) => {
          return this.prepareStatus(data.data);
        },
      },
      // {
      //   headerName: 'Actions',
      //   pinned: 'right',
      //   cellRendererFramework: CellActionComponent,
      //   maxWidth: 100,
      //   cellRendererParams: (params) => {
      //     return { actions: [] }
      //   }
      // }
    ]
  }

  filtersList: FilterList[] = [
    { id: "status", type: 'dropdown', label: 'Status', list: this.statusList, defaultValue: this.statusList[1] }
  ]

  public init(): void {
    this.user = this._authService.getLoggedInUser();
    this.defaultfromDate.setDate(this.defaultToDate.getMonth() - 1);
    this.showTable = true;
    this.showAddEdit = false;
    this.onRefresh();


    super.init();
  }
  public onRefresh(): void {
    this.getPODataList();
    if (this._commonService.getTenantUid() == "titan") {
      this.prepareColumnsForTitan();
    } else {
      this.prepareColumns();
    }
  }

  private getPODataList(): void {
    APP_UI_CONFIG.purchaseOrders.getByStatus.paramList["id"] = this.selectedStatusCode;
    let url: any = APP_UI_CONFIG.purchaseOrders.getByStatus;
    if (this._commonService.getTenantUid()== "titan") {
      url = APP_UI_CONFIG.purchaseOrders.getByGroup;
    } else {
      APP_UI_CONFIG.purchaseOrders.getByStatus.paramList["id"] = this.selectedStatusCode;
      url = APP_UI_CONFIG.purchaseOrders.getByStatus;
    }
    this.invokeService.serviceInvocation(url).subscribe({
      next: response => {
        if (response) {
          this.preparePoData(response)
        }
      }
    });
  }
  private preparePoData(response: any): void {
    this.poDataList = response.map(item => ({ deliveryDateText: this._commonService.dateFormat(item.deliveryDate), poCreationDateText: this._commonService.dateFormat(item.poCreationDate), ...item }))
  }

  public getFilterList(event: any): void {
    let filterData: FilterList[] = event;
    filterData.forEach(item => {
      if (item.id == "fromDate") {
        this.FromToDate.from = item.value;
      }
      else if (item.id == "toDate") {
        this.FromToDate.to = item.value;
      } else if (item.id == "status") {
        this.selectedStatusCode = item.value;
      }
    })
    this.init();
  }

  public getImportEvent(event): void {
    this.openImportDialog();
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];
    if (this._commonService.getTenantConfig().tenantUid == 'titan') {
      iconsList.push({ type: 'view', title: "View PO", icon: 'view', data: row });
      iconsList.push({ type: 'delete', title: "Delete PO", icon: 'delete', data: row });
    } else {
      if (row.status == 4 && (this.user.role.indexOf("VENDOR") != -1 || this.user.role.indexOf("FACTORY") != -1 || this.user.role.indexOf("ADMIN") != -1)) {
        iconsList.push({ type: 'accept', title: "Accept PO", icon: 'accept_green', data: row });
        iconsList.push({ type: 'reject', title: "Reject PO", icon: 'reject_red', data: row });
      }
      if (row.status == 0) {
        if (row.deleted == 1) {
          iconsList.push({ type: 'activate', title: "Activate", icon: 'activate', data: row });
        }
        if (row.deleted == 0) {
          // iconsList.push({ type: 'deactivate', title: "Deactivate", icon: 'deactivate', data: row });
        }
      }
      if (this._commonService.getTenantConfig().tenantUid != 'norlanka') {
        iconsList.push({ type: 'download', title: "Download", icon: 'download_blue', data: row });
      }
    }
    return iconsList;
  }

  protected prepareStatus(row): string {
    if (row.status == 0) {
      return "Pending"
    } else if (row.status == 2) {
      return "Submitted"
    } else if (row.status == 3) {
      return "Accepted"
    } else if (row.status == -1) {
      return "Rejected"
    } else if (row.status == 4) {
      return "PO Uploaded"
    } else if (row.status == 5) {
      return "PO Accepted"
    } else if (row.status == 6) {
      return "PO Rejected"
    } else if (row.status == 7) {
      return "PO Closed"
    }
  }

  public getActions(event): void {
    let actionData: any = event;
    this.status = actionData.status;
    this.selectedData = actionData.data;
    if (actionData.status == 'edit') {
      this.showTable = false;
      this.showAddEdit = true;
    }
    else if (actionData.status == 'delete') {
      this.onDelete(actionData.data);
    }
    else if (actionData.status == 'view') {
      const dialogRef = this.dialog.open(ViewPoDetailsComponent, {
        width: '80%',
        data: {},
        disableClose: true,
      });
      dialogRef.componentInstance.poNumber = actionData.data.purchaseOrderNo;
    }
    else if (actionData.status == 'activate') {

    }
    else if (actionData.status == 'deactivate') {

    }
    else if (actionData.status == 'viewJson') {

    }
    else if (actionData.status == 'accept') {
      APP_UI_CONFIG.quotation.acceptPO.paramList['id'] = actionData.data.uuid
      this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.acceptPO).subscribe(res => {
        actionData.status = 5
        this.gridPo.refreshCells()
        this._commonService.success('PO accepted successfully');
      });
    }
    else if (actionData.status == 'reject') {
      APP_UI_CONFIG.quotation.rejectPO.paramList['id'] = actionData.data.uuid
      this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.rejectPO).subscribe(res => {
        actionData.status = 6
        this.gridPo.refreshCells()
        this._commonService.success('PO rejected successfully');
      });
    }
    else if (actionData.status == 'download') {
      let headers = new HttpHeaders({ 'Content-Type': 'application/csv' })
        .set("Authorization", this.basicAuth).set('Client', this._commonService.getTenantUid());;
      this.http.get(environment.apiUrl + 'saleOrder/po/' + this.selectedData.uuid + '/refdoc', {
        observe: 'response',
        responseType: 'blob',
        headers: headers
      }).subscribe(res => {
        this.fileSaverService.save((<any>res).body, this.selectedData.purchaseOrderNo + ".pdf");
      });
    }
  }

  private onDelete(action): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: 'Are you sure you want to delete the purchase order?',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        APP_UI_CONFIG.PO.deletePo.paramList.purchaseOrderNo = action?.purchaseOrderNo;
        this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.deletePo, null, null, null).subscribe({
          next: res => {
            if (res && res.code == 200) {
              this._commonService.success("Purchase order deleted successfully");
              this.onRefresh();
            } else {
              this._commonService.error(res.message ? res.message :'Failed to delete the purchase order');
            }
          },
          error: error => {
            this._commonService.handleError(error);
          }
        })
      }
    })
  }



  public onPurchaseOrderDownloadTemplate(): void {
    APP_UI_CONFIG.PO.downloadPurchaseOrderTemplate.paramList.PurchaseOrder = 'PurchaseOrder'
    this.invokeService.downloadFile(APP_UI_CONFIG.PO.downloadPurchaseOrderTemplate, '', `Purchase_Order_Template.csv`, '');
  }

  private openImportDialog(): void {
    const userCategoryMapEnabled = this._commonService.getTenantConfig()?.featuresSet ? JSON.parse(this._commonService.getTenantConfig()?.featuresSet)?.enableImportExportConfig == 1 : false;
    let url = "saleOrder/po/import";
    if (userCategoryMapEnabled) {
      url = "impex/PurchaseOrder/import";
    }

    const dialogRef = this.dialog.open(UploadFileComponent, {
      width: '50%',
      data: { dailogHeadig: 'Import Purchase orders', extensions: ".csv", apiData: { 'url': url, type: "POST", }, message: { success: 'Purchase Order import successfully', error: 'Purchase Order not imported' }, dropFileslabel: 'purchase order' },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.init();
      }
    });
  }
}
