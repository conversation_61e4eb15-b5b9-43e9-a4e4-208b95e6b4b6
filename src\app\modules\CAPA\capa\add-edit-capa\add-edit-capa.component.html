<div class="w-full flex flex-col gap-2">
    <div class="tps-full-screen-header justify-between items-center ">
        <tfl-page-header [headerName]="hdr" [showBackIcon]="true" (close)="closeCapa()"></tfl-page-header>
        <div class="flex flex-row gap-4 items-center justify-end">
             <p-splitButton label="Reports" [model]="ReportOptions"></p-splitButton>
            <mat-icon (click)="getByAuditUid()" pTooltip="Refresh"
                style="font-size:2rem;color:#517EBC;cursor:pointer">refresh</mat-icon>
            <mat-icon (click)="expandInfoSection = !expandInfoSection; adjustTableHeight()"
                pTooltip="Click to Expand/Collapse Info Section"
                style="font-size:2rem;color:#517EBC;cursor:pointer">{{!expandInfoSection?
                'aspect_ratio' :'fullscreen'}}</mat-icon>
        </div>

    </div>
    <ng-container *ngIf="capaGridData.length>0">
        <button class="float-top-btn" pTooltip="Scroll To Top" tooltipPosition="left" (click)="scrollToTopOfPage()">
            <i class="pi pi-arrow-up" style="font-size: 15px;
        color: #fff;
        font-weight: 600;"></i>
        </button>
        <button class="float-end-btn" pTooltip="Scroll To Bottom" tooltipPosition="left"
            (click)="scrollToBottomOfPage()">
            <i class="pi pi-arrow-down" style="font-size: 15px;
        color: #fff;
        font-weight: 600;"></i>
        </button>
    </ng-container>
    <div class="tps-full-screen-content gap-2 pl-2" style="max-height:72vh ;overflow-y:auto;position: relative;">
        <div *ngIf="expandInfoSection" class="padding-card pb-2" style="padding-top: 1px !important;">
            <div class="row form-row">
                <div class="col-md-4">
                    <div class="row tfl-flex-align-center">
                        <div class="col-md-4">
                            <strong>Factory:</strong>
                        </div>
                        <div class="col-md-8">
                            {{selectedCapaDetails?.factoryName}}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row tfl-flex-align-center">
                        <div class="col-md-4">
                            <strong>Vendor</strong>
                        </div>
                        <div class="col-md-8">
                            {{selectedCapaDetails?.vendorName}}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row tfl-flex-align-center">
                        <div class="col-md-4" *ngIf="!isTesting">
                            <strong>Agency:</strong>
                        </div>
                        <div class="col-md-4" *ngIf="isTesting">
                            <strong>Seller:</strong>
                        </div>
                        <div class="col-md-8">
                            {{ isTesting ? testingRequest?.sellerName : schedulePlanningData?.agencyName}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="row form-row">
                <div class="col-md-4">
                    <div class="row tfl-flex-align-center">
                        <div class="col-md-4">
                            <strong>Type:</strong>
                        </div>
                        <div class="col-md-8">
                            {{ isTesting ? testingRequest?.testType : selectedCapaDetails?.auditTypeName }}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row tfl-flex-align-center">
                        <div class="col-md-4">
                            <strong>Issued Date:</strong>
                        </div>
                        <div class="col-md-8">
                            {{selectedCapaDetails?.issuedTime | date}}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row tfl-flex-align-center">
                        <div class="col-md-4">
                            <strong>Followup Date:</strong>
                        </div>
                        <div class="col-md-8">
                            {{selectedCapaDetails?.followupTime | date}}
                        </div>
                    </div>
                </div>
            </div>


            <div class="row form-row">
                <div class="col-md-4" *ngIf="!isTesting">
                    <div class="row tfl-flex-align-center">
                        <div class="col-md-4">
                            <strong>Occurrence:</strong>
                        </div>
                        <div class="col-md-8">
                            {{schedulePlanningData?.auditTypeLabel }}
                        </div>
                    </div>
                </div>
                <div class="col-md-4" *ngIf="!isTesting">
                    <div class="row tfl-flex-align-center">
                        <div class="col-md-4">
                            <strong>Number:</strong>
                        </div>
                        <div class="col-md-8">
                            {{schedulePlanningData?.auditNumber }}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row tfl-flex-align-center">
                        <div class="col-md-4" *ngIf="!isTesting">
                            <strong>Auditor Name:</strong>
                        </div>
                        <div class="col-md-4" *ngIf="isTesting">
                            <strong>Lab Name:</strong>
                        </div>
                        <div class="col-md-8">
                            {{ isTesting ? testingRequest?.labName : schedulePlanningData?.auditorName }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="w-full capaItemWrapper flex flex-col gap-1" *ngFor="let capaItem of capaGridData;"
            [ngStyle]="{'border': (capaItem.status==3 && isSeller) || (capaItem.status==2 && isVendor) ? '5px solid #b7cbe7':'5px solid #e2e8f0'}">
            <div class="flex flex-row items-center border-b gap-6 p-2" [id]="capaItem.uuid"
                [ngStyle]="{'background-color': (capaItem.status==3 && isSeller) || (capaItem.status==2 && isVendor) ? '#b7cbe7':'#e2e8f0'}">
                <p-checkbox *ngIf="isSeller && (capaItem.status==3 || capaItem.status==6)" name="{{capaItem.section}}"
                    (onChange)="capaItem.isSelected=$event.checked;onSelectCapaItems(capaItem,$event)"></p-checkbox>
                <div class="w-full flex flex-row items-center">
                    <div class="col-md-6 flex flex-row items-center gap-2">
                        <b>Section:</b>
                        <label>{{capaItem.checkpointCategoryName}}</label>
                    </div>
                    <!-- <div class="col-md-4 flex flex-row items-center gap-2">
                        <b>ASIN Code:</b>
                        <label>{{capaItem.designCodeName}}</label>
                    </div> -->
                    <div class="col-md-6 flex flex-row items-center justify-between gap-2">
                        <div class="flex flex-row items-center gap-2">
                            <i class="pi pi-flag" [ngClass]="capaItem.severityColor"
                                style="font-size: 1rem;font-weight:bold"></i>
                            <b>Severity:</b>
                            <label class="font-semibold"
                                [ngClass]="capaItem.severityColor">{{capaItem.severity}}</label>
                        </div>
                        <tfl-button *ngIf="capaItem.isAccept && isSeller" [buttonName]="'Accept'"
                            (click)="getActions({status:'accept',data:capaItem})"></tfl-button>
                        <i *ngIf="capaItem.status==8" pTooltip="CAPA is accepted and closed" tooltipPosition="bottom"
                            class="pi pi-check-circle acceptIcon"></i>
                    </div>
                </div>
            </div>
            <form class="w-full " #f="ngForm" novalidate>
                <div class="flex flex-col flex-wrap items-center p-2 ">
                    <div class="w-full flex flex-row items-center gap-1 mb-3 pl-2">
                        <label> <b>Clause : </b> <span
                                class="clauseValue">{{capaItem.description?capaItem.description:'N/A'}}</span></label>
                    </div>
                    <div class="departments-list w-full pl-2 mb-1">
                        <label><b>ASIN Code : </b></label>
                        <span *ngFor="let designCodeName of capaItem.designCodeNameList"
                            class="department-chip truncate" [pTooltip]="designCodeName" tooltipPosition="bottom">
                            <i class="pi pi-building mr-1"></i>{{designCodeName}}
                        </span>
                    </div>
                    <div class="w-full">
                        <tps-edit-capa-details class="w-full"
                            [formData]=" { status:capaItem.capaStatusText, data: capaItem, auditType: schedulePlanningData.type }"
                            [capaItemList]="capaGridData" (emitSaveOrSubmitEvent)="onSaveCapaDetails($event)"
                            (onCopyDetails)="onCopyDetails($event)"
                            (refreshPage)="getByAuditUid()"></tps-edit-capa-details>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div *ngIf="(isVendor || isSeller ) && selectedCapaItems.length>0"
        class="tps-full-screen-footer  w-full flex flex-row items-center gap-2 justify-end">
        <tfl-button [buttonName]="'Submit & Accept'" (submit)="onSubmitAll()"></tfl-button>
    </div>
    <div *ngIf="(isVendor || isSeller) && selectedCapaItems.length==0"
        class="tps-full-screen-footer  w-full flex flex-row items-center gap-2 justify-end">
        <b>Once one or more CAPA are responded, please click the button to notify all stakeholders.</b>
        <tfl-button [buttonName]="'Notify CAPA Activity'" (submit)="onCapSubmissionNotify()"></tfl-button>
    </div>
</div>