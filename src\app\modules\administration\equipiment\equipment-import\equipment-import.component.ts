import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Status } from 'app/core/qinspect/_model/status.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-equipment-import',
  templateUrl: './equipment-import.component.html',
  styleUrls: ['./equipment-import.component.scss']
})
export class EquipmentImportComponent implements OnInit {

  public files: NgxFileDropEntry[] = [];
  basicAuth = this.globals.basicAuth;
  status: Status = new Status();

  constructor(
    public dialogRef: MatDialogRef<EquipmentImportComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private http: HttpClient,
    private globals: Globals,
    private toastr: ToastrService,
  ) { }

  ngOnInit(): void {
  }

  public fileOver(event) {
    //console.log(event);
  }

  public fileLeave(event) {
    //console.log(event);
  }

  deleteFile(i) {
    this.files.splice(i, 1)
    this.status = new Status()
  }

  public setFiles(files: NgxFileDropEntry[]) {
    this.files = files
  }

  public uploadFiles(): void {
    for (const droppedFile of this.files) {
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
          const formData = new FormData()
          formData.append('file', file, droppedFile.relativePath)
          let headers = new HttpHeaders()
            .set("Authorization", this.basicAuth).set('Client', tenantUid)
          this.http.post(environment.apiUrl + 'config/checkpoint/optiontype/import', formData, { headers: headers, responseType: 'json' })
            .subscribe(data => {
              this.status = data as Status
              if (this.status.code != '500') {
                this.toastr.success(this.data.message ? this.data.message : 'Record Saved');
                this.files = [];
                this.dialogRef.close(true);
              } else {
                this.status.message = "";
                this.toastr.warning(this.data.message ? this.data.message : 'Unable to save the record');
              }
            }, error => {
              this.toastr.error(error.message ? error.message : 'Unable to save the record');
            })

        });
      } else {
        this.toastr.warning('Please select file')
        // It was a directory (empty directories are added, otherwise only files)
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;

      }
    }
  }

}
