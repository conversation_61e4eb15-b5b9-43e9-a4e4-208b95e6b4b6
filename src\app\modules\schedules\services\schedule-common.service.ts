import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';

import { APP_UI_CONFIG } from '../../../app-config.constants';
import { GenericList } from '../../../core/qinspect/_common/list';
import { SeasonsOptions, SeasonsYears } from '../../../core/qinspect/_common/seasons';
import { Util } from '../../../core/qinspect/_common/util';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../core/qinspect/_model/auditor.model';
import { AuditType } from '../../../core/qinspect/_model/AuditType.model';
import { Qi<PERSON>rand } from '../../../core/qinspect/_model/brand.model';
import { QiCategory } from '../../../core/qinspect/_model/category.model';
import { DefectType } from '../../../core/qinspect/_model/DefectType.model';
import { DesignCodeMaster } from '../../../core/qinspect/_model/DesignCodeMaster.model';
import { QiFactory } from '../../../core/qinspect/_model/factory.model';
import { InspectionAgency } from '../../../core/qinspect/_model/InspectionAgency.model';
import { Product } from '../../../core/qinspect/_model/Product.model';
import { ProductType } from '../../../core/qinspect/_model/ProductType.model';
import { QiCustomer } from '../../../core/qinspect/_model/qicustomer.model';
import { AMENDSCH, QiSchedule } from '../../../core/qinspect/_model/schedule.model';
import { TestingLab } from '../../../core/qinspect/_model/TestingLab.model';
import { QiUser } from '../../../core/qinspect/_model/user.model';
import { QiVendor } from '../../../core/qinspect/_model/vendor.model';
import { Globals } from '../../../core/qinspect/appconfig/appconfig.component';
import { InvokeService } from '../../../services/invoke.service';


@Injectable({
  providedIn: 'root'
})
export class ScheduleCommonService {
  seasons: SeasonsOptions;
  seasonYears: SeasonsYears;
  auditTypeList: GenericList<AuditType>;
  auditorList: GenericList<QiAuditor>;
  supportingAuditorList: GenericList<QiAuditor>;
  agencyList: GenericList<InspectionAgency> = new GenericList<InspectionAgency>();
  labList: GenericList<TestingLab> = new GenericList<TestingLab>();
  brandList: GenericList<QiBrand>;
  customerList: GenericList<QiCustomer>;
  vendorList: GenericList<QiVendor>;
  factoryList: GenericList<QiFactory>;
  categoryList: GenericList<QiCategory>;
  productList: GenericList<Product>;
  productTypeList: GenericList<ProductType>;
  defectTypeList: GenericList<DefectType>;
  designcodeList: GenericList<DesignCodeMaster>;
  actualFactoryList: GenericList<QiFactory>;

  user: QiUser;
  loading: boolean;
  loadedCount: number;

  constructor(
    public globals: Globals,
    public _invokeService: InvokeService) {
    this.loadedCount = 0;
    this.loading = true;
    let today = Util.getToday();
    this.auditTypeList = new GenericList<AuditType>();
    this.brandList = new GenericList<QiBrand>();
    this.customerList = new GenericList<QiCustomer>();
    this.auditorList = new GenericList<QiAuditor>();
    this.supportingAuditorList = new GenericList<QiAuditor>();
    this.vendorList = new GenericList<QiVendor>();
    this.factoryList = new GenericList<QiFactory>();
    this.actualFactoryList = new GenericList<QiFactory>();
    this.categoryList = new GenericList<QiCategory>();
    this.productTypeList = new GenericList<ProductType>();
    this.productList = new GenericList<Product>();
    this.defectTypeList = new GenericList<DefectType>();
    this.designcodeList = new GenericList<DesignCodeMaster>();
    this.seasons = new SeasonsOptions();
    this.seasonYears = new SeasonsYears();
    this.user = this.globals.getUser();
    // this.aToken.userEmitter.subscribe((_token) => {
    //   this.user = _token;
    // });
  }

  addLoaded() {
    this.loadedCount++;
    if (this.loadedCount > 7) this.loading = false;
  }

  getData() {
    // if (this.auditorList.list.length) return;
    // if (this.loading == false) return;
    this.loadedCount = 0;
    this.loading = true;
    // this.getAssociatedDataDummy();
    this.getAssociatedData();
  }

  clearData() {
    this.auditTypeList.setValue('');
    this.brandList.setValue('');
    this.customerList.setValue('');
    this.auditorList.setValue('');
    this.supportingAuditorList.setValue('');
    this.vendorList.setValue('');
    this.factoryList.setValue('');
    this.categoryList.setValue('');
    this.productTypeList.setValue('');
    this.productList.setValue('');
    this.defectTypeList.setValue('');
    this.designcodeList.setValue('');
    this.actualFactoryList.setValue('');
  }

  getAssociatedDataDummy() {
    if (this.user.role != 'AUDITOR') {

      // this.service.getData(URL_CONSTANTS.AUDITOR, this.callBackAud.bind(this));
    } else {
      let aud: QiAuditor[] = [];
      aud.push(<QiAuditor>this.user);
      this.callBackAud(aud);
    }
    if (this.user.role != 'VENDOR') {

      // this.service.getData(URL_CONSTANTS.VENDOR, this.callBackVen.bind(this));
    } else {
      let ven: QiVendor[] = [];
      ven.push(<QiVendor>this.user);
      this.callBackVen(ven);
    }
  }

  getDesigncodes() {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.designCodeMaster.get).subscribe(res => {
      if (res) {
        this.designcodeList = res
      }

    });
  }

  getAssociatedData() {
    const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
    if (tenantUid == 'norlanka') {
      APP_UI_CONFIG.schedules.getAutiTypeList.paramList['id'] = 1;
    }
    else {
      APP_UI_CONFIG.schedules.getAutiTypeList.paramList['id'] = 0;
    }
    this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getAutiTypeList).subscribe(res => {
      if (res) {
        this.callBackAudTy(res);
      }
    });
    if (this.user.role == 'AUDITOR') {
      this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getAuditors).subscribe(res => {
        if (res) {
          this.callBackSupAud(res);
        }
      });
    }
    if (this.user.role != 'AUDITOR') {
      this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getAuditors).subscribe(res => {
        this.callBackAud(res);
        this.callBackSupAud(res);
      });
    } else {
      let aud: QiAuditor[] = [];
      aud.push(<QiAuditor>this.user);
      this.callBackAud(aud);
    }
    // if(this.user.role == 'ADMIN') {
    //   this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getAuditors).subscribe(res => {
    //     if (res) {
    //       this.callBackSupAud(res);
    //     }
    //   });
    // }

    if (this.user.role != 'VENDOR') {
      this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getVendors).subscribe(res => {
        if (res) {
          this.callBackVen(res)
        }
      });
    } else {
      let ven: QiVendor[] = [];
      ven.push(<QiVendor>this.user);
      this.callBackVen(ven);
    }
    this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getBrands).subscribe(res => {
      if (res) {
        this.callBackBrand(res);
      }

    });
    this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getCustomers).subscribe(res => {
      if (res) {
        this.callBackCustomer(res);
      }

    });
    this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getFactory).subscribe(res => {
      if (res) {
        this.callBackFac(res);
      }

    });
    this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getCategory).subscribe(res => {
      if (res) {
        this.callBackCat(res);
      }

    });

    this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getProductTypes).subscribe(res => {
      if (res) {
        this.callBackPrdTy(res);
      }

    });
    APP_UI_CONFIG.schedules.getDefective.params = [{ key: 'last-modified', value: '0' }]
    this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getDefective).subscribe(res => {
      if (res) {
        this.callBackDefectTy(res);
      }
    });


    //    this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getDesignCodes).subscribe(res => {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.designCodeMaster.get).subscribe(res => {
      if (res) {
        this.callBackDesigncode(res);
      }

    });

    this._invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.inspectionAgency.get).subscribe(res => {
      if (res) {
        this.callBackAgency(res);
      }

    });
    APP_UI_CONFIG.masterdata.inspectionAgency.getByType.paramList.type = 'testing';
    this._invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.inspectionAgency.getByType).subscribe(res => {
      if (res) {
        this.callBackLab(res);
      }

    });

  }
  auditorData: any[] = [];
  callBackAudTy(aud: AuditType[]) {
    this.auditTypeList.setList3(aud, 'code', 'name', 'uuid');
    // this.auditTypeList.sort();
    this.addLoaded();
  }

  callBackBrand(brands: QiBrand[]) {
    this.brandList.setList3(brands, 'code', 'name', 'uuid');
    this.brandList.sort();
    this.addLoaded();
  }

  callBackCustomer(customers: QiCustomer[]) {
    this.customerList.setList3(customers, 'code', 'name', 'uuid');
    this.customerList.sort();
    this.addLoaded();
  }

  callBackAud(aud: QiAuditor[]) {
    this.auditorData = aud;

    // this.auditorList.setList2(aud, 'firstName', 'lastName', 'code', 'uuid');
    this.auditorList.setList3(aud, 'code', 'fullName', 'uuid');
    this.addLoaded();
  }

  callBackSupAud(aud: QiAuditor[]) {
    for (let i in aud) {
      aud[i].fullName = aud[i].firstName + ' ' + aud[i].lastName;
    }
    // this.auditorList.setList2(aud, 'firstName', 'lastName', 'code', 'uuid');
    this.supportingAuditorList.setList3(aud, 'code', 'fullName', 'uuid');
    this.addLoaded();
  }

  callBackCat(cat: QiCategory[]) {
    this.categoryList.setList3(cat, 'code', 'category', 'uuid');
    this.categoryList.sort();
    this.addLoaded();
  }

  callBackPrdTy(list: ProductType[]) {
    this.productTypeList.setList3(list, 'code', 'name', 'uuid');
    this.productTypeList.sort();
    this.addLoaded();
  }

  callBackDefectTy(list: DefectType[]) {
    this.defectTypeList.setList3(list, 'code', 'name', 'uuid');
    this.defectTypeList.sort();
    this.addLoaded();
  }

  callBackVen(vendors: QiVendor[]) {
    this.vendorList.setList3(vendors, 'code', 'firstName', 'uuid');
    this.addLoaded();
  }

  callBackFac(factories: QiFactory[]) {
    this.factoryList.setList3(factories, 'code', 'name', 'uuid');
    this.callBackActualFac(factories);
    this.addLoaded();
  }
  callBackActualFac(factories: QiFactory[]) {
    this.actualFactoryList.setList3(factories, 'code', 'name', 'uuid');
    this.addLoaded();
  }

  callBackDesigncode(designcodes: DesignCodeMaster[]) {
    this.designcodeList.setList3(designcodes, 'code', 'name', 'uuid');
    this.addLoaded();
  }

  callBackAgency(agencies: InspectionAgency[]) {
    this.agencyList.setList3(agencies, 'code', 'name', 'uuid');
    this.agencyList.sort();
    this.addLoaded();
  }

  callBackLab(labs: TestingLab[]) {
    this.labList.setList3(labs, 'code', 'name', 'uuid');
    this.labList.sort();
    this.addLoaded();
  }

  getResultsOptions() {
    return Util.getResultsList();
  }

  getSeasonAndYear() {
    let seasonYear =
      this.seasons.selectedItem + this.seasonYears.selectedItem[2] + this.seasonYears.selectedItem[3]
      + " - " + this.seasons.getSelectedLabel() + " "
      + this.seasonYears.selectedItem[2] + this.seasonYears.selectedItem[3];
    return seasonYear;
  }

  setSeasonAndYear(sy: string) {
    let season = sy[0];
    let year = "20" + sy[1] + sy[2];
    // this.common.seasons.setSelection(this.rec.seasonAndYear); // 117 - 1 - Core 17, 517 - 5 - Autumn Winter 17
    this.seasons.setSelection(season);
    this.seasonYears.setSelection(year);
  }

  copyForUpdate(from: QiSchedule) {
    let to = <QiSchedule>Util.clone(AMENDSCH);
    if (from.auditor != null) {
      to.auditor.uuid = from.auditor.uuid;
    }
    to.vendor.uuid = from.vendor.uuid;
    to.factory.uuid = from.factory.uuid;
    to.auditType.uuid = from.auditType.uuid;
    // to.category.uuid = from.category.uuid;
    to.seasonAndYear = from.seasonAndYear;
    to.auditId = from.auditId;
    to.auditDate = from.auditDate;
    to.status = from.status;
    to.designcodes[0].uuid = from.designcodes[0].uuid;
    to.designcodes[0].auditId = from.designcodes[0].auditId;
    to.designcodes[0].designcode = from.designcodes[0].designcode;
    to.designcodes[0].poNumber = from.designcodes[0].poNumber;
    to.designcodes[0].orderQty = from.designcodes[0].orderQty;
    to.designcodes[0].offerQty = from.designcodes[0].offerQty;
    to.designcodes[0].goldSeal = from.designcodes[0].goldSeal;
    to.designcodes[0].size = from.designcodes[0].size;
    to.designcodes[0].channel = from.designcodes[0].channel;
    to.designcodes[0].carton = from.designcodes[0].carton;
    to.designcodes[0].productTypeUid = from.designcodes[0].productTypeUid;
    to.designcodes[0].brandUid = from.designcodes[0].brandUid;
    to.designcodes[0].comments = from.designcodes[0].comments;
    to.designcodes[0].status = from.designcodes[0].status;
    to.designcodes[0].createdOn = from.designcodes[0].createdOn;
    to.designcodes[0].modifiedOn = from.designcodes[0].modifiedOn;
    to.designcodes[0].fabricCode = from.designcodes[0].fabricCode;
    to.designcodes[0].offerQty2 = from.designcodes[0].offerQty2;
    to.designcodes[0].offerQty3 = from.designcodes[0].offerQty3;
    return to;
  }
}
