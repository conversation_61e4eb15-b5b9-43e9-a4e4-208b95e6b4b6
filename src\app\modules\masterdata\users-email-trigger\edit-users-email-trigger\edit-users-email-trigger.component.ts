import { ChangeDetectorRef, Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CountryList } from 'app/core/qinspect/_common/country';
import { GenericList } from 'app/core/qinspect/_common/list';
import { Util } from 'app/core/qinspect/_common/util';
import { Qi<PERSON>udi<PERSON> } from 'app/core/qinspect/_model/auditor.model';
import { QiAuditorIncharge } from 'app/core/qinspect/_model/auditorincharge.model';
import { UserRole } from 'app/core/qinspect/_model/UserRole.model';
import { ROLE } from 'app/enum-constants';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';
import * as sha512 from 'js-sha512';
import { forkJoin } from 'rxjs';

import { APP_UI_CONFIG } from '../../../../app-config.constants';

@Component({
  selector: 'app-edit-users-email-trigger',
  templateUrl: './edit-users-email-trigger.component.html',
  styleUrls: ['./edit-users-email-trigger.component.scss']
})
export class EditUsersEmailTriggerComponent implements OnInit {
  rec: any = {};
  @Output() close: EventEmitter<any> = new EventEmitter();
  status: any;
  hdr: string = 'Add Auditor';
  editRec: boolean = false;
  codeinpt: boolean = false;
  ctryList: CountryList;
  aicList: GenericList<QiAuditorIncharge>;
  loading: boolean = false;
  auditorleads: QiAuditorIncharge[] = [];
  addRec: boolean = false;
  resetPwdValue: any = "random";
  tenantUid = this._commonService.getTenantUid();
  currentAuditorData: any;
  roleList: UserRole[] = [];
  selectedRoles: any[] = [];
  inspectionAgencyList: any[] = [];
  selectedInspectionAgency: any = {};
  reviewLevelList: any[] = [
    {
      label: 'One Level',
      value: 1
    },
    {
      label: 'Two Level',
      value: 2
    }
  ];
  selectedReviewLevel: any = {};
  constructor(
    private _authService: AuthService,
    private invokeService: InvokeService,
    private cdRef: ChangeDetectorRef,
    private _commonService: CommonService,
    public dialogRef: MatDialogRef<EditUsersEmailTriggerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any) { }

  ngOnInit(): void {
    this.ctryList = new CountryList();
    this.aicList = new GenericList<QiAuditorIncharge>();
    this.currentAuditorData = this.data.data;
    this.status = this.data.status;
    APP_UI_CONFIG.masterdata.auditorincharge.get.param = null;
    const inChargeObs = this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.auditorincharge.get);
    const roleObs = this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.userRole.get);
    const agencyObs = this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.inspectionAgency.get);
    forkJoin([inChargeObs, roleObs, agencyObs]).subscribe(results => {
      this.callBackAIC(results[0]);
      this.roleList = results[1];
      this.inspectionAgencyList = results[2];
      if (this.data && this.data.data) {
        if (this.status == 'edit')
          this.onEdit(this.currentAuditorData);
        else if (this.status = "view") {
          this.onView(this.currentAuditorData);
        }
      } else {
        this.openAddRecord();
      }
    });
  }

  public openAddRecord() {
    this.addRec = true;
    this.hdr = 'Add User';
    this.rec = {};
    this.rec.setPasswordAsUserName = true;
    this.editRec = false;
    this.codeinpt = false;
    this.ctryList.setCountry(this.rec.country);
    this.aicList.setValue(this.rec.inchargeUid);

    if(this._authService.hasLoggedUserPermitted(ROLE.ADMIN))
      this.editRec = false;
    else
      this.editRec = true;
  }

  onEdit(data) {
    this.addRec = false;
    if(this._authService.hasLoggedUserPermitted(ROLE.ADMIN) || this._authService.hasLoggedUserPermitted(ROLE.OPERATIONS)) {
      this.hdr = 'Update User';
      this.editRec = false;
    } else {
      this.hdr = 'View User';
      this.editRec = true;
    }
    this.codeinpt = true;
    this.rec = <any>Util.clone(data);
    this.ctryList.setCountry(this.rec.country);
    this.aicList.setValue(this.rec.inchargeUid);
    this.selectedRoles = this.roleList?.filter(role => this.rec.role.split(',').includes(role.code));
    this.selectedInspectionAgency = this.inspectionAgencyList.find(agency => agency.uuid == this.rec.inspectionAgencyUid);
    this.selectedReviewLevel = this.reviewLevelList.find(level => level.value == this.rec.reviewStep);
  }

  callBackAIC(arr: QiAuditorIncharge[]) {
    this.auditorleads = arr;
    this.aicList.setList2(this.auditorleads, 'firstName', 'lastName', 'userName', 'uuid');
    this.cdRef.detectChanges();
  }

  public closeAddAuditor(): void {
    this.dialogRef.close(true);
  }

  public onSave() {
    this.loading = true;
    this.rec.inchargeUid = this.aicList.selectedItem;
    this.rec.country = this.ctryList.selectedCtry;
    this.rec.role = this.selectedRoles?.map(role => role.code)?.join(',');
    this.rec.inspectionAgencyUid = this.selectedInspectionAgency?.uuid;
    this.rec.reviewStep = this.selectedReviewLevel?.value;
    if (this.currentAuditorData && this.currentAuditorData.uuid) {
      this.rec.password = sha512.sha512(this.rec.userName);
    } else {
      this.rec.password = sha512.sha512(this.rec.code);
    }
    
    if (this.rec.uuid != null) {
      this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.users.update, null, null, Util.clone(this.rec)).subscribe(res => {
        this.processResponse(res);
        this._commonService.success(res.message);
      }, (err: any) => {
        this._commonService.handleError(err);
      })

    } else {
      this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.users.add, null, null, Util.clone(this.rec)).subscribe(res => {
        this.processResponse(res);
        this._commonService.success(res.message);
      }, (err: any) => {
        this._commonService.handleError(err);
      })
    }
  }

  processResponse(data) {
    this.loading = false;
    if (data.code == "200") {
      this.closeAddAuditor();
    }
    else {
      this._commonService.toaster.warning(data.message);
    }
  }

  setToggleValue(e) {
    let val = 0
    if (e.checked) {
      val = 1
    }
    this.rec.setPasswordAsUserName = val;
  }

  setResetPassword(e) {
    let val = 0
    if (e.target.checked) {
      val = 1
    }
    this.rec.resetPassword = val;
  }
  changeResetPwdValue(e) {
    if (e.value == "same") {
      this.rec.setPasswordAsUserName = true;
    } else {
      this.rec.setPasswordAsUserName = false;
    }
  }

  public hasAuditorRole(): boolean {
    return this.selectedRoles?.filter(role => role.code == 'AUDITOR' || role.code == 'REVIEWER').length > 0;
  }

  onView(data) {
    this.editRec = true;
    this.addRec = false;
    this.hdr = 'View Auditor';
    this.codeinpt = true;
    this.rec = <any>Util.clone(data);
    this.ctryList.setCountry(this.rec.country);
    this.aicList.setValue(this.rec.inchargeUid);
    this.cdRef.detectChanges();
    if (this.tenantUid == 'godrej') {
      this.rec.setPasswordAsUserName = true;
      this.resetPwdValue = 'same'
    }
  }
}

