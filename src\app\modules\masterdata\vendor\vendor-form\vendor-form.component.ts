import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatAccordion } from '@angular/material/expansion';
import { ActivatedRoute, Data, Router } from '@angular/router';
import { CountryList } from 'app/core/qinspect/_common/country';
import { QiFactory } from 'app/core/qinspect/_model/factory.model';
import { QiSchedule } from 'app/core/qinspect/_model/schedule.model';
import { QiVendor } from 'app/core/qinspect/_model/vendor.model';
import {
  customers,
  facProductTypes,
  factories,
  inhouseTestings,
  processes,
  productTypes,
  qualityTeams,
  vendorGenForm,
} from 'app/core/qinspect/_model/VendorGenral.model';
import { CommonService } from 'app/services/common.service';
import { DashboardService } from 'app/services/dashboard.service';
import { InvokeService } from 'app/services/invoke.service';
import { MessageService } from 'app/services/message.service';
import { environment } from 'environments/environment';
import { ToastrService } from 'ngx-toastr';

export interface imageUrlType {
  factoryIndex: number;
  imageIndex: number;
  imageUrl: string;
}
@Component({
  selector: 'vendor-form',
  templateUrl: './vendor-form.component.html',
  styleUrls: ['./vendor-form.component.scss'],
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { showError: true },
    },
  ],

})
export class VendorFormComponent implements OnInit {
  //vgi stands for Vendor General Information
  //vps stands for Vendor Product/Category Specialty

  vendorGenForm: vendorGenForm = new vendorGenForm();
  vendorFormData: any = {};

  @ViewChild(MatAccordion) accordion: MatAccordion;
  @ViewChild('fileInput') el: ElementRef;
  imageUrl: imageUrlType[] = [];
  editFile: boolean = true;
  removeUpload: boolean = false;

  vgiFormGroup: FormGroup;
  vpsFormGroup: FormGroup;
  empForm: FormGroup;
  vpsFormDataArray: FormArray;
  vpsBestSellingFormGroup: FormGroup;
  vpsBestSellingFormDataArray: FormArray;
  toogle = new FormControl('', []);
  toogle2 = new FormControl('', []);
  companyName = "Amazon";


  yseNo: string[] = ['YES', 'NO'];
  facLicyseNo: string[] = ['YES', 'NO'];
  countryList: CountryList;
  companyType: string[] = ['Direct Manufacturer - Foreign Invested', 'Direct Manufacturer - Private or Government Owned Enterprise',
    'Trader/Manufacturer - Uses several subcontracts but owns at least one production facility', 'Importer/Trader with license or brand', 'Importer/Trader', 'Other'];
  ownershipType: string[] = ['Sole Proprietorship', 'Private Limited', 'PartnerShip', 'Limited PartnerShip', 'Limited Liability Company (LLC)', 'Public Listed', 'Other'];

  lastFinancialYear: string;
  currentFinancialYear: string;
  factoryList: any[] = [1];
  hdr: string = 'View Vendor Details'
  tenantConfig: any;
  isEdit = false;
  // @Input() data: any;
  @Input() status: string;
  @Input() data: any;
  @Output() close: EventEmitter<any> = new EventEmitter();

  employees1: FormArray;

  constructor(

    private _formBuilder: FormBuilder,
    private cd: ChangeDetectorRef,
    private toaster: ToastrService,
    private _commonService: CommonService,
    private invokeService: InvokeService,
    public _dashboardService: DashboardService,
    private _router: Router,
    private _messageService: MessageService
  ) { }

  ngOnInit(): void {
    this.tenantConfig = JSON.parse(localStorage.getItem('user'));
    this.initlizeForms();
    if (this.data?.uuid) {
      this.fetchVendorFormData(this.data?.uuid);
    }
    else if (this.tenantConfig.role == 'VENDOR' && this.tenantConfig.vendorApprovalStatus == 0) {
      this.fetchVendorFormData(this.tenantConfig?.uuid);
    }

  }

  public initlizeForms() {
    this.countryList = new CountryList();
    this.vendorGenInfoFormInti();
    this.vpsformIntilization();
    this.vpsBestSellingformIntilization();
    this.empForm = this._formBuilder.group({
      employees: this._formBuilder.array([])
    });
    this.addEmployee();
    if (Object.keys(this.vendorFormData).length == 0) {
      this.toogle.valueChanges.subscribe(newToogleValue => {
        this.populateCommonVendorFactoryFields(newToogleValue);
      });
    }
  }

  public isVendorFormData(): boolean {
    return Object.keys(this.vendorFormData).length != 0;
  }

  public vendorGenInfoFormInti() {
    //vgi stands for Vendor General Information
    this.vgiFormGroup = this._formBuilder.group({
      vgiUpdatedOn: [''],
      vgiEcomBiz: [''],
      vgiIsAmazVendor: [''],
      vgiVendorId: [''],
      vgiAmazonSeller: [''],
      vgiRegEmail: ['', [Validators.email]],

      vgiVendorNameEng: [this.vendorFormData.name || '', Validators.required],
      // vgiVendorNamelocal: ['', Validators.required],
      vgiVendorAddressEng: [this.vendorFormData.addressLine1 || '', Validators.required],
      // vgiVendorAddresslocal: ['', Validators.required],

      vgiVendorFullAddressEng: this._formBuilder.group({
        city: [this.vendorFormData.city || '', [Validators.required]],
        state: [this.vendorFormData.state || '', [Validators.required]],
        pincode: [this.vendorFormData.pincode || '', [Validators.required]],
        country: [this.vendorFormData.country || '', [Validators.required]],
      }),

      vgiChiefDeciMaker: this._formBuilder.group({
        name: [this.vendorFormData.decisionMakerName || '', [Validators.required, Validators.maxLength(200)]],
        title: [this.vendorFormData.decisionMakerTitle || '', [Validators.required, Validators.maxLength(100)]],
        telephone: [this.vendorFormData.decisionMakerTelephone || '', [Validators.required, Validators.maxLength(20)]],
        cellphone: [this.vendorFormData.decisionMakerMobile || '', [Validators.required, Validators.maxLength(20)]],
        email: [this.vendorFormData.decisionMakerEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      vgiMainContactPerson: this._formBuilder.group({
        name: [this.vendorFormData.mainContactName || '', [Validators.required, Validators.maxLength(200)]],
        title: [this.vendorFormData.mainContactTitle || '', [Validators.required, Validators.maxLength(100)]],
        telephone: [this.vendorFormData.mainContactTelephone || '', [Validators.required, Validators.maxLength(20)]],
        cellphone: [this.vendorFormData.mainContactMobile || '', [Validators.required, Validators.maxLength(20)]],
        email: [this.vendorFormData.mainContactEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      vgiQualityContactPerson: this._formBuilder.group({
        name: [this.vendorFormData.qualityContactName || '', [Validators.required, Validators.maxLength(200)]],
        title: [this.vendorFormData.qualityContactTitle || '', [Validators.required, Validators.maxLength(100)]],
        telephone: [this.vendorFormData.qualityContactTelephone || '', [Validators.required, Validators.maxLength(20)]],
        cellphone: [this.vendorFormData.qualityContactMobile || '', [Validators.required, Validators.maxLength(20)]],
        email: [this.vendorFormData.qualityContactEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      vgiWebsite: [this.vendorFormData.website || '', Validators.maxLength(200)],
      vgiTypeOfCompany: [this.vendorFormData.companyType || '', [Validators.required, Validators.maxLength(200)]],
      vgiTypeOfCompanyOther: [''],
      vgiTradeMark: [this.vendorFormData.trademark?.length > 0 ? 'YES' : 'NO' || '', [Validators.required, Validators.maxLength(200)]],
      vgiTradeMarkName: [this.vendorFormData.trademark || ''],
      vgiDateofEstablish: [this.vendorFormData.establishedDate || '', Validators.required],

      vgiTypeOfOwnerShip: [this.vendorFormData.ownershipType || '', Validators.required],
      vgiTypeOfOwnerShipOther: [''],
      vgiFactoryLic: [this.vendorFormData.licenseNo?.length > 0 ? 'YES' : 'NO' || '', Validators.required],
      vgiFactoryLicName: [''],

    });
  }

  private vpsformIntilization(): void {
    this.vpsFormGroup = this._formBuilder.group({
      vpsFormDataArray: this._formBuilder.array([])
    });
    this.addNewRow();
  }

  private vpsBestSellingformIntilization(): void {
    this.vpsBestSellingFormGroup = this._formBuilder.group({
      vpsBestSellingFormDataArray: this._formBuilder.array([])
    });
    this.addNewRow2();
  }

  public getDynamicFormControls(name): FormArray {
    return this.vpsFormGroup.get(name) as FormArray;
  }

  public getDynamicFormControls2(name): FormArray {
    return this.vpsBestSellingFormGroup.get(name) as FormArray;
  }

  private createVpsformIntilizationFormRow(): any {
    return this._formBuilder.group({
      vpsProductCategManuf: [{ value: '', disabled: false }, [Validators.required]],
      vpsProductIncInEachCateg: [{ value: '', disabled: false }, [Validators.required]],
      isSelected: [{ value: false, disabled: false }, [Validators.required]],
      vpsYearExp: [{ value: '', disabled: false }, [Validators.required]],
      vpsPercentageOfYourBuss: [{ value: '', disabled: false }],
      isNewFormRow: [{ value: true, disabled: false }],
    });
  }

  private createVpsformIntilizationFormRowWithData(data): any {
    return this._formBuilder.group({
      vpsProductCategManuf: [{ value: data.productTypeCategoryName, disabled: false }, [Validators.required]],
      vpsProductIncInEachCateg: [{ value: data.productTypeName, disabled: false }, [Validators.required]],
      isSelected: [{ value: (data.plannedForSourcing == '1' ? true : false), disabled: false }],
      vpsYearExp: [{ value: data.experienceInYrs, disabled: false }, [Validators.required]],
      vpsPercentageOfYourBuss: [{ value: data.percentOfBusiness, disabled: false }, [Validators.required]],
      isNewFormRow: [{ value: true, disabled: false }],
    });
  }

  private createVpsbestSellingformIntilizationFormRow(): any {
    return this._formBuilder.group({
      vpsMainCust: [{ value: '', disabled: false }],
      vpsProductDesc: [{ value: '', disabled: false }],
      vpsCountry: [{ value: '', disabled: false }],
      vpsQtyShipped: [{ value: '', disabled: false }],
      isNewFormRow: [{ value: true, disabled: false }],
    });
  }

  private createVpsbestSellingformIntilizationFormRowWithData(data): any {
    return this._formBuilder.group({
      vpsMainCust: [{ value: data.customerName, disabled: false }],
      vpsProductDesc: [{ value: data.productDescription, disabled: false }],
      vpsCountry: [{ value: data.country, disabled: false }],
      vpsQtyShipped: [{ value: data.qtyShipped, disabled: false }],
      isNewFormRow: [{ value: true, disabled: false }],
    });
  }

  public addNewRow(): void {
    this.vpsFormDataArray = this.vpsFormGroup.get('vpsFormDataArray') as FormArray;
    if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.productTypes) {
      this.vendorFormData.productTypes.forEach(row => {
        this.vpsFormDataArray.push(this.createVpsformIntilizationFormRowWithData(row));
      })

    } else {
      this.vpsFormDataArray.push(this.createVpsformIntilizationFormRow());
    }
  }

  public addNewRow2(): void {
    this.vpsBestSellingFormDataArray = this.vpsBestSellingFormGroup.get('vpsBestSellingFormDataArray') as FormArray;
    if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.customers) {
      this.vendorFormData.customers.forEach(row => {
        this.vpsBestSellingFormDataArray.push(this.createVpsbestSellingformIntilizationFormRowWithData(row));
      })
    } else {
      this.vpsBestSellingFormDataArray.push(this.createVpsbestSellingformIntilizationFormRow());
    }
  }

  // private findIfNewFieldExist(): any {
  //   let newFormGroup: any[] = this.getDynamicFormControls().controls;
  //   let findNewFormIndex = newFormGroup.findIndex(item => {
  //     return (item.controls.isNewFormRow.value == true)
  //   });
  //   return findNewFormIndex;
  // }

  public employees(): FormArray {
    return this.empForm.get('employees') as FormArray;
  }

  public newEmployee(): FormGroup {

    return this._formBuilder.group({
      fgiFactoryOwnership: ['', Validators.required],
      fgiFactoryNameEng: ['', Validators.required],
      fgiFactoryAddressEng: ['', Validators.required],

      fgiFactoryFullAddressEng: this._formBuilder.group({
        city: ['', [Validators.required]],
        state: ['', [Validators.required]],
        pincode: ['', [Validators.required]],
        country: ['', [Validators.required]],
      }),

      fgiChiefDeciMaker: this._formBuilder.group({
        name: ['', [Validators.required, Validators.maxLength(200)]],
        title: ['', [Validators.required, Validators.maxLength(100)]],
        telephone: ['', [Validators.required, Validators.maxLength(20)]],
        cellphone: ['', [Validators.required, Validators.maxLength(20)]],
        email: ['', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiBusinessContactPerson: this._formBuilder.group({
        name: ['', [Validators.required, Validators.maxLength(200)]],
        title: ['', [Validators.required, Validators.maxLength(100)]],
        telephone: ['', [Validators.required, Validators.maxLength(20)]],
        cellphone: ['', [Validators.required, Validators.maxLength(20)]],
        email: ['', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiAuditContactPerson: this._formBuilder.group({
        name: ['', [Validators.required, Validators.maxLength(200)]],
        title: ['', [Validators.required, Validators.maxLength(100)]],
        telephone: ['', [Validators.required, Validators.maxLength(20)]],
        cellphone: ['', [Validators.required, Validators.maxLength(20)]],
        email: ['', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiIsoCertificate: ['', [Validators.required, Validators.maxLength(200)]],
      fgiIsoDateOfCertificate: ['', Validators.maxLength(20)],
      fgiFactoryCapacityperMonth: ['', [Validators.required]],
      fgiFactoryAvailVolume: ['', [Validators.required]],
      fgiFactoryworkerCount: ['', [Validators.required]],
      fgiFactorycomments: [''],
      fgiDepForQualityControl: ['', [Validators.required]],
      fgiDepForProductDev: ['', [Validators.required]],
      fgiProductTestingMaintaince: ['', [Validators.required]],

      fgiQualityTeamEngineerPerson: ['', [Validators.required]],
      fgiQualityTeamEngineerYearOfExp: ['', [Validators.required]],
      fgiQualityTeamIQCPerson: ['', [Validators.required]],
      fgiQualityTeamIQCYearOfExp: ['', [Validators.required]],
      fgiQualityTeamInLineIQCPerson: ['', [Validators.required]],
      fgiQualityTeamInLineIQCYearOfExp: ['', [Validators.required]],
      fgiQualityTeamFinalQCPerson: ['', [Validators.required]],
      fgiQualityTeamFinalQCYearOfExp: ['', [Validators.required]],

      processControll: this._formBuilder.array([]),
      qualityTeams: this._formBuilder.array([]),
      inHouseTesting: this._formBuilder.array([]),
      productImage: this._formBuilder.array([]),
    });
  }



  public addEmployee() {
    let name = [
      'Quality Engineer',
      'IQC (Incoming material checking)',
      'In-Line QC',
      'Final QC'
    ]
    let isThere: boolean = false;
    if (this.vendorFormData?.factories) {
      this.vendorFormData.factories.forEach((row) => {
        this.employees().push(this.populateFactoryFormWithData(row));
        this.addfactoryProcessControll(this.employees().length - 1);
        this.addfactoryinHouseTesting(this.employees().length - 1);
        this.addnewProductImage(this.employees().length - 1);

        if (row.qualityTeams) {
          row.qualityTeams.forEach(element => {
            if (name.includes((element.qualityTeamName)) == false) {

            }
            else {
              isThere = true;
            }
          });
        }
        if (isThere) {
          this.addQualityTeamControll(this.employees().length - 1);
        }
      })
    } else {
      this.employees().push(this.newEmployee());
      this.addfactoryProcessControll(this.employees().length - 1);
      this.addfactoryinHouseTesting(this.employees().length - 1);
      this.addnewProductImage(this.employees().length - 1);
    }
  }

  public removeEmployee(empIndex: number) {
    //write code to delete image also
    // this.imageUrl.splice(processIndex, 1);
    this.employees().removeAt(empIndex);
  }


  // these functions are to add and remove Process Controlled table inside Capability fields of each factory
  public factoryProcessControll(index: number): FormArray {
    return this.employees()
      .at(index)
      .get('processControll') as FormArray;
  }

  public newProcessControll(): FormGroup {
    return this._formBuilder.group({
      inHouseProcessName: '',
      inHouseKeyOperation: '',
    });
  }

  public newProcessControllWithData(data): FormGroup {
    return this._formBuilder.group({
      inHouseProcessName: data.processName || '',
      inHouseKeyOperation: data.keyOperations || '',
    });
  }

  public addfactoryProcessControll(index: number) {
    if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.factories) {
      let dataArray: any[] = this.vendorFormData?.factories[index]?.processes;
      dataArray.forEach((element) => {
        this.factoryProcessControll(index).push(this.newProcessControllWithData(element));
      });
      if (dataArray.length == 0) {
        this.factoryProcessControll(index).push(this.newProcessControll());
      }
    } else
      this.factoryProcessControll(index).push(this.newProcessControll());
  }

  public removefactoryProcessControll(index: number, processIndex: number) {
    this.factoryProcessControll(index).removeAt(processIndex);
  }
  // these functions are to add and remove Process Controlled table inside Capability fields of each factory

  // these functions are to add and remove qualityTeams table of each factory
  public factoryqualityTeamsControll(index: number): FormArray {
    return this.employees()
      .at(index)
      .get('qualityTeams') as FormArray;
  }

  public newQualityTeamControll(): FormGroup {
    return this._formBuilder.group({
      qualityTeamName: '',
      personCount: '',
      avgExperienceInYrs: ''
    });
  }

  public newQualityTeamControllWithData(data): FormGroup {
    return this._formBuilder.group({
      qualityTeamName: data.qualityTeamName || '',
      personCount: data.personCount || '',
      avgExperienceInYrs: data.avgExperienceInYrs || ''
    });
  }

  public addQualityTeamControll(index: number) {
    let name = [
      'Quality Engineer',
      'IQC (Incoming material checking)',
      'In-Line QC',
      'Final QC'
    ]
    if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.factories[index]?.qualityTeams) {
      this.vendorFormData?.factories[index]?.qualityTeams.forEach(element => {
        if (name.includes((element.qualityTeamName))) {

        } else {
          this.factoryqualityTeamsControll(index).push(this.newQualityTeamControllWithData(element));
        }
      });
    } else
      this.factoryqualityTeamsControll(index).push(this.newQualityTeamControll());
  }

  public removeQualityTeamControll(index: number, processIndex: number) {
    this.factoryqualityTeamsControll(index).removeAt(processIndex);
  }
  // these functions are to add and remove qualityTeams table  of each factory


  // these functions are to add and remove  FACTORY IN-HOUSE TESTING CAPABILITY (LAB/ON-SITE TESTING)table inside Capabilty fields of each factory
  public factoryinHouseTesting(index: number): FormArray {
    return this.employees()
      .at(index)
      .get('inHouseTesting') as FormArray;
  }

  public newinHouseTesting(): FormGroup {
    return this._formBuilder.group({
      typeOfTest: '',
      scales: '',
      equipment: '',
      testOfStandard: ''
    });
  }

  public newinHouseTestingWithData(data): FormGroup {
    return this._formBuilder.group({
      typeOfTest: data.testName || '',
      scales: data.scales || '',
      equipment: data.equipment || '',
      testOfStandard: data.standardTest || ''
    });
  }

  public addfactoryinHouseTesting(index: number) {
    if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.factories) {
      let dataArray: any[] = this.vendorFormData?.factories[index]?.inhouseTestings;
      dataArray.forEach((element) => {
        this.factoryinHouseTesting(index).push(this.newinHouseTestingWithData(element));
      });
      if (dataArray.length == 0) {
        this.factoryinHouseTesting(index).push(this.newinHouseTesting())
      }
    } else
      this.factoryinHouseTesting(index).push(this.newinHouseTesting());
  }

  public removefactoryinHouseTesting(index: number, processIndex: number) {
    this.factoryinHouseTesting(index).removeAt(processIndex);
  }
  // these functions are to add and remove  FACTORY IN-HOUSE TESTING CAPABILITY (LAB/ON-SITE TESTING) table inside Capability fields of each factory



  // these functions are to add and remove  product image  inside Capability fields of each factory
  public factoryProductImage(index: number): FormArray {
    return this.employees()
      .at(index)
      .get('productImage') as FormArray;
  }

  public newProductImage(): FormGroup {
    return this._formBuilder.group({
      name: '',
      desc: '',
      imageFile: [null],
    });
  }

  public newProductImageWithData(data): FormGroup {
    return this._formBuilder.group({
      name: data.productTypeName || '',
      desc: '',
      imageFile: data.productImageUrl || '',
    });
  }

  public addnewProductImage(index: number) {
    if (Object.keys(this.vendorFormData).length != 0 && this.vendorFormData?.factories) {
      let dataArray: any[] = this.vendorFormData?.factories[index]?.productTypes;
      dataArray.forEach((element) => {
        this.factoryProductImage(index).push(this.newProductImageWithData(element));
      });
      if (dataArray.length == 0) {
        this.factoryProductImage(index).push(this.newProductImage());
      }
    } else
      this.factoryProductImage(index).push(this.newProductImage());
  }

  public removeProductImage(index: number, processIndex: number) {
    // this.imageUrl.splice(processIndex, 1);
    this.factoryProductImage(index).removeAt(processIndex);
  }

  /*########################## File Upload ########################*/


  uploadFile(event, i, empIndex) {
    let reader = new FileReader(); // HTML5 FileReader API
    let file = event.target.files[0];
    if (event.target.files && event.target.files[0]) {
      reader.readAsDataURL(file);

      // When file uploads set it to file formcontrol
      reader.onload = () => {
        this.imageUrl.push({
          factoryIndex: empIndex,
          imageIndex: i,
          imageUrl: reader.result + ''
        })

        this.factoryProductImage(empIndex).controls[i]['controls'].imageFile?.setValue(reader.result + '');

        //  this.imageUrl.splice(i, 0, reader.result + '')
        // this.registrationForm.patchValue({
        //   file: reader.result
        // });

        this.editFile = false;
        this.removeUpload = true;
      }
      // ChangeDetectorRef since file is loading outside the zone
      this.cd.markForCheck();
    }

  }

  public fetchImage(factIndex, imgIndex): string {
    let url = '';
    url = this.factoryProductImage(factIndex).controls[imgIndex]['controls'].imageFile?.value;
    return url;
  }
  

  public populateCommonVendorFactoryFields(value: boolean) {
    if (value) {
      this.employees().patchValue([{
        'fgiFactoryNameEng': this.vgiFormGroup.get('vgiVendorNameEng').value,
        'fgiFactoryAddressEng': this.vgiFormGroup.get('vgiVendorAddressEng').value,
        'fgiFactoryFullAddressEng': {
          city: this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'city']).value,
          state: this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'state']).value,
          pincode: this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'pincode']).value,
          country: this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'country']).value,
        },
        'fgiChiefDeciMaker': {
          name: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'name']).value,
          title: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'title']).value,
          telephone: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'telephone']).value,
          cellphone: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'cellphone']).value,
          email: this.vgiFormGroup.get(['vgiChiefDeciMaker', 'email']).value,
        },
        'fgiBusinessContactPerson': {
          name: this.vgiFormGroup.get(['vgiMainContactPerson', 'name']).value,
          title: this.vgiFormGroup.get(['vgiMainContactPerson', 'title']).value,
          telephone: this.vgiFormGroup.get(['vgiMainContactPerson', 'telephone']).value,
          cellphone: this.vgiFormGroup.get(['vgiMainContactPerson', 'cellphone']).value,
          email: this.vgiFormGroup.get(['vgiMainContactPerson', 'email']).value,
        }
      },
      { selfOnly: true }]);
    } else {
      this.employees().patchValue([{}, { selfOnly: true }]);
    }

  }

  public populateFactoryFormWithData(data): any {
    return this._formBuilder.group({
      fgiFactoryOwnership: [data.ownership || '', Validators.required],
      fgiFactoryNameEng: [data.name || '', Validators.required],
      fgiFactoryAddressEng: [data.addressLine1 || '', Validators.required],

      fgiFactoryFullAddressEng: this._formBuilder.group({
        city: [data.city || '', [Validators.required]],
        state: [data.state || '', [Validators.required]],
        pincode: [data.pincode || '', [Validators.required]],
        country: [data.country || '', [Validators.required]],
      }),

      fgiChiefDeciMaker: this._formBuilder.group({
        name: [data.decisionMakerName || '', [Validators.required, Validators.maxLength(200)]],
        title: [data.decisionMakerTitle || '', [Validators.required, Validators.maxLength(100)]],
        telephone: [data.decisionMakerTelephone || '', [Validators.required, Validators.maxLength(20)]],
        cellphone: [data.decisionMakerMobile || '', [Validators.required, Validators.maxLength(20)]],
        email: [data.decisionMakerEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiBusinessContactPerson: this._formBuilder.group({
        name: [data.mainContactName || '', [Validators.required, Validators.maxLength(200)]],
        title: [data.mainContactTitle || '', [Validators.required, Validators.maxLength(100)]],
        telephone: [data.mainContactTelephone || '', [Validators.required, Validators.maxLength(20)]],
        cellphone: [data.mainContactMobile || '', [Validators.required, Validators.maxLength(20)]],
        email: [data.mainContactEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiAuditContactPerson: this._formBuilder.group({
        name: [data.qualityContactName || '', [Validators.required, Validators.maxLength(200)]],
        title: [data.qualityContactTitle || '', [Validators.required, Validators.maxLength(100)]],
        telephone: [data.qualityContactTelephone || '', [Validators.required, Validators.maxLength(20)]],
        cellphone: [data.qualityContactMobile || '', [Validators.required, Validators.maxLength(20)]],
        email: [data.qualityContactEmail || '', [Validators.required, Validators.email, Validators.maxLength(200)]],
      }),

      fgiIsoCertificate: [data.isoCertificationDate ? 'YES' : 'NO', [Validators.required, Validators.maxLength(200)]],
      fgiIsoDateOfCertificate: [data.isoCertificationDate || '', , Validators.maxLength(20)],
      fgiFactoryCapacityperMonth: [data.capacityPerMonth || '', [Validators.required]],
      fgiFactoryAvailVolume: [data.availableCapacityPerMonth || '', [Validators.required]],
      fgiFactoryworkerCount: [data.workerCount || '', [Validators.required]],
      fgiFactorycomments: [data.comments || ''],
      fgiDepForQualityControl: [data.hasDedicatedQualityTeam == 1 ? 'YES' : 'NO', [Validators.required]],
      fgiDepForProductDev: [data.hasDedicatedProductDevTeam == 1 ? 'YES' : 'NO', [Validators.required]],
      fgiProductTestingMaintaince: [data.productQualityAudit == 1 ? 'YES' : 'NO', [Validators.required]],

      fgiQualityTeamEngineerPerson: [this.personCount(data.qualityTeams, 'Quality Engineer') || '', [Validators.required]],
      fgiQualityTeamEngineerYearOfExp: [this.avgExperienceInYrs(data.qualityTeams, 'Quality Engineer') || '', [Validators.required]],
      fgiQualityTeamIQCPerson: [this.personCount(data.qualityTeams, 'IQC (Incoming material checking)') || '', [Validators.required]],
      fgiQualityTeamIQCYearOfExp: [this.avgExperienceInYrs(data.qualityTeams, 'IQC (Incoming material checking)') || '', [Validators.required]],
      fgiQualityTeamInLineIQCPerson: [this.personCount(data.qualityTeams, 'In-Line QC') || '', [Validators.required]],
      fgiQualityTeamInLineIQCYearOfExp: [this.avgExperienceInYrs(data.qualityTeams, 'In-Line QC') || '', [Validators.required]],
      fgiQualityTeamFinalQCPerson: [this.personCount(data.qualityTeams, 'Final QC') || '', [Validators.required]],
      fgiQualityTeamFinalQCYearOfExp: [this.avgExperienceInYrs(data.qualityTeams, 'Final QC') || '', [Validators.required]],

      processControll: this._formBuilder.array([]),
      qualityTeams: this._formBuilder.array([]),
      inHouseTesting: this._formBuilder.array([]),
      productImage: this._formBuilder.array([]),
    });
  }

  public personCount(data: any[], name: string): any {
    let ret = undefined;
    data.forEach(element => {
      if ((element.qualityTeamName + '').trim().toLowerCase() == (name + '').trim().toLowerCase()) {
        ret = element.personCount;
      }
    });
    return ret;
  }
  public avgExperienceInYrs(data: any[], name: string): any {
    let ret = undefined;
    data.forEach(element => {
      if ((element.qualityTeamName + '').trim().toLowerCase() == (name + '').trim().toLowerCase()) {
        ret = element.avgExperienceInYrs;
      }
    });
    return ret;
  }

  public addNewFactory() {
    this.factoryList.push(1);
  }

  public isPrevProductImage(): boolean {
    return true;
  }

  onSubmitForm() {
    this.vendorGenForm = new vendorGenForm();
    this.vendorGenForm.code = 'abc';
    // this.vendorGenForm.uuid = this.vendorFormData.uuid;
    this.vendorGenForm.name = this.vgiFormGroup.controls['vgiVendorNameEng'].value;
    this.vendorGenForm.addressLine1 = this.vgiFormGroup.controls['vgiVendorAddressEng'].value;
    // this.vendorGenForm.addressLine2 = this.vgiFormGroup.controls['vgiVendorAddresslocal'].value;
    this.vendorGenForm.city = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'city']).value;
    this.vendorGenForm.pincode = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'pincode']).value;
    this.vendorGenForm.country = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'country']).value;
    this.vendorGenForm.state = this.vgiFormGroup.get(['vgiVendorFullAddressEng', 'state']).value;

    this.vendorGenForm.decisionMakerName = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'name']).value;
    this.vendorGenForm.decisionMakerTitle = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'title']).value;
    this.vendorGenForm.decisionMakerTelephone = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'telephone']).value;
    this.vendorGenForm.decisionMakerMobile = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'cellphone']).value;
    this.vendorGenForm.decisionMakerEmail = this.vgiFormGroup.get(['vgiChiefDeciMaker', 'email']).value;

    this.vendorGenForm.mainContactName = this.vgiFormGroup.get(['vgiMainContactPerson', 'name']).value;
    this.vendorGenForm.mainContactTitle = this.vgiFormGroup.get(['vgiMainContactPerson', 'title']).value;
    this.vendorGenForm.mainContactTelephone = this.vgiFormGroup.get(['vgiMainContactPerson', 'telephone']).value;
    this.vendorGenForm.mainContactMobile = this.vgiFormGroup.get(['vgiMainContactPerson', 'cellphone']).value;
    this.vendorGenForm.mainContactEmail = this.vgiFormGroup.get(['vgiMainContactPerson', 'email']).value;

    this.vendorGenForm.qualityContactName = this.vgiFormGroup.get(['vgiQualityContactPerson', 'name']).value;
    this.vendorGenForm.qualityContactTitle = this.vgiFormGroup.get(['vgiQualityContactPerson', 'title']).value;
    this.vendorGenForm.qualityContactTelephone = this.vgiFormGroup.get(['vgiQualityContactPerson', 'telephone']).value;
    this.vendorGenForm.qualityContactMobile = this.vgiFormGroup.get(['vgiQualityContactPerson', 'cellphone']).value;
    this.vendorGenForm.qualityContactEmail = this.vgiFormGroup.get(['vgiQualityContactPerson', 'email']).value;

    this.vendorGenForm.website = this.vgiFormGroup.get('vgiWebsite').value;
    this.vendorGenForm.companyType = this.vgiFormGroup.get('vgiTypeOfCompany').value == 'Other' ? this.vgiFormGroup.get('vgiTypeOfCompanyOther').value : this.vgiFormGroup.get('vgiTypeOfCompany').value;
    this.vendorGenForm.ownershipType = this.vgiFormGroup.get('vgiTypeOfOwnerShip').value == 'Other' ? this.vgiFormGroup.get('vgiTypeOfOwnerShipOther').value : this.vgiFormGroup.get('vgiTypeOfOwnerShip').value;
    this.vendorGenForm.trademark = this.vgiFormGroup.get('vgiTradeMarkName').value;
    this.vendorGenForm.licenseNo = this.vgiFormGroup.get('vgiFactoryLicName').value;
    this.vendorGenForm.establishedDate = this.vgiFormGroup.get('vgiDateofEstablish').value;

    let items: any[] = this.vpsFormGroup.value.vpsFormDataArray;
    let exists = items.some(a => a.vpsProductCategManuf ? true : false);

    if (exists) {
      let vpsFormGroupItems: productTypes[] = [];
      items.forEach(item => {
        if (item.vpsProductCategManuf) {
          let obj: productTypes = {
            productTypeCategoryName: item.vpsProductCategManuf.trim(),
            productTypeName: item.vpsProductIncInEachCateg.trim(),
            plannedForSourcing: item.isSelected == true ? 1 : 0,
            experienceInYrs: item.vpsYearExp,
            percentOfBusiness: item.vpsPercentageOfYourBuss
          }
          vpsFormGroupItems.push(obj);
        }
      });
      this.vendorGenForm.productTypes = vpsFormGroupItems;
    }


    let itemsBestSelling: any[] = this.vpsBestSellingFormGroup.value.vpsBestSellingFormDataArray;
    let existsBestSelling = itemsBestSelling.some(a => a.vpsMainCust ? true : false);

    if (existsBestSelling) {
      let vpsBestSellingItems: customers[] = [];
      itemsBestSelling.forEach(item => {
        if (item.vpsMainCust) {
          let obj: customers = {
            customerName: item.vpsMainCust,
            productDescription: item.vpsProductDesc,
            country: item.vpsCountry,
            qtyShipped: item.vpsQtyShipped,
          }
          vpsBestSellingItems.push(obj);
        }
      });
      this.vendorGenForm.customers = vpsBestSellingItems;
    }

    let itemsFact: any[] = this.empForm.value.employees;
    let existsFact = itemsFact.some(a => a.fgiFactoryNameEng ? true : false);
    // console.log("totalFactory:", itemsFact);
    // console.log("existsFact:", existsFact);

    if (existsFact) {
      let empFormItems: factories[] = [];
      itemsFact.forEach(item => {
        // console.log("item :", item);
        if (item.fgiFactoryNameEng) {

          // for process realted data in factory form
          let itemsprocesses: any[] = item.processControll;
          // console.log("itemsprocesses :", itemsprocesses)
          let factoryprocessesItems: processes[] = [];
          itemsprocesses.forEach(item => {
            let obj: processes = {
              processName: item.inHouseProcessName,
              keyOperations: item.inHouseKeyOperation,
            }
            if (obj.processName) {
              factoryprocessesItems.push(obj);
            }

            let obj2: processes = {
              processName: item.outSourcedProcessName,
              keyOperations: item.outSourcedKeyOperation,
            }
            if (obj2.processName) {
              factoryprocessesItems.push(obj2);
            }
          });
          // for process realted data in factory form


          // for quqlityTeam realted data in factory form
          let itemsQualityTeam: any[] = item.qualityTeams;
          //console.log("itemsQualityTeam :", itemsQualityTeam)
          let factoryQulaityTeamItems: qualityTeams[] = [];
          let obj1: qualityTeams = {
            qualityTeamName: "Quality Engineer",
            personCount: item.fgiQualityTeamEngineerPerson,
            avgExperienceInYrs: item.fgiQualityTeamEngineerYearOfExp
          }
          if (obj1.personCount) {
            factoryQulaityTeamItems.push(obj1);
          }

          let obj2: qualityTeams = {
            qualityTeamName: "IQC (Incoming material checking)",
            personCount: item.fgiQualityTeamIQCPerson,
            avgExperienceInYrs: item.fgiQualityTeamIQCYearOfExp
          }
          if (obj2.personCount) {
            factoryQulaityTeamItems.push(obj2);
          }

          let obj3: qualityTeams = {
            qualityTeamName: "In-Line QC",
            personCount: item.fgiQualityTeamInLineIQCPerson,
            avgExperienceInYrs: item.fgiQualityTeamInLineIQCYearOfExp
          }
          if (obj3.personCount) {
            factoryQulaityTeamItems.push(obj3);
          }

          let obj4: qualityTeams = {
            qualityTeamName: "Final QC",
            personCount: item.fgiQualityTeamFinalQCPerson,
            avgExperienceInYrs: item.fgiQualityTeamFinalQCYearOfExp
          }
          if (obj4.personCount) {
            factoryQulaityTeamItems.push(obj4);
          }

          itemsQualityTeam.forEach(item => {
            let obj: qualityTeams = {
              qualityTeamName: item.qualityTeamName,
              personCount: item.personCount,
              avgExperienceInYrs: item.avgExperienceInYrs
            }
            if (obj.personCount) {
              factoryQulaityTeamItems.push(obj);
            }
          });
          // for quqlityTeam realted data in factory form


          // for inhouseTestings realted data in factory form
          let itemsinhouseTestings: any[] = item.inHouseTesting;
          //console.log("itemsinhouseTestings :", itemsinhouseTestings)
          let factoryinhouseTestingsItems: inhouseTestings[] = [];
          itemsinhouseTestings.forEach(item => {
            let obj: inhouseTestings = {
              testName: item.typeOfTest,
              scales: item.scales,
              equipment: item.equipment,
              standardTest: item.testOfStandard,
            }
            if (obj.testName)
              factoryinhouseTestingsItems.push(obj);
          });
          // for inhouseTestings realted data in factory form

          // for productTypes realted data in factory form
          let itemsproductTypes: any[] = item.productImage;
          //console.log("itemsproductTypes :", itemsproductTypes)
          let factoryproductTypesItems: facProductTypes[] = [];
          itemsproductTypes.forEach(item => {
            let obj: facProductTypes = {
              productTypeName: item.name,
              productImageUrl: item.imageFile,
            }
            if (obj.productTypeName)
              factoryproductTypesItems.push(obj);
          });
          // for productTypes realted data in factory form

          let obj: factories = {
            // productTypeCategoryName: item.vpsProductCategManuf.trim(),
            name: item.fgiFactoryNameEng,
            ownership: item.fgiFactoryOwnership,
            addressLine1: item.fgiFactoryAddressEng,
            city: item.fgiFactoryFullAddressEng.city,
            country: item.fgiFactoryFullAddressEng.country,
            pincode: item.fgiFactoryFullAddressEng.pincode,
            state: item.fgiFactoryFullAddressEng.state,

            decisionMakerName: item.fgiChiefDeciMaker.name,
            decisionMakerTitle: item.fgiChiefDeciMaker.title,
            decisionMakerTelephone: item.fgiChiefDeciMaker.telephone,
            decisionMakerMobile: item.fgiChiefDeciMaker.cellphone,
            decisionMakerEmail: item.fgiChiefDeciMaker.email,

            mainContactName: item.fgiBusinessContactPerson.name,
            mainContactTitle: item.fgiBusinessContactPerson.title,
            mainContactTelephone: item.fgiBusinessContactPerson.telephone,
            mainContactMobile: item.fgiBusinessContactPerson.cellphone,
            mainContactEmail: item.fgiBusinessContactPerson.email,

            qualityContactName: item.fgiAuditContactPerson.name,
            qualityContactTitle: item.fgiAuditContactPerson.title,
            qualityContactTelephone: item.fgiAuditContactPerson.telephone,
            qualityContactMobile: item.fgiAuditContactPerson.cellphone,
            qualityContactEmail: item.fgiAuditContactPerson.email,

            isoCertificationNumber: item.fgiIsoCertificate,
            isoCertificationDate: item.fgiIsoDateOfCertificate,
            capacityPerMonth: item.fgiFactoryCapacityperMonth,
            availableCapacityPerMonth: item.fgiFactoryAvailVolume,
            workerCount: item.fgiFactoryworkerCount,
            comments: item.fgiFactorycomments,
            hasDedicatedQualityTeam: item.fgiDepForQualityControl == "YES" ? 1 : 0,
            hasDedicatedProductDevTeam: item.fgiDepForProductDev == "YES" ? 1 : 0,
            productQualityAudit: item.fgiProductTestingMaintaince == "YES" ? 1 : 0,

            processes: factoryprocessesItems,
            inhouseTestings: factoryinhouseTestingsItems,
            productTypes: factoryproductTypesItems,
            qualityTeams: factoryQulaityTeamItems,

          }
          empFormItems.push(obj);
        }
      });

      this.vendorGenForm.factories = empFormItems;
    }


    //console.log("v:", this.vendorGenForm);
    this._dashboardService.submitVendorForm(this.vendorGenForm).subscribe(res => {
      this.processResponse(res);
    }, (err: any) => {
      this._commonService.handleError(err);
    })

  }

  public fetchVendorFormData(uuid) {
    this._dashboardService.getVendorFormData(uuid).subscribe(res => {
      this.vendorFormData = res;
      this.initlizeForms();
    }, (err: any) => {
      this._commonService.handleError(err);
      this.initlizeForms();
    });

  }

  processResponse(data) {

    if (data.uuid != null) {
      //this.toaster.success(data.message);
      this.toaster.success("Vendor Created Successfully.");
      if (this.isEdit) return;
      //Create a schedule or Audit
      const usrole = JSON.parse(localStorage.getItem("currentUser"));
      let vendorObj = new QiVendor();
      vendorObj.uuid = usrole.uuid;
      vendorObj.fullName = usrole.name;
      let scheduleRequestObj = new QiSchedule();
      // scheduleRequestObj.vendor.uuid = usrole.uuid;
      // scheduleRequestObj.vendor.fullName = usrole.name;
      scheduleRequestObj.vendor = vendorObj;

      let factoryObj = new QiFactory();
      factoryObj.uuid = data.factories[0]?.uuid;
      factoryObj.name = data.factories[0]?.name;

      scheduleRequestObj.factory = factoryObj;

      //Calling Schedule Service For SAQ
      this._dashboardService.createScheduleSAQ(this.tenantConfig?.uuid).subscribe(resp => {
        console.log("Schedule working done --- ")
        this._messageService.sendMessage("CanUseSAQ")
        localStorage.setItem(`${usrole.uuid}-scheduleData`, JSON.stringify(resp))

        console.log(resp)
      }, (err: any) => {
        this._commonService.handleError(err);
      });
    }
    else {
      this.toaster.warning(data.message);
    }
    this.cd.detectChanges();
  }

  public closeAddVendor(): void {
    if (this.tenantConfig.role == 'VENDOR' && this._commonService.getTenantUid() == 'amazon') {
      this._router.navigate(['dashboard/vpfDashboard'])
    } else
      this.close.emit(true);
  }

}
