import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { QiDiscussionHistory } from 'app/core/qinspect/_model/DiscussionHistory.model';
import { FactoryProductType } from 'app/core/qinspect/_model/FactoryProductType.model';
import { Review } from 'app/core/qinspect/_model/Review.model';
import { DesignCodeProductMapping, SchedulePlanning } from 'app/core/qinspect/_model/SchedulePlanning.model';
import { IUserTraining } from 'app/core/qinspect/_model/UserTraining.model';
import { environment } from 'environments/environment';
import { saveAs } from 'file-saver';
import { Observable } from 'rxjs';

import { DashboardSummary } from '../core/qinspect/_model/DashboardSummary.model';
import { Globals } from '../core/qinspect/appconfig/appconfig.component';
import { CommonService } from './common.service';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  http: HttpClient;
  basicAuth = this.globals.basicAuth;

  constructor(private httpin: HttpClient, private globals: Globals, private _commonService: CommonService) {
    this.http = httpin;
  }

  public getCategorywise(): Observable<DashboardSummary[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<DashboardSummary[]>(environment.apiUrl + 'qualityDashboard/categoryWise', {
      headers: headers
    });
  }

  public getDefectByCategory(uuid: string): Observable<DashboardSummary[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<DashboardSummary[]>(environment.apiUrl + 'qualityDashboard/categoryWise/' + uuid, {
      headers: headers
    });
  }

  public getDefectOptionsByCheckpointUid(uuid: string): Observable<DashboardSummary[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<DashboardSummary[]>(environment.apiUrl + 'qualityDashboard/checkpointWise/' + uuid, {
      headers: headers
    });
  }

  downloadDimensions(docUid: string) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() }).set("Authorization", this.basicAuth);
    var HTTPOptions = {
      headers: headers,
      'responseType': 'text'
    }
    this.http.get(environment.apiUrl + 'cad/' + docUid + '/download', {
      headers: headers,
      'responseType': 'text'
    })
      .toPromise()
      .then(response =>
        this.saveToFileSystem("dimensions.csv", response)
      );
  }

  saveToFileSystem(filename, response) {
    //const filename = "orderitems.csv";//parts[1].split('=')[1];
    const blob = new Blob([response], { type: 'text/plain' });
    saveAs(blob, filename);
  }

  public getUniqueYears(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'audit/uniqueYears', {
      headers: headers
    });
  }

  public getChartData(store, payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<any[]>(environment.apiUrl + 'audit/scores?dataFor=' + store, JSON.stringify(payload), {
      headers: headers
    });
  }

  public getTopFailedCheckPoints(count, payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<any[]>(environment.apiUrl + 'audit/failedCheckpoints?count=' + count, JSON.stringify(payload), {
      headers: headers
    });
  }

  public getCheckPointData(payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<any[]>(environment.apiUrl + 'audit/cpDataForDashboard', JSON.stringify(payload), {
      headers: headers
    });
  }

  public submitVendorForm(payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.post<any[]>(environment.apiUrl + 'vendor/profile', JSON.stringify(payload), {
      headers: headers
    });
  }

  public submitVendorRequestForm(payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.post<any[]>(environment.apiUrl + 'vendor/request', JSON.stringify(payload), {
      headers: headers
    });
  }

  public submitSchedulePlanningRequest(payload:SchedulePlanning): Observable<any[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.post<any[]>(environment.apiUrl + 'schedulePlanning', JSON.stringify(payload), {
      headers: headers
    });
  }
  public updateSchedulePlanningRequest(schedulePlanningUid, payload:SchedulePlanning): Observable<any[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.put<any[]>(environment.apiUrl + 'schedulePlanning/' + schedulePlanningUid , JSON.stringify(payload), {
      headers: headers
    });
  }

  public createScheduleSAQ(uuid): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + 'vendor/' + uuid + '/createSchedule', {
      headers: headers
    });
  }

  public getVendorFormData(uuid): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + 'vendor/' + uuid + '/profile', {
      headers: headers
    });
  }
  public getVendorFactoryScores(uuid): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + 'vendor/' + uuid + '/getVendorFactoryScores', {
      headers: headers
    });
  }

  public getAllVendors(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + 'vendor/allVendors', {
      headers: headers
    });
  }

  public getScheduleData(auditUid: string): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + `audit/testMethod/${auditUid}`, {
      headers: headers
    });
  }

  public getAllCategory(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + 'category/all', {
      headers: headers
    });
  }

  public getAllProductTypes(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + 'config/productType/all', {
      headers: headers
    });
  }

  public createOrUpdateFactoryProductTypes(payload:FactoryProductType[]): Observable<any[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.post<any[]>(environment.apiUrl + 'vendor/productTypesMapping', JSON.stringify(payload), {
      headers: headers
    });
  }

  public saveReviews(payload:Review[]): Observable<any[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.post<any[]>(environment.apiUrl + 'review', JSON.stringify(payload), {
      headers: headers
    });
  }

  public updateReviews(payload:Review[]): Observable<any[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.put<any[]>(environment.apiUrl + 'review', JSON.stringify(payload), {
      headers: headers
    });
  }


  public getHorecaData(payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<any[]>(environment.apiUrl + 'audit/horeca', JSON.stringify(payload), {
      headers: headers
    });
  }

  public getDepartments(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'config/auditType/', {
      headers: headers
    });
  }

  public getStorelist(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + '/factory', {
      headers: headers
    });
  }

  public getResponseSummary(payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<any[]>(environment.apiUrl + 'audit/responseSummary/filter', JSON.stringify(payload), {
      headers: headers
    });
  }


  public downloadTopFailed(count, payload): any {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() }).set("Authorization", this.basicAuth);
    this.http.post(environment.apiUrl + 'audit/failedCheckpoints/download?count=' + count, JSON.stringify(payload), {
      headers: headers,
      'responseType': 'text'
    })
      .toPromise()
      .then(response =>
        this.saveToFileSystem("failed_list.csv", response)
      );
  }

  public downloadCheckPointData(payload): any {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() }).set("Authorization", this.basicAuth);
    this.http.post(environment.apiUrl + 'audit/cpDataForDashboard/download ', JSON.stringify(payload), {
      headers: headers,
      'responseType': 'text'
    })
      .toPromise()
      .then(response =>
        this.saveToFileSystem("CheckPoint.csv", response)
      );
  }

  public downloadHorecaTable(payload): any {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() }).set("Authorization", this.basicAuth);
    this.http.post(environment.apiUrl + 'audit/horeca/download', JSON.stringify(payload), {
      headers: headers,
      'responseType': 'text'
    })
      .toPromise()
      .then(response =>
        this.saveToFileSystem("Horeca_list.csv", response)
      );
  }

  public getTrend(payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<any[]>(environment.apiUrl + '/audit/trends', JSON.stringify(payload), {
      headers: headers
    });
  }

  public getTetantnsConfig(tetant: any): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'config/tenantConfig/' + tetant, {
      headers: headers
    });
  }

  public getPurchaseOrderStatus(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'sourcing/dashboard/purchaseorder/status', {
      headers: headers
    });
  }

  public getPurchaseOrderStatusTenetWise(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'sourcing/dashboard/purchaseorder/status/tenantwise', {
      headers: headers
    });
  }

  public getPurchaseOrderDelayPhaseWise(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'sourcing/dashboard/purchaseorder/delay/phasewise', {
      headers: headers
    });
  }

  public getPurchaseOrderbookedVsDelivered(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'sourcing/dashboard/purchaseorder/bookedVsDelivered', {
      headers: headers
    });
  }

  public getPoVsDeliveredData(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'sourcing/dashboard/poVsdelivered', {
      headers: headers
    });
  }

  public getPurchaseOrderDeliveryVolume(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'sourcing/dashboard/purchaseorder/delivery/volume', {
      headers: headers
    });
  }

  public getPurchaseOrderTotalTenentWise(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'sourcing/dashboard/purchaseorder/total/tenantwise', {
      headers: headers
    });
  }

  public getActivitywise(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'sourcing/dashboard/activitywise', {
      headers: headers
    });
  }

  public getActivitywiseDesigncode(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'sourcing/dashboard/activitywise/designcode', {
      headers: headers
    });
  }

  public submitDiscussion(payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.post<any[]>(environment.apiUrl + 'discussionHistory', JSON.stringify(payload), {
      headers: headers
    });
  }

  public getAllDiscussion(recordUid): Observable<QiDiscussionHistory[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<QiDiscussionHistory[]>(environment.apiUrl + 'discussionHistory/all/' + recordUid, {
      headers: headers
    });
  }

  public approveVendorRequest(recordUid): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<any>(environment.apiUrl + 'vendor/' + recordUid + '/approve', {
      headers: headers
    });
  }

  public initiateFQA(recordUid): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<any>(environment.apiUrl + 'vendor/' + recordUid + '/initiate', {
      headers: headers
    });
  }

  public makeVPFEditable(recordUid): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<any>(environment.apiUrl + 'vendor/' + recordUid + '/editable', {
      headers: headers
    });
  }

  public approveRejectFactoryRequest(factoryUid, status): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<any>(environment.apiUrl + `vendor/${factoryUid}/factoryStatus/${status}`, {
      headers: headers
    });
  }

  public getTrainingPlaylist(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + 'trainingPlaylist', {
      headers: headers
    });
  }

  public getUserTraining(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + 'userTraining/byUser', {
      headers: headers
    });
  }

  public updateUserTraining(trainingUid: string): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.put<any[]>(environment.apiUrl + `userTraining/${trainingUid}`, null, {
      headers: headers
    });
  }

  public saveUserTraining(training: IUserTraining): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.post<any[]>(environment.apiUrl + 'userTraining', JSON.stringify(training), {
      headers: headers
    });
  }

  public updateVendorTrainingStatus(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.put<any[]>(environment.apiUrl + `userTraining/trainingStatus`, null, {
      headers: headers
    });
  }

  public createOrUpdateProductMapping(payload: DesignCodeProductMapping[]): Observable<any[]> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.post<any[]>(environment.apiUrl + 'productMapping', JSON.stringify(payload), {
      headers: headers
    });
  }

  public generateSAQReport(auditUid: string): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<any[]>(environment.apiUrl + `audit/genreport/${auditUid}/draft`, null, {
      headers: headers
    });
  }

  public createProductTypeConfiguration(prodTypeUid: string): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<any[]>(environment.apiUrl + `config/productType/configMappings/${prodTypeUid}`, null, {
      headers: headers
    });
  }

  public uploadProductSpecificChecklist(formData: any, categoryHeaderName:string,createdBy: string, auditTypeId:string) : Observable<any>{

    let headers = new HttpHeaders()
            .set("Authorization", this.basicAuth).set('Client', this._commonService.getTenantUid())
      
      return this.http.post(environment.apiUrl + `config/checkpoint/imports/${categoryHeaderName}/${createdBy}/${auditTypeId}`,
       formData, { headers: headers, responseType: 'json' });            
  }


}
