import { HttpHeaders } from '@angular/common/http';
import { Component } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { CostingMode, designCodeFeedback, DesignMaster, REQUEST_TYPES } from 'app/core/qinspect/_common/AppConstants';
import { Util } from 'app/core/qinspect/_common/util';
import { DesignCodeMaster } from 'app/core/qinspect/_model/DesignCodeMaster.model';
import { FilterList } from 'app/core/qinspect/_model/filterList.model';
import { RoadmapMaster } from 'app/core/qinspect/_model/RoadmapMaster.model';
import { FORM_CONTROL_TYPES, HTTP_STATUS, ROLE, TABLE_ACTION_TYPES } from 'app/enum-constants';
import { ICON_LIST } from 'app/icon-list';
import {
  AddAttachmentsByDesigncodeDocumentComponent,
} from 'app/modules/sourcing/roadmap/add-attachments-by-designcode/add-attachments-by-designcode.component';
import { QuotationStatus, SOURCING_CONFIGURATION, SOURCINGMENUTYPE } from 'app/modules/sourcing/sourcing.configuraiton';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { TflChatboxComponent } from 'app/shared/tfl-chatbox/tfl-chatbox.component';
import { TflFeedbackFormComponent } from 'app/shared/tfl-feedback-form/tfl-feedback-form.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { environment } from 'environments/environment';
import { Observable, ReplaySubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import {
  AddEditSpecificationsComponent,
} from '../../quotation-response/add-edit-specifications/add-edit-specifications.component';
import { AttachTestProtocolComponent } from '../attach-test-protocol/attach-test-protocol.component';
import { TechnicalSpecifications } from 'app/core/qinspect/_model/TechSpecification.model';

@Component({
  selector: 'view-quotation-response-by-request',
  templateUrl: './view-response-by-request.component.html',
  styleUrls: ['./view-response-by-request.component.scss']
})
export class ViewQuotationResponseByRequestComponent extends BaseTableSharedComponent {
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  hdr: string = 'View/Compare EOI';
  supplierType: string = '';
  quotationRequestUid: string
  basicAuth = this.globals.basicAuth;
  showAddEditDocuments: boolean = false;
  selectedStatusCode = "-2";
  allDesignCodes: any[] = [];
  designCodesByReponse: any[] = [];
  quotationResponse: any[] = [];
  isSeller: boolean = this._commonService.getUseRole() == ROLE.SELLER;
  statusList: any[] = [
    { "label": "All", value: "-2" },
    { "label": "Pending", value: "0" },
    { "label": "Live", value: "1" },
    { "label": "Closed", value: "2" },
    { "label": "Cancelled", value: "-1" }
  ];
  roadmapMaster: RoadmapMaster = new RoadmapMaster();
  filtersList: FilterList[] = [
    { id: "status", type: 'dropdown', label: 'Status', list: this.statusList, defaultValue: this.statusList[2] }
  ]
  columnDefs: any[] = [];
  requestColumnDefs: any[] = [];
  tabIndex: number = 0;
  designCodeMaster: DesignCodeMaster[];
  quotationResponses: any[] = [];
  supplierWiseDesignCode: any[] = [];
  quotationResponsesColumns: any[] = [];
  designCodeColumnDefsForOpenCosting: any[] = [];
  designCodeColumnDefsForClosedCosting: any[] = [];
  designCodeColumDefsForLabs: any[] = [];
  biddingRequestData: any[] = [];
  showTRFConfirmationDialog: boolean = false;
  isAllSelectedDiDHavePPT: boolean = false;
  isSomeSelectedDiDHavePPT: boolean = false;
  showIsPPTRequiredMsg: boolean = false;
  internalTestingLab: any[] = [];
  selectedInternalLabUid: any;
  groupedDesignCodes: any[] = [];
  filteredQuotationResponse: any[] = [];
  groupWiseDesignCodes: any[] = [];
  isShowGroupWiseDesignCodes: boolean = false;
  groupWiseDesignCodesList: any[] = [];
  groupWiseColumnDefs: any[] = [];
  menuType: string;

  public init(): void {
    this.menuType = JSON.parse(sessionStorage.getItem("menuType"));
    this.quotationRequestUid = this.route.snapshot.params["uuid"];
    this.onRefresh();
  }


  public onRefresh(): void {
    this.getResponseByRequestUid();
  }

  private prepareTableColumns(): void {
    this.quotationResponsesColumns = [
      { headerName: "Vendor Name", field: "vendorName", sortable: true, wrapText: false, minWidth: 150, unSortIcon: true, hide: this.quotationResponse[0]?.requestType == REQUEST_TYPES.BIDDING },
      { headerName: "Lab Name", field: "labName", sortable: true, wrapText: false, minWidth: 150, unSortIcon: true, hide: this.quotationResponse[0]?.requestType != REQUEST_TYPES.BIDDING },
      // {
      //   headerName: "Product Type", field: "productTypeName", sortable: true, unSortIcon: true, autoHeight: false,
      // },
      { headerName: "Collection Name", field: "collectionName", sortable: true, unSortIcon: true, autoHeight: false, minWidth: 180, },
      { headerName: "Brand", field: "brand", sortable: true, unSortIcon: true, autoHeight: false },
      { headerName: "GL Name", field: "glName", sortable: true, unSortIcon: true, },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, wrapText: false, minWidth: 130,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Status", field: "", sortable: true, editable: false, pinned: 'right', minWidth: 220,
        cellRenderer: (params) => { return this.prepareDesignCodesStatus(this.checkIfTRFRaised(params.data)) }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareResponseActionIconButtons(params.data), onAction: (action) => this.onResponseAction(action) }
        }
      }
    ];
    this.designCodeColumnDefsForOpenCosting = [
      {
        field: "", checkboxSelection: params => (this.returnIsSelectionEnabled(params.data)), headerCheckboxSelectionFilteredOnly: true, minWidth: 70, maxWidth: 70, pinned: 'left',
        hide: !(this._commonService.getUseRole().includes(ROLE.SELLER) || this._commonService.getUseRole().includes(ROLE.SME)),
      },
      { headerName: "DID No", field: "code", sortable: true, wrapText: false, unSortIcon: true, pinned: 'left', minWidth: 140 },
      { headerName: "Vendor Name", field: "vendorName", sortable: true, wrapText: false, unSortIcon: true, minWidth: 140, },
      {
        headerName: "Production Lead Time(in Days)", field: "productionLeadTime", sortable: true, unSortIcon: true, minWidth: 120,
        valueGetter: params => {
          return this.isSeller ? params.data.productionLeadTime : '*******'
        }
      },
      {
        headerName: "Currency", field: "currency", sortable: true, unSortIcon: true, minWidth: 120,
        valueGetter: params => {
          return this.isSeller ? params.data.currency : '*******'
        }
      },
      {
        headerName: "MOQ", field: "moq", sortable: true, minWidth: 80,
        valueGetter: params => {
          return this.isSeller ? params.data.moq : '*******';
        }
      },
      {
        headerName: "Product Cost", field: "productionProposedPrice", sortable: true, minWidth: 120,
        valueGetter: params => {
          return this.isSeller ? params.data.productionProposedPrice : '*******';
        }
      },
      {
        headerName: "Packaging Cost", field: "packingProposedPrice", sortable: true, minWidth: 90,
        valueGetter: params => {
          return this.isSeller ? params.data.packingProposedPrice : '*******';
        }
      },
      {
        headerName: "Shipping Cost", field: "shipmentProposedPrice", sortable: true, minWidth: 100,
        valueGetter: params => {
          return this.isSeller ? params.data.shipmentProposedPrice : '*******';
        }
      },
      {
        headerName: "Margin %", field: "marginProposedPrice", sortable: true, minWidth: 100,
        valueGetter: params => {
          return this.isSeller ? params.data.marginProposedPrice : '*******';
        }
      },
      {
        headerName: "Total Cost", field: "totalProposedPrice", sortable: true, editable: false, minWidth: 100,
        valueGetter: params => {
          return this.isSeller ? params.data.totalProposedPrice?.toFixed(2) : '*******';
        }
      },
      {
        headerName: "Tax %", field: "taxProposedPrice", sortable: true, minWidth: 90,
        valueGetter: params => {
          return this.isSeller ? params.data.taxProposedPrice : '*******';
        }
      },
      {
        headerName: "Final Cost with Tax", field: "finalCostWithTax", sortable: true, editable: false, minWidth: 150,
        cellRenderer: (params) => {
          return this.isSeller ? this.calcTotalwithTax(params.data) : '*******';
        }
      },
      {
        headerName: "Category", field: "glName", sortable: true, wrapText: false, minWidth: 150, cellRenderer: params => {
          return this.roadmapMaster.glName;
        }
      },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, wrapText: false, minWidth: 130,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Brand", field: "brand", sortable: true, wrapText: false,
      },
      {
        headerName: "Status", field: "", sortable: true, editable: false, pinned: 'right', minWidth: 220,
        cellRenderer: (params) => { return this.prepareDesignCodesStatus(this.checkIfTRFRaised(params.data)) }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(this.checkIfTRFRaised(params.data)), onAction: (action) => this.onAction(action) }
        }
      }
    ];
    this.designCodeColumnDefsForClosedCosting = [
      {
        field: "", checkboxSelection: params => (this.returnIsSelectionEnabled(params.data)), headerCheckboxSelectionFilteredOnly: true, minWidth: 70, maxWidth: 70, pinned: 'left',
        hide: !(this._commonService.getUseRole().includes(ROLE.SELLER) || this._commonService.getUseRole().includes(ROLE.SME)),
      },
      {
        headerName: "DID No", field: "code", sortable: true, unSortIcon: true, wrapText: false, pinned: 'left', minWidth: 180,
      },
      { headerName: "Vendor Name", field: "vendorName", sortable: true, wrapText: false, unSortIcon: true, minWidth: 140 },
      {
        headerName: "Production Lead Time(in Days)", field: "productionLeadTime", sortable: true, unSortIcon: true, minWidth: 180,
        valueGetter: params => {
          return this.isSeller ? params.data.productionLeadTime : '*******'
        }
      },
      {
        headerName: "Currency", field: "currency", sortable: true, unSortIcon: true, minWidth: 120,
        valueGetter: params => {
          return (this.isSeller || this._commonService.getUseRole() == ROLE.VENDOR) ? params.data.currency : '*******';
        }
      },
      {
        headerName: "FOB", field: "fob", sortable: true, unSortIcon: true, minWidth: 90,
        valueGetter: params => {
          return (this.isSeller || this._commonService.getUseRole() == ROLE.VENDOR) ? params.data.fob : '*******';
        }
      },
      {
        headerName: "MOQ", field: "moq", sortable: true, minWidth: 80,
        valueGetter: params => {
          return (this.isSeller || this._commonService.getUseRole() == ROLE.VENDOR) ? params.data.moq : '*******';
        }
      },
      {
        headerName: "Items Included in costing", field: "scope", sortable: true, minWidth: 190,
        valueGetter: params => {
          return (this.isSeller || this._commonService.getUseRole() == ROLE.VENDOR) ? params.data.scope : '*******';
        }
      },
      {
        headerName: "Items excluded in costing", field: "outOfScope", sortable: true, minWidth: 200,
        valueGetter: params => {
          return (this.isSeller || this._commonService.getUseRole() == ROLE.VENDOR) ? params.data.outOfScope : '*******';
        }
      },
      {
        headerName: "Category", field: "glName", sortable: true, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.glName;
        }

      },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, wrapText: false, minWidth: 130,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Brand", field: "brand", sortable: true, unSortIcon: true, wrapText: false, minWidth: 120,

      },
      {
        headerName: "Status", field: "", sortable: true, editable: false, pinned: 'right', minWidth: 220,
        cellRenderer: (params) => { return this.prepareDesignCodesStatus(this.checkIfTRFRaised(params.data)) }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(this.checkIfTRFRaised(params.data)), onAction: (action) => this.onAction(action) }
        }
      }
    ];
    this.designCodeColumDefsForLabs = [
      {
        field: "", checkboxSelection: params => (this.returnIsSelectionEnabled(params.data)), headerCheckboxSelectionFilteredOnly: true, minWidth: 70, maxWidth: 70, pinned: 'left',
        hide: !(this._commonService.getUseRole().includes(ROLE.SME) || this._commonService.getUseRole().includes(ROLE.ADMIN)),
      },
      { headerName: "Group Name", field: "groupName", sortable: true, unSortIcon: true, wrapText: false, maxWidth: 180, pinned: 'left' },
      {
        headerName: "DID No", field: "code", sortable: false, unSortIcon: true, minWidth: 180,
        wrapText: false, pinned: 'left', hide: this.quotationResponse[0]?.costingMode == 2,
      },
      { headerName: "Lab Name", field: "labName", sortable: false, unSortIcon: true, wrapText: false, maxWidth: 150, },

      {
        headerName: "Required Samples", field: "labSamplesRequired", sortable: false,
        // valueGetter: params => {
        //   return (this.isProductCompliance) ? params.data.labSamplesRequired : '*******';
        // }
      },
      {
        headerName: "No Of Days For Lab Testing", field: "noOfDaysForLabTesting", sortable: false, minWidth: 220,
        // valueGetter: params => {
        //   return (this.isProductCompliance) ? params.data.noOfDaysForLabTesting : '*******';
        // }
      },
      {
        headerName: 'Currency', field: 'currency', sortable: false, maxWidth: 130
        // valueGetter: params => {
        //   return (this.isProductCompliance) ? params.data.currency : '*******';
        // }
      },
      {
        headerName: "Total Price", field: "labTestingTotalPrice", sortable: false, minWidth: 130
        // valueGetter: params => {
        //   return (this.isProductCompliance) ? params.data.labTestingTotalPrice : '*******';
        // }

      },
      {
        headerName: "Category", field: "categoryName", sortable: false, unSortIcon: true, wrapText: false, minWidth: 120

      },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, wrapText: false, minWidth: 160,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Brand", field: "brand", sortable: false, unSortIcon: true, wrapText: false, minWidth: 100,

      },
      {
        headerName: "Status", field: "status", unSortIcon: true, sortable: false, wrapText: false,
        pinned: 'right', minWidth: 220,
        cellRenderer: (params) => { return this.prepareDesignCodesStatus(this.checkIfTRFRaised(params.data)) }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        cellRendererFramework: CellActionComponent,
        minWidth: 200,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(this.checkIfTRFRaised(params.data)), onAction: (action) => this.onAction(action) }
        }
      }
    ];
  }

  //Get Responses by Request
  private getResponseByRequestUid(): void {
    APP_UI_CONFIG.quotation.getResponsesByRequest.paramList.requestUid = this.quotationRequestUid;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getResponsesByRequest)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response && response.length > 0) {
            this.designCodeMaster = response[0]?.designcodes;
            this.quotationResponse = response;
            if (this.menuType == SOURCINGMENUTYPE.TPS) {
              this.filteredQuotationResponse = response?.filter(item => (item.status != QuotationStatus.DROPPED));
            } else if (this.menuType == SOURCINGMENUTYPE.QUOTATION)
              this.filteredQuotationResponse = response;
            else {
              this.filteredQuotationResponse = response?.filter(item => (item.status != QuotationStatus.DROPPED));
            }
            this.quotationResponses = response;
            this.groupedDesignCodes = [];
            response?.forEach(item => {
              this.groupedDesignCodes = this.groupedDesignCodes.concat(item.groupedDesignCodes);
            });
            this.getQuotationStatus();

            this.getRoadmapDetailsByRoadmapUid(response);


          }
        },
        error: error => {

        }
      })
  }
  //Get Roadmap Details
  private getRoadmapDetailsByRoadmapUid(quotationResponse: any[]): void {
    APP_UI_CONFIG.sourcingRoadmap.getByUid.paramList.roadMapUid = this.quotationResponse[0]['roadmapUid'];
    this.invokeService.serviceInvocation(APP_UI_CONFIG.sourcingRoadmap.getByUid)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.roadmapMaster = response;
          if (response) {
            this.prepareDesignCodes(quotationResponse, response);
          }
        },
        error: error => {

        }
      })
  }
  //Prepare Table columns based on quotation type
  private prepareColumnDefs(): void {
    this.prepareTableColumns();
    let vendorResponse = JSON.parse(sessionStorage.getItem('vendorResponse'));
    this.requestColumnDefs = this.quotationResponsesColumns;
    if (vendorResponse.costingMode == CostingMode.OPEN) {
      this.supplierType = 'open';
      this.hdr = (this.menuType == 'TPS') ? 'View Specs' : 'View/Compare EOI';
      this.columnDefs = this.designCodeColumnDefsForOpenCosting
    } else if (vendorResponse.costingMode == CostingMode.CLOSED) {
      this.supplierType = 'closed';
      this.hdr = (this.menuType == 'TPS') ? 'View Specs' : 'View/Compare EOI';
      this.columnDefs = this.designCodeColumnDefsForClosedCosting
    }
    else if (vendorResponse.costingMode == CostingMode.LAB) {
      this.supplierType = 'lab';
      this.columnDefs = this.designCodeColumDefsForLabs;
      this.hdr = 'Compare/View Bidding Responses';
    }
  }
  private prepareDesignCodes(responses: any[], roadMapData) {
    this.allDesignCodes = [];
    let allResponses: any[] = responses;
    this.biddingRequestData = allResponses.find(item => item.requestType == 'BIDDING REQUEST');
    let allDesignCode: any[] = [];
    allResponses.forEach(item => {
      let eachItemsArray = item.designcodes;
      if (eachItemsArray && eachItemsArray.length > 0) {
        eachItemsArray?.forEach(item1 => {
          allDesignCode.push(item1)
        })
      }
    });

    let filteredDllDesignCodes = allDesignCode;
    this.prepareDesignCodesData(filteredDllDesignCodes, roadMapData)
  }
  enableSubmitTps: boolean = true;
  private prepareDesignCodesData(designCodes: any[], roadMapData) {
    if (this.menuType == 'TPS') {
      designCodes = designCodes.filter(item => item.status != QuotationStatus.DROPPED)
    }
    this.allDesignCodes = designCodes.map(item => ({
      categoryName: roadMapData.categoryName,
      subCatName: roadMapData.subCatName,
      brand: roadMapData.brand,
      groupName: this.groupedDesignCodes?.find(designCode => designCode?.designCodeUid == item?.designCodeUid)?.groupName || '',
      ...item
    }));
    this.allDesignCodes = this._commonService.sortByAlphanumeric(this.allDesignCodes, 'code', 'code');
    this.enableSubmitTps = this.allDesignCodes.some(item => item.status == QuotationStatus.ADDITIONALSPECS && (this._commonService.isProductCompliance() || this._commonService.isVendor()))
    this.prepareGroupWiseData(Util.clone(this.allDesignCodes));
    this.prepareColumnDefs();
  }

  private prepareGroupWiseData(allDesignCodes: any[]): void {
    this.groupWiseDesignCodes = [];
    allDesignCodes.forEach(eachDesignCode => {
      let groupIndex = this.groupWiseDesignCodes.findIndex(item => item.groupName == eachDesignCode.groupName && item.labUid == eachDesignCode.labUid);
      if (groupIndex > -1) {
        if (this.groupWiseDesignCodes[groupIndex]['list']) {
          this.groupWiseDesignCodes[groupIndex]['list'].push(eachDesignCode);
        }
      } else {
        let designCodeGroup = {};
        designCodeGroup = eachDesignCode;
        designCodeGroup['list'] = [eachDesignCode];
        this.groupWiseDesignCodes.push(designCodeGroup);
      }
    })
  }

  public submitTps(): void {
    if (this.allDesignCodes.some(item => item.copiedSpec)) {
      let payload = this.allDesignCodes.filter(item => item.copiedSpec)?.map(designCodeItem => ({
        code: designCodeItem.code,
        productTypeName: designCodeItem.productTypeName,
        productTypeUid: designCodeItem.productTypeUid,
        quotationResponseUid: designCodeItem.quotationResponseUid,
        quotationResponseDesigncodeUid: designCodeItem.uuid,
        designCodeUid: designCodeItem.designCodeUid,
        categoryUid: designCodeItem.categoryUid,
        uuid: designCodeItem.technicalSpecifications ? this.checkIfSpecUpdate(designCodeItem) : null,
        specifications: this.prepareSpecification(designCodeItem)
      }))

      this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.saveTechSpecifications, null, null, Util.clone(payload))
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            if (response && response.code == HTTP_STATUS.SUCCESS) {
              this._commonService.success("TPS(s) Submitted Successfully");
              this.onRefresh();
              this._commonService.navigate('sourcing/quotation/quotationRequest/TPS')
            } else {
              this._commonService.error("Failed To Submit the TPS");
            }
          }, error: error => {
            this._commonService.handleError(error);
          }
        });
    }
    else {
      this._commonService.error("No TPS are found to submit");
    }
  }

  private checkIfSpecUpdate(designCodeItem): any {
    return designCodeItem.technicalSpecifications[0].uuid;
  }


  private prepareSpecification(designCodeItem): any {
    if (designCodeItem) {
      let specifications: TechnicalSpecifications = {};
      let specList: TechnicalSpecifications[] = [];
      let rec: TechnicalSpecifications = new TechnicalSpecifications();
      Object.keys(designCodeItem.copiedSpec).forEach(key => {
        rec.specificationName = key;
        let isUnitFor: boolean = (key.toLowerCase().includes("unit_")) ? true : false;
        rec.value = this.checkInputType(designCodeItem.specConfigList, key, isUnitFor) == FORM_CONTROL_TYPES.MULTI_SELECT?.toLowerCase() ? designCodeItem.copiedSpec[key]?.toString() : this.checkInputType(designCodeItem.specConfigList, key, isUnitFor) == FORM_CONTROL_TYPES.DATE?.toLowerCase() ? +new Date(designCodeItem.copiedSpec[key]) : designCodeItem.copiedSpec[key];
        rec.isUpdated = true;
        specList.push(Util.clone(rec));
      });
      specList.forEach(specItem => {
        let matchingTechItem;
        if (specItem.specificationName.toLowerCase().includes("unit_")) {
          matchingTechItem = designCodeItem.specConfigList.find((techItem: any) => techItem.unitFor == specItem.specificationName.split("_")[1]);
        } else {
          matchingTechItem = designCodeItem.specConfigList.find((techItem: any) => techItem.specificationName == specItem.specificationName);
        }
        if (matchingTechItem) {
          specItem.code = matchingTechItem.code;
          specItem.groupName = matchingTechItem.specificationGroup;
          specItem.uuid = matchingTechItem.uuid;
          specItem.groupCode = matchingTechItem.specificationGroupCode;
          specItem.isNew = matchingTechItem.isNew;
          specItem.sectionName = matchingTechItem.sectionName;
          specItem.uom = matchingTechItem.uom;
        }

      });

      specList.forEach((data) => {
        specifications[data.specificationName] = {
          specificationName: data.specificationName,
          value: this.getValue(designCodeItem, data),
          isUpdated: data.isUpdated,
          isNew: data.isNew,
          sectionName: data.sectionName,
          code: data.code,
          groupName: data.groupName,
          uuid: data.uuid,
          groupCode: data.groupCode,
          multiplier: (data.value) ? data.specificationName.toLowerCase().includes("unit_") ? this.getMultiplier(designCodeItem.specsUnitMasterConfigurationList, designCodeItem.specConfigList.find((techItem: any) => techItem.unitFor == data.specificationName.split("_")[1]), data.value) : '' : '',
          uom: data.uom
        };
      });
      return specifications;
    } else {
      return;
    }
  }


  private checkInputType(masterSpecList: any[], specificationName, isUnitFor?): any {
    let spec: any;
    if (isUnitFor) {
      if (specificationName.toLowerCase().includes("unit_")) {
        spec = masterSpecList?.find(spec => spec?.unitFor == specificationName?.replace(/unit_/gi, ""))
      } else {
        spec = masterSpecList?.find(spec => spec?.unitFor == specificationName)
      }
    } else {
      spec = masterSpecList.find(spec => spec.specificationName == specificationName);
    }
    if (spec && spec.inputType) {
      let specType: any;
      if (spec.inputType?.toLowerCase() == FORM_CONTROL_TYPES.SINGLE_SELECT.toLowerCase()) {
        specType = FORM_CONTROL_TYPES.SINGLE_SELECT;
      }
      else if (spec.inputType?.toLowerCase() == FORM_CONTROL_TYPES.MULTI_SELECT.toLowerCase()) {
        specType = FORM_CONTROL_TYPES.MULTI_SELECT;
      } else {
        specType = spec.inputType.toLowerCase();
      }
      return specType;
    } else {
      return FORM_CONTROL_TYPES.TEXT.toLowerCase();
    }
  }
  private getMultiplier(specsUnitMasterConfigurationList: any[], matchingTechItem, value): any {
    let matchSepcItem = specsUnitMasterConfigurationList.find(specItem => specItem.keyword == matchingTechItem?.keyword);
    if (matchSepcItem) {
      let data = JSON.parse(matchSepcItem.jsonConfig);
      return data.find(item1 => item1.value == value)?.multiplier || '';
    }
  }

  specFileUid: any;
  private getValue(designCodeItem, data: any): any {
    this.specFileUid = designCodeItem['specFileUid'];
    const matchingItem = designCodeItem.specConfigList.find(
      (item) => (item?.specificationName === data?.specificationName && item.inputType?.toLowerCase() == 'file')
    );
    if (matchingItem) {
      return this.specFileUid;
    }
    return data.value;
  }

  //enable accept, reject, drop actions
  private returnIsSelectionEnabled(row): boolean {
    let designCodeMasterList = this.quotationResponse[0].designcodemaster.filter(item => item.code == row.code && item.status == 7)
    if (this._commonService.isProductCompliance() && this.menuType == SOURCINGMENUTYPE.QUOTATION) {
      return false;
    }
    if (this._authService.isSupplier(this.user)) {
      if (row.status == 0) {
        return true;
      } else {
        return false;
      }
    } else if (this._commonService.getUseUid() == this.quotationResponse[0].pcUserUid || this._commonService.getUseRole() == ROLE.ADMIN) {
      if (row.status == QuotationStatus.BIDDINGRAPPROVED && designCodeMasterList.length > 0) {
        return false;
      }
      if (row.status == QuotationStatus.ADDITIONALSPECS || row.status == QuotationStatus.RFQ2APPROVED || row.status == QuotationStatus.PENDING || row.status == QuotationStatus.BIDDINGRAPPROVED || row.status == QuotationStatus.BIDDINGREJECTED || row.status == QuotationStatus.QuotationSubmitted) {
        return row.status == QuotationStatus.BIDDINGRAPPROVED ? true : this.checkIfEveryValueExist(row);
      } else {
        return false;
      }
    }
    else {
      if (this._commonService.getUseRole() == ROLE.SELLER || this._commonService.getUseRole() == ROLE.ADMIN) {
        if (row.status == QuotationStatus.CREATED || row.status == QuotationStatus.PENDING || row.status == QuotationStatus.REJECTED || row.status == QuotationStatus.QuotationSubmitted) {
          return this.checkIfEveryValueExist(row);
        } else {
          return false;
        }
      }
    }
  }

  public prepareResponseActionIconButtons(row): any[] {
    let iconsList: any[] = [];
    if (this.menuType == SOURCINGMENUTYPE.QUOTATION) {
      if (this._commonService.getUseRole().includes(ROLE.SELLER) || this._commonService.getUseRole().includes(ROLE.ADMIN)) {
        if (row.status == QuotationStatus.CREATED) {
          iconsList.push({ type: 'response_quotation_reject', title: "Negotiate Quotation", icon: ICON_LIST.REQUEST_QUOTE_RED, data: row });
          iconsList.push({ type: 'response_quotation_accept', title: "Approve Quotation", icon: 'accept_green', data: row });
        }
        else if (row.status == QuotationStatus.QuotationSubmitted) {
          iconsList.push({ type: 'response_quotation_reject', title: "Negotiate Quotation", icon: ICON_LIST.REQUEST_QUOTE_RED, data: row });
          iconsList.push({ type: 'response_quotation_accept', title: "Approve Quotation", icon: 'accept_green', data: row });
        }
        else if (row.status == QuotationStatus.REJECTED) {
          iconsList.push({ type: 'response_view_reject_comments', title: "Add/View Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
          iconsList.push({ type: 'response_quotation_accept', title: "Accept Quotation", icon: 'accept_green', data: row });
        }
        // else if (row.status == QuotationStatus.ADDITIONALSPECS) {//After Submit
        //   iconsList.push({ type: 'response_feedback_specs_response', title: "Specs Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
        // }
      }
    }
    else if (this.menuType == SOURCINGMENUTYPE.TPS) {
      if (this._commonService.getUseUid() == this.quotationResponse[0].pcUserUid) {//For pc
        if (row.status == QuotationStatus.ADDITIONALSPECS) {//After Submit
          iconsList.push({ type: 'response_feedback_specs_response', title: "Specs Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
          iconsList.push({ type: 'response_spec_approve', title: "Approve Specs", icon: 'accept_green', data: row });
        }
      }
    }
    else if (this.menuType == SOURCINGMENUTYPE.BIDDING) {
      if (this._commonService.getUseUid() == this.quotationResponse[0].pcUserUid) {//For pc
        if (row.status == QuotationStatus.CREATED) {
          iconsList.push({ type: 'response_bidding_reject', title: "Negotiate Bidding", icon: ICON_LIST.REQUEST_QUOTE_RED, data: row });
          iconsList.push({ type: 'response_bidding_approve', title: "Approve Bidding", icon: 'accept_green', data: row });
        }
        if (row.status == QuotationStatus.QuotationSubmitted) {
          iconsList.push({ type: 'response_bidding_reject', title: "Negotiate Bidding", icon: ICON_LIST.REQUEST_QUOTE_RED, data: row });
          iconsList.push({ type: 'response_bidding_approve', title: "Approve Bidding", icon: 'accept_green', data: row });
        }
        if (row.status == QuotationStatus.BIDDINGREJECTED) {
          iconsList.push({ type: 'response_view_reject_comments', title: "Add/View Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
          iconsList.push({ type: 'response_bidding_approve', title: "Approve Bidding", icon: 'accept_green', data: row });
        }
      }
    }
    return iconsList;
  }

  public onResponseAction(action): void {
    switch (action.type) {
      case 'view_response':
        this.filterDesignCodesByResponse(action.data);
        break;
      case 'response_quotation_reject':
        this.rejectQuotationResponse(action.data);
        break;
      case 'response_quotation_drop':
        this.filterDesignCodesByResponse(action.data);
        break;
      case 'response_quotation_accept':
        this.approveQuotationResponse(action.data);
        break;
      case 'response_bidding_reject':
        this.onRejectBiddingResponse(action.data);
        break;
      case 'response_bidding_drop':
        this.filterDesignCodesByResponse(action.data);
        break;
      case 'response_bidding_approve':
        this.onApproveBiddingResponse(action.data);
        break;
      case 'response_attach_protocol':
        this.filterDesignCodesByResponse(action.data);
        break;
      case 'response_view_reject_comments':
        this.getFeedbackListByRequest(action.data);
        break;
      case 'response_feedback_specs_response':
        this.getFeedbackListByRequest(action.data);
        break;
      case 'response_spec_approve':
        this.onApproveSpecs(action.data);
        break;
      case 'response_final_submit':
        this.finalSubmitQuotation(action.data);
        break;


    }
  }

  private filterDesignCodesByResponse(data: any): void {
    this.supplierWiseDesignCode = data.designcodes;
    this.tabIndex = 1;
  }

  private rejectQuotationResponse(responseData): void {
    let designCodes: any[] = responseData?.designcodes?.filter(designCode => designCode.status == QuotationStatus.QuotationSubmitted);
    if (designCodes.length > 0) {
      this.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.negotiate)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(confirm => {
          if (confirm) {
            this.openDialogForFeedbackReject({}, `Reason For Negotiation`)
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: res => {
                  if (res != false) {
                    let feedbacks = {};
                    feedbacks['feedbacCategoryType'] = 1;
                    feedbacks['feedback'] = res;
                    feedbacks['quotation_reponse_uid'] = responseData.uuid;
                    feedbacks['userRole'] = this._commonService.getUseRole();
                    feedbacks['userUid'] = this._commonService.getCurrentUser()?.uuid;
                    feedbacks['userName'] = this._commonService.getCurrentUser()?.userName;
                    feedbacks['designCodeUid'] = designCodes.find(item => item.uuid)?.uuid
                    let payload = responseData;
                    payload['feedbacks'] = [feedbacks];
                    this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.rejectQuotation, '', '', [payload]).subscribe({
                      next: res => {
                        this._commonService.success(`Request successfully placed for negotiation`);
                        this.refreshPage();
                      }, error: error => {
                        this._commonService.handleError(error);
                      }
                    });
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })

          }
        })
    } else {
      this._commonService.error('Selected request is/are already "Under Negotiation". Please select design codes in "Quotation Received" status to proceed')
    }
  }



  //Get list of feedback on specs
  private getFeedbackListByRequest(response): void {
    let requestType: any
    if (this.menuType == SOURCINGMENUTYPE.QUOTATION) {
      requestType = 'QUOTATION';
    } else if (this.menuType == SOURCINGMENUTYPE.TPS) {
      requestType = 'TPS';
    } else {
      requestType = 'BIDDING';
    }
    let urlObject = {
      url: `sourcing/quotation/response/${response.uuid}/feedback/byResponse?1=1&requestType=${requestType}`,
      type: 'GET'
    }
    this.invokeService.serviceInvocation(urlObject)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          if (response.status == QuotationStatus.REJECTED) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.QUOTATIONFEEDBACK);
            this.saveOrUpdateFeedbackByRequest(response, feedbackForQuotationReject);
          }
          if (response.status == QuotationStatus.ADDITIONALSPECS) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.SPECSFEEDBACK);
            this.saveOrUpdateFeedbackByRequest(response, feedbackForQuotationReject);
          }
          if (response.status == QuotationStatus.BIDDINGREJECTED) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.BIDDINGFEEDBACK);
            this.saveOrUpdateFeedbackByRequest(response, feedbackForQuotationReject);
          }
        }, error: error => {
          this.invokeService.handleError(error);
          this.saveOrUpdateFeedbackByRequest(response, []);
        }
      });

  }
  //Share feedback
  private saveOrUpdateFeedbackByRequest(response, feedbackList): void {
    this.openDialogForFeedbackRequest(feedbackList, 'Share Feedback').subscribe(res => {
      if (res) {
        let feedbacks: any = {};
        if (response.status == QuotationStatus.REJECTED) {
          feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : designCodeFeedback.QUOTATIONFEEDBACK;
        }
        if (response.status == QuotationStatus.ADDITIONALSPECS) {
          feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : designCodeFeedback.SPECSFEEDBACK;
        }
        if (response.status == QuotationStatus.BIDDINGREJECTED || response.status == QuotationStatus.BIDDINGDROPPED) {
          feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : designCodeFeedback.BIDDINGFEEDBACK;
        }
        feedbacks['feedback'] = res;
        feedbacks["quotation_reponse_uid"] = response.uuid;
        feedbacks['userRole'] = this._commonService.getUseRole();
        feedbacks['userUid'] = this._commonService.getCurrentUser()?.uuid;
        feedbacks['userName'] = this._commonService.getCurrentUser()?.userName;
        const quotationResponse = response;
        quotationResponse['feedbacks'] = [feedbacks];
        this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.updateFeedbackByResponse, null, null, quotationResponse)
          .pipe(takeUntil(this.$destroyed))
          .subscribe({
            next: res => {
              this._commonService.success(`Feedback shared successfully`);
              this.onRefresh();
            }, error: error => {
              this._commonService.handleError(error);
            }
          });
      }
    })
  }

  private openDialogForFeedbackRequest(data, header?, isDisabled = false): Observable<any> {
    const dialogRef = this.dialog.open(TflChatboxComponent, {
      width: '50%',
      data: { data: data, header: header, isDisabled: isDisabled },
      disableClose: true,
    });
    return dialogRef.afterClosed()
  }

  // DID Level

  private approveQuotationResponse(responseData): void {
    this.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.approve).subscribe({
      next: confirm => {
        if (confirm) {
          let acceptPayload: any[] = this.quotationResponses.filter(item => item.vendorUid != responseData.vendorUid).map(item => {
            let { request, ...rest } = item;
            return rest;
          });
          if (acceptPayload.length > 0) {
            this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.dropQuotation, '', '', acceptPayload)
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response && response.code == HTTP_STATUS.SUCCESS) {
                    this.dropQuotationResponseAutomatically(responseData);
                  } else {
                    this._commonService.handleError(response);
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          } else {
            this.dropQuotationResponseAutomatically(responseData);
          }
        }
      }
    })
  }

  private dropQuotationResponseAutomatically(approvedQuotation): void {
    this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.acceptQuotation, '', '', [approvedQuotation])
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          this._commonService.success(`EOI(s) has been accepted successfully`);
          this.refreshPage();
        }, error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private onRejectBiddingResponse(responseData): void {
    this.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.negotiate).subscribe(res => {
      if (res) {
        this.openDialogForFeedbackReject({}, 'Reason For Negotiation').subscribe({
          next: res => {
            if (res != false) {
              let payload = responseData;
              delete payload['request'];
              let feedbacks = {};
              feedbacks['feedbacCategoryType'] = 3;
              feedbacks['feedback'] = res;
              feedbacks['quotationResponseUid'] = responseData.uuid;
              feedbacks['userRole'] = this._commonService.getUseRole();
              feedbacks['userUid'] = this._commonService.getCurrentUser()?.uuid;
              feedbacks['userName'] = this._commonService.getCurrentUser()?.userName;
              payload['feedbacks'] = [feedbacks];
              this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.rejectBidding, '', '', [payload]).subscribe({
                next: res => {
                  this._commonService.success(`Request successfully placed for negotiation`);
                  this.refreshPage();
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
            }
          }, error: error => {
            this._commonService.handleError(error);
          }
        })

      }
    })
  }

  private onApproveBiddingResponse(responseData): void {
    this.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.approve).subscribe(confirm => {
      if (confirm) {
        let droppedQuotation: any[] = this.quotationResponses.filter(item => item.labUid != responseData.labUid);
        if (droppedQuotation.length > 0) {
          this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.dropBidding, '', '', droppedQuotation).subscribe({
            next: res => {
              if (res && res.code == HTTP_STATUS.SUCCESS) {
                this.dropQuotationResponseByAtuotmatically(responseData)
              } else {

              }
            }, error: error => {
              this._commonService.handleError(error);
            }
          });
        } else {
          this.dropQuotationResponseByAtuotmatically(responseData)
        }
      }
    })
  }

  private dropQuotationResponseByAtuotmatically(approvedQuotation) {
    this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.approveBidding, '', '', [approvedQuotation]).subscribe({
      next: res => {
        this._commonService.success('Bidding request approved successfully');
        this.refreshPage();
      }, error: error => {
        this._commonService.handleError(error);
      }
    });
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];

    if (this.menuType == SOURCINGMENUTYPE.QUOTATION) {
      iconsList.push({ type: 'view_attachments', title: "View Attachments", icon: 'view_file_blue', data: row });
      if (row.status == QuotationStatus.REJECTED) {
        if (this._commonService.getUseRole().includes(ROLE.SELLER) || this._commonService.getUseRole().includes(ROLE.ADMIN)) {
          iconsList.push({ type: 'view_reject_comments', title: "Add/View Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
        }
      }
    }
    else if (this.menuType == SOURCINGMENUTYPE.TPS) {
      iconsList.push({ type: 'view_attachments', title: "View Attachments", icon: 'view_file_blue', data: row });
      if (this._commonService.getUseRole().includes(ROLE.SME) || this._commonService.getUseRole().includes(ROLE.ADMIN)) {
        if (row.status == QuotationStatus.RFQ1APPROVED || row.status == QuotationStatus.BIDDINGREREQUESTSTARTED || row.status == QuotationStatus.RFQ2APPROVED) {
          iconsList.push({ type: 'view_specs', title: "View Specs", icon: 'view', data: row });
        }
        else if (row.status == QuotationStatus.REJECTED) {
          iconsList.push({ type: 'view_reject_comments', title: "Add/View Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
        }
        else if (row.status == QuotationStatus.ADDITIONALSPECS) {//After Submit
          iconsList.push({ type: 'feedback_specs', title: "Specs Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
          iconsList.push({ type: 'approve_specs', title: "Approve/View Specs", icon: 'view', data: row });
        }
        if (row?.pptReportDocUid && (row.status == QuotationStatus.ADDITIONALSPECS || row.status == QuotationStatus.BIDDINGREREQUESTSTARTED || row.status == QuotationStatus.RFQ2APPROVED)) {
          iconsList.push({ type: 'view_ppt', title: "View Test Report", icon: ICON_LIST.REPORT_BLUE, data: row });
        }
      }
    }
    else if (this.menuType == SOURCINGMENUTYPE.BIDDING) {
      if (this._commonService.getUseRole().includes(ROLE.SME) || this._commonService.getUseRole().includes(ROLE.ADMIN)) {
        if (row.status == QuotationStatus.BIDDINGREJECTED) {
          iconsList.push({ type: 'view_reject_comments', title: "Add/View Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });

          if (row.status > QuotationStatus.ADDITIONALSPECS && row.status != QuotationStatus.BIDDINGREJECTED && row.status != QuotationStatus.BIDDINGRAPPROVED) {//After Submit
            iconsList.push({ type: 'view_specs', title: "View Specs", icon: 'view', data: row });
            if (row?.pptReportDocUid) {
              iconsList.push({ type: 'view_ppt', title: "View Test Report", icon: ICON_LIST.REPORT_BLUE, data: row });
            }
          }
        }
        if (this.supplierType == 'lab') {
          iconsList.push({ type: "VIEW_GROUPWISE_DETAILS", title: "View DesignCodes", icon: ICON_LIST.VIEW, data: row });
        }
        if (row.testProtocolUid && (row.status >= QuotationStatus.BIDDINGREREQUESTSTARTED) && this.supplierType != 'lab') {
          iconsList.push({ type: "VIEW_TEST_PROTOCOL", title: "View Protocol", icon: ICON_LIST.MICROSCOPE, data: row });
        }
        let isSelectedDesignCodesHasProductTypeUid = this.selectedDesignCodesForFinalSubmit.every(item => item.productTypeUid && item.productTypeName);
        if (row.status == QuotationStatus.RFQ2APPROVED && this._commonService.isProductCompliance() && !isSelectedDesignCodesHasProductTypeUid) {
          iconsList.push({ type: TABLE_ACTION_TYPES.EDIT, title: "Update Product Type", icon: ICON_LIST.EDIT_DOCUMENT_BLUE, data: row });
        }
      }
    }

    return iconsList;
  }

  selectedDesignCodes: any[] = [];
  selectedDesignCodesForSpecApprove: any[] = [];
  selectedDesignCodesForFinalSubmit: any[] = [];
  selectedDesignCodesForTRF: any[] = [];
  selectedDesignCodesForLab: any[] = [];
  selectedApprovedList: boolean = false;
  isValidToAccept: boolean = false;
  isDesignCodeValidToBidding: boolean = false;

  isValidToApprove: boolean = false;
  public onSelectionChanged(data: any): void {
    this.selectedDesignCodes = [];
    this.selectedDesignCodesForSpecApprove = [];
    this.selectedDesignCodesForFinalSubmit = [];
    this.selectedDesignCodesForTRF = [];
    this.selectedDesignCodesForLab = [];
    let selectedCodes: any[] = [];
    let isSelectedDesignCodes: any;
    let groupedDesignCode = [];
    if (this.supplierType == 'lab') {
      data?.forEach(item => {
        groupedDesignCode = groupedDesignCode.concat(item.list);
      })
      selectedCodes = groupedDesignCode.map(gropDesign => {
        let { list, ...newData } = gropDesign;
        return newData;
      });
    } else {
      selectedCodes = data;
    }
    this.selectedDesignCodes = selectedCodes.filter(designCode => (!designCode.labName && !designCode.labUid) && (designCode.status == QuotationStatus.CREATED || designCode.status == QuotationStatus.PENDING || designCode.status == QuotationStatus.REJECTED || designCode.status == QuotationStatus.QuotationSubmitted));
    this.selectedDesignCodesForLab = selectedCodes.filter(designCode => (designCode.status == QuotationStatus.PENDING || designCode.status == QuotationStatus.BIDDINGREJECTED || designCode.status == QuotationStatus.QuotationSubmitted));
    // if (this.selectedDesignCodesForLab.length > 0 && this.selectedDesignCodeApproved.length > 0) {
    //   this.isValidToApprove = true
    //   this._commonService.error('Selected DID is already rejected/approved')
    // } else {
    //   this.isValidToApprove = false
    // }
    this.isDesignCodeValidToBidding = selectedCodes.filter(item => item.vendorUid && item.status == QuotationStatus.RFQ2APPROVED).every(eachDesignCode => {
      return selectedCodes.filter(item => item.vendorUid && item.status == QuotationStatus.RFQ2APPROVED).every(item => item.vendorUid == eachDesignCode.vendorUid) ? true : false
    })
    !this.isDesignCodeValidToBidding ? this._commonService.error("Please select DID which has same vendor name") : ''
    let selctedDIDToApprove = selectedCodes.filter(item => item.status == QuotationStatus.QuotationSubmitted && item.status == QuotationStatus.REJECTED)
    isSelectedDesignCodes = selectedCodes.some(designCode => (designCode.status == QuotationStatus.CREATED || designCode.status == QuotationStatus.PENDING || designCode.status == QuotationStatus.REJECTED));
    if (selectedCodes.every(item => item.status == selectedCodes[0].status)) {
      if (this._commonService.getUseRole() == ROLE.SELLER || this._commonService.getUseRole() == ROLE.ADMIN) {
        this.isValidToAccept = this.selectedDesignCodes.some(item => this.checkIfEveryValueExist(item));
      }
      else if (this._commonService.getUseRole() == ROLE.SME || this._commonService.getUseRole() == ROLE.ADMIN) {
        if (selectedCodes[0]?.status == QuotationStatus.ADDITIONALSPECS) {
          this.selectedDesignCodesForSpecApprove = selectedCodes.filter(designCode => designCode.status == QuotationStatus.ADDITIONALSPECS);
        }
        else if (selectedCodes[0]?.status == QuotationStatus.RFQ2APPROVED) {
          this.selectedDesignCodesForFinalSubmit = selectedCodes.filter(designCode => designCode.status == QuotationStatus.RFQ2APPROVED);
        }
        // else if (selectedCodes[0]?.status == QuotationStatus.BIDDINGRAPPROVED) {
        //   this.selectedDesignCodesForLab = selectedCodes.filter(designCode => designCode.status == QuotationStatus.PENDING);
        // }
        else if (selectedCodes[0]?.status == QuotationStatus.BIDDINGRAPPROVED) {
          this.selectedDesignCodesForTRF = selectedCodes.filter(designCode => designCode.status == QuotationStatus.BIDDINGRAPPROVED);
        }
      } else {

      }

      if (this.selectedDesignCodesForFinalSubmit.length > 0) {
        this.enableSubmitTps = false;
      } else {
        this.enableSubmitTps = true;
      }
    } else {
      if (!isSelectedDesignCodes && !selctedDIDToApprove) {
        this._commonService.error("Please select same status design codes")
      }
    }

  }

  private checkIfSameVendorForOneDesignCode(designCode) {
    return this.selectedDesignCodes.filter(item => item.code == designCode.code).length == 1;
  }





  public approveQuotation(): void {//Drop First and Drop
    let isValidDesignCode = this.selectedDesignCodes.every(designCode => this.checkIfSameVendorForOneDesignCode(designCode));
    if (isValidDesignCode) {
      let approvedDesignCodes: any[] = this.selectedDesignCodes.filter(designCode => (designCode.status == QuotationStatus.CREATED || designCode.status == QuotationStatus.PENDING || designCode.status == QuotationStatus.REJECTED || designCode.status == QuotationStatus.QuotationSubmitted));
      if (approvedDesignCodes.length == 0) {
        this._commonService.error("Please select at least one designcode to accept");
      } else {
        //Drop First and Approve
        let droppedDesignCodes: any[] = [];
        if (this.quotationResponse[0].vendorUid) {
          droppedDesignCodes = this.allDesignCodes.filter(first => approvedDesignCodes.some((second) => first.code == second.code && first.vendorUid != second.vendorUid));
        } else {
          droppedDesignCodes = this.allDesignCodes.filter(first => approvedDesignCodes.some((second) => first.code == second.code && first.vendorUid != second.vendorUid));
        }
        if (droppedDesignCodes.length > 0) {
          this._commonService.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.accept)
            .pipe(takeUntil(this.$destroyed))
            .subscribe(confirm => {
              if (confirm) {
                let payload = this.prepareAndMapDesignCodesWithResponse(droppedDesignCodes);
                this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.dropQuotation, '', '', payload)
                  .pipe(takeUntil(this.$destroyed))
                  .subscribe({
                    next: response => {
                      if (response && response.code == HTTP_STATUS.SUCCESS) {
                        this.dropDesignCodesAutomatically(approvedDesignCodes);
                      } else {
                        this._commonService.handleError(response);
                      }
                    }, error: error => {
                      this._commonService.handleError(error);
                    }
                  });
              }
            })
        } else {
          this.dropDesignCodesAutomatically(approvedDesignCodes);
        }
      }
    } else {
      this._commonService.error("Please select one vendor for each design code")
    }

  }

  private dropDesignCodesAutomatically(approvedDesignCodes: any[]): void {//Approve after Drop
    let payload = this.prepareAndMapDesignCodesWithResponse(approvedDesignCodes);
    this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.acceptQuotation, '', '', payload)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response && response.code == HTTP_STATUS.SUCCESS) {
            this._commonService.success(`Quotation(s) has been approved successfully`);
            this.refreshPage();
          } else {
            this._commonService.handleError(response);
          }
        }, error: error => {
          this._commonService.handleError(error);
        }
      });

    // this._commonService.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.accept)
    //   .pipe(takeUntil(this.$destroyed))
    //   .subscribe(confirm => {
    //     if (confirm) {

    //     }
    //   })
  }

  public rejectQuotation(): void {
    //in seller login no more pending status after vendor given prices it is in 200 status now
    let designCodes: any[] = this.selectedDesignCodes.filter(designCode => designCode.status == QuotationStatus.QuotationSubmitted);
    if (this.selectedDesignCodes.every(item => (item.status == this.selectedDesignCodes[0].status))) {
      if (designCodes.length > 0) {
        this.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.negotiate)
          .pipe(takeUntil(this.$destroyed))
          .subscribe(confirm => {
            if (confirm) {
              this.openDialogForFeedbackReject({}, 'Reason For Negotiation')
                .pipe(takeUntil(this.$destroyed))
                .subscribe({
                  next: res => {
                    if (res != false) {
                      let feedbacks = {};
                      feedbacks['feedbacCategoryType'] = 1;
                      feedbacks['feedback'] = res;
                      feedbacks['userRole'] = this._commonService.getUseRole();
                      feedbacks['userUid'] = this._commonService.getCurrentUser()?.uuid;
                      feedbacks['userName'] = this._commonService.getCurrentUser()?.userName;
                      feedbacks['designCodeUid'] = designCodes.find(item => item.uuid)?.uuid;
                      designCodes.forEach(item => {
                        item['feedbacks'] = [feedbacks];
                      });
                      let payload = this.prepareAndMapDesignCodesWithResponse(designCodes);
                      this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.rejectQuotation, '', '', payload).subscribe({
                        next: res => {
                          this._commonService.success(`Request successfully placed for negotiation`);
                          this.refreshPage();
                        }, error: error => {
                          this._commonService.handleError(error);
                        }
                      });
                    }
                  }, error: error => {
                    this._commonService.handleError(error);
                  }
                })

            }
          })
      } else {
        this._commonService.error('Selected Design code(s) is/are already "Under Negotiation". Please select design codes in "Quotation Received" status to proceed')
      }
    } else {
      this._commonService.error('Selected Design code(s) is/are already "Under Negotiation". Please select design codes in "Quotation Received" status to proceed')
    }
  }

  public dropQuotation(): void {
    let designCodes: any[] = this.selectedDesignCodes.filter(designCode => designCode.status == 0);
    if (designCodes.length == 0) {
      this._commonService.error("Please select at least one designcode to accept")
    } else {
      let payload = this.prepareAndMapDesignCodesWithResponse(designCodes);
      this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.dropQuotation, '', '', payload)
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: res => {
            this._commonService.success(`Quotation(s) has been dropped successfully`);
            this.refreshPage();
          }, error: error => {
            this._commonService.handleError(error);
          }
        });
    }
  }

  public onApproveSpecs(quotationResponseByRequest): void {
    this._commonService.openDialogForConfirmation(`Are you sure you want to approve technical specifications?`)
      .subscribe(confirm => {
        if (confirm) {
          let payload: any[] = [];
          if (quotationResponseByRequest) {
            let { request, ...selectedQuotationResponse } = quotationResponseByRequest;
            //if we selcted two vendors but 1st DID is one vendor and 2nd DID is another vendor when approved specs request level 1st DID then automatically approving 2nd DID also , so fix this issue aloowing status which is not equal to -1
            selectedQuotationResponse.designcodes = selectedQuotationResponse.designcodes.filter(item => item.status != -1);
            payload = [selectedQuotationResponse];
          } else {
            let quotationResponse: any[] = this.quotationResponses.filter(o => this.selectedDesignCodesForSpecApprove.some((child) => o.vendorUid === child.vendorUid));
            quotationResponse.forEach(eachResponse => {
              let { request, ...selectedQuotationResponse } = eachResponse;
              let selectedDesignCodes = this.selectedDesignCodesForSpecApprove.filter(item => item.vendorUid == selectedQuotationResponse.vendorUid);
              selectedQuotationResponse['designcodes'] = selectedDesignCodes;
              payload.push(selectedQuotationResponse);
            })
          }

          this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.approveQuotation, null, null, payload)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                if (response && response.code == HTTP_STATUS.SUCCESS) {
                  this._commonService.success(`TPS approved successfully`);
                  this.onRefresh();
                  this.selectedDesignCodesForSpecApprove = [];
                } else {
                  this._commonService.handleError(response);
                }
              }, error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      })
  }

  //onApprove Bidding

  private checkIfSameLabForOneDesignCode(designCode) {
    return this.selectedDesignCodesForLab.filter(item => item.code == designCode.code).length == 1;
  }
  public onApproveBidding(): void {
    let isValidDesignCode = this.selectedDesignCodesForLab.every(designCode => this.checkIfSameLabForOneDesignCode(designCode));
    if (isValidDesignCode) {
      let approvedDesignCodes: any[] = this.selectedDesignCodesForLab.filter(designCode => (designCode.status == QuotationStatus.CREATED || designCode.status == QuotationStatus.PENDING || designCode.status == QuotationStatus.BIDDINGREJECTED || designCode.status == QuotationStatus.QuotationSubmitted));
      if (approvedDesignCodes.length == 0) {
        this._commonService.error("Please select at least one designcode to approve");
      } else {
        let droppedDesignCodes: any[] = [];
        if (this.quotationResponse[0].labUid) {
          droppedDesignCodes = this.allDesignCodes.filter(first => approvedDesignCodes.some((second) => first.code == second.code && first.labUid != second.labUid));
        } else {
          droppedDesignCodes = this.allDesignCodes.filter(first => approvedDesignCodes.some((second) => first.code == second.code && first.labUid != second.labUid));
        } if (droppedDesignCodes.length > 0) {
          this._commonService.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.approve)
            .pipe(takeUntil(this.$destroyed))
            .subscribe(confirm => {
              if (confirm) {
                let payload = this.prepareAndMapDesignCodesWithResponse(droppedDesignCodes);
                this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.dropBidding, '', '', payload)
                  .pipe(takeUntil(this.$destroyed))
                  .subscribe({
                    next: response => {
                      if (response && response.code == HTTP_STATUS.SUCCESS) {
                        this.dropBiddingDesignCodesAutomatically(approvedDesignCodes);
                      } else {
                        this._commonService.handleError(response);
                      }
                    }, error: error => {
                      this._commonService.handleError(error);
                    }
                  });
              }
            })
        } else {
          this.dropBiddingDesignCodesAutomatically(approvedDesignCodes);
        }
      }
    } else {
      this._commonService.error("Please select only one lab for each Group")
    }
  }

  private dropBiddingDesignCodesAutomatically(approvedDesignCodes: any[]): void {
    let designCodeList: any[] = approvedDesignCodes.map(item => {
      let { list, ...newDesignCode } = item;
      return newDesignCode;
    })
    let payload = this.prepareAndMapDesignCodesWithResponse(designCodeList);
    // let designCodes: any[] = [];
    this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.approveBidding, '', '', payload)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response && response.code == HTTP_STATUS.SUCCESS) {
            this._commonService.success(`Bidding(s) has been approved successfully`);
            this.refreshPage();
          } else {
            this._commonService.handleError(response);
          }
        }, error: error => {
          this._commonService.handleError(error);
        }
      });

  }


  public rejectBidding(): void {
    // to approve/reject bidding status starts from 200 
    let designCodes: any[] = this.selectedDesignCodesForLab.filter(designCode => designCode.status == QuotationStatus.QuotationSubmitted);
    if (this.selectedDesignCodesForLab.every(item => (item.status == this.selectedDesignCodesForLab[0].status))) {
      if (designCodes.length > 0) {
        this.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.negotiate)
          .pipe(takeUntil(this.$destroyed))
          .subscribe(confirm => {
            if (confirm) {
              this.openDialogForFeedbackReject({}, 'Reason For Negotiation')
                .pipe(takeUntil(this.$destroyed))
                .subscribe({
                  next: res => {
                    if (res != false) {
                      let feedbacks = {};
                      feedbacks['feedbacCategoryType'] = 3;
                      feedbacks['feedback'] = res;
                      feedbacks['userRole'] = this._commonService.getUseRole();
                      feedbacks['userUid'] = this._commonService.getCurrentUser()?.uuid;
                      feedbacks['userName'] = this._commonService.getCurrentUser()?.userName;
                      designCodes.forEach(item => {
                        item['feedbacks'] = [feedbacks];
                      });
                      let payload = this.prepareAndMapDesignCodesWithResponse(designCodes);
                      this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.rejectBidding, '', '', payload).subscribe({
                        next: res => {
                          this._commonService.success(`Request successfully placed for negotiation`);
                          this.refreshPage();
                        }, error: error => {
                          this._commonService.handleError(error);
                        }
                      });
                    }
                  }, error: error => {
                    this._commonService.handleError(error);
                  }
                })

            }
          })
      } else {
        this._commonService.error('Selected Design code(s) is/are already "Under Negotiation". Please select design codes in "Bidding Received" status to proceed')
      }
    } else {
      this._commonService.error('Selected Design code(s) is/are already "Under Negotiation". Please select design codes in "Bidding Received" status to proceed')
    }
  }




  public onAction(event): void {
    let actionData: any = event;
    this.selectedData = event.data;
    switch (actionData.type) {
      case 'accept'://Todo
        break;
      case 'reject'://Todo

        break;
      case 'drop'://Todo
        break;
      case 'approve_specs':
        // this._commonService.openDialog(AddEditSpecificationsComponent, { designCodeData: event.data, responseData: this.quotationResponse, quotationResponseList: this.quotationResponses, specificationGroupUids: this.roadmapMaster.specificationGroupUids, roadmapDetails: this.roadmapMaster }, 'APPROVE', '70%');


        const dialogRef = this.dialog.open(AddEditSpecificationsComponent, {
          minWidth: '90%',
          width: '90%',
          data: { data: { designCodeData: event.data, responseData: this.quotationResponse, quotationResponseList: this.quotationResponses, specificationGroupUids: this.roadmapMaster.specificationGroupUids, roadmapDetails: this.roadmapMaster }, status: 'APPROVE', isCopied: false },
          disableClose: true,
        });
        dialogRef.componentInstance.onCopySpec.subscribe(response => {
          if (response) {
            this.allDesignCodes.forEach(designCodeItem => {
              if (response.designcodeList?.includes(designCodeItem.code)) {
                designCodeItem['copiedSpec'] = response.spec;
                designCodeItem['specConfigList'] = response.specsConfigurationList;
                designCodeItem['specsUnitMasterConfigurationList'] = response.specsUnitMasterConfigurationList;
                designCodeItem['specFileUid'] = response.specFileUid;
              } else {
                return;
              }
            })
          }
        });
        dialogRef.afterClosed().subscribe(response => {
        });
        break;
      case 'feedback_specs':
        this.getFeedbackList(actionData.data);
        break;
      case 'view_ppt':
        this.getFileList(actionData.data)
        break;
      case 'view_specs':
        this._commonService.openDialog(AddEditSpecificationsComponent, { designCodeData: event.data, responseData: this.quotationResponse, specificationGroupUids: this.roadmapMaster.specificationGroupUids, roadmapDetails: this.roadmapMaster }, TABLE_ACTION_TYPES.VIEW, '70%');
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this._commonService.navigate('sourcing/request/update-designcode-configuration/""' + actionData.data.requestUid)
        break;
      case 'attach_protocol':
        this.openDialogForAttachProtocol(actionData.data)
          .pipe(takeUntil(this.$destroyed))
          .subscribe({
            next: res => {
              if (res) {
                if (res == 'create') {
                  this.showTable = false;
                  this.showCreteProtocol = true;
                } else if (res) {
                  let payload = this.quotationResponse.filter(item => item.uuid == actionData.data.quotationResponseUid)[0];
                  delete payload['request'];
                  let designCode = actionData.data;
                  designCode['testProtocolName'] = res.name;
                  designCode['testProtocolUid'] = res.uuid;
                  this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.attachTestProtocol, null, null, [designCode]).subscribe({
                    next: res => {
                      this._commonService.success(`Test Protocol has been added for ${actionData.data.code} Design code successfully`);
                      this.refreshPage();
                    }, error: error => {
                      this._commonService.handleError(error);
                    }
                  });
                }

              }

            }, error: error => {

            }
          })
        break;
      case 'final_submit':
        this._commonService.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.bidding)
          .pipe(takeUntil(this.$destroyed))
          .subscribe(confirm => {
            if (confirm) {
              let selectedResponse: any = this.quotationResponse.find(o => this.selectedDesignCodesForFinalSubmit[0].quotationResponseUid == o.uuid);
              let { request, ...deletedRequestResponse } = selectedResponse;
              deletedRequestResponse['designcodes'] = this.selectedDesignCodesForFinalSubmit;
              this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.finalSubmitQuotation, null, null, deletedRequestResponse)
                .pipe(takeUntil(this.$destroyed))
                .subscribe({
                  next: res => {
                    this._commonService.success(`Successfully initiated bidding process, Go to Bidding request and select newly created request and proceed with bidding`);
                    this.refreshPage();
                    this.close();
                  }, error: error => {
                    this._commonService.handleError(error);
                  }
                });
            }
          });

        break;
      case 'bidding_reject':
        this.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.negotiate).subscribe(res => {
          if (res) {
            this.openDialogForFeedbackReject({}, 'Reason For Negotiation').subscribe({
              next: res => {
                let payload = this.quotationResponse.filter(item => item.uuid == actionData.data.quotationResponseUid)[0]
                delete payload['request'];
                payload['designcodes'] = [actionData.data];

                let feedbacks = {};
                feedbacks['feedbacCategoryType'] = 2;
                feedbacks['feedback'] = res;
                feedbacks['userRole'] = this._commonService.getUseRole();
                feedbacks['userUid'] = this._commonService.getCurrentUser()?.uuid;
                feedbacks['userName'] = this._commonService.getCurrentUser()?.userName;
                payload['designcodes'][0]['feedbacks'] = [feedbacks];

                this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.rejectBidding, '', '', [payload]).subscribe({
                  next: res => {
                    this._commonService.success(`Request successfully placed for negotiation`);
                    this.refreshPage();
                  }, error: error => {
                    this._commonService.handleError(error);
                  }
                });
              }, error: error => {
                this._commonService.handleError(error);
              }
            })

          }
        })
        break;
      case 'bidding_drop':
        this.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.drop).subscribe(res => {
          if (res) {
            let payload = this.quotationResponse.filter(item => item.uuid == actionData.data.quotationResponseUid)[0]
            delete payload['request'];
            payload['designcodes'] = [actionData.data];
            this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.dropBidding, null, null, [payload]).subscribe({
              next: res => {
                this._commonService.success(`${actionData.data.code} bidding has been dropped successfully`);
                this.refreshPage();
              }, error: error => {
                this._commonService.handleError(error);
              }
            });
          }
        })
        break;
      case 'bidding_approve':

        break;
      case 'start_ppt':
        sessionStorage.setItem('trfDesignCodes', JSON.stringify(this.selectedDesignCodesForTRF));
        this._router.navigate([`testing/testRequisition/${TABLE_ACTION_TYPES.CREATE}/${this.selectedDesignCodesForTRF[0].quotationResponseUid}`], { queryParams: { page: 'sourcing', testProtocolUid: this.selectedDesignCodesForTRF[0].testProtocolUid, labUid: this.selectedDesignCodesForTRF[0].labUid } })
        break;
      case 'view_attachments':
        this._commonService.openDialog(AddAttachmentsByDesigncodeDocumentComponent, { isDocumentUpload: false, designCode: actionData.data, roadMap: { uuid: '' } }, '', '55%').subscribe(res => {
          if (res) {
          }
        });
        break;
      case 'add_attachments':
        this.onAddAttachments(actionData.data)
        break;
      case 'view_reject_comments':
        this.getFeedbackList(actionData.data);
        break;
      case 'VIEW_GROUPWISE_DETAILS':
        this.isShowGroupWiseDesignCodes = true;
        this.groupWiseDesignCodesList = actionData.data?.list;
        this.groupWiseColumnDefs = this.GroupWiseDesignColumns();
        break;
      case 'VIEW_TEST_PROTOCOL':
        const url = this._router.serializeUrl(
          this._router.createUrlTree(['testing/testProtocol/edit/' + actionData.data.testProtocolUid])
        );
        window.open(url, '_blank');
      default:
        // code block
        break;
    }
  }

  private onAddAttachments(action): void {
    this._commonService.openDialog(AddAttachmentsByDesigncodeDocumentComponent, { isDocumentUpload: true, designCode: action, roadMap: this.roadmapMaster }, '', '50%')
      .pipe(takeUntil(this.$destroyed))
      .subscribe(res => {
        if (res) {
        }
      });
  }
  private GroupWiseDesignColumns(): any[] {
    return [
      {
        headerName: "Group", field: "groupName", sortable: false, unSortIcon: true, wrapText: false, pinned: 'left', maxWidth: 100
      },
      { headerName: "DID No", field: "code", sortable: false, unSortIcon: true, wrapText: false, pinned: 'left' },
      {
        headerName: "Category", field: "glName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.glName;
        }
      },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Brand", field: "brand", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.brand;
        }
      },
      {
        headerName: "Reference ASINs", field: "referenceAsin", sortable: true, unSortIcon: true, minWidth: 300,
        cellRenderer: (params) => {
          return this._commonService.prepareReferenceASINsLinks(this.roadmapMaster?.referenceAsin);
        }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: (params.data.testProtocolUid && !this._commonService.getUseRole().includes(ROLE.VENDOR)) ? [{ type: "VIEW_TEST_PROTOCOL", title: "View Protocol", icon: ICON_LIST.MICROSCOPE, data: params.data }] : [], onAction: (action) => this.onAction(action) }
        }
      }


    ];
  }

  private viewAttchments(documentsList): void {
    let docUid = documentsList.find(item => item.docUid)?.docUid;
    let fileExtension = documentsList.find(item => item.docUid)?.fileExtension
    let headers = new HttpHeaders()
      .set("Authorization", this._commonService.getBasicAuth()).set('Client', this._commonService.getTenantUid());;
    this.http.get(environment.apiUrl + `sourcing/quotation/response/${docUid}/doc`, {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, fileExtension + fileExtension);
    });
  }

  //get docUid from here to get view attachments
  private getFileList(row): void {
    APP_UI_CONFIG.quotation.getAllFilesBasedOnDesignCode.paramList.designCodeUid = row?.uuid;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getAllFilesBasedOnDesignCode)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response.length > 0) {
            this.viewAttchments(response);
          } else {
            // this._commonService.error("PPT not uploaded")
          }
        },
        error: error => {

        }
      })
  }
  // //get quotation feedback and view
  // private getQuotationFeedbackList(designCode): void {
  //   APP_UI_CONFIG.quotation.getFeedbackByDesignCode.paramList.designcodeUid = designCode.uuid;
  //   this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getFeedbackByDesignCode).subscribe({
  //     next: res => {
  //       this.openDialogForFeedback(res, 'View Reject Reason', true);
  //     }, error: error => {
  //       this._commonService.handleError(error);
  //     }
  //   });
  // }

  //Get list of feedback on specs and bidding
  private getFeedbackList(designCode): void {
    APP_UI_CONFIG.quotation.getFeedbackByDesignCode.paramList.designcodeUid = designCode.uuid;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getFeedbackByDesignCode)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          if (designCode.status == QuotationStatus.REJECTED) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.QUOTATIONFEEDBACK);
            this.saveOrUpdateFeedback(designCode, feedbackForQuotationReject);
          }
          if (designCode.status == QuotationStatus.ADDITIONALSPECS) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.SPECSFEEDBACK);
            this.saveOrUpdateFeedback(designCode, feedbackForQuotationReject);
          }
          if (designCode.status == QuotationStatus.BIDDINGREJECTED) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.BIDDINGFEEDBACK);
            this.saveOrUpdateFeedback(designCode, feedbackForQuotationReject);
          }

        }, error: error => {
          this._commonService.handleError(error);
          this.saveOrUpdateFeedback(designCode, []);
        }
      });
  }
  //Share feedback
  private saveOrUpdateFeedback(designCode, feedbackList): void {
    this.openDialogForFeedback(feedbackList, 'Share Feedback')
      .pipe(takeUntil(this.$destroyed))
      .subscribe(res => {
        if (res) {
          let payload: any;
          let feedbacks: any = {};
          if (designCode.status == QuotationStatus.REJECTED) {
            feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : 1;
          }
          if (designCode.status == QuotationStatus.ADDITIONALSPECS) {
            feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : 2;
          }
          if (designCode.status == QuotationStatus.BIDDINGREJECTED) {
            feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : 3;
          }

          if (designCode.status != QuotationStatus.BIDDINGREJECTED) {
            feedbacks['feedback'] = res;
            feedbacks["designCodeUid"] = designCode.uuid;
            feedbacks['userRole'] = this._commonService.getUseRole();
            feedbacks['userUid'] = this._commonService.getCurrentUser()?.uuid;
            feedbacks['userName'] = this._commonService.getCurrentUser()?.userName;

            const quotationResponse = designCode;
            quotationResponse['feedbacks'] = [feedbacks];
            let feedbackPayload = this.quotationResponses.find(item => item.uuid == designCode.quotationResponseUid);
            delete feedbackPayload.request;
            feedbackPayload['designcodes'] = [quotationResponse];
            payload = feedbackPayload;
          } else {
            let list: any[] = designCode.list;
            let designCodeList: any[] = [];
            list.forEach(item => {
              feedbacks['feedback'] = res;
              feedbacks["designCodeUid"] = item.uuid;
              feedbacks['userRole'] = this._commonService.getUseRole();
              feedbacks['userUid'] = this._commonService.getCurrentUser()?.uuid;
              feedbacks['userName'] = this._commonService.getCurrentUser()?.userName;
              let { list, ...newDesignCode } = item;
              newDesignCode['feedbacks'] = [feedbacks];
              designCodeList.push(newDesignCode);
            });
            let feedbackPayload = this.quotationResponses.find(item => item.uuid == designCode.quotationResponseUid);
            delete feedbackPayload.request;
            feedbackPayload['designcodes'] = designCodeList;
            payload = feedbackPayload;
          }
          this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.updateFeedbackByResponse, null, null, payload)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: res => {
                this._commonService.success(`Feedback shared successfully`);
                this.refreshPage();
              }, error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      })
  }
  public close(): void {
    this._commonService.historyBack();
  }

  private openDialogForFeedbackReject(data, header?): Observable<any> {
    const dialogRef = this.dialog.open(TflFeedbackFormComponent, {
      width: '50%',
      data: { data: data, header: header },
      disableClose: true,
    });
    return dialogRef.afterClosed()
  }

  private openDialogForFeedback(data, header?, isDisabled = false): Observable<any> {
    const dialogRef = this.dialog.open(TflChatboxComponent, {
      width: '50%',
      data: { data: data, header: header, isDisabled: isDisabled },
      disableClose: true,
    });
    return dialogRef.afterClosed()
  }

  private openDialogForConfirmation(message): Observable<any> {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '30%',
      data: message,
      disableClose: true,
    });
    return dialogRef.afterClosed()
  }

  private openDialogForAttachProtocol(data): Observable<any> {
    const dialogRef = this.dialog.open(AttachTestProtocolComponent, {
      width: '70%',
      data: data,
      disableClose: true,
    });
    return dialogRef.afterClosed()
  }

  public refreshPage(): void {
    this.selectedDesignCodes = [];
    this.selectedDesignCodesForFinalSubmit = [];
    this.selectedDesignCodesForLab = [];
    this.onRefresh();
  }

  public finalSubmitQuotation(quotationResponse): void {
    this._commonService.openDialogForConfirmation(SOURCING_CONFIGURATION.confirmationMessages.bidding)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          let { request, ...deletedRequestResponse } = quotationResponse;
          this.invokeService.serviceInvocation(APP_UI_CONFIG.quotation.finalSubmitQuotation, null, null, deletedRequestResponse)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: res => {
                this._commonService.success(`Successfully initiated bidding process, Go to Bidding request and select newly created request and proceed with bidding`);
                this.refreshPage();
                this.close();
              }, error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  public finalSubmit(): void {
    let isSelectedDesignCodeHasProductTypeUid = this.selectedDesignCodesForFinalSubmit.every(item => item.productTypeUid && item.productTypeName);
    (isSelectedDesignCodeHasProductTypeUid) ? this.onAction({ 'type': 'final_submit' }) : this._commonService.error('Protocols not attached');
  }
  public onRequestTRF(): void {
    let isTestProtocolUid = this.selectedDesignCodesForTRF.filter(item => item.testProtocolUid).every(eachDesignCode => {
      return this.selectedDesignCodesForTRF.filter(item => item.testProtocolUid).every(item => item.testProtocolUid == eachDesignCode.testProtocolUid) ? true : false
    })

    let isTrfLab = this.selectedDesignCodesForTRF.filter(item => item.testProtocolUid).every(eachDesignCode => {
      return this.selectedDesignCodesForTRF.filter(item => item.testProtocolUid).every(item => item.labName == eachDesignCode.labName) ? true : false
    })
    if (!isTrfLab && isTestProtocolUid) {
      this._commonService.error("Lab is not same for selected group")
    }
    else if (isTestProtocolUid && isTrfLab) {
      this.checkForInternalLabAddition();
    } else {
      this._commonService.error("Test protocol is not same for each selected DID")
    }

  }

  public checkForInternalLabAddition(): void {
    this.isAllSelectedDiDHavePPT = false;
    this.showIsPPTRequiredMsg = false;
    this.showTRFConfirmationDialog = false;
    this.internalTestingLab = [];
    this.selectedInternalLabUid = {};
    this.isSomeSelectedDiDHavePPT = false;
    if (this.selectedDesignCodesForTRF.every((item) => item.pptReportDocUid)) {
      this.showTRFConfirmationDialog = true;
      this.isAllSelectedDiDHavePPT = true;
      this.showIsPPTRequiredMsg = true;
    } else if (this.selectedDesignCodesForTRF.some((item) => item.pptReportDocUid)) {
      this.showTRFConfirmationDialog = true;
      this.isSomeSelectedDiDHavePPT = true;
    } else if (this.selectedDesignCodesForTRF.every((item) => !item.pptReportDocUid)) {
      this.openCreateTRFPage();
    }
  }

  public openCreateTRFPage(): void {
    this.onAction({ 'type': 'start_ppt' });
  }

  public closeTRFConfirmationDialog(): void {
    this.showTRFConfirmationDialog = false;
  }

  public onNoPPTRequired(): void {
    this.showTRFConfirmationDialog = false;
  }

  public onPPTRequired(): void {
    this.showTRFConfirmationDialog = false;
    this.openCreateTRFPage();
  }

  public goForSelfPPT(): void {
    this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.testingLab.getInternalLab, null, null, null)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          this.internalTestingLab = res;
        }, error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  public requestTRFWithInternalLab(): void {
    sessionStorage.setItem('trfDesignCodes', JSON.stringify(this.selectedDesignCodesForTRF));
    this._router.navigate([`testing/testRequisition/${TABLE_ACTION_TYPES.CREATE}/${this.selectedDesignCodesForTRF[0].quotationResponseUid}`], { queryParams: { page: 'sourcing', testProtocolUid: this.selectedDesignCodesForTRF[0].testProtocolUid, labUid: this.selectedInternalLabUid.uuid } })
  }

  private prepareAndMapDesignCodesWithResponse(designCodes: any[]): any[] {
    let payload: any[] = [];
    designCodes.forEach(designCode => {
      let responseIndex: any;
      responseIndex = payload.findIndex(response => response.uuid == designCode.quotationResponseUid);
      if (responseIndex > -1) {
        payload[responseIndex]['designcodes'].push(designCode);
      } else {
        let response = this.quotationResponse.filter(response => response.uuid == designCode.quotationResponseUid)[0];
        delete response['request'];
        response['designcodes'] = [designCode];
        payload.push(response);
      }
    });
    return payload;
  }

  private checkIfEveryValueExist(el) {
    if (this.supplierType == 'open') {
      if (el.moq && el.productionProposedPrice && el.packingProposedPrice && el.shipmentProposedPrice && el.marginProposedPrice && el.totalProposedPrice && el.taxProposedPrice) {
        return true;
      } else {
        return false;
      }
    } else if (this.supplierType == 'closed') {
      if (el.fob && el.moq && el.scope && el.outOfScope) {
        return true;
      } else {
        return false;
      }
    }
    else if (this.supplierType == 'lab') {
      if (el.labSamplesRequired && el.noOfDaysForLabTesting && el.labTestingTotalPrice) {
        return true;
      } else {
        return false;
      }
    }
  }

  public calcTotalwithTax(rowData): any {
    let total = this.checkNull(rowData.productionProposedPrice) + this.checkNull(rowData.rawMaterialProposedPrice) + this.checkNull(rowData.brandingProposedPrice) + this.checkNull(rowData.packingProposedPrice) +
      this.checkNull(rowData.shipmentProposedPrice) + this.checkNull(rowData.productionProposedPrice);
    let totalProposedPrice = total + ((this.checkNull(rowData.marginProposedPrice) / 100) * total);
    let finalCostWithTax = ((this.checkNull(rowData.taxProposedPrice) / 100) * totalProposedPrice) + totalProposedPrice;
    return finalCostWithTax?.toFixed(2);
  }

  checkNull(value): number {
    if (value == null || value == '' || value == ' ') {
      return 0
    }
    return parseFloat(value)
  }

  showCreteProtocol: boolean = false;
  public handleClose(): void {
    this.showCreteProtocol = false;
    this.showTable = false;
  }


  public onChangeTab(event): void {
    this.tabIndex = event.index;
    this.prepareColumnDefs();
  }

  public checkIfTRFRaised(row): any {
    let rowMaster = this.designCodeMaster.filter(item => item.uuid == row.designCodeUid)[0];
    if (row.status == 9 && (rowMaster && rowMaster.status == DesignMaster.TRF_REQUESTED)) {
      row['status'] = 10;
    }
    return row
  }

  quotationStatus: string = '';
  public getQuotationStatus(): any {
    if (+new Date(Number(this.quotationResponse[0]?.quotationEndDate)) < +new Date()) {
      this.quotationStatus = this.quotationResponse[0].costingMode == 2 ? 'Bidding Closed' : 'Quotation Closed';
    } else {
      this.quotationStatus = this.quotationResponse[0].costingMode == 2 ? 'Bidding Open' : 'Quotation Open';
    }
    return this.quotationStatus;
  }

  private prepareDesignCodesStatus = (row) => {
    let status: string = '';
    if (this.menuType == SOURCINGMENUTYPE.QUOTATION) {
      switch (row.status) {
        case QuotationStatus.CREATED:
          status = this._commonService.prepareSourcingStatus("Quotation Created", "#ffa500");
          break;
        case QuotationStatus.PENDING:
          status = this._commonService.prepareSourcingStatus("Quotation Pending", "#64748b");
          break;
        case QuotationStatus.QuotationSubmitted:
          status = this._commonService.prepareSourcingStatus("Quotation Received", "#0000ff");
          break;
        case QuotationStatus.REJECTED:
          status = this._commonService.prepareSourcingStatus("Under Negotiation", "#f97316");
          break;
        case QuotationStatus.DROPPED:
          status = this._commonService.prepareSourcingStatus("Quotation Rejected", "#f97316");
          break;
        case QuotationStatus.RFQ1APPROVED:
          status = this._commonService.prepareSourcingStatus("Quotation Approved", "#22c55e");
          break;
        default:
          status = this._commonService.prepareSourcingStatus("Quotation Approved", "#22c55e");
      }
    }
    else if (this.menuType == SOURCINGMENUTYPE.TPS) {
      switch (row.status) {
        case QuotationStatus.RFQ1APPROVED:
          status = this._commonService.prepareSourcingStatus("Specs Pending", "#009688");
          break;

        case QuotationStatus.ADDITIONALSPECS:
          status = this._commonService.isProductCompliance() ? this._commonService.prepareSourcingStatus("Specs Received", "#64748b") : this._commonService.prepareSourcingStatus("In-Progress", "#ffa500");;
          break;

        case QuotationStatus.RFQ2APPROVED:
          status = this._commonService.prepareSourcingStatus("Specs Approved", "#22c55e");
          break;
        case QuotationStatus.BIDDINGREREQUESTSTARTED:
          status = this._commonService.prepareSourcingStatus("Bidding Started", "#ffa500");
          break;
        default:
          status = this._commonService.prepareSourcingStatus("Specs Approved", "#22c55e");
      }
    }
    else if (this.menuType == SOURCINGMENUTYPE.BIDDING) {
      switch (row.status) {
        case QuotationStatus.CREATED:
          status = this._commonService.prepareSourcingStatus("In-Progress", "#ffa500");
          break;

        case QuotationStatus.PENDING:
          status = this._commonService.isProductCompliance() ? this._commonService.prepareSourcingStatus("Bidding Pending", "#64748b") : this._commonService.prepareSourcingStatus("In-Progress", "#ffa500");;
          break;

        case QuotationStatus.QuotationSubmitted:
          status = this._commonService.prepareSourcingStatus("Bidding Received", "#0000ff");
          break;

        case QuotationStatus.BIDDINGREJECTED:
          status = this._commonService.prepareSourcingStatus("Under Negotiation", "#f97316");
          break;
        case QuotationStatus.BIDDINGDROPPED:
          status = this._commonService.prepareSourcingStatus("Bidding Rejected", "#f97316");
          break;
        case QuotationStatus.BIDDINGRAPPROVED:
          status = this._commonService.prepareSourcingStatus("Bidding Approved", "#22c55e");
          break;
        default:
          status = this._commonService.prepareSourcingStatus("Bidding Approved", "#22c55e");
      }
    } else {

    }

    return status;

    return status;
  }

  //destroy all
  public ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
