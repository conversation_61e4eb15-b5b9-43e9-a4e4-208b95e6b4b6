<div class="container-fluid w-full h-full abc">
    <div class="row h-full">
        <p-sidebar [(visible)]="visibleSidebar5" [fullScreen]="true" [baseZIndex]="10000">
            <div class="timeline-header">Portal Updates Notifications</div>
            <div class="mt-6"></div>
            <div>
                <p-timeline [value]="events1" align="alternate" styleClass="customized-timeline">
                    <ng-template pTemplate="marker" let-event>
                        <span class="custom-marker p-shadow-2" [style.backgroundColor]="event.color">
                            <i [ngClass]="event.icon"></i>
                        </span>
                    </ng-template>
                    <ng-template pTemplate="content" let-event>
                        <p-card [header]="event.status" [subheader]="event.date">
                            <ul *ngFor="let item of event.description" style="list-style: disc;">
                                <li>{{item}}</li>
                            </ul>
                        </p-card>
                    </ng-template>
                </p-timeline>
            </div>
        </p-sidebar>
        <div class="col-md-7 col-lg-7 d-none d-sm-block d-sm-none d-md-block h-full left_side_sign_in_wrapper"
            style="z-index: 0;">
            <img src="assets/svg/sign_in_circle_pattern.svg" alt="Circle Pattern" class="left_side_top_pattern">
            <img src="assets/svg/circle_pattern.svg" alt="Circles Pattern" class="right_middle_side_top_pattern">
            <div class="row  w-full h-full">
                <div class="z-10 relative w-full">
                    <div class="sign_main_title">
                        <div class="welcome-text">
                            <div class="signIn-label">Welcome to</div>
                        </div>
                        <div class="ml-5 portal_title">
                            <div class="main-label">{{title}}</div>
                        </div>
                        <div class="slogan_text">
                            {{subTitle}}

                        </div>
                    </div>
                    <div class="hyperlinks">
                        <ul class="flex items-center contact-us">
                            <li class="mr-14 ml-4 cursor-pointer" (click)="onOpenTerms('https://www.3frameslab.com/')">
                                Terms</li>
                            <li class="mr-14 cursor-pointer" (click)="onOpenTerms('https://www.3frameslab.com/')">
                                Privacy</li>
                            <li class="mr-9 cursor-pointer" (click)="onOpenTerms('https://www.3frameslab.com/contact')">
                                Contact Us</li>
                        </ul>
                        <div class="copy-right mt-4">
                            <span class="copyright-symbol">&copy;</span> <span class="ml-6"> {{today | date:'YYYY'}}
                            </span><span class="ml-1">{{copyrights}}</span>
                        </div>
                    </div>
                </div>
            </div>
            <img src="assets/svg/sign_in_square_pattern.svg" alt="Square Pattern" class="right_side_bottom_pattern">
        </div>
        <div class="col-md-5 col-lg-5 col-sm-12 col-xs-12 h-full" style="background-color: #FFFFFF;
        box-shadow: 0px 4px 10px rgba(123, 134, 153, 0.1);z-index: 1;">
            <div class="w-full h-full sing_in_box">
                <!-- Logo -->
                <div class="flex flex-row justify-center">
                    <img style="height:50px;width:200px;" alt="logo" [src]="headerStickyLogo" alt="Logo image">
                </div>
                <div *ngIf="!showOTP">

                    <!-- Title -->
                    <div class="flex flex-col justify-center gap-1 mb-4">
                        <!-- <div class="flex justify-center signIn-text">WELCOME ONBOARD</div> -->
                        <div class="flex justify-center signIn-text">Sign In</div>
                        <div class="flex justify-center sign-title-hint">Sign in using your user credentials that you
                            entered during the registration
                        </div>
                    </div>



                    <!-- Alert -->
                    <fuse-alert class=" " *ngIf="showAlert" [appearance]="'outline'" [showIcon]="false"
                        [type]="alert.type" [@shake]="alert.type === 'error'">
                        {{alert.message}}
                    </fuse-alert>

                    <!-- Sign in form -->
                    <form class="mt-8" [formGroup]="signInForm" #signInNgForm="ngForm">
                        <!-- Email field -->
                        <div class="flex flex-column gap-2 w-full mb-4">
                            <label htmlFor="username">Username</label>
                            <input id="email" pInputText id="username" aria-describedby="username-help"
                                [formControlName]="'email'" required />
                            <div
                                *ngIf="signInForm.controls['email'].invalid && (signInForm.controls['email'].dirty || signInForm.controls['email'].touched)">
                                <small id="username-help" class="p-error"
                                    *ngIf="signInForm.get('email').hasError('required')"> Username is required.</small>
                                <small id="username-help" class="p-error"
                                    *ngIf="signInForm.get('email').hasError('email')">
                                    Please enter a valid email address.</small>
                            </div>
                        </div>
                        <div class="flex flex-column gap-2 w-full">
                            <label htmlFor="password">Password</label>
                            <p-password id="password" [formControlName]="'password'" class="w-full" [feedback]="false"
                                [toggleMask]="true" required></p-password>
                            <div
                                *ngIf="signInForm.controls['password'].invalid && (signInForm.controls['password'].dirty || signInForm.controls['password'].touched)">
                                <small id="username-help" class="p-error"
                                    *ngIf="signInForm.get('password').hasError('required')">Password is
                                    required.</small>
                            </div>
                        </div>
                        <!-- Submit button -->
                        <!-- <div class="w-full  mt-8 ">
                        <p-checkbox class="keep-me-text" name="groupname" value="val1"
                            label=" Keep me signed in"></p-checkbox>
                    </div> -->
                        <button class="bg-primary-button w-full mrt-signin mt-16 login-btn-color" style="height:32px"
                            [disabled]="signInForm.disabled" (click)="signIn()">
                            <span *ngIf="!signInForm.disabled" style="font-weight: 400;">
                                Sign In
                            </span>
                            <mat-progress-spinner *ngIf="signInForm.disabled" [diameter]="24" [mode]="'indeterminate'">
                            </mat-progress-spinner>
                        </button>

                        <div class="inline-flex items-center justify-center w-full mt-3">
                            <div class="text-16 font-medium text-center Primary_Red_Neutrals_900 text-center "
                                style="cursor: pointer;" (click)="showForgetPasswordPage()">
                                Forgot Password?
                            </div>
                        </div>

                    </form>
                </div>
                <div class="opt-wrapper" *ngIf="showOTP">
                    <div *ngIf="showRegister" class="flex flex-col gap-2">
                        <div class="otp-register_title">
                            Register Now
                        </div>
                        <div class="flex flex-col gap-1">
                            <div>1.Please install Authenticator App in your mobile(<span>Click <a target="_blank"
                                        href="https://play.google.com/store/apps/details?id=com.azure.authenticator&hl=en&gl=US">here</a>
                                    to install</span>)</div>
                            <div>2.Scan QR Code through Authenticator App</div>
                            <div>3.Enter MFA Code </div>
                            <div>4.Click on verify</div>
                        </div>
                        <div class="flex flex-row tfl-flex-justify-center">
                            <img [src]="qrCodeImageUrl" alt="" style="width:150px;height:150px">
                        </div>
                    </div>
                    <div class="mb-2">
                        <i class="pi pi-mobile" style="font-size: 2rem"></i>

                    </div>
                    <div class="otp-title-wrapper">
                        <div class="otp-title">
                            MFA Verification
                        </div>
                        <div>
                            Please enter the verification code
                        </div>
                    </div>
                    <div class="opt-input-wrapper w-full">
                        <div class="flex flex-column gap-2 w-full">
                            <code-input [isCodeHidden]="false" [codeLength]="6" (codeChanged)="onCodeChanged($event)"
                                (codeCompleted)="onCodeCompleted($event)">
                            </code-input>

                            <!-- <p-password id="authenticationCode" [(ngModel)]="authenticationCode" placeholder="XXXXXX"
                                class="w-full" required></p-password> -->
                        </div>
                    </div>
                    <div class="w-full opt-action-wraper">
                        <button class="bg-primary-button w-full mrt-signin mt-3 login-btn-color"
                            [class.button_disabled]="(authenticationCode?.length !=6)"
                            [disabled]="(authenticationCode?.length !=6)"
                            style="height:32px;font-weight: 400; cursor: pointer;" (click)="onOtpVerification()">Verify
                            Code</button>
                        <div class="mt-2">
                            Open your Two-Factor Authenticator (TOTP) app to view your
                            Authentication Code.
                        </div>
                        <div *ngIf="!showRegister" style="font-weight: bold;">
                            Don't have an MFA code? <span style="color:#2196F3;cursor:pointer;align-items:center;"
                                (click)="registerMfaAuthentication()"> Register now.</span>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
</div>