
export class CapFollowupDetail {

  uuid: string;

  capUid: number;
  auditUid: string;
  reportUid: string;
  parentCapUid: number;
  capIterationNo: number;
  defectCode: number;
  designCodeUid: string;
  designCodeName: string;
  specificationUid: string;
  comments: string;

  picture: string
  videos: string
  attachments: string

  checkpointCode: string
  imageUrl: string
  optionUid: string
  optionValue: string
  observations: string
  productTypeUid: string
  productTypeName: string
  sampleUid: string
  checkpointUid: string
  checkpointChecklistUid: string
  checkpointCategoryUid: string
  checkpointCategoryName: string

  defectTypeUid: string
  defectTypeCategoryUid: string
  defectTypeCategoryName: string
  description: string
  cause: string
  srcType: string
  correctiveAction: string
  preventiveAction: string
  complianceReview: string
  plannedCompletionDate: string
  severity: string
  failureCategory: string
  followupRemarks: string
  weightage: string

  isSelected?:boolean
}