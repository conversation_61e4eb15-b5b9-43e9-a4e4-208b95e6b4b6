import { Location } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { CapaRefrenceDataMap } from 'app/core/qinspect/_model/CapaRefreancedatamap.model';
import { CapFollowup } from 'app/core/qinspect/_model/CapFollowup.model';
import { CapFollowupDetail } from 'app/core/qinspect/_model/CapFollowupDetail.model';
import { TestRequisitionForm } from 'app/core/qinspect/_model/TestRequisitionForm.model';
import { ROLE } from 'app/enum-constants';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import { CustomRenderTooltipComponent } from 'app/shared/custom-render-tooltip/custom-render-tooltip.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import * as _ from 'lodash';
import moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';

import { severity } from '../../../../core/qinspect/_model/GenericDashBoard.mode';
import { EditCapaDetailsComponent } from './edit-capa-details/edit-capa-details.component';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';

@Component({
  selector: 'add-edit-capa',
  templateUrl: './add-edit-capa.component.html',
  styleUrls: ['./add-edit-capa.component.scss']
})
export class AddEditCapaComponent implements OnInit, OnDestroy {
  @ViewChild('gridCapa') gridCapa: any;
  showeditcapa: boolean = false;
  dataforEditcapaComp: any;
  hdr: string = '';
  // data:CapaRefrenceDataMap[]=[];
  capaGridData: CapFollowupDetail[] = []
  rec: CapaRefrenceDataMap = new CapaRefrenceDataMap();
  // @Input() data: any;
  @Input() status: any;
  @Output() close: EventEmitter<any> = new EventEmitter();
  auditUid: string = "";
  capUid: string = "";
  selectedData: CapFollowupDetail;
  selectedCapaDetails: any;
  capFollowup: any[] = [];
  schedulePlanningData: any;
  user: any;
  returnUrl?: string;
  isTesting: boolean = false;
  isAuditor: boolean = false;
  isAdmin: boolean = false;
  isVendor: boolean = false;
  isFactory: boolean = false;
  isSeller: boolean = false;
  isLab: boolean = false;
  isAgency: boolean = false;
  isOps: boolean = false;
  testingRequest: TestRequisitionForm = {};
  expandInfoSection: boolean = false;
  hasDesignCodes: boolean = false;
  selectedCapa: any[] = [];
  tableHeight: string = '45vh';
  timeoutIdForScroll: any;
  columnDefs: any[] = [
    // {
    //   field: '',
    //   // headerCheckboxSelection: true,
    //   checkboxSelection: true,
    //   showDisabledCheckboxes: true,
    //   width: 52,
    //   minWidth: 52,
    // },
    // {
    //   headerName: "Status", field: "detailStatusText", sortable: true, unSortIcon: true, wrapText: false, filter: 'agTextColumnFilter',
    //   autoHeight: false,
    //   width: 100,
    //   minWidth: 100,
    //   cellRenderer: (params: ICellRendererParams) => {
    //     if (params.data.detailStatusText !== "CLOSED") {
    //       return "PENDING"
    //     }
    //     return params.data.detailStatusText;
    //   }
    // },
    {
      headerName: "Section", field: "checkpointCategoryName", sortable: true, unSortIcon: true, wrapText: true, filter: 'agTextColumnFilter',
      autoHeight: true, minWidth: 225,
      cellClass: function (params) {

        switch (params.data.severity) {
          case 'CRITICAL': return 'CRITICAL';
            break;
          case 'MAJOR': return 'MAJOR';
            break;
          case 'MAJAOR': return 'MAJAOR';
            break;
          case 'MINOR': return 'MINOR';
            break;
          default: return 'DEFAULT';
            break;
        }
      }

    },
    {
      headerName: "ASIN Code", field: "designCodeName", sortable: true, unSortIcon: true, wrapText: false, filter: 'agTextColumnFilter',
      autoHeight: false, minWidth: 150, hide: false
    },
    {
      headerName: "Clause", field: "description", sortable: true, unSortIcon: true, wrapText: true, filter: 'agTextColumnFilter',
      autoHeight: true,

      minWidth: 375,
      cellClass: ['text-align-left'],
      cellRendererFramework: CustomRenderTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
      //   cellStyle: function(params) {

      //     switch(params.data.status.toString()){
      //       case '8': return {backgroundColor: '#DAF7A6'};
      //         break;          
      //       default: return {backgroundColor: 'white'};
      //           break;
      //     }        
      // }
    },
    // {
    //   headerName: "Severity", field: "severity", wrapText: false,
    //   width: 150,
    //   minWidth: 150,
    //   autoHeight: false,
    //   sortable: true, unSortIcon: true,
    // },


    // {
    //   headerName: "ASIN Code", field: "designCodeName", wrapText: false,
    //   width: 150,
    //   minWidth: 150,
    //   autoHeight: false,
    //   sortable: true, unSortIcon: true,
    // },


    {
      headerName: "Cause", field: "cause", sortable: true, unSortIcon: true, wrapText: true, filter: 'agTextColumnFilter',
      autoHeight: true,
      minWidth: 375,
      cellClass: ['text-align-left'],
      cellRendererFramework: CustomRenderTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
    },
    {
      headerName: "Corrective Action", field: "correctiveAction", sortable: true, unSortIcon: true, wrapText: true, filter: 'agTextColumnFilter',
      autoHeight: true,
      minWidth: 375,
      cellClass: ['text-align-left'],
      cellRendererFramework: CustomRenderTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
    },
    {
      headerName: "Preventive Action", field: "preventiveAction", sortable: true, unSortIcon: true, wrapText: true, filter: 'agTextColumnFilter',
      autoHeight: true,
      minWidth: 375,
      cellClass: ['text-align-left'],
      cellRendererFramework: CustomRenderTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
    },
    {
      headerName: "CAP Closure Date",
      field: "plannedCompletionTime",
      sortable: true,
      wrapText: false,
      filter: 'agTextColumnFilter',
      autoHeight: false,
      unSortIcon: true,
      width: 250,
      minWidth: 250,
      cellRendererFramework: CustomDateTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data
    },
    // {
    //   headerName: "Compliance Review", field: "complianceReview", sortable: true, unSortIcon: true, wrapText: false, filter: 'agTextColumnFilter',
    //   autoHeight: false,
    // },
    {
      headerName: "Proof Submission Feedback", field: "capClosureComments", sortable: true, unSortIcon: true, wrapText: true, filter: 'agTextColumnFilter',
      autoHeight: true,
      minWidth: 375,
      cellClass: ['text-align-left'],
      cellRendererFramework: CustomRenderTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
    },
    {
      headerName: "CAP Feedback", field: "remarksL1", sortable: true, unSortIcon: true, wrapText: true, filter: 'agTextColumnFilter',
      autoHeight: true,
      minWidth: 375,
      cellClass: ['text-align-left'],
      cellRendererFramework: CustomRenderTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
    },
    {
      headerName: " Nonconformity", field: "comments", sortable: true, unSortIcon: true, wrapText: true, filter: 'agTextColumnFilter',
      autoHeight: true,
      minWidth: 375,
      cellClass: ['text-align-left'],
      cellRendererFramework: CustomRenderTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
    },
    {
      headerName: "Severity", field: "severity", wrapText: false,
      width: 150,
      minWidth: 150,
      autoHeight: false,
      sortable: true, unSortIcon: true,
    },
    // {
    //   headerName: "FollowUp Remarks", field: "followupRemarks", sortable: true, unSortIcon: true, wrapText: false, filter: 'agTextColumnFilter',
    //   autoHeight: false,
    // },

    {
      headerName: 'Actions',
      pinned: 'right',
      cellRendererFramework: CellActionComponent,
      cellRendererParams: (params) => {
        return { actions: this.prepareActionIconButtons(params.data) }
      }
    }

  ];

  ReportOptions = [
    {
      label: 'Download Report',
      icon: 'pi pi-download',
      command: () => {
        this.downloadReport();
      }
    }
  ];

  constructor(
    public _toastr: ToastrService,
    public _invokeService: InvokeService,
    public _commonService: CommonService,
    public route: ActivatedRoute,
    public dialog: MatDialog,
    public _authService: AuthService,
    private location: Location
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.returnUrl = params.returnUrl;
      if (this.returnUrl.includes('testing'))
        this.isTesting = true;
    });
    if (this._authService.hasLoggedUserPermitted(ROLE.AUDITOR))
      this.isAuditor = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.VENDOR))
      this.isVendor = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.FACTORY))
      this.isFactory = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.AGENCY))
      this.isAgency = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.LAB))
      this.isLab = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.ADMIN))
      this.isAdmin = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.SELLER))
      this.isSeller = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.OPERATIONS))
      this.isOps = true;
    this.auditUid = this.route.snapshot.params["auditUid"]
    this.capUid = this.route.snapshot.params["capUid"]

    if (this.capaGridData) {
      // this.rec = <CapaRefrenceDataMap>Util.clone(this.capaGridData);
    }
    if (this.status == 'edit') {
      this.hdr = 'View CAPA Items';
      // this.editRec = true;    
    }
    if (this.status == 'view') {
      this.hdr = 'View CAPA Items';
      // this.editRec = true;
    }
    this.getByAuditUid();
  }


  private getFormStatus(row): string {
    if (!row.status || row.status == 1) {
      if (this.isAuditor) {
        // iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
        return 'view';
      } else {
        return 'view';
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 2) {
      if (this.isVendor || this.isFactory) {
        return 'edit';
        // iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        return 'view';
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 3) {
      if (this.isAgency || this.isLab || this.isSeller || this.isAuditor) {
        return 'edit';
        //iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        return 'view';
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 4) {
      if (this.isVendor) {
        return 'edit';
        //iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        return 'view';
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 5) {
      if (this.isAgency || this.isSeller) {
        return 'edit';
        // iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        return 'view';
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 6) {
      if ((this.isAdmin || this.isOps) && this.selectedCapaDetails.auditTypeUid != "13b7e124-37ac-11ef-9454-0242ac120002") {
        return 'edit';
        //iconsList.push({ type: 'accept', title: "Accept", icon: 'accept_green', data: row });
        // iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      }
      if (this.isSeller && this.selectedCapaDetails.auditTypeUid == "13b7e124-37ac-11ef-9454-0242ac120002") {
        return 'view';
        // iconsList.push({ type: 'accept', title: "Accept", icon: 'accept_green', data: row });
      }
      if ((this.isVendor || this.isFactory) && row.capClosureComments != "CAPA Satisfactory") {
        return 'edit';
        //iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      }
    }
    else if (row.status == 8 && !this._authService.hasLoggedUserPermitted(ROLE.TEST_USER)) {
      return 'close';
      // iconsList.push({ type: 'close', title: "Closed", icon: 'close', data: row });
    }
    // iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
    // return iconsList;
  }
  private isFormAccept(row): boolean {
    if (!row.status || row.status == 1) {
      if (this.isAuditor) {
        // iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
        return false;
      } else {
        return false;
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 2) {
      if (this.isVendor || this.isFactory) {
        return false;
        // iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        return false;
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 3) {
      if (this.isAgency || this.isLab || this.isSeller || this.isAuditor) {
        return false;
        //iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        return false;
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 4) {
      if (this.isVendor) {
        return false;
        //iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        return false;
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 5) {
      if (this.isAgency || this.isSeller) {
        return false;
        // iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        return false;
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 6) {
      if ((this.isAdmin || this.isOps) && this.selectedCapaDetails.auditTypeUid != "13b7e124-37ac-11ef-9454-0242ac120002") {
        return true;
        //iconsList.push({ type: 'accept', title: "Accept", icon: 'accept_green', data: row });
        // iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      }
      if (this.isSeller && this.selectedCapaDetails.auditTypeUid == "13b7e124-37ac-11ef-9454-0242ac120002") {
        return true;
        // iconsList.push({ type: 'accept', title: "Accept", icon: 'accept_green', data: row });
      }
      if ((this.isVendor || this.isFactory) && row.capClosureComments != "CAPA Satisfactory") {
        return false;
        //iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      }
    }
    else if (row.status == 8 && !this._authService.hasLoggedUserPermitted(ROLE.TEST_USER)) {
      return false;
      // iconsList.push({ type: 'close', title: "Closed", icon: 'close', data: row });
    }
    // iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
    // return iconsList;
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];

    if (!row.status || row.status == 1) {
      if (this.isAuditor) {
        // iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 2) {
      if (this.isVendor || this.isFactory) {
        iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 3) {
      if (this.isAgency || this.isLab || this.isSeller || this.isAuditor) {
        iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 4) {
      if (this.isVendor) {
        iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 5) {
      if (this.isAgency || this.isSeller) {
        iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      } else {
        //iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      }
    }
    else if (row.status == 6) {
      if ((this.isAdmin || this.isOps) && this.selectedCapaDetails.auditTypeUid != "13b7e124-37ac-11ef-9454-0242ac120002") {
        iconsList.push({ type: 'accept', title: "Accept", icon: 'accept_green', data: row });
        iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      }
      if (this.isSeller && this.selectedCapaDetails.auditTypeUid == "13b7e124-37ac-11ef-9454-0242ac120002") {
        iconsList.push({ type: 'accept', title: "Accept", icon: 'accept_green', data: row });
      }
      if ((this.isVendor || this.isFactory) && row.capClosureComments != "CAPA Satisfactory") {
        iconsList.push({ type: 'edit', title: "Update", icon: 'edit', data: row });
      }
    }
    else if (row.status == 8 && !this._authService.hasLoggedUserPermitted(ROLE.TEST_USER)) {
      iconsList.push({ type: 'close', title: "Closed", icon: 'close', data: row });
    }
    iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
    return iconsList;
  }

  private isCheckBoxEnabled(row): boolean {
    if (!!row.description && !!row.cause && !!row.correctiveAction) {
      return true;
    } else
      return false;
  }


  private isCheckBoxColumnHide(): boolean {
    return false;
  }

  private getCapaItemsBy(): void {
    if (this.capUid == "") {
      this.prepareCapaData(this.capFollowup);
      // this.capaGridData = this.capFollowup;
      // if (this.gridCapa != null) {
      //   this.gridCapa.rowData = this.capFollowup;
      //   this.gridCapa.updateData(this.capFollowup)

      // }
    }
  }

  public getByAuditUid(): void {
    // APP_UI_CONFIG.capa.getCapaDetails.paramList['auditUid'] = this.auditUid
    // this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.getCapaDetails).subscribe(res => {
    //   this.capFollowup = res;
    //   this.selectedCapaDetails = this.capFollowup[0];
    // }, error => {
    //   if (error) {
    //     this._toastr.error(error.message)
    //   } else {
    //     this._toastr.error('Failed to get the data');
    //   }
    // })

    APP_UI_CONFIG.testingModule.testRequests.getTestRequestsByAuditId.paramList.auditId = this.auditUid;
    const testingObs$ = this._invokeService.serviceInvocation(APP_UI_CONFIG.testingModule.testRequests.getTestRequestsByAuditId);
    APP_UI_CONFIG.capa.getCapaDetails.paramList['auditUid'] = this.auditUid
    const capaDetailsObs$ = this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.getCapaDetails);

    APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulePlanningByAudiId.paramList.uuid = this.auditUid;
    const schedulePlanningByAuditId$ = this._invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulePlanningByAudiId);

    APP_UI_CONFIG.capa.getCapaDetailsByCap.paramList['auditUid'] = this.auditUid;
    APP_UI_CONFIG.capa.getCapaDetailsByCap.paramList['capUid'] = this.capUid;
    const capaDetailsByCap$ = this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.getCapaDetailsByCap)

    forkJoin([capaDetailsObs$, schedulePlanningByAuditId$, capaDetailsByCap$]).subscribe(results => {
      this.capFollowup = results[0] as any;
      this.selectedCapaDetails = this.capFollowup[0];
      this.prepareCapaData(_.sortBy(results[2], ['severity']) as any);
      // this.capaGridData = _.sortBy(results[2], ['severity']) as any;
      this.hdr = `${this.selectedCapaDetails.trackingNumber}, Audit Type:${this.selectedCapaDetails.auditTypeName}`
      this.schedulePlanningData = results[1];
      if (results[1].type == "INSPECTION") {
        this.columnDefs[1].hide = false;
      }
      //this.testingRequest = results[3];

    }), (err: any) => {
      this._commonService.handleError(err);
    };
  }

  private getByCapUid() {
    APP_UI_CONFIG.capa.getCapaDetailsByCap.paramList['auditUid'] = this.auditUid
    APP_UI_CONFIG.capa.getCapaDetailsByCap.paramList['capUid'] = this.capUid
    this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.getCapaDetailsByCap)
      .subscribe({
        next: response => {
          if (response) {
            this.prepareCapaData(response);
          }
        },
        error: error => {
          this._commonService.handleError(error);
        }
      })
  }

  private prepareCapaData(response: any[]): void {
    this.capaGridData = response?.map(capaItem => ({
      severityColor: capaItem.severity == 'CRITICAL' ? 'text-danger-500' : capaItem.severity == 'MAJOR' ? 'text-warning-500' : capaItem.severity == 'MAJAOR' ? 'text-ping-900' : capaItem.severity == 'MINOR' ? 'text-yellow-700' : '',
      plannedCompletionTimeText: capaItem.plannedCompletionTime ? moment(+new Date(capaItem.plannedCompletionTime)).format('MMMM Do YYYY') : '',
      capaStatusText: this.getFormStatus(capaItem),
      isAccept: this.isFormAccept(capaItem),
      isSelected: false,
      designCodeNameList: capaItem.designCodeName.split(','),
      ...capaItem
    }));
    if (this.isSeller) {
      this.capaGridData.sort((a, b) => {
        const aIs3 = a['status'] === 3 ? 0 : 1;
        const bIs3 = b['status'] === 3 ? 0 : 1;
        return aIs3 - bIs3;
      });
    }
    if (this.isVendor) {
      this.capaGridData.sort((a, b) => {
        const aIs3 = a['status'] === 2 ? 0 : 1;
        const bIs3 = b['status'] === 2 ? 0 : 1;
        return aIs3 - bIs3;
      });
    }
  }

  public getActions(event): void {
    let actionData: any = event;
    this.selectedData = actionData.data;
    if (actionData.status == 'accept') {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'Confirm CAPA item to accept and close?',
        disableClose: true,
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList['auditUid'] = actionData.data.auditUid
          APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList['capUid'] = actionData.data.capUid
          APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList['detailUid'] = actionData.data.uuid
          APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList['code'] = 8
          this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.updateCapaDetailStatus, null, null, this.selectedData).subscribe(res => {
            if (res.code == 200) {
              this._commonService.success(res.message);
              this.getRefreshEvent('');
            } else {
              this._commonService.error(res.message);
            }
          }, error => {
            this._commonService.handleError(error);
          })
        }
      });
    }
    else if (actionData.status == 'reject') {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'Confirm?',
        disableClose: true,
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList['auditUid'] = actionData.data.auditUid
          APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList['capUid'] = actionData.data.capUid
          APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList['detailUid'] = actionData.data.uuid
          APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList['code'] = -1
          this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.updateCapaDetailStatus, null, null, this.selectedData).subscribe(res => {
            if (res.code == 200) {
              this._commonService.success(res.message);
              this.getRefreshEvent('');
            } else {
              this._commonService.error(res.message);
            }
          }, error => {
            this._commonService.handleError(error);
          })
        }
      });
    }

    else if (actionData.status == 'edit') {
      this.showeditcapa = true;
      this.setData(actionData.status);
    }
    else if (actionData.status == 'view') {
      this.showeditcapa = true;
      this.setData(actionData.status);
    }
  }

  onSaveCapaDetails(event) {
    let result = event;
    if (result == false) {

    } else
      this.saveCAPADetails(result.rec, result.code, result.isSaving,  result.isSavingMessage);
    this.showeditcapa = false;
  }

  capaStatus: string;
  public setData(status): void {
    this.capaStatus = status;
    this.dataforEditcapaComp = { status: status, data: this.selectedData, auditType: this.schedulePlanningData.type };
    // const dialogRef = this.dialog.open(EditCapaDetailsComponent, {
    //   disableClose: true,
    //   data: { status: status, data: this.selectedData, auditType: this.schedulePlanningData.type },
    //   width: '70%'
    // });

    // dialogRef.afterClosed().subscribe((result: any) => {
    //   if (result) {
    //     this.saveCAPADetails(result.rec, result.code, result.isSaving);
    //   }
    // });
  }
  // UPDATE CAPA Details status
  private updateCAPADetailsStatus(data, code, isSavingMessage): void {
    APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList.auditUid = data.auditUid;
    APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList.capUid = data.capUid;
    APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList.detailUid = data.uuid;
    APP_UI_CONFIG.capa.updateCapaDetailStatus.paramList.code = (this._commonService.getUseRole() == ROLE.SELLER && data.capClosureComments && data.capClosureComments != 'CAPA Satisfactory') ? 2 : code;
    let capaDet = new CapFollowup()
    capaDet.details.push(data)
    this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.updateCapaDetailStatus, null, null).subscribe(res => {
      if (res.code == 200) {
        if(isSavingMessage){
           this._commonService.success("Data saved successfully");
        }else
        this._commonService.success(res.message ? res.message : 'CAPA Details Status has been updated successfully');
        
        this.getRefreshEvent('');
      } else {
        this._commonService.error(res.message ? res.message : 'Failed to update the CAPA Details Status');
      }
    }, error => {
      this._commonService.handleError(error);
    })
  }
  //Update Capa details
  public saveCAPADetails(data, code, isSaving,isSavingMessage ): void {
    APP_UI_CONFIG.capa.updateCapaDetails.paramList.auditUid = data.auditUid;
    APP_UI_CONFIG.capa.updateCapaDetails.paramList.capUid = data.capUid;
    let capaDet = new CapFollowup()
    capaDet.details.push(data)
    this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.updateCapaDetails, null, null, capaDet).subscribe(res => {
      if (res.code == 200) {
        this.selectedCapa = [];
        this._commonService.success(res.message ? res.message : 'CAPA Details has been updated successfully');
        this.updateCAPADetailsStatus(data, code, isSavingMessage);
        if (code == 2 && !isSaving) {
          this.updateCAPAStatus(data, code);
        }

      } else {
        this.selectedCapa = [];
        this._commonService.error(res.message ? res.message : 'Failed to update the CAPA Details');
      }
    }, error => {
      this.selectedCapa = [];
      this._commonService.handleError(error);
    })
  }

  public scrollToBottomOfPage(): void {
    let id = this.capaGridData[this.capaGridData.length - 1].uuid;
    let el = document.getElementById(id);
    this.timeoutIdForScroll = setTimeout(() => {
      el.scrollIntoView({ behavior: "smooth" });
    }, 10)
  }
  public scrollToTopOfPage(): void {
    let id = this.capaGridData[0].uuid;
    let el = document.getElementById(id);
    this.timeoutIdForScroll = setTimeout(() => {
      el.scrollIntoView({ behavior: "smooth" });
    }, 10)
  }

  public onMultipleSubmit() {

  }

  //Update main CAPA Status
  private updateCAPAStatus(data, code): void {
    APP_UI_CONFIG.capa.updateCapaStatus.paramList.auditUid = this.selectedCapaDetails.auditUid;
    APP_UI_CONFIG.capa.updateCapaStatus.paramList.capUid = this.selectedCapaDetails.uuid;
    APP_UI_CONFIG.capa.updateCapaStatus.paramList.code = 2;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.updateCapaStatus).subscribe(res => {
      if (res.code == 200) {
        this._commonService.success(res.message);
      } else {
        this._commonService.error(res.message);
      }
    }, error => {
      this._commonService.handleError(error);
    });
  }

  public onSelectionChanged(event): void {
    this.selectedCapa = event;
  }



  formatToolTip(params: any) {
    return params
  }

  public getRefreshEvent(event): void {
    if (this.capUid == "") {
      this.getCapaItemsBy();
    } else {
      this.getByCapUid();
    }
  }

  public adjustTableHeight() {
    if (this.expandInfoSection) {
      this.tableHeight = '45vh';
    } else {
      this.tableHeight = '73vh';
    }
  }

  public onCapSubmissionNotify() {
    APP_UI_CONFIG.capa.notifyCapEntries.paramList['auditUid'] = this.auditUid
    APP_UI_CONFIG.capa.notifyCapEntries.paramList['capUid'] = this.capUid
    this._invokeService.serviceInvocation(APP_UI_CONFIG.capa.notifyCapEntries).subscribe(res => {
    }, error => {
      if (error) {
        this._toastr.error(error.message)
      } else {
        this._toastr.error('Failed to get the data');
      }
    })
  }

  public onCopyDetails(copiedItems): void {
    this.capaGridData.forEach(item => {
      let capaItem = copiedItems.find(cItem => cItem.uuid == item.uuid);
      if (capaItem) {
        item['capClosureComments'] = capaItem.capClosureComments;
        item['capClosureFeedback'] = capaItem.capClosureFeedback;
        item['remarksL1'] = capaItem.remarksL1;
        item['code'] = capaItem.code;
      }
    })
  }

  selectedCapaItems: any[] = [];
  public onSelectCapaItems(capaItem, event): void {
    this.selectedCapaItems = this.capaGridData.filter(capaItem => capaItem.isSelected);
  }

  public isFollowupRemarksRequired(data): boolean {
    if (data.capClosureComments == "CAPA Satisfactory" && !data.capClosureFeedback) {
      return true
    }
    else {
      return false
    }
  }

  public onSubmitAll(): void {
    let isAnyItemWithMissingData: boolean = false;;
    this.selectedCapaItems.forEach(capaItem => {
      if (!capaItem.capClosureComments || !capaItem.remarksL1 || this.isFollowupRemarksRequired(capaItem)) {
        isAnyItemWithMissingData = true;
      }
    })
    if (isAnyItemWithMissingData) {
      this._commonService.warning("Some of selected item have missing values ,Please fill all the required fields of your select capa item.")
    } else {
      this.selectedCapaItems.forEach(capaItem => {
        this.saveCAPADetails(capaItem, 8, false , false);
      })
      this.selectedCapaItems = [];
    }
  }

  public ngOnDestroy(): void {
    clearTimeout(this.timeoutIdForScroll);
  }

  public downloadReport() {
    let reportUrl = URL_CONSTANTS.BASE_URL + URL_CONSTANTS.AUDIT_GETREPORT + '/' + this.auditUid + '.pdf';
    window.open(reportUrl, '_blank');
  }


  public closeCapa(): void {
    //this.close.emit(true);
    this.selectedCapa = [];
    this.location.back();
  }

}
