import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { FilterList } from 'app/core/qinspect/_model/filterList.model';
import { ROLE } from 'app/enum-constants';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import moment from 'moment';

@Component({
  selector: 'app-capa',
  templateUrl: './capa.component.html',
  styleUrls: ['./capa.component.scss']
})
export class CapaComponent extends BaseTableSharedComponent {
  isAdmin: boolean = false;
  isOps: boolean = false;
  isSeller: boolean = false;
  auditTypeStr: string = ""
  capaScheduleList: any[] = [];
  filtersList: FilterList[] = [
    { id: "fromDate", type: 'date', label: 'From', defaultValue: this.defaultfromDate, value: this.defaultfromDate },
    { id: "toDate", type: 'date', label: 'To', defaultValue: this.defaultToDate, value: this.defaultToDate },
  ];
  columnDefs: any[] = [
    // {
    //   field: '',
    //   // headerCheckboxSelection: true,
    //   checkboxSelection: true,
    //   showDisabledCheckboxes: true,
    //   width: 50,
    //   minWidth: 50,
    // },
    {
      headerName: "Tracking No.", field: "trackingNumber", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
      width: 175,
      minWidth: 175,
    },
    {
      headerName: "Type", field: "auditTypeName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',

    },
    {
      headerName: "Status", field: "capStatusText", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
    },
    { headerName: "Vendor", field: "vendorName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    { headerName: "Factory", field: "factoryName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    {
      headerName: "Issued Date", field: "issuedTime", sortable: true, unSortIcon: true, sort: 'desc',
      cellRendererFramework: CustomDateTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
      width: 150,
      minWidth: 150,
    },
    {
      headerName: "Followup Date", field: "followupTime", sortable: true, unSortIcon: true,
      cellRendererFramework: CustomDateTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
      width: 200,
      minWidth: 200,
    },
    {
      headerName: "Completed Date", field: "completedTime", sortable: true, unSortIcon: true,
      cellRendererFramework: CustomDateTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
      width: 200,
      minWidth: 200,
    },

    { headerName: "Agency", field: "inspectionAgencyName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 200, },
    {
      headerName: "CAPA ID", field: "uuid", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 200,
    },
    { headerName: "Schedule ID ", field: "auditUid", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 200, },
    {
      headerName: 'Actions',
      pinned: 'right',
      cellRendererFramework: CellActionComponent,
      cellRendererParams: (params) => {
        return { actions: this.prepareActionIconButtons(params.data) }
      }
    }
  ];

  public init(): void {
    this.defaultfromDate.setDate(this.defaultToDate.getDate() - 30);
    this.setDates(this.defaultfromDate, this.defaultToDate);

    this.route.paramMap.subscribe(paramMap => {
      APP_UI_CONFIG.capa.getCapa.paramList.type = paramMap.get('type');
      this.auditTypeStr = paramMap.get('type')
      APP_UI_CONFIG.capa.getCapa.paramList.from = moment(this.defaultfromDate).startOf("day").valueOf();
      APP_UI_CONFIG.capa.getCapa.paramList.to = moment(this.defaultToDate).endOf("day").valueOf();

      this.serviceInvocation()
    });
  }

  public getOnGetDate(event: any): void {
    this.FromToDate.from = event.from;
    this.FromToDate.to = event.to;

    APP_UI_CONFIG.capa.getCapa.paramList.type = this.auditTypeStr;
    APP_UI_CONFIG.capa.getCapa.paramList.from = moment(this.FromToDate.from).startOf("day").valueOf();
    APP_UI_CONFIG.capa.getCapa.paramList.to = moment(this.FromToDate.to).endOf("day").valueOf();

    this.serviceInvocation();
  }

  private serviceInvocation(): void {
    this.subscription.add(
      this.invokeService.serviceInvocation(APP_UI_CONFIG.capa.getCapa, null, null)
        .subscribe({
          next: response => {
            this.capaScheduleList = response
          },
          error: error => {
            this._commonService.handleError(error);
          }
        }))
  }

  dateComparator(date1: any, date2: any) {
    console.log("date1:", date1);
    return date2 - date1;
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];
    iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
    if (this._authService.hasLoggedUserPermitted(ROLE.ADMIN))
      this.isAdmin = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.OPERATIONS))
      this.isOps = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.SELLER))
      this.isSeller = true;
    if (this.isAdmin || this.isOps || this.isSeller) {
      if (row.capStatusText != "CAPA_ACCEPTED") {
        iconsList.push({ type: 'accept', title: "Accept", icon: 'accept', data: row });
      }
      if (!this.isSeller && !this._authService.hasLoggedUserPermitted(ROLE.TEST_USER))
        iconsList.push({ type: 'reAudit', title: "Re-Audit", icon: 'history', data: row });
    }

    return iconsList;
  }

  public getAddEvent(event): void {
    this.selectedData = {};
  }

  public getActions(event): void {
    let actionData: any = event;
    this.selectedData = actionData.data;
    if (actionData.status == 'view') {
      this._commonService.setCAPAItem(this.selectedData);
      this._router.navigate(['/capa/capaDetails/' + actionData.data.auditUid + "/capa/" + actionData.data.uuid], { queryParams: { returnUrl: this._router.url } });
      return
    } else if (actionData.status == 'create') {
      APP_UI_CONFIG.capa.createReinspection.paramList['auditUid'] = actionData.data.auditUid
      this.invokeService.serviceInvocation(APP_UI_CONFIG.capa.createReinspection).subscribe(res => {
        this._commonService.success('Created Re-inspection successfully.');
      });
    } else if (actionData.status == 'accept') {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'Confirm CAPA closure?',
        disableClose: true,
      });
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          APP_UI_CONFIG.capa.updateCapaStatus.paramList.auditUid = actionData.data.auditUid;
          APP_UI_CONFIG.capa.updateCapaStatus.paramList.capUid = actionData.data.uuid;
          APP_UI_CONFIG.capa.updateCapaStatus.paramList.code = 4;
          this.invokeService.serviceInvocation(APP_UI_CONFIG.capa.updateCapaStatus).subscribe(res => {
            if (res.code == 200) {
              this._commonService.success(res.message);
              this.init();
            } else {
              this._commonService.error(res.message);
            }
          }, error => {
            if (error.message) {
              this._commonService.error(error.message);
            } else {
              this._commonService.error('Failed to approve');
            }
          });
        }
      });
    } else if (actionData.status == 'reAudit') {
      APP_UI_CONFIG.capa.reAuditCapaStatus.paramList.auditUid = actionData.data.auditUid;
      APP_UI_CONFIG.capa.reAuditCapaStatus.paramList.capUid = actionData.data.uuid;

      this.invokeService.serviceInvocation(APP_UI_CONFIG.capa.reAuditCapaStatus).subscribe(res => {
        if (res == true) {
          this._router.navigate(['/master/auditPlanning/create/'], { queryParams: { parentAuditId: actionData.data.auditUid } });
        } else {
          this._commonService.warning("Re-Audit is not recommended.");
        }
      }, error => {
        if (error.message) {
          this._commonService.error(error.message);
        }
      });
    }
  }

  public getRefreshEvent(event: any): void {
    this.serviceInvocation();
  }

  public closeViewCpga() {
  }

  public prepareExportData(): any[] {
    let data = [];
    this.capaScheduleList.forEach(eachItem => {
      if (eachItem) {
        data.push(this.getRowData(eachItem))
      }
    });
    return data;
  }

  private getRowData(eachItem): any {
    let rowObject = {}
    this.columnDefs.forEach(item => {
      if (item.headerName != 'Actions') {
        if (item.headerName == 'Issued Date' || item.headerName == 'Followup Date' || item.headerName == 'Completed Date') {
          rowObject[item.headerName] = (item.field && eachItem[item.field] != 0) ? this._commonService.dateFormat(eachItem[item.field]) : '';
        } else
          rowObject[item.headerName] = item.field ? eachItem[item.field] : '';
      }
    });
    return rowObject;
  }

  public onExportEvent(): void {
    let exportData = this.prepareExportData();
    import("xlsx").then(xlsx => {
      const worksheet = xlsx.utils.json_to_sheet(exportData);
      const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
      const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
      this._commonService.saveAsCSVFile(excelBuffer, 'CAPA');
    });
  }

}
