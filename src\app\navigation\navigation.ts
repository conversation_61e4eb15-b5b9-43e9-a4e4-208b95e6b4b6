/* tslint:disable:max-line-length */
import { FuseNavigationItem } from '@fuse/components/navigation';


export const navigation: FuseNavigationItem[] = [
    {
        id: 'home_1',
        title: 'Home',
        type: 'basic',
        icon: 'home',
        link: '/home',
        disabled: false,

        role: ['ADMIN', 'VENDOR', 'AUDITOR', 'SELLER', 'FACTORY', 'SOURCING', 'AGENCY', 'LAB', 'OPERATIONS', 'TEST_USER', 'REVIEWER', 'PRODUCT MANAGER', 'SQM_OPS', 'SME'],
        permission: ['lots', 'test', 'praj', 'givaudan', 'test', 'demo', 'gokaldas', 'amazon', 'twills', 'titan', 'labconnect', 'godrej', 'norlanka', 'quality360', 'mtr', 'manjushree'],
    },
    {
        id: 'Dashboard_1',
        title: 'Dashboard',
        type: 'aside',
        icon: 'dashboard',
        disabled: false,
        permission: ['lots', 'test', 'praj', 'givaudan', 'test', 'demo', 'gokaldas'],
        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
        permissionCode: 'DASHBOARD',
        isParent: true,
        children: [
            {
                id: 'QualityDashboard',
                title: 'Quality Dashboard',
                type: 'basic',
                link: '/dashboard/qualityDashboard',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'lotsDashboard',
                title: 'Dashboard',
                type: 'basic',
                link: '/dashboard/lotsDashboard',
                disabled: false,
                permission: ['lots'],
                role: ['ADMIN'],
            },
            {

                id: 'PowerBIDashboard',
                title: 'PowerBI Dashboard',
                type: 'basic',
                link: '/dashboard/powerBIDashboard',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'quality360'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'MyDashboard',
                title: 'My Dashboard',
                type: 'basic',
                link: '/dashboard/customDashboard',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'quality360'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'dailyProdReport',
                title: 'Daily Production Report',
                type: 'basic',
                link: '/dashboard/dailyProdReport',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'quality360'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'dailyProdStatus',
                title: 'Daily Production Status',
                type: 'basic',
                link: '/dashboard/dailyProdStatus',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'quality360'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'ganttChart',
                title: 'Gantt Chart',
                type: 'basic',
                link: '/dashboard/ganttChart',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'quality360'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'grc-dashboard',
                title: 'Integrated Governance',
                type: 'basic',
                link: '/dashboard/integrated-governance',
                disabled: false,
                permission: ['demo'],
                role: ['ADMIN'],
            },
            {
                id: 'policy_dashboard',
                title: 'Policy Dashboard',
                type: 'basic',
                link: 'admin/policy-dashboard',
                disabled: false,
                permission: ['demo'],
                role: ['ADMIN'],
            },
            {
                id: 'view_policy',
                title: 'Risk Assessment',
                type: 'basic',
                link: 'admin/policy-management/view-policy-excel',
                disabled: false,
                permission: ['demo'],
                role: ['ADMIN'],
            },
        ]
    },
    {
        id: 'vpfTraining_2',
        title: 'Training',
        type: 'basic',
        link: '/vendor/training',
        icon: 'training',
        disabled: false,
        role: ['VENDOR', 'FACTORY'],
        permission: ['amazon'],

    },
    {
        id: 'Inbox',
        title: 'Inbox',
        type: 'basic',
        icon: 'fact_check',
        link: '/inbox/home',
        disabled: false,
        permission: ['amazon'],
        role: ['ADMIN', 'VENDOR', 'AUDITOR', 'SELLER', 'FACTORY', 'SOURCING', 'AGENCY', 'LAB', 'OPERATIONS', 'TEST_USER', 'REVIEWER', 'SME', 'PRODUCT MANAGER', 'PRODUCT_MANAGER'],
    },
    {
        id: 'Inbox',
        title: 'Inbox',
        type: 'basic',
        icon: 'fact_check',
        link: '/inbox/homepage',
        disabled: false,
        permission: ['twills', 'demo'],
        role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER', 'SECURITY', 'AUDITOR'],
    },
    {
        id: 'reports_reports',
        title: 'Reports',
        type: 'aside',
        // icon: 'fact_check',
        isParent: true,
        disabled: false,
        permission: ['twills'],
        role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],
        children: [
            {
                id: 'bulkfabric_inward',
                title: 'Bulk Fabric Inwards',
                type: 'basic',
                link: '/reports/bulk_fabric_inward_report',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
            },
            {
                id: 'FinishedGoods_reports',
                title: 'FG - PO vs Shipped vs GRN',
                type: 'basic',
                link: '/reports/fg_po_vs_shipped_vs_GRN_report',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
            },
            {
                id: 'finished_goods_dispatch',
                title: 'FG - PO vs Shipped vs GRN - Size Wise',
                type: 'basic',
                link: '/reports/fg_po_vs_shipped_vs_grn_sizewise_report',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
            },
            {
                id: 'WIP_reports',
                title: 'Article Status',
                type: 'basic',
                link: '/reports/article_status_report',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
            },
            {
                id: 'wip__details_report',
                title: 'Production Status Qty Wise',
                type: 'basic',
                link: '/reports/production_status_qtywise_report',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
            },
            {
                id: 'leadtime_average',
                title: 'Average Lead Time',
                type: 'basic',
                link: '/reports/avg_lead_time_report',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
            },
            {
                id: 'category_wise',
                title: 'Category Wise Report',
                type: 'collapsable',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
                children: [
                    {
                        id: 'RM_Sampling_Report',
                        title: 'RM Sampling Report',
                        type: 'basic',
                        link: '/reports/RM_Sampling_Report',
                        disabled: false,
                        permission: ['twills'],
                        role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
                    },
                    {
                        id: 'FG_Sampling_Report',
                        title: 'FG Sampling Report',
                        type: 'basic',
                        link: '/reports/FG_Sampling_Report',
                        disabled: false,
                        permission: ['twills'],
                        role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
                    },
                    {
                        id: 'PO_status_report',
                        title: 'PO Status Report',
                        type: 'basic',
                        link: '/reports/PO_status_report',
                        disabled: false,
                        permission: ['twills'],
                        role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
                    },
                ]
            },
            {
                id: 'activity_status_report',
                title: 'Activity Status Report',
                type: 'basic',
                link: '/reports/activity_status_report',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
            },


        ]
    },
    {
        id: 'SAQForm',
        title: 'Self Assessment Questionnaires (SAQ) Form',
        type: 'basic',
        link: 'vendor/saqForm',
        icon: 'saqf',
        disabled: false,
        isParent: false,
        permission: ['amazon'],
        role: ['FACTORY'],
        children: []
    },
    {
        id: 'vpfForm_3',
        title: 'Vendor Profile Form',
        type: 'basic',
        link: '/vendor/vpfForm',
        icon: 'vpf',
        disabled: false,
        isParent: true,
        role: ['VENDOR'],
        permission: ['amazon'],
    },
    {
        id: 'PurchaseOrders ',
        title: 'POs',
        type: 'aside',
        icon: 'fact_check',
        disabled: false,
        isParent: true,
        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills', 'twills', 'titan', 'quality360'],
        role: ['ADMIN', 'VENDOR', 'FACTORY', 'SOURCING', 'PRODUCT MANAGER'],
        children: [
            {
                id: 'SalesOrderData',
                title: 'Sales Order Data',
                type: 'basic',
                link: '/schedules/salesorder',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'amazon', 'quality360'],
                role: ['ADMIN', 'VENDOR', 'FACTORY'],
            },
            {
                id: 'PurchaseOrders',
                title: 'Purchase Orders',
                type: 'basic',
                link: '/schedules/purchaseorder',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'amazon', 'titan', 'quality360'],
                role: ['ADMIN', 'VENDOR', 'FACTORY'],
            },
            {
                id: 'ShipmentData',
                title: 'Shipment Data',
                type: 'basic',
                link: '/schedules/shipmentdata',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'amazon', 'quality360'],
                role: ['ADMIN', 'VENDOR', 'FACTORY'],
            },
            {
                id: 'PO_ShipmentData_1',
                title: 'PO Shipment Data',
                type: 'basic',
                link: '/po/po-shipment',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'VENDOR', 'SOURCING', 'PRODUCT MANAGER'],
            },
            {
                id: 'poDetails_2',
                title: "PO(s)",
                type: 'basic',
                link: '/po/poList',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER', 'VENDOR',],
            },
            {
                id: 'invoiceList',
                title: "Invoice(s)",
                type: 'basic',
                link: '/po/invoice-list',
                disabled: false,
                permission: [''],
                role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER', 'VENDOR',],
            }
        ]
    },
    {
        id: 'auditHead',
        title: 'Audits',
        type: 'aside',
        icon: 'fact_check',
        disabled: false,
        role: ['ADMIN', 'OPERATIONS', 'TEST_USER', 'AGENCY', 'VENDOR', 'AUDITOR', 'REVIEWER', 'SOURCING', 'SME'],
        permission: ['amazon'],
        isParent: true,
        children: [
            {
                id: 'InspectionSchedules_main_1_2_1',
                title: 'Inspection Schedules',
                type: 'basic',
                link: '/schedules/inspectionSchedules/audit',
                disabled: false,
                permission: ['labconnect', 'godrej'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'home-1_1',
                title: 'Audit Summary',
                type: 'basic',
                link: '/master/auditSummary',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'OPERATIONS', 'TEST_USER', 'AGENCY', 'VENDOR', 'AUDITOR', 'REVIEWER', 'SOURCING', 'SME'],
            },
            // {
            //     id: 'AuditfqaSchedules_1_2_1',
            //     title: 'Schedule History',
            //     type: 'basic',
            //     link: '/schedules/inspectionSchedules/fqa',
            //     disabled: false,
            //     permission: ['amazon'],
            //     role: ['ADMIN', 'AGENCY', 'OPERATIONS','TEST_USER', 'AUDITOR'],
            // },
            {
                id: 'auditPlanning_1_4',
                title: 'FQA Planning',
                type: 'basic',
                link: '/master/auditPlanning',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'OPERATIONS', 'TEST_USER', 'AGENCY', 'VENDOR', 'SOURCING', 'SME'],
            },
            {
                id: 'jjjauditReviewProcess_1_5',
                title: 'Review Report',
                type: 'basic',
                link: '/schedules/reviewProcess/fqa',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'REVIEWER', 'AUDITOR', 'AGENCY'],
            },
            {
                id: 'okkauditReviewProcess_1_5_1',
                title: 'Draft Process',
                type: 'basic',
                link: '/schedules/draftProcess/fqa',
                disabled: false,
                permission: ['amazon'],
                role: ['AUDITOR'],
            },
            {
                id: 'capa1_6',
                title: 'CAPA',
                type: 'basic',
                link: '/capa/capa/fqa',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'VENDOR', 'AGENCY', 'OPERATIONS', 'TEST_USER', 'AUDITOR', 'REVIEWER', 'SME'],
            },
            {
                id: 'auditFinalReport_1_7',
                title: 'Final Report',
                type: 'basic',
                link: '/schedules/reviewProcess/fqa',
                disabled: false,
                permission: ['amazon'],
                role: ['OPERATIONS', 'TEST_USER',],
            },
            {
                id: 'VendorRequest1_6',
                title: 'Add New Vendor',
                type: 'basic',
                link: '/master/vendor-request',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'OPERATIONS', 'TEST_USER',],
            }
        ]
    },
    {
        id: 'inspectionHead',
        title: 'Inspection',
        type: 'aside',
        icon: 'schedule',
        disabled: false,
        isParent: true,
        permission: ['amazon'],
        permissionCode: 'INSPECTION',
        role: ['ADMIN', 'AGENCY', 'SELLER', 'VENDOR', 'AUDITOR', 'REVIEWER', 'OPERATIONS', 'TEST_USER', 'SQM_OPS', 'SME', 'FACTORY', 'SOURCING'],
        children: [

            // {
            //     id: 'inspectionhome_2_1',
            //     title: 'Inspection Summary',
            //     type: 'basic',
            //     link: '/inspection/inspection-home',
            //     disabled: false,
            //     permission: ['amazon'],
            //     permissionCode: 'INSPECTION',
            //     role: ['ADMIN', 'SELLER', 'OPERATIONS','TEST_USER', 'SQM_OPS'],
            // },
            // {
            //     id: 'InspectionSchedules_2_2',
            //     title: 'Schedule History',
            //     type: 'basic',
            //     link: '/schedules/inspectionSchedules/inspection',
            //     disabled: false,
            //     permission: ['amazon'],
            //     permissionCode: 'INSPECTION',
            //     role: ['ADMIN', 'AGENCY', 'SELLER', 'AUDITOR', 'OPERATIONS','TEST_USER', 'SQM_OPS'],
            // },
            {
                id: 'InspectionSchedules_2_2',
                title: 'Inspection Summary',
                type: 'basic',
                link: '/master/inspectionSummary',
                disabled: false,
                permission: ['amazon'],
                permissionCode: 'INSPECTION',
                role: ['ADMIN', 'AGENCY', 'SELLER', 'AUDITOR', 'VENDOR', 'OPERATIONS', 'TEST_USER', 'SQM_OPS', 'SME', 'FACTORY', 'SOURCING'],
            },
            {
                id: 'InspectionRequest_2_4',
                title: 'Request Inspection',
                type: 'basic',
                link: '/master/inspectionRequest',
                disabled: false,
                permission: ['amazon'],
                permissionCode: 'INSPECTION',
                role: ['ADMIN', 'AGENCY', 'SELLER', 'VENDOR', 'OPERATIONS', 'TEST_USER', 'SQM_OPS', 'SME', 'FACTORY','SOURCING'],
            },
            {
                id: 'inspectionReviewProcess_2_4',
                title: 'Review Report',
                type: 'basic',
                link: '/schedules/reviewProcess/inspection',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SELLER', 'REVIEWER', 'AUDITOR', 'AGENCY', 'OPERATIONS', 'TEST_USER',],
            },
            {
                id: 'okkauditReviewProcess_1_5_12',
                title: 'Draft Process',
                type: 'basic',
                link: '/schedules/draftProcess/inspection',
                disabled: false,
                permission: ['amazon'],
                role: ['AUDITOR'],
            },
            {
                id: 'capa_2_6',
                title: 'CAPA',
                type: 'basic',
                link: '/capa/capa/inspection',
                disabled: false,
                permission: ['amazon'],
                permissionCode: 'INSPECTION',
                role: ['ADMIN', 'SELLER', 'VENDOR', 'OPERATIONS', 'TEST_USER', 'SQM_OPS', 'FACTORY', 'SME'],
            },

        ]
    },
    {
        id: 'administration_1_2',
        title: 'Administration',
        type: 'aside',
        icon: 'fact_check',
        disabled: false,
        isParent: true,
        role: ['ADMIN', 'APPROVER', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
        permission: [''],
        children: [
            {
                id: 'InspectionTemplates',
                title: 'Inspection Templates',
                type: 'basic',
                link: '/inspection/inspectionTemplate',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'MasterData',
                title: 'Master data',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                children: [
                    {
                        id: 'Auditor',
                        title: 'Auditor',
                        type: 'basic',
                        link: '/master/auditor',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'AuditorIncharge',
                        title: 'Auditor Incharge',
                        type: 'basic',
                        link: '/master/auditorincharge',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Customers',
                        title: 'Customers',
                        type: 'basic',
                        link: '/master/customers',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Brands',
                        title: 'Brands',
                        type: 'basic',
                        link: '/master/brands',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Category',
                        title: 'Categories',
                        type: 'basic',
                        link: '/master/category',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'ProductType',
                        title: 'Product Type',
                        type: 'basic',
                        link: '/master/producttypes',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'TA',
                        title: 'T&A',
                        type: 'basic',
                        link: '/activity/activityMaster',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Auditor-FactoryAssignment',
                        title: 'Auditor-Factory Assignment',
                        type: 'basic',
                        link: '/master/auditorfactory',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Vendor',
                        title: 'Vendor',
                        type: 'basic',
                        link: '/master/vendor',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Vendor-Request',
                        title: 'Vendor Request',
                        type: 'basic',
                        link: '/master/vendor-request',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'audit-planning',
                        title: 'Audit Planning',
                        type: 'basic',
                        link: '/master/auditPlanning',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Inspection-Request',
                        title: 'Inspection Request',
                        type: 'basic',
                        link: '/master/inspectionRequest',
                        disabled: false,
                        permission: ['amazon', 'test'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Factory',
                        title: 'Factory',
                        type: 'basic',
                        link: '/master/factory',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'agency',
                        title: 'Inspection Agency',
                        type: 'basic',
                        link: '/master/inspectionAgency',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'testingLab',
                        title: 'Testing Lab',
                        type: 'basic',
                        link: '/master/testingLab',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'fabric',
                        title: 'Fabrics',
                        type: 'basic',
                        link: '/master/raw-materials/',
                        disabled: false,
                        permission: ['twills', 'norlanka', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN'],
                    },
                    {
                        id: 'Stylecodes',
                        title: 'Stylecodes',
                        type: 'basic',
                        link: '/master/stylecodes',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'CustomDashboards',
                        title: 'Custom Dashboards',
                        type: 'basic',
                        link: '/master/customdashboard',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },

                ]
            },
            {
                id: 'Administration',
                title: 'Admin',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                children: [
                    {
                        id: 'Organizations',
                        title: 'Organizations',
                        type: 'basic',
                        link: '/admin/organizations',
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                        disabled: false,
                    },
                    {
                        id: 'OrganizationChart',
                        title: 'Organization Chart',
                        type: 'basic',
                        link: '/admin/organizationchart',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'TaskStateManagement',
                        title: 'Task State Management',
                        type: 'basic',
                        link: '/admin/state',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Auditcategories',
                        title: 'Audit categories',
                        type: 'basic',
                        link: '/admin/auditcategories',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Audittypes',
                        title: 'Audit types',
                        type: 'basic',
                        link: '/admin/audittypes',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Products',
                        title: 'Products',
                        type: 'basic',
                        link: '/admin/products',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'AQL',
                        title: 'AQL',
                        type: 'basic',
                        link: '/admin/aql',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'ProductType-AQL',
                        title: 'Product Type - AQL',
                        type: 'basic',
                        link: '/admin/producttypeaql',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },

                    {
                        id: 'Attributes',
                        title: 'Attributes',
                        type: 'basic',
                        link: '/admin/attributes',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Penalties',
                        title: 'Penalties',
                        type: 'basic',
                        link: '/admin/penatlies',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'OptionTypes',
                        title: 'Option Types',
                        type: 'basic',
                        link: '/admin/option-types',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Checkpoints',
                        title: 'Checkpoints',
                        type: 'basic',
                        link: '/admin/checkpoints',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'CheckpointCategory',
                        title: 'Checkpoint Categories',
                        type: 'basic',
                        link: '/admin/checkpointcategory',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Defecttypes',
                        title: 'Defect types',
                        type: 'basic',
                        link: '/admin/defecttypes',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Defecttypecategory',
                        title: 'Defect type Categories',
                        type: 'basic',
                        link: '/admin/defecttypecategory',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'Configurations',
                        title: 'Configurations',
                        type: 'basic',
                        link: '/admin/configurations',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'categoryGrouping',
                        title: 'Category Group',
                        type: 'basic',
                        link: '/designCode/categoryGroup',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'demo', 'gokaldas', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'articleType',
                        title: 'Article Type',
                        type: 'basic',
                        link: '/articleType',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'demo', 'gokaldas', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'rawMaterialType',
                        title: 'Raw Material Type',
                        type: 'basic',
                        link: 'admin/rawMaterialType',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'demo', 'gokaldas', 'twills'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    }
                ]
            },
        ]
    },
    {
        id: 'Inspections_1',
        title: 'Audits',
        type: 'aside',
        icon: 'schedule',
        disabled: false,
        isParent: true,
        permission: ['test', 'norlanka', 'quality360', 'praj', 'givaudan', 'test', 'lots', 'demo', 'gokaldas', 'crompton', 'titan', 'timexgarments', 'labconnect', 'godrej', 'mtr', 'manjushree'],
        permissionCode: 'INSPECTION',
        role: ['ADMIN', 'APPROVER', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
        children: [
            {
                id: 'Dashboard_1_1',
                title: 'Dashboard',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'praj', 'givaudan', 'test', 'demo', 'gokaldas', 'crompton'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'QualityDashboard_1_1_1',
                        title: 'Quality Dashboard',
                        type: 'basic',
                        link: '/dashboard/qualityDashboard',
                        disabled: false,
                        permission: ['test', 'praj', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'CustomDashboard_1_1_2',
                        title: 'Custom Dashboard',
                        type: 'basic',
                        link: '/dashboard/customDashboard',
                        disabled: false,
                        permission: ['test', 'praj', 'givaudan', 'test', 'demo', 'gokaldas'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    }
                ]
            },
            {
                id: 'Schedules_1_2',
                title: 'Schedules',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'APPROVER', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'InspectionSchedules_1_2',
                        title: 'Inspection Schedules',
                        type: 'basic',
                        link: '/schedules/inspectionSchedules/inspection',
                        disabled: false,
                        permission: ['titan', 'labconnect', 'godrej', 'mtr', 'manjushree','demo'],
                        role: ['ADMIN', 'AUDITOR', 'AUDITORINCHARGE'],
                    },
                    {
                        id: 'InspectionSchedules_1_2_1',
                        title: 'Audit Schedules',
                        type: 'basic',
                        link: '/schedules/inspectionSchedules/audit',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'givaudan', 'test', 'lots', 'demo', 'gokaldas', 'crompton'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'labSchedules',
                        title: 'Lab Schedules',
                        type: 'basic',
                        link: '/schedules/inspectionSchedules/lab',
                        disabled: false,
                        permission: ['test', 'praj', 'givaudan', 'test','demo'],
                        role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'fqaSchedules',
                        title: 'FQA Schedules',
                        type: 'basic',
                        link: '/schedules/inspectionSchedules/fqa',
                        disabled: false,
                        permission: ['test', 'praj', 'givaudan', 'test','demo'],
                        role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'ExceptionSchedules',
                        title: 'Exception Schedules',
                        type: 'basic',
                        link: '/schedules/exceptionSchedules',
                        disabled: false,
                        permission: ['test', 'praj', 'givaudan', 'norlanka', 'test', 'quality360',],
                        role: ['ADMIN', 'APPROVER', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'calender',
                        title: 'Calender',
                        type: 'basic',
                        link: '/schedules/calender',
                        disabled: false,
                        permission: ['test', 'praj', 'givaudan', 'test'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'reviewProcess',
                        title: 'Review Process',
                        type: 'basic',
                        link: '/schedules/reviewProcess',
                        disabled: false,
                        permission: ['test', 'praj', 'givaudan', 'test'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    }
                ]
            },
            {
                id: 'capa',
                title: 'CAPA',
                type: 'basic',
                link: '/capa/capa',
                disabled: false,
                permission: ['test', 'praj', 'givaudan', 'test'],
                role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            },
            {
                id: 'FabricInspection',
                title: 'Fabric Inspection',
                type: 'basic',
                link: '/inspection/fabricInspection',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'timexgarments'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'PackageInspection',
                title: 'Package Inspection',
                type: 'basic',
                link: '/inspection/packageInspection',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'timexgarments'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'DocumentsTechpacks',
                title: 'Documents & Techpacks',
                type: 'basic',
                link: '/inspection/techpacks',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'timexgarments'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            // {
            //     id: 'InspectionTemplates',
            //     title: 'Inspection Templates',
            //     type: 'basic',
            //     link: '/inspection/inspectionTemplate',
            //     disabled: false,
            //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'timexgarments'],
            //     role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            // },
            // {
            //     id: 'MasterData',
            //     title: 'Master data',
            //     type: 'collapsable',
            //     disabled: false,
            //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills', 'timexgarments'],
            //     role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //     children: [
            //         {
            //             id: 'Auditor',
            //             title: 'Auditor',
            //             type: 'basic',
            //             link: '/master/auditor',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'AuditorIncharge',
            //             title: 'Auditor Incharge',
            //             type: 'basic',
            //             link: '/master/auditorincharge',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Customers',
            //             title: 'Customers',
            //             type: 'basic',
            //             link: '/master/customers',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Brands',
            //             title: 'Brands',
            //             type: 'basic',
            //             link: '/master/brands',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Category',
            //             title: 'Categories',
            //             type: 'basic',
            //             link: '/master/category',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills', 'twills', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'ProductType',
            //             title: 'Product Type',
            //             type: 'basic',
            //             link: '/master/producttypes',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'TA',
            //             title: 'T&A',
            //             type: 'basic',
            //             link: '/activity/activityMaster',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas', 'twills', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Auditor-FactoryAssignment',
            //             title: 'Auditor-Factory Assignment',
            //             type: 'basic',
            //             link: '/master/auditorfactory',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Vendor',
            //             title: 'Vendor',
            //             type: 'basic',
            //             link: '/master/vendor',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Vendor-Request',
            //             title: 'Vendor Request',
            //             type: 'basic',
            //             link: '/master/vendor-request',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'audit-planning',
            //             title: 'Audit Planning',
            //             type: 'basic',
            //             link: '/master/auditPlanning',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Inspection-Request',
            //             title: 'Inspection Request',
            //             type: 'basic',
            //             link: '/master/inspectionRequest',
            //             disabled: false,
            //             permission: ['amazon', 'test'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Factory',
            //             title: 'Factory',
            //             type: 'basic',
            //             link: '/master/factory',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'agency',
            //             title: 'Inspection Agency',
            //             type: 'basic',
            //             link: '/master/inspectionAgency',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'testingLab',
            //             title: 'Testing Lab',
            //             type: 'basic',
            //             link: '/master/testingLab',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'fabric',
            //             title: 'Fabrics',
            //             type: 'basic',
            //             link: '/master/raw-materials/',
            //             disabled: false,
            //             permission: ['twills', 'norlanka', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'twills'],
            //             role: ['ADMIN', 'TENANT_ADMIN'],
            //         },
            //         {
            //             id: 'Stylecodes',
            //             title: 'Stylecodes',
            //             type: 'basic',
            //             link: '/master/stylecodes',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas', 'twills'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'CustomDashboards',
            //             title: 'Custom Dashboards',
            //             type: 'basic',
            //             link: '/master/customdashboard',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'labconnect', 'godrej', 'demo', 'gokaldas'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },

            //     ]
            // },
            // {
            //     id: 'Administration',
            //     title: 'Admin',
            //     type: 'collapsable',
            //     disabled: false,
            //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills', 'timexgarments'],
            //     role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //     children: [
            //         {
            //             id: 'Organizations',
            //             title: 'Organizations',
            //             type: 'basic',
            //             link: '/admin/organizations',
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //             disabled: false,
            //         },
            //         {
            //             id: 'OrganizationChart',
            //             title: 'Organization Chart',
            //             type: 'basic',
            //             link: '/admin/organizationchart',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'TaskStateManagement',
            //             title: 'Task State Management',
            //             type: 'basic',
            //             link: '/admin/state',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Auditcategories',
            //             title: 'Audit categories',
            //             type: 'basic',
            //             link: '/admin/auditcategories',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Audittypes',
            //             title: 'Audit types',
            //             type: 'basic',
            //             link: '/admin/audittypes',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Products',
            //             title: 'Products',
            //             type: 'basic',
            //             link: '/admin/products',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'AQL',
            //             title: 'AQL',
            //             type: 'basic',
            //             link: '/admin/aql',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'ProductType-AQL',
            //             title: 'Product Type - AQL',
            //             type: 'basic',
            //             link: '/admin/producttypeaql',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },

            //         {
            //             id: 'Attributes',
            //             title: 'Attributes',
            //             type: 'basic',
            //             link: '/admin/attributes',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Penalties',
            //             title: 'Penalties',
            //             type: 'basic',
            //             link: '/admin/penatlies',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'OptionTypes',
            //             title: 'Option Types',
            //             type: 'basic',
            //             link: '/admin/optiontypes',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'timexgarments'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Checkpoints',
            //             title: 'Checkpoints',
            //             type: 'basic',
            //             link: '/admin/checkpoints',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'CheckpointCategory',
            //             title: 'Checkpoint Categories',
            //             type: 'basic',
            //             link: '/admin/checkpointcategory',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Defecttypes',
            //             title: 'Defect types',
            //             type: 'basic',
            //             link: '/admin/defecttypes',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Defecttypecategory',
            //             title: 'Defect type Categories',
            //             type: 'basic',
            //             link: '/admin/defecttypecategory',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'Configurations',
            //             title: 'Configurations',
            //             type: 'basic',
            //             link: '/admin/configurations',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'categoryGrouping',
            //             title: 'Category Group',
            //             type: 'basic',
            //             link: '/designCode/categoryGroup',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'twills'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'articleType',
            //             title: 'Article Type',
            //             type: 'basic',
            //             link: '/articleType',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'twills'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         },
            //         {
            //             id: 'rawMaterialType',
            //             title: 'Raw Material Type',
            //             type: 'basic',
            //             link: 'admin/rawMaterialType',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'twills'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            //         }
            //     ]
            // },
        ]
    },
    {
        id: 'sourcing_1',
        title: 'Product Development',
        type: 'aside',
        icon: 'integration_instructions',
        isParent: true,
        disabled: false,
        permission: ['test', 'praj', 'givaudan', 'test'],
        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER', 'PRODUCT MANAGER'],
        permissionCode: 'ACTIVITY',
        children: [
            {
                id: 'Dashboard',
                title: 'Dashboard',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                children: [
                    // {
                    //     id: 'ProductStatus',
                    //     title: 'Product Status',
                    //     type: 'basic',
                    //     link: '/activity/tnaStatusMain',
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // }, {
                    //     id: 'ProductStatus',
                    //     title: 'Product By Vendor',
                    //     type: 'basic',
                    //     link: '/activity/productVendorListComponent',
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // },
                    {
                        id: 'Dashboard',
                        title: 'Product Status Summary',
                        type: 'basic',
                        link: '/activity/dashboard',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    },
                    // {
                    //     id: 'Dashboard',
                    //     title: 'Product Status Summary - Qty Vs Options',
                    //     type: 'basic',
                    //     link: '/activity/dashboard1',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // }
                    // ,
                    // {
                    //     id: 'Dashboard2',
                    //     title: 'Product Dispatch Status',
                    //     type: 'basic',
                    //     link: '/activity/dashboard2',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // },
                    // {
                    //     id: 'Dashboard',
                    //     title: 'Product T&A Status',
                    //     type: 'basic',
                    //     link: '/activity/dashboard3',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // },
                    // // {
                    // //     id: 'Dashboard',
                    // //     title: 'Dashboard4',
                    // //     type: 'basic',
                    // //     link: '/activity/dashboard4',
                    // //     disabled: false,
                    // //     permission: ['all'],
                    // //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // // },
                    // {
                    //     id: 'Dashboard',
                    //     title: 'Product - Design Status',
                    //     type: 'basic',
                    //     link: '/activity/dashboard5',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // },
                    // {
                    //     id: 'Dashboard',
                    //     title: 'Product - PO Summary',
                    //     type: 'basic',
                    //     link: '/activity/dashboard6',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // },
                    // {
                    //     id: 'Dashboard',
                    //     title: 'Product Inspection Summary',
                    //     type: 'basic',
                    //     link: '/activity/dashboard7',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // },
                    {
                        id: 'Dashboard',
                        title: 'Purchase Orders Summary',
                        type: 'basic',
                        link: '/activity/dashboard8',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    },
                    // {
                    //     id: 'Dashboard',
                    //     title: 'Supplier Quality',
                    //     type: 'basic',
                    //     link: '/activity/dashboard9',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // },
                    // {
                    //     id: 'Dashboard',
                    //     title: 'Procurement',
                    //     type: 'basic',
                    //     link: '/activity/dashboard10',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // },
                    {
                        id: 'Dashboard',
                        title: 'T&A - Purchase Orders',
                        type: 'basic',
                        link: '/activity/dashboard11',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    },
                    // {
                    //     id: 'Dashboard',
                    //     title: 'T&A - Supplier',
                    //     type: 'basic',
                    //     link: '/activity/dashboard12',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // }
                ]
            },
            {
                id: 'Calendar',
                title: 'Calendar',
                type: 'basic',
                link: '/activity/activityCalendar',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            },
            {
                id: 'roadmap_1_3',
                title: 'Roadmap',
                type: 'basic',
                link: '/sourcing/roadmap/road-map-list',
                disabled: false,
                permission: ['amazon',],
                role: ['ADMIN', 'PRODUCT MANAGER'],
            },
            {
                id: 'CollectionRequests_1_4',
                title: 'Collection Requests',
                type: 'basic',
                link: '/sourcing/request/request-list',
                disabled: false,
                permission: [''],
                role: ['ADMIN', 'SELLER'],
            },
            {
                id: 'QuotationRequests_1_5',
                title: 'Quotation Requests',
                type: 'basic',
                link: '/sourcing/quotation/quotationRequest',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SELLER'],
            },
            {
                id: 'Quotation Response_1_6',
                title: 'Received Quotation Request(s)',
                type: 'basic',
                link: '/sourcing/quotation/quotationResponse',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'VENDOR',],
            },
            {
                id: 'Quotation bidding_1_6',
                title: 'Received Bidding Request(s)',
                type: 'basic',
                link: '/sourcing/quotation/quotationResponse/BIDDING',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'LAB'],
            },

            // {
            //     id: 'Quotations',
            //     title: 'Quotations',
            //     type: 'basic',
            //     link: '/activity/quotationRequest',
            //     disabled: false,
            //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test'],
            //     role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            // },
            // {
            //     id: 'QuotationResponses',
            //     title: 'Quotations',
            //     type: 'basic',
            //     link: '/activity/quotationResponse',
            //     disabled: false,
            //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test'],
            //     role: ['VENDOR', 'FACTORY'],
            // }
            ,//{
            //     id: 'Products',
            //     title: 'Products List',
            //     type: 'basic',
            //     link: '/activity/productList',
            //     disabled: false,
            //     permission: ['all'],
            //     role: ['ADMIN', 'TENANT_ADMIN', 'VENDOR', 'FACTORY', 'SELLER'],
            // },                          
            //},
            // {
            //     id: 'TNAStatusMain',
            //     title: 'T&A Status',
            //     type: 'basic',
            //     link: '/activity/tnaStatusMain',
            //     permission: ['all'],
            //     role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            // },
            {
                id: 'productList',
                title: 'Product List',
                type: 'basic',
                link: '/activity/productList',
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            },
            // {
            //     id: 'Products',
            //     title: 'Products',
            //     type: 'collapsable',
            //     disabled: false,
            //     permission: ['all'],
            //     role: ['ADMIN', 'TENANT_ADMIN', 'VENDOR', 'FACTORY', 'SOURCING', 'SELLER'],
            //     children: [
            //         // {
            //         //     id: 'Products',
            //         //     title: 'Products',
            //         //     type: 'basic',
            //         //     link: '/activity/designcodes',
            //         //     permission: ['all'],
            //         //     role: ['ADMIN', 'TENANT_ADMIN', 'VENDOR', 'FACTORY', 'SOURCING','SELLER'],
            //         // },
            //         {
            //             id: 'productList',
            //             title: 'Product List',
            //             type: 'basic',
            //             link: '/activity/productList',
            //             permission: ['all'],
            //             role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            //         },
            //         {
            //             id: 'productVendorListComponent',
            //             title: 'Product Vendor List',
            //             type: 'basic',
            //             link: '/activity/productVendorListComponent',
            //             permission: ['all'],
            //             role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            //         },
            //         {
            //             id: 'TNAStatusMain',
            //             title: 'TNA Status Main',
            //             type: 'basic',
            //             link: '/activity/tnaStatusMain',
            //             permission: ['all'],
            //             role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            //         }
            //     ]
            // },
            // {
            //     id: 'designcodeActivity',
            //     title: 'Product Activity',
            //     type: 'basic',
            //     link: '/activity/designcodeActivity',
            //     disabled: false,
            //     permission: ['all'],
            //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            // },                                 
            {
                id: 'quotations',
                title: 'Quotations Requests',
                type: 'basic',
                link: '/sourcing/quotation/quotationRequest',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'amazon'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'Request',
                title: 'Request',
                type: 'basic',
                link: '/sourcing/request/request-list',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'amazon'],
                role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            },
            {
                id: 'RoadMap',
                title: 'Roadmap',
                type: 'basic',
                link: '/sourcing/roadmap/road-map-list',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            },
        ],

    },
    {
        id: 'CADDesigns',
        title: 'CAD Designs',
        type: 'aside',
        icon: 'architecture',
        disabled: false,
        isParent: true,
        permission: ['test', 'praj', 'givaudan', 'test'],
        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
        permissionCode: 'CAD',
        children: [
            // {
            //     id: 'Dashboard',
            //     title: 'Dashboard',
            //     type: 'basic',
            //     link: '/cad/dashboard',
            //     disabled: false,
            //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test'],
            //     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            // },
            {
                id: 'CADDocuments',
                title: 'CAD Documents',
                type: 'basic',
                link: '/cad/cadDocuments',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            // {
            //     id: 'MasterData',
            //     title: 'Master Data',
            //     type: 'collapsable',
            //     disabled: false,
            //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test'],
            //     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            //     children: [

            //     ]
            // },
            {
                id: 'Admin',
                title: 'Admin',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'CADDataExtractors',
                        title: 'CAD Data Extractors',
                        type: 'basic',
                        link: '/admin/appBundles',
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    }
                ]
            },

        ]
    },
    {
        id: 'SupplierCertificates',
        title: 'Supplier Certificates',
        type: 'aside',
        icon: 'task',
        disabled: false,
        isParent: true,
        permission: ['test', 'praj', 'givaudan', 'test'],
        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
        permissionCode: 'SUPPLIER',
        children: [
            // {
            //     id: 'Dashboard',
            //     title: 'Dashboard',
            //     type: 'basic',
            //     link: '/dashboard/supplierCertDashboard',
            //     disabled: false,
            //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test'],
            //     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            // },
            {
                id: 'Suppliers',
                title: 'Suppliers',
                type: 'basic',
                link: '/mtc/suppliers',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'ScannedDocuments',
                title: 'Scanned Documents',
                type: 'basic',
                link: '/mtc/documents',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'Projects',
                title: 'Projects',
                type: 'basic',
                link: '/mtc/projects',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'Certificates',
                title: 'Certificates',
                type: 'basic',
                link: '/mtc/mtc',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'Materials',
                title: 'Materials',
                type: 'basic',
                link: '/mtc/materials',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            },
            {
                id: 'MasterData',
                title: 'Master Data',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'ReferenceData',
                        title: 'Reference Data',
                        type: 'basic',
                        link: '/admin/refData',
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                ]
            },
            {
                id: 'Admin',
                title: 'Admin',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'Certificate Configuration',
                        title: 'Certificate Configuration',
                        type: 'basic',
                        link: '/admin/matTestCertConfig',
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    }
                ]
            }
        ]
    },
    {
        id: 'VisualInspections',
        title: 'Visual Inspections',
        type: 'aside',
        icon: 'visibility',
        disabled: false,
        isParent: true,
        permission: ['licious', 'test'],
        role: ['ADMIN'],
        permissionCode: 'VISUAL',
        children: [
            {
                id: 'dashboard',
                title: 'Dashboard',
                type: 'basic',
                link: '/cad/dashboard',
                disabled: false,
                permission: ['test', 'test'],
                role: ['ADMIN'],
            },
            {
                id: 'notificationHistory',
                title: 'Notification History',
                type: 'basic',
                link: '/visual/notificationHistory',
                disabled: false,
                permission: ['test', 'test'],
                role: ['ADMIN'],
            },
            {
                id: 'ProductInspections',
                title: 'Product Inspections',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'test'],
                role: ['ADMIN'],
                children: [
                    {
                        id: 'OilLeakageInspections',
                        title: 'Oil Leakage Inspections',
                        type: 'basic',
                        link: '/admin/appBundles',
                        disabled: false,
                        permission: ['test', 'test'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'GasCuttingInspections',
                        title: 'GasCuttingInspections',
                        type: 'basic',
                        link: '/admin/appBundles',
                        disabled: false,
                        permission: ['test', 'test'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'WeldingInspections',
                        title: 'WeldingInspections',
                        type: 'basic',
                        link: '/admin/appBundles',
                        disabled: false,
                        permission: ['test', 'test'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'SurfaceInspections',
                        title: 'Surface Inspections',
                        type: 'basic',
                        link: '/visual/surface',
                        disabled: false,
                        permission: ['test', 'test'],
                        role: ['ADMIN'],
                    }
                ]
            },
            {
                id: 'Package/Label Inspections',
                title: 'Label Check',
                type: 'basic',
                link: '/visual/package',
                disabled: false,
                permission: ['licious', 'test'],
                role: ['ADMIN'],
            },
            {
                id: 'MasterData',
                title: 'Master Data',
                type: 'collapsable',
                disabled: false,
                permission: ['all'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                children: [
                    {
                        id: 'facilities',
                        title: 'Factory/Sites',
                        type: 'basic',
                        link: '/admin/facility',
                        permission: ['all'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'facilityEdges',
                        title: 'Edge Devices',
                        type: 'basic',
                        link: '/admin/facilityEdges',
                        permission: ['all'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'facilityCameras',
                        title: 'Cameras',
                        type: 'basic',
                        link: '/admin/facilityCameras',
                        permission: ['all'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'facilityNotifiers',
                        title: 'Notifiers',
                        type: 'basic',
                        link: '/admin/facilityNotifiers',
                        permission: ['all'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                    {
                        id: 'ReferenceData',
                        title: 'SKU',
                        type: 'basic',
                        link: '/admin/visualRefData',
                        permission: ['all'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                    },
                ]
            },
            {
                id: 'Admin',
                title: 'Admin',
                type: 'collapsable',
                disabled: false,
                permission: ['all'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'Product',
                        title: 'Product',
                        type: 'basic',
                        link: '/admin/appBundles',
                        permission: ['all'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'facilityCameraInspectionConfig',
                        title: 'Camera Inspection Configuration',
                        type: 'basic',
                        link: '/master/facilityCameraInspectionConfig',
                        permission: ['all'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    }
                ]
            },

        ]
    },
    // {
    //     id: 'EquipmentIntegration',
    //     title: 'Equipment Integration',
    //     type: 'aside',
    //     icon: 'integration_instructions',
    //     isParent: true,
    //     disabled: false,
    //     permission: ['all_engg'],
    //     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //     permissionCode: 'EQUIPMENT',
    //     children: [
    //         {
    //             id: 'Dashboard',
    //             title: 'Dashboard',
    //             type: 'basic',
    //             link: '/dashboard/dashboard',
    //             disabled: false,
    //             permission: ['all'],
    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //         },
    //         {
    //             id: 'Equipment/Machinary',
    //             title: 'Equipment/Machinary',
    //             type: 'basic',
    //             link: '/dashboard/equipmentDashboard',
    //             disabled: false,
    //             permission: ['all'],
    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //         },
    //         {
    //             id: 'MasterData',
    //             title: 'Master Data',
    //             type: 'collapsable',
    //             disabled: false,
    //             permission: ['all'],
    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //             children: [

    //             ]
    //         },
    //         {
    //             id: 'Admin',
    //             title: 'Admin',
    //             type: 'collapsable',
    //             disabled: false,
    //             permission: ['all'],
    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //             children: [
    //                 {
    //                     id: 'Equipment/Gauges/LCD/Machinary',
    //                     title: 'Equipment/Gauges/LCD/Machinary',
    //                     type: 'basic',
    //                     link: '/admin/equipment',
    //                     permission: ['all'],
    //                     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //                 }
    //             ]
    //         },
    //     ],

    // },
    // {
    //     id: 'CCTVAnalytics',
    //     title: 'CCTV Analytics',
    //     type: 'aside',
    //     icon: 'photo_camera',
    //     disabled: false,
    //     isParent: true,
    //     permission: ['all_engg'],
    //     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //     permissionCode: 'CCTV',
    //     children: [
    //         {
    //             id: 'Dashboard',
    //             title: 'Dashboard',
    //             type: 'collapsable',
    //             disabled: false,
    //             permission: ['all'],
    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //             children: [
    //                 {
    //                     id: 'ZoneDashboard',
    //                     title: 'Zone Dashboard',
    //                     type: 'basic',
    //                     link: '/videoAnalytics/categoryWiseCustomerCounter',
    //                     disabled: false,
    //                     permission: ['all'],
    //                     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //                 },
    //                 {
    //                     id: 'Custom-wise-Dashboard',
    //                     title: 'Custom-wise Dashboard',
    //                     type: 'basic',
    //                     link: '/dashboard/cctvCategoryWiseDashboard',
    //                     disabled: false,
    //                     permission: ['all'],
    //                     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //                 }
    //             ]
    //         },
    //         {
    //             id: 'Tools',
    //             title: 'Tools',
    //             type: 'collapsable',
    //             disabled: false,
    //             permission: ['all'],
    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //             children: [
    //                 {
    //                     id: 'LicensePlateVerifier',
    //                     title: 'License Plate Verifier',
    //                     type: 'basic',
    //                     link: '/videoAnalytics/licensePlate',
    //                     disabled: false,
    //                     permission: ['all'],
    //                     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //                 }
    //             ]
    //         },
    //         {
    //             id: 'MasterData',
    //             title: 'Master Data',
    //             type: 'collapsable',
    //             disabled: false,
    //             permission: ['all'],
    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //             children: [
    //                 {
    //                     id: 'Test',
    //                     title: 'Test',
    //                     type: 'basic',
    //                     link: '/dashboard/customDashboard',
    //                     permission: ['all'],
    //                     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //                 },
    //             ]
    //         },
    //         {
    //             id: 'Admin',
    //             title: 'Admin',
    //             type: 'collapsable',
    //             disabled: false,
    //             permission: ['all'],
    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //             children: [
    //                 {
    //                     id: 'Facilities',
    //                     title: 'Facilities',
    //                     type: 'basic',
    //                     link: '/admin/facility',
    //                     permission: ['all'],
    //                     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //                 },
    //                 {
    //                     id: 'CCTV Configuration',
    //                     title: 'CCTV Configuration',
    //                     type: 'basic',
    //                     link: '/admin/cams',
    //                     permission: ['all'],
    //                     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //                 }
    //             ]
    //         }
    //     ]
    // }, 
    {
        id: 'DocumentManagement ',
        title: 'Documents',
        type: 'aside',
        icon: 'picture_as_pdf',
        disabled: false,
        isParent: true,
        permission: ['test', 'praj', 'givaudan'],
        role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
        children: [
            {
                id: 'Documents',
                title: 'Documents',
                type: 'basic',
                link: '/documentManagement/document',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            },
            {
                id: 'DocumentTree',
                title: 'Document Tree',
                type: 'basic',
                link: '/documentManagement/documentTree',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            },
            {
                id: 'docUploadArticle',
                title: 'Upload documents',
                type: 'collapsable',
                link: '',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    // {
                    //     id: 'Brand',
                    //     title: 'Brand',
                    //     type: 'basic',
                    //     link: '/schedules/docUploadArticle',
                    //     disabled: false,
                    //     permission: ['notlot'],
                    //     role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    // },
                    {
                        id: 'Sourcing',
                        title: 'Sourcing',
                        type: 'basic',
                        link: '',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    },
                    {
                        id: 'Supplier',
                        title: 'Supplier',
                        type: 'basic',
                        link: '/schedules/poApproval',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    },
                    {
                        id: 'Quality',
                        title: 'Quality',
                        type: 'basic',
                        link: '',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    }

                ]
            },
            {
                id: 'fabricView',
                title: 'Assign Documents',
                type: 'collapsable',
                link: '',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'Design',
                        title: 'Design',
                        type: 'basic',
                        link: '/schedules/fabricView',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
                    },
                    {
                        id: 'Sourcing',
                        title: 'Sourcing',
                        type: 'basic',
                        link: '/schedules/articleDocuments',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                ]
            },

        ]
    },
    {
        id: 'npd',
        title: 'NPD',
        type: 'aside',
        icon: 'picture_as_pdf',
        disabled: false,
        isParent: true,
        permission: ['test', 'praj', 'givaudan', 'test', 'twills'],
        role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER', 'VENDOR', 'PRODUCT MANAGER'],
        children: [
            {
                id: 'indentCreation',
                title: 'Indent Requests',
                type: 'basic',
                disabled: false,
                link: '/npd/sampling/indent-requests',
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER', 'PRODUCT MANAGER'],
                children: [
                    {
                        id: 'rawMaterials',
                        title: 'Raw Materials Indent Master',
                        type: 'basic',
                        disabled: false,
                        link: '/npd/sampling/indent-requests/RM',
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER', 'PRODUCT MANAGER'],
                        // children: [
                        //     {
                        //         id: 'createIndent1_1',
                        //         title: 'Create Indent',
                        //         type: 'basic',
                        //         link: 'npd/raw-materials',
                        //         disabled: false,
                        //         permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        //         role: ['ADMIN', 'PRODUCT MANAGER'],
                        //     },
                        //     {
                        //         id: 'viewRawIndent_1',
                        //         title: 'View Indent',
                        //         type: 'basic',
                        //         link: '/npd/sampling/indent-requests',
                        //         disabled: false,
                        //         permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        //         role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER', 'PRODUCT MANAGER'],
                        //     },
                        // ]
                    },
                    {
                        id: 'fg',
                        title: 'FG (Finish Good) Indent Master',
                        type: 'basic',
                        disabled: false,
                        link: '/npd/sampling/indent-requests/FG',
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER', 'PRODUCT MANAGER'],
                        // children: [
                        //     {
                        //         id: 'createIndent2_1',
                        //         title: 'Create Indent',
                        //         type: 'basic',
                        //         link: 'npd/finished-goods',
                        //         disabled: false,
                        //         permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        //         role: ['ADMIN', 'PRODUCT MANAGER'],
                        //     },
                        //     {
                        //         id: 'viewRawIndent_2',
                        //         title: 'View Indent',
                        //         type: 'basic',
                        //         link: '/npd/sampling/indent-requests',
                        //         disabled: false,
                        //         permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        //         role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER', 'PRODUCT MANAGER'],
                        //     },
                        // ]
                    },
                    // {
                    //     id: 'sampleGarment',
                    //     title: 'Sample Garment',
                    //     type: 'collapsable',
                    //     disabled: false,
                    //     permission: ['all'],
                    //     role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    //     children: [
                    //         {
                    //             id: 'roadShow',
                    //             title: 'RoadShow',
                    //             type: 'basic',
                    //             link: '',
                    //             disabled: false,
                    //             permission: ['all'],
                    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    //         },
                    //         {
                    //             id: 'fit',
                    //             title: 'Fit',
                    //             type: 'basic',
                    //             link: '',
                    //             disabled: false,
                    //             permission: ['all'],
                    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    //         },
                    //         {
                    //             id: 'style',
                    //             title: 'Style',
                    //             type: 'basic',
                    //             link: '',
                    //             disabled: false,
                    //             permission: ['all'],
                    //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    //         }
                    //     ],
                    // },

                ]
            },
            {
                id: 'npdSamplingRequests_2',
                title: 'Sample Requests',
                type: 'basic',
                link: '/npd/sampling/request-list',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                role: ['SOURCING', 'PRODUCT MANAGER', 'ADMIN'],
            },
            {
                id: 'npdSamplingResponse_3',
                title: 'Sample Request',
                type: 'basic',
                link: '/npd/sampling/response-list',
                disabled: false,
                permission: ['twills'],
                role: ['VENDOR'],
            },
            {
                id: 'npdSamplingResponse_3',
                title: 'Sample Request',
                type: 'basic',
                link: '/npd/sampling/response-list',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['VENDOR', 'ADMIN'],
            },
            {
                id: 'documentUpload',
                title: 'Document Upload',
                type: 'basic',
                link: '/npd/docUpload/upload',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],

            },
            {
                id: 'allDocuments',
                title: 'Assign Documents',
                type: 'basic',
                link: '/npd/assignDocument/assign',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],

            },
            {
                id: 'documentManagement',
                title: 'Document Management',
                type: 'basic',
                link: '/activity/npd/documentManagement',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],

            },
            {
                id: 'qaAllocation',
                title: 'QA Allocation',
                type: 'basic',
                link: '/activity/npd/qaAllocation',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],

            },
            // {
            //     id: 'masterCreation',
            //     title: 'Master Creation',
            //     type: 'collapsable',
            //     disabled: false,
            //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test'],
            //     role: ['ADMIN', 'TENANT_ADMIN', 'SOURCING', 'SELLER'],
            //     children: [
            //         {
            //             id: 'createRawMaterials',
            //             title: 'Raw Materials',
            //             type: 'basic',
            //             link: 'npd/raw-materials',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test'],
            //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            //         },
            //         {
            //             id: 'createFg',
            //             title: 'Finished Goods',
            //             type: 'basic',
            //             link: 'npd/finished-goods',
            //             disabled: false,
            //             permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test'],
            //             role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
            //         },
            //     ]
            // },

            {
                id: 'cost&gross',
                title: 'Cost & Gross Margin',
                type: 'basic',
                link: '/activity/npd/costgrossmargin',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test'],
                role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],
            },
            {
                id: 'bom',
                title: 'BOM',
                type: 'basic',
                disabled: false,
                link: '/npd/bom',
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                role: ['ADMIN', 'PRODUCT MANAGER', 'SOURCING'],
                // children: [
                //     {
                //         id: 'createbom',
                //         title: 'Create BOM',
                //         type: 'basic',
                //         link: '/npd/bom',
                //         disabled: false,
                //         permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                //         role: ['ADMIN', 'PRODUCT MANAGER'],
                //     },
                // {
                //     id: 'planningbom',
                //     title: 'Planning BOM',
                //     type: 'basic',
                //     link: '/activity/npd/planningbom',
                //     disabled: false,
                //     permission: ['test', 'norlanka','quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                //     role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],
                // },
                // ]
            },
            {
                id: 'seasonPlancreation',
                title: 'Season Plan Creation',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test',],
                role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],
                children: [
                    {
                        id: 'createseasonPlan',
                        title: 'Create',
                        type: 'basic',
                        link: '/activity/npd/createseasonPlancreation',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],
                    },
                    {
                        id: 'viewseasonPlancreation',
                        title: 'View',
                        type: 'basic',
                        link: '/activity/npd/viewseasonPlan',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'SOURCING', 'PRODUCT MANAGER'],
                    },
                ]
            },
            {
                id: 'dashboards',
                title: 'Dashboards',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test',],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'dashGeneral',
                        title: 'NPD Dashboard',
                        type: 'basic',
                        link: '/activity/npd/dashboardGeneral',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],

                    },
                    {
                        id: 'fabricdevelopmentstatus',
                        title: 'Fabric Development Status',
                        type: 'basic',
                        link: '/activity/npd/fabricdevelopmentstatus',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'fgDevelopmentstatus',
                        title: 'FG Development Status',
                        type: 'basic',
                        link: '/activity/npd/fgDevelopmentstatus',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'dashBoardSourcing',
                        title: 'Sourcing',
                        type: 'basic',
                        link: '/activity/npd/dashBoardSourcing',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                    {
                        id: 'dashBoardQuality',
                        title: 'Quality',
                        type: 'basic',
                        link: '/activity/npd/dashBoardQuality',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    }
                ]
            },
            {
                id: 'roadshowActivities',
                title: 'Roadshow Activities',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test',],
                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'roadShowActUpload',
                        title: 'Upload',
                        type: 'basic',
                        link: '/activity/npd/roadShowActUpload',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],

                    },
                    {
                        id: 'roadShowStatus',
                        title: 'Status',
                        type: 'collapsable',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                        children: [
                            {
                                id: 'roadshowStatus',
                                title: 'Roadshow Status',
                                type: 'basic',
                                link: '/activity/npd/roadshowStatus',
                                disabled: false,
                                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],

                            },
                            {
                                id: 'prpoStatus',
                                title: 'PR & PO Status',
                                type: 'basic',
                                link: '/activity/npd/prpoStatus',
                                disabled: false,
                                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],

                            },
                            {
                                id: 'articleStatus',
                                title: 'Article Status',
                                type: 'basic',
                                link: '/activity/npd/articleStatus',
                                disabled: false,
                                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],

                            },
                            {
                                id: 'channelWiseStatus',
                                title: 'Channel wise Status',
                                type: 'basic',
                                link: 'activity/npd/channelWiseStatus',
                                disabled: false,
                                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'twills'],
                                role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'VENDOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],

                            },

                        ]
                    },
                ]
            },
            {
                id: 'capacityPlanning',
                title: 'Capacity Planning',
                type: 'collapsable',
                disabled: false,
                permission: ['all'],
                role: ['ADMIN', 'AUDITORINCHARGE', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                children: [
                    {
                        id: 'vendorCapacity',
                        title: 'Vendor Capacity',
                        type: 'basic',
                        link: '/activity/npd/vendorCapacity',
                        disabled: false,
                        permission: ['all'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],

                    },
                    {
                        id: 'sourcingCapacity',
                        title: 'Sourcing Capacity',
                        type: 'basic',
                        link: '/activity/npd/sourcingCapacity',
                        disabled: false,
                        permission: ['all'],
                        role: ['ADMIN', 'AUDITORINCHARGE', 'AUDITOR', 'FACTORY', 'TECHNICIAN', 'CUSTOMER', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
                    },
                ]
            },
        ],
    },
    {
        id: 'testingHead',
        title: 'Testing',
        type: 'aside',
        icon: 'fact_check',
        disabled: false,
        isParent: true,
        role: ['ADMIN', 'SELLER', 'VENDOR', 'FACTORY', 'AGENCY', 'LAB', 'AUDITOR', 'SME'],
        permission: ['amazon'],
        children: [
            {
                id: 'testinghome_4_1',
                title: 'Home',
                type: 'basic',
                link: '/testing/testing-home',
                disabled: false,
                role: ['ADMIN', 'SELLER', 'VENDOR'],
                permission: ['amazon']
            },
            {
                id: 'testProtocol_4_2',
                title: 'Test Protocol',
                type: 'basic',
                link: '/testing/testProtocol',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SELLER', 'SME'],
            },
            {
                id: 'InspectionSchedules_4_6',
                title: 'Schedules',
                type: 'basic',
                link: '/schedules/inspectionSchedules/testing',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SELLER', 'VENDOR', 'FACTORY', 'AGENCY', 'LAB', 'AUDITOR', 'PRODUCT_COMPLIANCE'],
            },
            {
                id: 'testRequisition_4_3',
                title: 'Test Requisition Form (TRF)',
                type: 'basic',
                link: '/testing/testRequisition',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SELLER', 'VENDOR', 'FACTORY', 'AGENCY', 'LAB', 'AUDITOR', 'SME'],
            },
            {
                id: 'testingReviewProcess_2_4',
                title: 'Review Process',
                type: 'basic',
                link: '/schedules/reviewProcess/testing',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'REVIEWER', 'AUDITOR'],
            },
            {
                id: 'capa_4_4',
                title: 'CAPA',
                type: 'basic',
                link: '/capa/capa/testing',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'VENDOR', 'FACTORY', 'AUDITOR', 'SELLER', 'AGENCY', 'LAB', 'REVIEWER'],
            }
        ]
    },
    {
        id: 'administration',
        title: 'Administration',
        type: 'aside',
        icon: 'fact_check',
        disabled: false,
        isParent: true,
        permission: ['titan'],
        role: ['ADMIN', 'PRODUCT_COMPLIANCE', 'PRODUCT_MANAGER'],
        children: [
            {
                id: 'InspectionTemplates_1',
                title: 'Inspection Templates',
                type: 'basic',
                link: '/inspection/inspectionTemplate',
                disabled: false,
                permission: ['titan'],
                role: ['ADMIN', 'PRODUCT_COMPLIANCE', 'PRODUCT_MANAGER'],
            },
        ]
    },
    {
        id: 'MasterData',
        title: 'Master data',
        type: 'aside',
        disabled: false,
        isParent: true,
        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills', 'titan', 'mtr', 'manjushree'],
        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER', 'PRODUCT MANAGER', 'SOURCING'],
        children: [
            {
                id: 'Users_5_2_1p',
                title: 'Users',
                type: 'basic',
                link: '/master/users',
                disabled: false,
                permission: ['titan', 'twills'],
                role: ['ADMIN'],
            },
            {
                id: 'Vendor',
                title: 'Vendor',
                type: 'basic',
                link: '/master/vendor',
                disabled: false,
                permission: ['twills'],
                role: ['ADMIN', 'TENANT_ADMIN'],
            },
            {
                id: 'product_details',
                title: 'Product Details',
                type: 'collapsable',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'crompton', 'twills', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                children: [
                    {
                        id: 'Brands',
                        title: 'Brands',
                        type: 'basic',
                        link: '/master/brands',
                        disabled: false,
                        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'twills', 'titan', 'mtr', 'manjushree'],
                        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER',],
                    },
                    {
                        id: 'M_articleType',
                        title: 'Article Type',
                        type: 'basic',
                        link: '/master/articleType',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    }, {
                        id: 'M_brandDepartment',
                        title: 'Brand Department',
                        type: 'basic',
                        link: '/master/brandDepartment',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_gender',
                        title: 'Gender',
                        type: 'basic',
                        link: '/master/gender',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_categoryType',
                        title: 'Category Type',
                        type: 'basic',
                        link: '/master/categoryType',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_subCategory',
                        title: 'Sub Category',
                        type: 'basic',
                        link: '/master/subCategory',
                        disabled: false,
                        permission: ['', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_UOM',
                        title: 'UOM',
                        type: 'basic',
                        link: '/master/uom',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_neckline',
                        title: 'Neckline',
                        type: 'basic',
                        link: '/master/neckline',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_colour',
                        title: 'Colour',
                        type: 'basic',
                        link: '/master/colour',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_fit',
                        title: 'Fit',
                        type: 'basic',
                        link: '/master/fit',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_offerMonth',
                        title: 'Offer Month',
                        type: 'basic',
                        link: '/master/offerMonth',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_season',
                        title: 'Season',
                        type: 'basic',
                        link: '/master/season',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_fabricContentGroup',
                        title: 'Fabric Content Group',
                        type: 'basic',
                        link: '/master/fabricContentGroup',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_fabricDesign',
                        title: 'Fabric Design',
                        type: 'basic',
                        link: '/master/fabricDesign',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_fabricType',
                        title: 'Fabric Type',
                        type: 'basic',
                        link: '/master/fabricType',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_fabricWeave',
                        title: 'Fabric Weave',
                        type: 'basic',
                        link: '/master/fabricWeave',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_sleeve',
                        title: 'Sleeve',
                        type: 'basic',
                        link: '/master/sleeve',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_categoryGroup',
                        title: 'Category Group',
                        type: 'basic',
                        link: '/master/categoryGroup',
                        disabled: false,
                        permission: ['twills', 'amazon'],
                        role: ['ADMIN'],
                    },
                ]
            },
            {
                id: 'Auditor',
                title: 'Auditor',
                type: 'basic',
                link: '/master/auditor',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'AuditorIncharge',
                title: 'Auditor Incharge',
                type: 'basic',
                link: '/master/auditorincharge',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Customers',
                title: 'Customers',
                type: 'basic',
                link: '/master/customers',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'crompton', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },

            {
                id: 'Category',
                title: 'Categories',
                type: 'basic',
                link: '/master/category',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'crompton', 'twills', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'ProductType',
                title: 'Product Type',
                type: 'basic',
                link: '/master/producttypes',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'TA',
                title: 'T&A',
                type: 'basic',
                link: '/activity/activityMaster',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'labconnect', 'demo', 'gokaldas', 'twills'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER', 'SOURCING'],
            },
            {
                id: 'Auditor-FactoryAssignment',
                title: 'Auditor-Factory Assignment',
                type: 'basic',
                link: '/master/auditorfactory',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'crompton', 'mtr'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Vendor',
                title: 'Vendor',
                type: 'basic',
                link: '/master/vendor',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Vendor-Request',
                title: 'Vendor Request',
                type: 'basic',
                link: '/master/vendor-request',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'audit-planning',
                title: 'Audit Planning',
                type: 'basic',
                link: '/master/auditPlanning',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Inspection-Request',
                title: 'Inspection Request',
                type: 'basic',
                link: '/master/inspectionRequest',
                disabled: false,
                permission: ['test'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Factory',
                title: 'Factory',
                type: 'basic',
                link: '/master/factory',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'titan', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'agency',
                title: 'Inspection Agency',
                type: 'basic',
                link: '/master/inspectionAgency',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'labconnect', 'demo', 'gokaldas'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'testingLab',
                title: 'Testing Lab',
                type: 'basic',
                link: '/master/testingLab',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'labconnect', 'demo', 'gokaldas'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Fabrics_5_2_16',
                title: 'Fabrics',
                type: 'basic',
                link: '/master/fabrics',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'labconnect', 'demo', 'gokaldas'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'm_designcodes',
                title: 'Designcodes/Product Codes',
                type: 'basic',
                link: '/activity/designcodes',
                permission: ['titan'],
                role: ['ADMIN'],
                disabled: false,
            },
            {
                id: 'fabric',
                title: 'Raw Materials',
                type: 'basic',
                link: '/master/raw-materials/',
                disabled: false,
                permission: ['twills', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'twills'],
                role: ['ADMIN', 'TENANT_ADMIN', 'PRODUCT MANAGER', 'SOURCING'],
            },
            {
                id: 'Stylecodes',
                title: 'Stylecodes',
                type: 'basic',
                link: '/master/stylecodes',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'labconnect', 'demo', 'gokaldas', 'twills'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER', 'PRODUCT MANAGER', 'SOURCING'],
            },
            {
                id: 'CustomDashboards',
                title: 'Custom Dashboards',
                type: 'basic',
                link: '/master/customdashboard',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'labconnect', 'demo', 'gokaldas'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Users_5_2_1p',
                title: 'Users',
                type: 'basic',
                link: '/master/users',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'M_userCategory',
                title: 'User Category',
                type: 'basic',
                link: '/master/userCategory',
                disabled: false,
                permission: ['twills', 'amazon', 'test'],
                role: ['ADMIN'],
            },

            {
                id: 'M_subBrand',
                title: 'Sub Brand',
                type: 'basic',
                link: '/master/subBrand',
                disabled: false,
                permission: ['twills', 'amazon'],
                role: ['ADMIN'],
            },
            {
                id: 'M_currency',
                title: 'Currency',
                type: 'basic',
                link: '/master/currency',
                disabled: false,
                permission: ['', 'amazon'],
                role: ['ADMIN'],
            },

            {
                id: 'M_rawMaterialSubType',
                title: 'Raw Material SubType',
                type: 'basic',
                link: '/master/rawMaterialSubType',
                disabled: false,
                permission: [ 'amazon'],
                role: ['ADMIN'],
            },


            {
                id: 'asin_5_3_0',
                title: 'ASIN',
                type: 'basic',
                link: '/admin/asin',
                permission: ['amazon'],
                role: ['SELLER',],
                disabled: false,
            },
            {
                id: 'Vendor-list',
                title: 'Vendor List',
                type: 'basic',
                link: '/master/vendor-list',
                disabled: false,
                permission: ['amazon'],
                role: ['SELLER'],
            },
        ]
    },
    {
        id: 'MasterData_2_2',
        title: 'Master data',
        type: 'aside',
        disabled: false,
        isParent: true,
        permission: ['amazon'],
        role: ['SELLER', 'OPERATIONS', 'TEST_USER', 'SQM_OPS', 'SOURCING'],
        children: [
            {
                id: 'asin2_2_0',
                title: 'ASIN',
                type: 'basic',
                link: '/admin/asin',
                permission: ['amazon'],
                role: ['OPERATIONS', 'TEST_USER', 'SQM_OPS'],
                disabled: false,
            },
            {
                id: 'Vendor-list_2_2_1',
                title: 'All Vendor',
                type: 'basic',
                link: '/master/vendor-list',
                disabled: false,
                permission: ['amazon'],
                role: ['OPERATIONS', 'TEST_USER', 'SOURCING'],
            },
            {
                id: 'User-list_2_2_2',
                title: 'Users',
                type: 'basic',
                link: '/master/vendorUsers',
                disabled: false,
                permission: ['amazon'],
                role: ['OPERATIONS', 'TEST_USER',],
            },
            {
                id: 'po_asin2_2_0',
                title: 'Import PO',
                type: 'basic',
                link: '/seller/purchase-order',
                permission: ['amazon'],
                role: ['SELLER'],
                disabled: false,
            },

        ]
    },
    {
        id: 'Administration',
        title: 'Admin',
        type: 'aside',
        disabled: false,
        isParent: true,
        permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'crompton', 'titan', 'mtr', 'manjushree'],
        role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
        children: [
            {
                id: 'policy',
                title: 'Policy Management',
                type: 'collapsable',
                disabled: false,
                permission: ['demo'],
                role: ['ADMIN'],
                children: [
                    {
                        id: 'orgsetup',
                        title: 'Org Setup',
                        type: 'basic',
                        link: 'admin/org-setup',
                        disabled: false,
                        permission: ['demo'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'policy_template',
                        title: 'Policy Templates',
                        type: 'basic',
                        link: 'admin/policy-templates',
                        disabled: false,
                        permission: ['demo'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'policy_management',
                        title: 'Policies',
                        type: 'basic',
                        link: 'admin/policy-management',
                        disabled: false,
                        permission: ['demo'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'incidents',
                        title: 'Incidents',
                        type: 'basic',
                        link: 'admin/incidents',
                        disabled: false,
                        permission: ['demo'],
                        role: ['ADMIN'],
                    },
                ]
            },
            {
                id: 'asin_5_3_0',
                title: 'ASIN',
                type: 'basic',
                link: '/admin/asin',
                permission: ['demo', 'amazon'],
                role: ['ADMIN',],
                disabled: false,
            },
            {
                id: 'InspectionTemplates_5_1',
                title: 'Inspection Templates',
                type: 'basic',
                link: '/inspection/inspectionTemplate',
                disabled: false,
                permission: ['test', 'labconnect', 'godrej', 'norlanka', 'demo', 'mtr'],
                role: ['ADMIN', 'PRODUCT_COMPLIANCE', 'PRODUCT_MANAGER'],
            },
            {
                id: 'Organizations',
                title: 'Organizations',
                type: 'basic',
                link: '/admin/organizations',
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
                disabled: false,
            },
            {
                id: 'OrganizationChart',
                title: 'Organization Chart',
                type: 'basic',
                link: '/admin/organizationchart',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'amazon', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'TaskStateManagement',
                title: 'Task State Management',
                type: 'basic',
                link: '/admin/state',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'amazon', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Auditcategories',
                title: 'Audit categories',
                type: 'basic',
                link: '/admin/auditcategories',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'amazon', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'InspectionTemplates_1',
                title: 'Inspection Templates',
                type: 'basic',
                link: '/inspection/inspectionTemplate',
                disabled: false,
                permission: ['quality360'],
                role: ['ADMIN', 'PRODUCT_COMPLIANCE', 'PRODUCT_MANAGER'],
            },
            {
                id: 'Audittypes',
                title: 'Audit types',
                type: 'basic',
                link: '/admin/audittypes',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Products',
                title: 'Products',
                type: 'basic',
                link: '/admin/products',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'amazon', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'AQL',
                title: 'AQL',
                type: 'basic',
                link: '/admin/aql',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'amazon', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'ProductType-AQL',
                title: 'Product Type - AQL',
                type: 'basic',
                link: '/admin/producttypeaql',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'amazon', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },

            {
                id: 'Attributes',
                title: 'Attributes',
                type: 'basic',
                link: '/admin/attributes',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Penalties',
                title: 'Penalties',
                type: 'basic',
                link: '/admin/penatlies',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'OptionTypes',
                title: 'Option Types',
                type: 'basic',
                link: '/admin/option-types',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Checkpoints',
                title: 'Checkpoints',
                type: 'basic',
                link: '/admin/checkpoints',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'CheckpointCategory',
                title: 'Checkpoint Categories',
                type: 'basic',
                link: '/admin/checkpointcategory',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Defecttypes',
                title: 'Defect types',
                type: 'basic',
                link: '/admin/defecttypes',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Defecttypecategory',
                title: 'Defect type Categories',
                type: 'basic',
                link: '/admin/defecttypecategory',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'titan', 'mtr',],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'Configurations',
                title: 'Configurations',
                type: 'basic',
                link: '/admin/configurations',
                disabled: false,
                permission: ['test', 'norlanka', 'quality360', 'praj', 'norlanka', 'givaudan', 'test', 'lots', 'labconnect', 'godrej', 'demo', 'gokaldas', 'amazon', 'titan', 'mtr', 'manjushree'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'categoryGrouping',
                title: 'Category Group',
                type: 'basic',
                link: '/designCode/categoryGroup',
                disabled: false,
                permission: ['test', 'quality360', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'articleType',
                title: 'Article Type',
                type: 'basic',
                link: '/articleType',
                disabled: false,
                permission: ['test', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'rawMaterialType',
                title: 'Raw Material Type',
                type: 'basic',
                link: 'admin/rawMaterialType',
                disabled: false,
                permission: ['test', 'praj', 'givaudan', 'test', 'lots', 'labconnect', 'demo', 'gokaldas', 'twills'],
                role: ['ADMIN', 'TENANT_ADMIN', 'SELLER'],
            },
            {
                id: 'schedulePersmission',
                title: 'Schedule Permission',
                type: 'basic',
                link: 'admin/schedule-permission',
                disabled: false,
                permission: ['norlanka', 'test'],
                role: ['ADMIN'],
            },



        ]
    },
    {
        id: 'sourcing_3',
        title: 'Product Development',
        type: 'aside',
        icon: 'fact_check',
        disabled: false,
        isParent: true,
        permission: ['amazon'],
        role: ['ADMIN', 'SOURCING', 'SME', 'PRODUCT MANAGER', 'SELLER', 'VENDOR', 'LAB'],
        children: [
            {
                id: 'Dashboard',
                title: 'Dashboard',
                type: 'collapsable',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'VENDOR'],
                children: [
                    {
                        id: 'Dashboard',
                        title: 'Product Status Summary',
                        type: 'basic',
                        link: '/activity/dashboard',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'VENDOR'],
                    },
                    {
                        id: 'Dashboard',
                        title: 'T&A - Purchase Orders',
                        type: 'basic',
                        link: '/activity/dashboard11',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'VENDOR'],
                    },
                ]
            },
            {
                id: 'Calendar',
                title: 'Calendar',
                type: 'basic',
                link: '/activity/activityCalendar',
                disabled: false,
                permission: [''],
                role: ['ADMIN', 'VENDOR'],
            },
            {
                id: 'roadMap_3_3',
                title: 'Roadmap(s)',
                type: 'basic',
                link: '/sourcing/roadmap/road-map-list',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SOURCING', 'SME', 'PRODUCT MANAGER', 'PRODUCT_MANAGER'],
            },
            {
                id: 'attach_protocol_to_DID_3_3',
                title: 'Design Code(s)',
                type: 'basic',
                link: '/sourcing/designcodes',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SELLER', 'VENDOR'],
            },

            {
                id: 'Request_757',
                title: 'Request(s)',
                type: 'collapsable',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SOURCING', 'SME', 'PRODUCT MANAGER', 'SELLER'],
                children: [
                    {
                        id: 'request_collection_requests',
                        title: 'Collection Request(s)',
                        type: 'basic',
                        link: '/sourcing/request/request-list/COLLECTION',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SOURCING', 'SME', 'PRODUCT MANAGER'],
                    },
                    {
                        id: 'request_seller_request',
                        title: 'Seller Request(s)',
                        type: 'basic',
                        link: '/sourcing/request/request-list/SELLER',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SOURCING', 'SELLER', 'SME'],
                    },
                    {
                        id: 'request_bidding_request',
                        title: 'Bidding Request(s)',
                        type: 'basic',
                        link: '/sourcing/request/request-list/BIDDING',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME',],
                    },
                ]
            },
            {
                id: 'quotations_3_1_034',
                title: 'EOI Request(s)',
                type: 'basic',
                link: '/sourcing/quotation/quotationRequest/QUOTATION',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SOURCING', 'SELLER', 'SME'],
            },
            {
                id: 'Quotation Response_1_6_56',
                title: 'Received EOI Request(s)',
                type: 'basic',
                link: '/sourcing/quotation/quotationResponse/QUOTATION',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'VENDOR'],
            },
            {
                id: 'quotations_3_5_67',
                title: 'TPS Request(s)',
                type: 'basic',
                link: '/sourcing/quotation/quotationRequest/TPS',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SME', 'PRODUCT MANAGER', 'PRODUCT_MANAGER', 'SOURCING'],
            },
            {
                id: 'quotations_3_5_12',
                title: 'Received TPS Request(s)',
                type: 'basic',
                link: '/sourcing/quotation/quotationResponse/TPS',
                disabled: false,
                permission: ['amazon'],
                role: ['VENDOR'],
            },
            {
                id: 'quotations_3_1_78',
                title: 'Lab Bidding Request(s)',
                type: 'basic',
                link: '/sourcing/quotation/quotationRequest/BIDDING',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SME', 'SOURCING'],
            },
            {
                id: 'Quotation bidding_1_6_89',
                title: 'Received Bidding Request(s)',
                type: 'basic',
                link: '/sourcing/quotation/quotationResponse/BIDDING',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'LAB'],
            },
            {
                id: 'quotations_3',
                title: 'Vendor Seller Mapping',
                type: 'basic',
                link: '/sourcing/factory/vendorSellerMap',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SOURCING', 'SELLER'],
            },
            {
                id: 'VendorRequest1_6',
                title: 'Add New Vendor',
                type: 'basic',
                link: '/master/vendor-request',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SOURCING'],
            },
            {
                id: 'MasterData_3_4',
                title: 'Master data',
                type: 'collapsable',
                disabled: false,
                permission: [''],
                role: ['ADMIN'],
                children: [
                    {
                        id: 'Auditor_3_4_1',
                        title: 'Raw Materials',
                        type: 'basic',
                        link: '/master/raw-materials/',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    }
                ]
            }
        ]
    },
    {
        id: 'administration_5',
        title: 'Administration',
        type: 'aside',
        icon: 'fact_check',
        disabled: false,
        isParent: true,
        permission: ['amazon'],
        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
        children: [
            {
                id: 'InspectionTemplates_5_1',
                title: 'Inspection Templates',
                type: 'basic',
                link: '/inspection/inspectionTemplate',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
            },
            {
                id: 'MasterData_5_2',
                title: 'Master data',
                type: 'collapsable',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                children: [
                    {
                        id: 'Auditor_5_2_1',
                        title: 'Auditor',
                        type: 'basic',
                        link: '/master/auditor',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'AuditorIncharge_5_2_2',
                        title: 'Auditor Incharge',
                        type: 'basic',
                        link: '/master/auditorincharge',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'Customers_5_2_3',
                        title: 'Customers',
                        type: 'basic',
                        link: '/master/customers',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'Brands_5_2_4',
                        title: 'Brands',
                        type: 'basic',
                        link: '/master/brands',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'Category_5_2_5',
                        title: 'Categories',
                        type: 'basic',
                        link: '/master/category',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'ProductType_5_2_6',
                        title: 'Product Type',
                        type: 'basic',
                        link: '/master/producttypes',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'TA_5_2_7',
                        title: 'T&A',
                        type: 'basic',
                        link: '/activity/activityMaster',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'Auditor-FactoryAssignment_5_2_8',
                        title: 'Auditor-Factory Assignment',
                        type: 'basic',
                        link: '/master/auditorfactory',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'Vendor_5_2_9',
                        title: 'Vendor',
                        type: 'basic',
                        link: '/master/vendor',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'Vendor-list',
                        title: 'Vendor list',
                        type: 'basic',
                        link: '/master/vendor-list',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SOURCING'],
                    },
                    {
                        id: 'Factory_5_2_13',
                        title: 'Factory',
                        type: 'basic',
                        link: '/master/factory',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'agency_5_2_14',
                        title: 'Inspection Agency',
                        type: 'basic',
                        link: '/master/inspectionAgency',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'agency_5_2_14',
                        title: 'Seller',
                        type: 'basic',
                        link: '/master/seller',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    // {
                    //     id: 'testingLab_5_2_15',
                    //     title: 'Testing Lab',
                    //     type: 'basic',
                    //     link: '/master/testingLab',
                    //     disabled: false,
                    //     permission: ['amazon'],
                    //     role: ['ADMIN', 'PRODUCT_COMPLIANCE', 'PRODUCT_MANAGER'],
                    // },
                    {
                        id: 'Fabrics_5_2_16',
                        title: 'Fabrics',
                        type: 'basic',
                        link: '/master/fabrics',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'Stylecodes_5_2_17',
                        title: 'Stylecodes',
                        type: 'basic',
                        link: '/master/stylecodes',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'activityMaster',
                        title: 'T&A Template',
                        type: 'basic',
                        link: '/activity/activityMaster',
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'CustomDashboards_5_2_18',
                        title: 'Custom Dashboards',
                        type: 'basic',
                        link: '/master/customdashboard',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME', 'PRODUCT_MANAGER'],
                    },
                    {
                        id: 'Users_5_2_1p',
                        title: 'Users',
                        type: 'basic',
                        link: '/master/users',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'sec_Group_5_2_20',
                        title: 'Specifications Group',
                        type: 'basic',
                        link: '/master/specGroup',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN', 'SME'],
                    },
                    {
                        id: 'ProductChecklist_5_2_19',
                        title: 'Product CheckList',
                        type: 'basic',
                        link: '/master/productChecklist',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    // {
                    //     id: 'technical_spec_5_2_19',
                    //     title: 'Technical Specifications Configuration',
                    //     type: 'basic',
                    //     link: '/master/tech-specs',
                    //     disabled: false,
                    //     permission: ['amazon'],
                    //     role: ['ADMIN', 'SME'],
                    // },

                ]
            },
            {
                id: 'Administration_5_3',
                title: 'Admin',
                type: 'collapsable',
                disabled: false,
                permission: ['amazon'],
                role: ['ADMIN'],
                children: [
                    {
                        id: 'asin_5_3_0',
                        title: 'ASIN',
                        type: 'basic',
                        link: '/admin/asin',
                        permission: ['demo', 'amazon'],
                        role: ['ADMIN',],
                        disabled: false,
                    },
                    {
                        id: 'Organizations_5_3_1',
                        title: 'Organizations',
                        type: 'basic',
                        link: '/admin/organizations',
                        permission: ['amazon'],
                        role: ['ADMIN'],
                        disabled: false,
                    },
                    {
                        id: 'OrganizationChart_5_3_2',
                        title: 'Organization Chart',
                        type: 'basic',
                        link: '/admin/organizationchart',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'TaskStateManagement_5_3_3',
                        title: 'Task State Management',
                        type: 'basic',
                        link: '/admin/state',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'Auditcategories_5_3_4',
                        title: 'Audit categories',
                        type: 'basic',
                        link: '/admin/auditcategories',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'Audittypes_5_3_5',
                        title: 'Audit types',
                        type: 'basic',
                        link: '/admin/audittypes',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'Products_5_3_6',
                        title: 'Products',
                        type: 'basic',
                        link: '/admin/products',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'AQL_5_3_7',
                        title: 'AQL',
                        type: 'basic',
                        link: '/admin/aql',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'ProductType-AQL_5_3_8',
                        title: 'Product Type - AQL',
                        type: 'basic',
                        link: '/admin/producttypeaql',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },

                    {
                        id: 'Attributes_5_3_9',
                        title: 'Attributes',
                        type: 'basic',
                        link: '/admin/attributes',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'Penalties_5_3_10',
                        title: 'Penalties',
                        type: 'basic',
                        link: '/admin/penatlies',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'OptionTypes_5_3_11',
                        title: 'Option Types',
                        type: 'basic',
                        link: '/admin/option-types',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'Checkpoints_5_3_12',
                        title: 'Checkpoints',
                        type: 'basic',
                        link: '/admin/checkpoints',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'CheckpointCategory_5_3_13',
                        title: 'Checkpoint Categories',
                        type: 'basic',
                        link: '/admin/checkpointcategory',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'EnabledProductSpecificCheckpointCategory_5_3_13',
                        title: 'Enable Product Specific Checkpoint',
                        type: 'basic',
                        link: '/admin/enable-product-specific-checkpoint',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'Defecttypes_5_3_14',
                        title: 'Defect types',
                        type: 'basic',
                        link: '/admin/defecttypes',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'Defecttypecategory_5_3_15',
                        title: 'Defect type Categories',
                        type: 'basic',
                        link: '/admin/defecttypecategory',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'Configurations_5_3_16',
                        title: 'Configurations',
                        type: 'basic',
                        link: '/admin/configurations',
                        disabled: false,
                        permission: ['amazon'],
                        role: ['ADMIN'],
                    },
                    {
                        id: 'M_userCategory',
                        title: 'User Category',
                        type: 'basic',
                        link: '/master/userCategory',
                        disabled: false,
                        permission: ['amazon', 'test'],
                        role: ['ADMIN'],
                    },
                ]
            },
        ]
    },
    {
        id: 'wrranty_1',
        title: 'Warranty',
        type: 'basic',
        link: '/warranty',
        icon: 'vpf',
        disabled: false,
        isParent: true,
        role: ['ADMIN'],
        permission: ['test'],
    },
    // {
    //     id: 'workflow_designer',
    //     title: 'Workflow Designer',
    //     type: 'basic',
    //     link: '/workflow-designer',
    //     icon: 'vpf',
    //     disabled: false,
    //     isParent: true,
    //     role: ['ADMIN'],
    //     permission: ['demo'],
    // },
    {
        id: 'Calendar',
        title: 'Calendar',
        type: 'basic',
        link: '/activity/activityCalendar',
        disabled: false,
        permission: ['demo'],
        role: ['ADMIN'],
        isParent: true,
    },
    // { 
    //     id: 'plan_the _planner',
    //     title: 'Plan the Planner',
    //     type: 'aside',
    //     icon: 'fact_check',
    //     disabled: false,
    //     isParent: true,
    //     permission: ['demo'],
    //     role: ['ADMIN'],
    //     children: [
    //         {
    //             id: 'Calendar',
    //             title: 'Calendar',
    //             type: 'basic',
    //             link: '/activity/activityCalendar',
    //             disabled: false,
    //             permission: ['demo'],
    //             role: ['ADMIN'],
    //         },
    //         {
    //             id: 'ta-workflow',
    //             title: 'T&A Workflow',
    //             type: 'basic',
    //             link: 'admin/ta-workflow',
    //             disabled: false,
    //             permission: ['demo'],
    //             role: ['ADMIN'],
    //         },

    //     ]
    // },
    {
        id: 'document-classification',
        title: 'Document Classification',
        type: 'basic',
        link: 'admin/document-classification',
        icon: 'vpf',
        disabled: false,
        isParent: true,
        role: ['ADMIN'],
        permission: ['demo'],
    },

    // {
    //     id: 'dashboardDesigner',
    //     title: 'Dashboard Designer',
    //     type: 'aside',
    //     disabled: false,
    //     isParent: true,
    //     icon: 'view_quilt',
    //     permission: ['all'],
    //     role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],

    //     children: [
    //         {
    //             id: 'Designer',
    //             title: 'Designer',
    //             type: 'basic',
    //             link: '/dashboard/designer',
    //             disabled: false,
    //             permission: ['all'],
    //             role: ['ADMIN', 'VENDOR', 'FACTORY', 'TENANT_ADMIN', 'SOURCING', 'AGENCY', 'LAB', 'SELLER'],
    //         }
    //     ]
    // }

];