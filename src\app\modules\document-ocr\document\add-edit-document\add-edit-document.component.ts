import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ScanDocument } from 'app/core/qinspect/_model/ScanDocument.model';
import { Status } from 'app/core/qinspect/_model/status.model';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-add-edit-document',
  templateUrl: './add-edit-document.component.html',
  styleUrls: ['./add-edit-document.component.scss']
})
export class AddEditDocumentComponent implements OnInit {
  public files: NgxFileDropEntry[] = [];
  status: Status = new Status();
  stepIndex: number = 0;
  displayImageinView: boolean = false;
  pages: any;
  basicAuth = this.globals.basicAuth;
  rec: ScanDocument = new ScanDocument();
  constructor(private _router: Router,
    private globals: Globals,
    private http: HttpClient,
    private toaster: ToastrService,) { }

  ngOnInit(): void {
  }
  closeDocument(): void {
    this._router.navigate(['mtc/documents']);

  }
  public fileOver(event) {
  }

  public fileLeave(event) {
  }

  deleteFile(i) {
    this.files.splice(i, 1)
    this.status = new Status()
  }

  public setFiles(files: NgxFileDropEntry[]) {
    this.files = files
  }
  public uploadFiles(): void {
    for (const droppedFile of this.files) {
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          let fileType = file.type;
          const formData = new FormData()
          formData.append('file', file, droppedFile.relativePath)
          formData.append('img', JSON.stringify(this.rec));
          const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
          let headers = new HttpHeaders()
            .set("Authorization", this.basicAuth).set('Client', tenantUid)
          this.http.post(environment.apiUrl + 'document/image', formData, { headers: headers, responseType: 'json' })
            .subscribe(data => {
              this.status = data as Status
              if (this.status.code != '500') {
                this.toaster.success('Uploaded Images for processing');
                this.files = [];
                this._router.navigate(['mtc/documents']);
              } else {
                this.status.message = "";
                this.toaster.warning('Failed to process the Images');
              }
            }, error => {
              if (error) {
                this.toaster.warning(error.message);
              } else {
                this.toaster.warning('Failed to process the Images');
              }
            })

        });
      } else {
        // It was a directory (empty directories are added, otherwise only files)
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
      }
    }
  }
}
