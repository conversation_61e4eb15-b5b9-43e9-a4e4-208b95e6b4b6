<!-- Navigation -->
<ng-container *ngIf="isScreenSmall">
    <fuse-vertical-navigation class="dark bg-gray-900 print:hidden" [mode]="'over'" [name]="'mainNavigation'"
        [navigation]="data.navigation.navigation" [opened]="false">
        <!-- Navigation header hook -->
        <ng-container fuseVerticalNavigationContentHeader>
            <!-- Logo -->
            <div class="flex items-center  px-8" style="background: #FFFFFF;
            box-shadow: 0px 4px 20px rgba(123, 134, 153, 0.1);">
                <img class="w-24" style="height: 48px;width: 100px;" alt="logo" [src]="headerStickyLogo"
                    alt="Logo image">
            </div>
        </ng-container>
    </fuse-vertical-navigation>
</ng-container>

<!-- Wrapper -->
<div class="flex flex-col flex-auto w-full min-w-0">
    <!-- Header -->
    <div class="tfl-header-wrapper flex flex-0 items-center w-full">
        <ng-container *ngIf="!isScreenSmall">
            <!-- Logo -->
            <div class="flex items-center"
                style="border-right: 2px solid #E7EAF0;padding-right: 32px;justify-content: center;">
                <div class="hidden lg:flex items-center" style="width: 120px;height: 36px;justify-content: center;">
                    <img *ngIf="tenantUid =='titan'" class="dark:hidden" alt="logo"
                        style="width: 70px;height: 64px;" [src]="headerStickyLogo">

                    <img *ngIf="tenantUid !='titan'"
                        [style.marginTop]="tenantUid =='amazon'? '15px' : null" class="dark:hidden" alt="logo"
                        style="width: 120px;height: 36px;" [src]="headerStickyLogo">
                    <img *ngIf="tenantUid !='titan'" class="hidden dark:flex" alt="logo"
                        style="width: 120px;height: 36px;" [src]="headerStickyLogo">
                </div>
                <img class="flex lg:hidden" style="width: 120px;height: 36px;" alt="logo" [src]="headerStickyLogo">
            </div>
            <!-- Horizontal navigation -->
            <fuse-horizontal-navigation class="mr-2" [name]="'mainNavigation'"
                [navigation]="data.navigation.navigation"></fuse-horizontal-navigation>
        </ng-container>
        <!-- Navigation toggle button -->
        <ng-container *ngIf="isScreenSmall">
            <button mat-icon-button (click)="toggleNavigation('mainNavigation')">
                <mat-icon [svgIcon]="'heroicons_outline:menu'"></mat-icon>
            </button>
            <img style="width: 120px;height: 36px;" alt="logo" [src]="headerStickyLogo">
        </ng-container>
        <!-- Components -->
        <div class="flex items-center pl-2 ml-auto space-x-2">
            <!-- <search [appearance]="'bar'"></search> -->
            <!-- <shortcuts [shortcuts]="data.shortcuts"></shortcuts> -->
            <!-- <messages [messages]="data.messages"></messages>  -->
            <!-- <notifications [notifications]="data.notifications"></notifications> -->
            <!-- <global-actions></global-actions> -->
            <user-menu></user-menu>
        </div>
    </div>

    <!-- Content -->
    <div class="flex flex-col flex-auto w-full padding-layout">
        <div class="container-fluid flex flex-col flex-auto min-w-0 w-full h-full"
            style="padding:20px 32px 20px 32px;background-color: #FFF;" id="q-body-wrapper">
            <!-- <bread-crumbs style="height:32px !important"></bread-crumbs> -->
            <div class="w-full h-full card pt-2 pl-3 pr-3 pb-1" id="q-body-card">
                <router-outlet *ngIf="true"></router-outlet>
            </div>

        </div>

        <!-- *ngIf="true" hack is required here for router-outlet to work correctly.
             Otherwise, layout changes won't be registered and the view won't be updated! -->

    </div>

    <!-- Footer -->
    <!-- <div style="background-color: #3e83c7  !important;color: white !important;"
        class="relative flex flex-0 items-center w-full h-7 sm:h-7 px-4 md:px-6 z-49 border-t dark:bg-transparent print:hidden">
        <div class="w-full" style="display: flex;justify-content: space-between;align-items: center; font-size: 14px;">
            <div>
                <span class="font-medium text-white">{{currentYear}} &copy; <a style="text-decoration: none;"
                        class="text-white" href="https://www.3frameslab.com/" target="_blank">3Frames Software Labs |
                        Quality360</a>
                </span>
            </div>
            <div>
                <span class="font-medium text-white "><a style="text-decoration: none;" class="text-white"
                        href="https://www.3frameslab.com/" target="_blank">Help</a>
                </span>
                <span class="font-medium text-white ml-3"> <a style="text-decoration: none;" class="text-white"
                        href="https://www.3frameslab.com/" target="_blank">FAQ</a>
                </span>
                <span class="font-medium text-white  ml-3"><a style="text-decoration: none;" class="text-white"
                        href="https://www.3frameslab.com/" target="_blank">Support</a> </span>
            </div>
        </div>
    </div> -->

</div>