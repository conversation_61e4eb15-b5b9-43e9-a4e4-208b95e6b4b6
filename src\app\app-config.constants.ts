import { InjectionToken } from '@angular/core';

import { IAppConfig } from './app-config.interface';
import { API_TYPES } from './enum-constants';

export const APP_UI_CONFIG: IAppConfig = {
    auth: {
        mfaRegistration: {
            url: "mfa/qrcode",
            type: API_TYPES.GET,
        },
        mfaValidation: {
            url: 'mfa/code/',
            type: API_TYPES.POST,
            paramList: { mfaCode: '' }
        }
    },
    configuration: {
        getTenant: {
            url: "user/getTenant",
            type: API_TYPES.GET
        },
        getTenantConfig: {
            url: "config/tenantConfig/{tenantUid}",
            type: API_TYPES.GET,
            paramList: { tenantUid: null }
        }
    },
    schedules: {
        getSchedules: {
            url: 'audit/day',
            type: API_TYPES.GET,
            params: null
        },
        getSchedulesByStatus: {
            url: 'audit/status/{type}',
            type: API_TYPES.GET,
            paramList: { type: null }
        },
        getSchedule: {
            url: 'audit/{auditId}',
            type: API_TYPES.GET,
            paramList: { auditId: null }
        },
        getExceptionSchedules: {
            url: 'audit/day/exceptionAudits',
            type: API_TYPES.GET,
            params: null
        },
        createExceptionAudit: {
            url: 'audit/exceptionAudit',
            type: API_TYPES.POST,
            payload: null
        },
        updateExceptionAudit: {
            url: 'audit/exceptionAudit/{id}',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getShipmentByPo: {
            url: 'shipment/{id}/po',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        getSalesOrderPolist: {
            url: 'saleOrder/polist/{id}',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        create: {
            url: 'audit',
            type: API_TYPES.POST,
            payload: null
        },
        createShipment: {
            url: 'audit/shipment/',
            type: API_TYPES.POST,
            payload: null
        },
        update: {
            url: 'audit',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        deleteSchedules: {
            url: 'audit',
            type: API_TYPES.DELETE,
            id: null,
            paramList: { id: null }
        },
        getAutiTypeList: {
            url: 'config/auditType/{id}/sap',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        getAuditTypesByType: {
            url: 'config/auditType/type/{auditType}',
            type: API_TYPES.GET,
            auditType: null,
            paramList: { auditType: null }
        },
        getAuditTypeById: {
            url: 'config/auditType/{id}',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        sendReport: {
            url: 'audit/sendreport/{id}',
            type: API_TYPES.POST,
            id: null,
            paramList: { id: null }
        },
        releaseFinalReport: {
            url: 'audit/report/releaseFinalReport/{auditId}',
            type: API_TYPES.POST,
            id: null,
            paramList: { auditId: null }
        },
        getAuditors: {
            url: 'auditor',
            type: API_TYPES.GET,
        },
        getVendors: {
            url: 'vendor',
            type: API_TYPES.GET,
        },
        getSellers: {
            url: 'seller',
            type: API_TYPES.GET,
        },
        getMappedSellers: {
            url: 'seller/mappedSellers/{vendorUid}',
            type: API_TYPES.GET,
            paramList: { vendorUid: null }
        },
        getMappings: {
            url: 'seller/mappings/{uuid}',
            type: API_TYPES.GET,
            paramList: { uuid: null }
        },
        getBrands: {
            url: 'brands',
            type: API_TYPES.GET,
        },
        getCustomers: {
            url: 'customer/',
            type: API_TYPES.GET,
        },
        getFactory: {
            url: 'factory',
            type: API_TYPES.GET,
        },
        getDesignCodes: {
            url: 'designCode/',
            type: API_TYPES.GET,
        },
        getCategory: {
            url: 'category',
            type: API_TYPES.GET,
        },
        getProductTypes: {
            url: 'config/productType/',
            type: API_TYPES.GET,
        },
        getDefective: {
            url: 'defecttype',
            type: API_TYPES.GET,
            params: null
        },
        getScheduleHistory: {
            url: 'scheduleHistory/{auditUid}',
            type: API_TYPES.GET,
            paramList: {
                auditUid: null
            }
        },
        canUpdateReport: {
            url: 'audit/canupdatereport/{id}',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        byAuditType: {
            url: 'factory/byAuditFactoryScore/FQA/{auditId}',
            type: API_TYPES.GET,
            paramList: { auditId: null }
        },
        getSchedulesListByType: {
            url: 'schedulePlanning/schedules/{from}/{to}/{type}',
            type: API_TYPES.GET,
            paramList: { from: null, to: null, type: null, }
        },
    },
    review: {
        get: {
            url: 'review/{uuid}',
            type: API_TYPES.GET,
            paramList: { uuid: null }
        },
        getByAudit: {
            url: 'review/audit/{auditId}',
            type: API_TYPES.GET,
            paramList: { auditId: null }
        }

    },
    capa: {
        getCapa: {
            url: 'audit/capa/{type}/{from}/{to}',
            type: API_TYPES.GET,
            paramList: { type: null, from: null, to: null }
        },
        create: {
            url: 'audit/{auditUid}/capa',
            type: API_TYPES.POST,
            payload: null,
            auditUid: null,
            paramList: { auditUid: null }
        },
        updateCapaDetails: {
            url: 'audit/{auditUid}/capa/{capUid}',
            type: API_TYPES.PUT,
            paramList: { auditUid: null, capUid: null }
        },
        getCapaDetails: {
            url: 'audit/{auditUid}/capa',
            type: API_TYPES.GET,
            auditUid: null,
            capUid: null,
            paramList: { auditUid: null }
        },
        getCapaDetailsByCap: {
            url: 'audit/{auditUid}/capa/{capUid}',
            type: API_TYPES.GET,
            auditUid: null,
            capUid: null,
            paramList: { auditUid: null, capUid: null }
        },
        updateCapaStatus: {
            url: 'audit/{auditUid}/capa/{capUid}/status/{code}',
            type: API_TYPES.PUT,
            paramList: { auditUid: null, capUid: null, code: null }
        },
        reAuditCapaStatus: {
            url: 'audit/{auditUid}/capareaudit/{capUid}',
            type: API_TYPES.GET,
            auditUid: null,
            capUid: null,
            paramList: { auditUid: null, capUid: null }
        },
        updateCapaDetailStatus: {
            url: 'audit/{auditUid}/capa/{capUid}/item/{detailUid}/status/{code}',
            type: API_TYPES.PUT,
            auditUid: null,
            capUid: null,
            paramList: { auditUid: null, capUid: null, detailUid: null, code: null }
        },
        createReinspection: {
            url: 'audit/{auditUid}/reinspection',
            type: API_TYPES.PUT,
            auditUid: null,
            paramList: { auditUid: null }
        },
        updateCapaDetailsAttachment: {
            url: 'audit/{auditUid}/capa/{capUid}/file',
            type: API_TYPES.POST,
            paramList: { auditUid: null, capUid: null },
            payload: null
        },
        getCapaDetailsAttachment: {
            url: 'audit/{auditUid}/capa/{capUid}/{fileUid}',
            type: API_TYPES.GET,
            paramList: { auditUid: null, capUid: null, fileUid: null }
        },
        notifyCapEntries: {
            url: 'audit/{auditUid}/capa/notify',
            type: API_TYPES.GET,
            paramList: { auditUid: null, capDetailUid: null }
        },
        deleteCapAttachment: {
            url: 'audit/{auditUid}/capa/{capUid}/delete/{fileUid}',
            type: API_TYPES.DELETE,
            paramList: { auditUid: null, capUid: null, fileUid: null }
        },
        getCheckpointResponse: {
            url: 'audit/{auditUid}/checkpointResponse/{checkpointUid}',
            type: API_TYPES.GET,
            paramList: { auditUid: null, checkpointUid: null, }
        }
    },
    inspection: {
        getinspection: {
            url: 'inspectionTemplate',
            type: 'GET'
        },
        getfabricInspection: {
            url: 'schedules/fabricInspection',
            type: 'GET'
        },
        getPkgInspection: {
            url: 'pkgInspection/img/base64',
            type: 'GET'
        },
        getTechparks: {
            url: 'doc/',
            type: API_TYPES.GET,
            params: null
        },
        createTechparks: {
            url: 'doc/',
            type: API_TYPES.POST,
            payload: null
        },
        UpdateTechparks: {
            url: 'doc/{id}',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getinspectionTemplate: {
            url: 'inspectionTemplate',
            type: API_TYPES.GET,
        },
        deployinspectionTemplate: {
            url: 'inspectionTemplate/{id}/activate',
            type: API_TYPES.PUT,
            paylod: {},
            id: null,
            paramList: { id: null }
        },
        unDeployinspectionTemplate: {
            url: 'inspectionTemplate/{id}/deactivate',
            type: API_TYPES.DELETE,
            paylod: {},
            id: null,
            paramList: { id: null }
        },
        getVendor: {
            url: 'vendor',
            type: API_TYPES.GET,
        },
        getFactories: {
            url: 'factory/all',
            type: API_TYPES.GET,
        },
        getPages: {
            url: 'config/mtcConfig/{id}',
            type: API_TYPES.GET,
            paramList: { id: null }
        },
        getConfigs: {
            url: 'config/mtcConfig/',
            type: API_TYPES.GET,
        },
        validateInspection: {
            url: 'audit/validates/{ponumber}/{designcode}/{audittype}',
            type: API_TYPES.GET,
            paramList: { ponumber: '', designcode: '', audittype: '' }
        }
    },
    cadDesign: {
        getCad: {
            url: 'cad/document',
            type: API_TYPES.GET,
        },
        extract: {
            url: "cad/{id}/extract",
            type: API_TYPES.PUT,
            payload: null,
            id: null,
            paramList: { id: null }
        },
        viewBase64: {
            url: "cad/{id}/base64",
            type: API_TYPES.GET,
            payload: null,
            id: null,
            paramList: { id: null }
        },
        view: {
            url: "cad/{id}/view",
            type: API_TYPES.PUT,
            payload: null,
            id: null,
            paramList: { id: null }
        },
        dimensions: {
            url: "cad/{id}/dimension",
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        preview: {
            url: "cad/{id}/preview",
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        uploadCad: {
            url: 'cad/document',
            type: API_TYPES.POST,
            payload: null,
        },
        saveBlocks: {
            url: 'cad/{id}/block',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { id: null }
        },
        exportCad: {
            url: 'cad/{uuid}/download',
            type: API_TYPES.GET,
            paramList: { uuid: null }
        }
    },
    shipment: {
        get: {
            url: 'saleOrder/shipment/',
            type: API_TYPES.GET,
            params: null
        },
        create: {
            url: 'audit',
            type: API_TYPES.POST,
            payload: null,
        },
        update: {
            url: 'audit',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        delete: {
            url: 'audit',
            type: API_TYPES.DELETE,
            id: null,
            paramList: { id: null }
        },
    },
    activity: {
        getInbox: {
            url: 'sourcing/timeline/inbox',
            type: 'GET'
        },
        getAllInboxItems: {
            url: 'sourcing/timeline/inbox/all',
            type: 'GET'
        },
        getInboxByUser: {
            url: 'timeline/byUser',
            type: 'GET'
        },
        getInboxByStatus: {
            url: 'sourcing/timeline/inbox/{statusCode}/bystatus',
            type: API_TYPES.GET,
            id: null,
            paramList: { statusCode: null }
        },
        getInboxItemDetails: {
            url: 'sourcing/timeline/inbox/{id}',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        getInboxItemByUid: {
            url: 'timeline/inbox/{id}',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        updateInboxItemStatus: {
            url: 'timeline/status/{id}/{status}',
            type: API_TYPES.PUT,
            id: null,
            paramList: { id: null, status: null }
        },
        updateInboxItemDetails: {
            url: 'sourcing/timeline/inbox/{id}',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        updateInboxItemsDetails: {
            url: 'sourcing/timeline/inbox/{id}/items',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getTemplates: {
            url: 'sourcing/activity/template',
            type: API_TYPES.GET,
            params: null
        },
        createOrUpdateTemplate: {
            url: 'sourcing/activity/template',
            type: API_TYPES.POST,
            payload: null,
        },
        getTemplateDetails: {
            url: 'sourcing/activity/template/{id}',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getTemplateDetailsList: {
            url: 'sourcing/activity/template/{id}/list',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getDesigncodes: {
            url: 'sourcing/activity/designcode',
            type: API_TYPES.GET,
            params: null
        },
        getDesigncodesByFilter: {
            url: 'sourcing/activity/designcode/byfilter',
            type: API_TYPES.GET,
            params: null
        },
        createOrUpdateDesigncode: {
            url: 'sourcing/activity/designcode',
            type: API_TYPES.POST,
            payload: null,
        },
        getDesigncodeDetails: {
            url: 'sourcing/activity/designcode/{id}',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getDesigncodeAuditTrailDetails: {
            url: 'sourcing/activity/designcode/{uid}/auditTrail',
            type: API_TYPES.GET,
            uid: null,
            payload: null,
            paramList: { id: null }
        },
        getDesigncodeSummary: {
            url: 'sourcing/activity/designcode/{id}/summary',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        uploadDesigncodeDocs: {
            url: 'sourcing/activity/designcode/{designcodeUid}/doc',
            type: API_TYPES.POST,
            paramList: { designcodeUid: null }
        },
        viewActivityDocument: {
            url: 'activity/{docUid}/doc',
            type: API_TYPES.GET,
            paramList: { docUid: null }
        },
        delete: {
            url: 'sourcing/activity/template',
            type: API_TYPES.DELETE,
            id: null,
            paramList: { id: null }
        },
        getCalenderItems: {
            url: 'sourcing/activity/calendar/list',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        updateViewStatus: {
            url: 'sourcing/timeline/inbox/{uuid}/{status}/updateViewStatus',
            type: API_TYPES.PUT,
            paramList: { uuid: '', status: '' }
        },
        getCalenderSummary: {
            url: 'sourcing/activity/calendar/summary',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
    },
    quotation: {
        getRequests: {
            url: 'sourcing/quotation/request',
            type: API_TYPES.GET,
            params: null
        },
        getRequestsByStatus: {
            url: 'sourcing/quotation/request/{statusCode}/bystatus',
            type: API_TYPES.GET,
            paramList: { statusCode: '' }
        },
        createOrUpdateRequest: {
            url: 'sourcing/quotation/request',
            type: API_TYPES.POST,
            payload: null,
        },
        sendQuotationByEmail: {
            url: 'sourcing/quotation/request/{id}/mail',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getRequestDetails: {
            url: 'sourcing/quotation/request/{id}',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getResponses: {
            url: 'sourcing/quotation/response',
            type: API_TYPES.GET,
            params: null
        },
        getResponsesByStatus: {
            url: 'sourcing/quotation/response/{statusCode}/bystatus',
            type: API_TYPES.GET,
            paramList: { statusCode: '' }
        },
        createOrUpdateResponse: {
            url: 'sourcing/quotation/response',
            type: API_TYPES.POST,
            payload: null,
        },
        acceptResponse: {
            url: 'sourcing/quotation/response/{id}/accept',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        shortlistResponse: {
            url: 'sourcing/quotation/response/shortlist',
            type: API_TYPES.PUT,
            id: null,
            payload: null
        },
        saveTechSpecifications: {
            url: 'sourcing/tech/specifications',
            type: API_TYPES.POST,
        },
        saveSpecifications: {
            url: 'sourcing/quotation/response/specs',
            type: API_TYPES.PUT,
        },
        negotiateResponse: {
            url: 'sourcing/quotation/response/negotiate',
            type: API_TYPES.PUT,
            id: null,
            payload: null
        },
        approveResponse: {
            url: 'sourcing/quotation/response/approve',
            type: API_TYPES.PUT,
            id: null,
            payload: null
        },
        getResponsesByRequest: {
            url: 'sourcing/quotation/response/{requestUid}/byrequest',
            type: API_TYPES.GET,
            paramList: { requestUid: '' }
        },
        getResponseDesigncodesByRequest: {
            url: 'sourcing/quotation/response/{id}/byrequest/designcode',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        updateQuotationResponseDesigncodeDetailsByUid: {
            url: 'sourcing/quotation/response/{responseUid}/designcode/{designcodeUid}',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { responseUid: null, designcodeUid: null }
        },
        updateResponseStatus: {
            url: 'sourcing/quotation/response/{responseUid}/status/{statusCode}',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { responseUid: null, statusCode: null }
        },
        submitResponseDesigncodeDetailsByUid: {
            url: 'sourcing/quotation/response/{responseUid}/designcode/{designcodeUid}/submit',
            type: API_TYPES.PUT,
            id: null,
            paramList: { responseUid: null, designcodeUid: null }
        },
        approveResponseDesigncodeDetailsByUid: {
            url: 'sourcing/quotation/response/{responseUid}/designcode/{designcodeUid}/approve',
            type: API_TYPES.PUT,
            id: null,
            paramList: { responseUid: null, designcodeUid: null }
        },
        approveResponseDesigncodeDetails: {
            url: 'sourcing/quotation/response/{responseUid}/designcode/approve',
            type: API_TYPES.PUT,
            id: null,
            paramList: { responseUid: null }
        },
        getQuotationResponseDesigncodeDetailsByUid: {
            url: 'sourcing/quotation/response/{responseUid}/designcode/{designcodeUid}',
            type: API_TYPES.GET,
            id: null,
            paramList: { responseUid: null, designcodeUid: null }
        },
        getResponsesByRequestAndStatus: {
            url: 'sourcing/quotation/response/{id}/byrequest/{status}/status',
            type: API_TYPES.GET,
            id: null,
            status: null,
            payload: null,
            paramList: { id: null }
        },
        getResponseDetails: {
            url: 'sourcing/quotation/response/{responseUid}',
            type: API_TYPES.GET,
            paramList: { responseUid: '' }
        },
        getRequestDetailsById: {
            url: 'sourcing/request/{requestUid}',
            type: API_TYPES.GET,
            paramList: { requestUid: '' }
        },
        importPOList: {
            url: 'sourcing/po/response/{responseUid}/po',
            type: "POST",
            paramList: { responseUid: null }
        },
        getPOList: {
            url: 'sourcing/po/all',
            type: "GET",
        },
        getPODetails: {
            url: 'sourcing/quotation/po/{id}',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        acceptPO: {
            url: 'sourcing/quotation/po/{id}/accept',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        rejectPO: {
            url: 'sourcing/quotation/po/{id}/reject',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getFeedbackByUser: {
            url: 'response/{responseUid}/feedback',
            type: API_TYPES.GET,
            paramList: { responseUid: null }
        },

        shareFeedback: {
            url: 'sourcing/quotation/response/negotiate/{feedbackCategory}',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { feedbackCategory: null }
        },
        acceptQuotation: {//Based on Design Codes
            url: 'sourcing/quotation/response/accept',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { designcodeUid: null }
        },
        rejectQuotation: {
            url: 'sourcing/quotation/response/reject',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { designcodeUid: null }
        },
        approveQuotation: {
            url: 'sourcing/quotation/response/approve',
            type: API_TYPES.PUT,
            payload: null
        },
        dropQuotation: {
            url: 'sourcing/quotation/response/drop',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { designcodeUid: null }
        },
        createPptUpload: {
            url: 'sourcing/quotation/response/file/save',
            type: API_TYPES.POST,
        },
        updateFile: {
            url: 'sourcing/quotation/response/file/{docUid}/update',
            type: API_TYPES.PUT,
            paramList: { docUid: null }
        },
        getUploadedFileList: {
            url: 'sourcing/quotation/response/{uuid}/doc',
            type: API_TYPES.GET,
            paramList: { uuid: null }
        },
        getAllFilesBasedOnDesignCode: {
            url: 'sourcing/quotation/response/designCode/{designCodeUid}/file/all',
            type: API_TYPES.GET,
            paramList: { designCodeUid: null }
        },
        viewPptAttchments: {
            url: 'sourcing/quotation/response/{docUid}/doc',
            type: API_TYPES.GET,
            paramList: { docUid: null }
        },
        getCurrencyList: {
            url: 'sourcing/quotation/currency',
            type: API_TYPES.GET,
        },
        getFeedbackByResponseUid: {
            url: 'sourcing/quotation/response/{responseUid}/feedback/byResponse',
            type: API_TYPES.GET,
            paramList: { responseUid: null }
        },
        updateFeedbackByResponse: {
            url: 'sourcing/quotation/response/feedback',
            type: API_TYPES.POST,
        },
        getFeedbackByDesignCode: {
            url: 'sourcing/quotation/response/{designcodeUid}/feedback',
            type: API_TYPES.GET,
            paramList: { designcodeUid: null }
        },
        sharedFeedbackByDesignCode: {
            url: 'sourcing/quotation/response/{designcodeUid}/feedback',
            type: API_TYPES.POST,
            payload: null,
            paramList: { designcodeUid: null }
        },
        attachTestProtocol: {
            url: 'sourcing/quotation/responseUid/testProtocol',
            type: API_TYPES.PUT,
            payload: {},
        },
        finalSubmitQuotation: {
            url: 'sourcing/quotation/response/createBiddingRequest',
            type: API_TYPES.PUT,
            payload: {},
        },
        rejectBidding: {
            url: 'sourcing/quotation/response/bidding/reject',
            type: API_TYPES.PUT,
            payload: {},
        },
        dropBidding: {
            url: 'sourcing/quotation/response/bidding/drop',
            type: API_TYPES.PUT,
            payload: {},
        },
        approveBidding: {
            url: 'sourcing/quotation/response/bidding/approve',
            type: API_TYPES.PUT,
            payload: {},
        },
        getTestRequestDetailsByDesignCode: {
            url: 'sourcing/quotation/{designcodeUid}/getTestRequest',
            type: API_TYPES.GET,
            payload: null,
            paramList: { designcodeUid: null }
        },
        getTestRequestsByVendor: {
            url: 'testProtocol/testRequests/byVendor/{vendorUid}',
            type: API_TYPES.GET,
            paramList: { vendorUid: null }
        },
        getTestRequestsByLab: {
            url: 'testRequests/byLab/{labUid}',
            type: API_TYPES.GET,
            paramList: { labUid: null }
        },
        saveOrUpdateShippingDetails: {
            url: 'testProtocol/testRequests/shippingDetails',
            type: API_TYPES.PUT,
            payload: {},
        },
        updateQuotationResponseViewStatus: {
            url: 'sourcing/quotation/response/updateViewStatus',
            type: API_TYPES.POST,
            payload: null,
        },
        updateQuotationRequestViewStatus: {
            url: 'sourcing/quotation/updateViewStatus',
            type: API_TYPES.POST,
            payload: null,
        },
        getAllDataByKeyWord: {
            url: 'sourcing/general/keyWord/{keyWord}',
            type: API_TYPES.GET,
            paramList: { keyWord: null }
        },
        designCodeGroupRequest: {
            url: 'sourcing/designCode/group',
            type: API_TYPES.POST,
            payload: null,
        },

    },

    fqaVendorSellerMapping: {
        getRequest: {
            url: 'factory/byAuditType/{type}',
            type: API_TYPES.GET,
            paramList: { type: null }
        },
        getSellerList: {
            url: 'vendorSellerMap/bySeller/{sellerUid}',
            type: API_TYPES.GET,
            paramList: { sellerUid: null }
        },
        assignSeller: {
            url: 'vendorSellerMap',
            type: API_TYPES.POST,
            payload: null,
        },
        assignVendor: {
            url: 'vendorSellerMap',
            type: API_TYPES.PUT,
            payload: null
        },
        getByFactory: {
            url: 'vendorSellerMap/byFactory/{factoryUid}',
            type: API_TYPES.GET,
            paramList: { factoryUid: null }
        },
    },

    request: {
        getRequest: {
            url: 'sourcing/request',
            type: API_TYPES.GET,
            params: null
        },
        activate: {
            url: 'sourcing/designCode/{uuid}/activate',
            type: API_TYPES.PUT,
            paramList: { uuid: null }
        },
        deactivate: {
            url: 'sourcing/designCode/{uuid}/deactivate',
            type: API_TYPES.DELETE,
            paramList: { uuid: null }
        },
        setFilters: {
            url: 'sourcing/request/filter',
            type: API_TYPES.POST,
            payload: null
        },
        addSupplierToRequest: {
            url: 'sourcing/request/{requestUid}',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { requestUid: null }
        },
        submitRequest: {
            url: 'sourcing/request/{requestUid}/submitRequest',
            type: API_TYPES.PUT,
            paramList: { requestUid: '' }
        },
        getRequestByUid: {
            url: 'sourcing/request/{requestUid}/details',
            type: API_TYPES.GET,
            paramList: { requestUid: '' }
        },
        create: {
            url: 'sourcing/request',
            type: API_TYPES.POST,
            payload: null,
        },
        updateIndentRequest: {
            url: 'sourcing/request/{requestUid}',
            type: API_TYPES.PUT,
            paramList: { requestUid: null }
        },
        updateRequestViewStatus: {
            url: 'sourcing/request/updateViewStatus',
            type: API_TYPES.POST,
            payload: null,
        },
        getInspectionProtocolList: {
            url: 'config/checkpointCategory/product/{designCodeUid}',
            type: API_TYPES.GET,
            paramList: { designCodeUid: null }
        },
        getCollectionListByUid: {
            url: 'sourcing/request/{collectionUid}',
            type: API_TYPES.GET,
            paramList: { collectionUid: null }
        },
        getDesignCodeListByCollectionUid: {
            url: 'sourcing/request/{requestUid}/designCodes',
            type: API_TYPES.GET,
            paramList: { requestUid: null }
        },
        getTestProtocolList: {
            url: 'testProtocol/',
            type: API_TYPES.GET,
            // paramList: { productTypeUid: null }
        },
        updateDesignCodeConfig: {
            url: 'sourcing/request/designCode/attach/protocols',
            type: API_TYPES.PUT,
            // paramList: { productTypeUid: any }
        },
        getProductTypeList: {
            url: 'sourcing/roadmap/designCode/productType',
            type: API_TYPES.GET,
        },
        getProductList: {
            url: 'product/all',
            type: API_TYPES.GET,
        },
        updatePORequestViewStatus: {
            url: 'sourcing/po/updateViewStatus',
            type: API_TYPES.PUT,
            payload: null,
        },
        updateSampleRequestViewStatus: {
            url: 'sourcing/sampleRequest/updateViewStatus',
            type: API_TYPES.POST,
            payload: null,
        },
        updateResponseViewStatus: {
            url: 'sourcing/sampleRequest/response/updateViewStatus',
            type: API_TYPES.POST,
        },
        createTempVendorUser: {
            url: 'vendor/tempVendor',
            type: API_TYPES.POST,
            payload: null,
        },
        getTempVendorUserByTempCode: {
            url: 'user/{tempCode}/findByTempCode',
            type: API_TYPES.GET,
            paramList: { tempCode: null }
        },
        getTempVendors: {
            url: 'vendor/tempVendors',
            type: API_TYPES.GET,
        },
        updateIndentRequestBySampling: {
            url: "sourcing/request/updateEditing",
            type: API_TYPES.PUT,
        }
    },
    sourcingRoadmap: {
        get: {
            url: 'sourcing/roadmap/all/{isALL}',
            type: API_TYPES.GET,
            paramList: { isALL: null }
        },
        activate: {
            url: 'sourcing/roadmap/{uuid}/activate',
            type: API_TYPES.PUT,
            paramList: { uuid: null }
        },
        deactivate: {
            url: 'sourcing/roadmap/{uuid}/deactivate',
            type: API_TYPES.DELETE,
            paramList: { uuid: null }
        },
        getByUid: {
            url: 'sourcing/roadmap/{roadMapUid}',
            paramList: { roadMapUid: null },
            type: API_TYPES.GET,
        },
        getSourcingUserList: {
            url: 'user/role/SOURCING',
            type: API_TYPES.GET,
            params: null
        },
        create: {
            url: 'sourcing/roadmap',
            type: API_TYPES.POST,
            payload: null,
        },
        updateDesigncodesByRoadmapUid: {
            url: 'sourcing/roadmap/{roadmpUid}/designCode',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { roadmpUid: null }
        },

        updateDesigncodeByDesignCodeUid: {
            url: 'sourcing/designCode/{designCodeUid}',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { designCodeUid: null }
        },
        copyDesignCode: {
            url: 'sourcing/designCode/updateDesigncodes',
            type: API_TYPES.PUT,
        },
        getDesigncodesByRoadmapUid: {
            url: 'sourcing/roadmap/{roadMapUid}/designCode',
            type: API_TYPES.GET,
            paramList: { roadMapUid: null }
        },
        getDocumentsByDesigncodeUid: {
            url: 'sourcing/request/document/{documentUid}/{extension}',
            type: API_TYPES.GET,
            paramList: { documentUid: null, extension: null }
        },
        getDocumentsByDocUid: {
            url: 'sourcing/request/document/{docUid}/doc',
            type: API_TYPES.GET,
            paramList: { docUid: null }
        },
        getDocumentList: {
            url: 'sourcing/document/{designUid}',
            type: API_TYPES.GET,
            paramList: { designUid: null, }
        },
        uploadDocumentsByDesigncodeUid: {
            url: 'sourcing/request/{uid}/designCode/{designUid}/document',
            type: API_TYPES.PUT,
            paramList: { uid: null }
        },
        update: {
            url: 'sourcing/roadmap',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { rmid: null }
        },
        updateRoadmapDocumentForImport: {
            url: 'sourcing/roadmap/import',
            type: API_TYPES.POST,
            payload: null
        },
        uploadAttachmentsByDesignCodeUid: {
            url: 'sourcing/request/designCode/{designUid}/document',
            type: API_TYPES.POST,
            payload: null,
            paramList: { designUid: null }
        },
        designCodeDetailsByUid: {
            url: 'sourcing/roadmap/{designCodeUid}/designCodeDetails',
            type: API_TYPES.GET,
            paramList: { designCodeUid: null }
        },
        designCodeDetailsByUuid: {
            url: 'sourcing/designCode/{designCodeUid}',
            type: API_TYPES.GET,
            paramList: { designCodeUid: null }
        },
        getAttachmentsByDesignCodeUid: {
            url: 'sourcing/request/{roadMapUid}/designCode/{designCodeUid}/document',
            type: API_TYPES.GET,
            payload: null,
            paramList: { roadMapUid: null, designCodeUid: null }
        },
        exportRoadmap: {
            url: 'sourcing/roadmap/export',
            type: API_TYPES.POST,
        },
        downloadRoadmapTemplate: {
            url: 'sourcing/roadmap/template',
            type: API_TYPES.GET,
        },
        updateRoadMapViewStatus: {
            url: 'sourcing/roadmap/updateViewStatus',
            type: API_TYPES.POST,
            // payload: null,
        },
        getProductList: {
            url: 'sourcing/roadmap/designCode/productType',
            type: API_TYPES.GET,
        },
        getUserListBasedOnUserCategoryMapping: {
            url: 'sourcing/category/{uuid}/getUsersByCategory',
            type: API_TYPES.GET,
            paramList: { uuid: null }
        },
        getAllDataByKeyWord: {
            url: 'sourcing/general/keyWord/{keyWord}',
            type: API_TYPES.GET,
            paramList: { keyWord: null }
        },
        getSubCategoryBasedOnCategory: {
            url: 'sourcing/category/categoryUid/{parentCategoryUid}/subCategory/{isAll}',
            type: API_TYPES.GET,
            paramList: { isAll: true, parentCategoryUid: null }
        },
    },
    sourcingDesignCode: {
        getRequestDesignCode: {
            url: 'sourcing/request/{id}/designcodes',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        getRequestDesignCodeDetails: {
            url: 'sourcing/request/{id}/designCodes/details',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        importDesignCodes: {
            url: 'sourcing/roadmap/designCode/import',
            type: API_TYPES.PUT,
            // paramList: { roadMapUid: null }
        },
        exportDesignCodes: {
            url: 'sourcing/roadmap/designcodeMaster/export',
            type: API_TYPES.POST,
            // paramList: { roadMapUid: null }
        },
        downloadDesignCodeTemplate: {
            url: 'sourcing/roadmap/designcode/template',
            type: API_TYPES.GET,
        },
        downloadDocumentByUid: {
            url: 'sourcing/request/document/{docUid}/{extension}',
            type: API_TYPES.GET,
            paramList: { docUid: null, extension: null }
        },

    },
    sourcingDashboard: {
        TotalSampleRequestCount: {
            url: 'sourcing/dashboard/sampling/requestCount',
            type: 'GET'
        },
        requestSummary: {
            url: 'sourcing/dashboard/sampling/requestCount/summary',
            type: 'GET'
        },
        getCategoryWiseSummaryOfFG: {
            url: 'sourcing/dashboard/sampling/articleCount/summary/FG',
            type: 'GET'
        },
        getCategoryWiseSummaryOfRM: {
            url: 'sourcing/dashboard/sampling/articleCount/summary/RawMaterial',
            type: 'GET'
        },
        getActivitySummaryByPO: {
            url: 'sourcing/dashboard/production/activity/summary/byPo',
            type: 'GET'
        }, getActivitySummary: {
            url: 'sourcing/dashboard/tanda/activitySummary/byProcessType',
            type: 'GET'
        },
        poQtySummary: {
            url: 'sourcing/dashboard/purchaseorder/poQtySummary',
            type: 'GET'
        }


    },
    finishGoodsDesignCode: {
        getFinishedGoods: {
            url: 'sourcing/designCode/all',
            type: API_TYPES.GET,
        },
        createFg: {
            url: 'sourcing/designCode',
            type: API_TYPES.POST,
            payload: null,
        },
        createPriceMaster: {
            url: 'sourcing/designCode/{uuid}/price',
            type: API_TYPES.POST,
            paramList: { uuid: null }
        },
        updatePriceMaster: {
            url: 'sourcing/designCode/{uuid}/price',
            type: API_TYPES.PUT,
            paramList: { uuid: null }
        },
        getUpdatedPriceMaster: {
            url: 'sourcing/designCode/price/{designCodeUid}',
            type: API_TYPES.GET,
            paramList: { designCodeUid: null }
        },
        getFinishedGoodsByUuid: {
            url: 'sourcing/designCode/{uuid}',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { uuid: null }
        },
        getFinishedFilterData: {
            url: 'sourcing/designCode/filter',
            type: API_TYPES.POST,
            payload: null,
        },
        update: {
            url: 'sourcing/designCode/{uuid}',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { uuid: null }
        },
        createRawMaterialArticle: {
            url: 'sourcing/rawmaterial',
            type: API_TYPES.POST,
            payload: null,
        },
        approveRawMaterialDocument: {
            url: 'sourcing/rawmaterial/{docUid}/{rmUid}/doc/approve',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { docUid: null, rmUid: null }
        },
        updateRawMaterialDocument: {
            url: 'sourcing/rawmaterial/doc/{docUid}/update',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { docUid: null }
        },
        deleteRawMaterialDocument: {
            url: 'sourcing/rawmaterial/{docUid}/doc/delete',
            type: API_TYPES.PUT,
            paramList: { docUid: null }
        },
        updateRawMaterialArticle: {
            url: 'sourcing/rawmaterial',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { uuid: null }
        },
        getRawMaterial: {
            url: 'sourcing/rawmaterial',
            type: API_TYPES.GET,
        },
        getRawMaterialByFilter: {
            url: 'sourcing/rawmaterial/filter',
            type: API_TYPES.POST,
            payload: null,
        },
        getRawMaterials: {
            url: 'sourcing/rawmaterial/all/filter',
            type: API_TYPES.POST,
            payload: null,
        },
        getRawMaterialByUuid: {
            url: 'sourcing/rawmaterial/{id}',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getMeasurements: {
            url: 'sourcing/measurment',
            type: API_TYPES.GET,
        },
        getMeasurementByUuid: {
            url: 'sourcing/measurment/{id}',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        createMeasurement: {
            url: 'sourcing/measurment',
            type: API_TYPES.POST,
            payload: null,
        },
        getGenderList: {
            url: 'sourcing/designCode/gender',
            type: API_TYPES.GET,
        },
        getFitList: {
            url: 'sourcing/designCode/fit',
            type: API_TYPES.GET,
        },
        getSourcingLovList: {
            url: 'sourcing/general/keyWord/{keyword}',
            type: API_TYPES.GET,
            paramList: { keyword: null }
        },
        getOfferMonthList: {
            url: 'sourcing/designCode/offerMonth',
            type: API_TYPES.GET,
        },
        getFabricContentGroup: {
            url: 'sourcing/designCode/fabricContentGroup',
            type: API_TYPES.GET,
        },
        getFabricDesignList: {
            url: 'sourcing/designCode/fabricDesign',
            type: API_TYPES.GET,
        },
        getFabricWaveList: {
            url: 'sourcing/designCode/fabricWave',
            type: API_TYPES.GET,
        },
        getFabricTypeList: {
            url: 'sourcing/designCode/fabricType',
            type: API_TYPES.GET,
        },
        getColorList: {
            url: 'sourcing/designCode/color',
            type: API_TYPES.GET,
        },
        getArticleTypeList: {
            url: 'sourcing/designCode/articalType',
            type: API_TYPES.GET,
        },
        createArticleTypeList: {
            url: 'sourcing/designCode/articalType',
            type: API_TYPES.POST,
            payload: null,
        },
        updateArticleTypeList: {
            url: 'sourcing/designCode/articalType',
            type: API_TYPES.PUT,
            payload: null,
        },
        getSleeveData: {
            url: 'sourcing/designCode/Sleeve',
            type: API_TYPES.GET,
        },
        getNecklineData: {
            url: 'sourcing/designCode/neckline',
            type: 'GET'
        },
        getFabricCodeData: {
            url: 'sourcing/designCode/fabric',
            type: 'GET'
        },
        fabricList: {
            url: 'sourcing/rawmaterial/all/byType',
            type: API_TYPES.GET,
        },
        getBrands: {
            url: 'sourcing/brand/{isAll}',
            type: API_TYPES.GET,
            paramList: { isAll: true }
        },
        getCategoryDropdownList: {
            url: 'sourcing/category/all',
            type: API_TYPES.GET,
        },
        getCategoryDropdownListBasedOnSubBrand: {
            // url: 'sourcing/category/subBrandUid/{subBrandUuid}/Category',
            url: 'sourcing/category/subBrandUid/{subBrandUuid}/Category/{isAll}',
            type: API_TYPES.GET,
            paramList: { subBrandUuid: null, isAll: true }
        },
        getCategoryDropdownListBasedOnMultiSubBrands: {
            // url: 'sourcing/category/subBrandUid/{subBrandUuid}/Category',
            url: 'sourcing/category/subBrandUid/{subBrandUuid}/categoryList/{isAll}',
            type: API_TYPES.GET,
            paramList: { subBrandUuid: null, isAll: true }
        },
        getCategoryDropdownListBasedOnMultiSubBrandOrBrandOrCategoryGroup: {
            url: 'sourcing/category/byFilter/{isAll}',
            type: API_TYPES.POST,
            paramList: { isAll: true }
        },
        getSubCategoryDropdownList: {
            url: 'sourcing/category/subCategory/all',
            type: API_TYPES.GET,
        },
        getSubCategoryDropdownListBasedOnCategory: {
            // url: 'sourcing/category/categoryUid/{categoryUuid}/subCategory',
            url: 'sourcing/category/categoryUid/{categoryUuid}/subCategory/{isAll}',
            type: API_TYPES.GET,
            paramList: { categoryUuid: null, isAll: true }
        },
        getPriceMaster: {
            url: 'sourcing/category/{categoryUid}/details',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { categoryUid: null }
        },
        getSubBrandsDropdownList: {
            url: 'sourcing/brand/subBrand',
            type: API_TYPES.GET,
        },
        getSubBrandsDropdownListBasedOnBrand: {
            // url: 'sourcing/brand/subBrand/{brandUuid}',
            url: 'sourcing/brand/subBrand/{brandUuid}/{isAll}',
            type: API_TYPES.GET,
            paramList: { brandUuid: null, isAll: true }
        },
        getSubBrandsDropdownListBasedOnMultiBrand: {
            // url: 'sourcing/brand/subBrand/{brandUuid}',
            url: 'sourcing/brand/subBrands/{brandUuid}/{isAll}',
            type: API_TYPES.GET,
            paramList: { brandUuid: null, isAll: true }
        },
        getSubBrandsDropdownListBasedOnMultiBrands: {
            url: 'sourcing/brand/subBrands/byFilter/{isAll}',
            type: API_TYPES.POST,
            paramList: { isAll: true }
        },
        getCategoryTypeDropdownListBasedOnCategoryGroup: {
            url: 'sourcing/category/group/{group}',
            type: API_TYPES.GET,
            paramList: { group: null }
        },
        getCategoryByCategoryGroupAndSubBrand: {
            // url: 'sourcing/category/group/{group}/subBrand/{subBrand}',
            url: 'sourcing/category/group/{group}/subBrand/{subBrand}/isAll/{isAll}',
            type: API_TYPES.GET,
            paramList: { group: null, subBrand: null, isAll: true }
        },
        getSeasonDropdownList: {
            url: 'sourcing/designCode/season',
            type: API_TYPES.GET,
        },
        getUomDropdownList: {
            url: 'sourcing/designCode/uom',
            type: API_TYPES.GET,
        },
        importRawMaterial: {
            url: 'sourcing/rawmaterial/doc/{designCodeUid}/{uuid}/{type}',
            type: API_TYPES.POST,
            paramList: { designCodeUid: null, uuid: null, type: null }
        },
        getRawMaterialDocList: {
            url: 'sourcing/rawmaterial/{designCodeUid}/byDesignCode/docs',
            type: API_TYPES.GET,
            paramList: { designCodeUid: null }
        },
        getRawMaterialDocListByUuid: {
            url: 'sourcing/rawmaterial/{id}/docs',
            type: API_TYPES.GET,
            paramList: { id: null }
        },
        downloadDocumentByUid: {
            url: 'sourcing/rawmaterial/document/{docUid}/{extension}',
            type: API_TYPES.GET,
            paramList: { docUid: null, extension: null }
        },
        createBom: {
            url: 'sourcing/bom',
            type: API_TYPES.POST,
            payload: null,
        },
        updateBom: {
            url: 'sourcing/bom',
            type: API_TYPES.PUT,
            payload: null,
        },
        getBomList: {
            url: 'sourcing/bom',
            type: API_TYPES.GET,
        },
        getBomListBybomUid: {
            url: 'sourcing/bom/{bomUid}',
            type: API_TYPES.GET,
            paramList: { bomUid: null }
        },
        getBomListByFgId: {
            url: 'sourcing/bom/{finishedGoodsId}/byFinishedGoods',
            type: API_TYPES.GET,
            paramList: { finishedGoodsId: null }
        },
        uploadDocumentGeneral: {
            url: 'sourcing/document/{type}',
            type: API_TYPES.PUT,
            paramList: { type: null }
        },
        getDocumentForSourcingByType: {
            url: 'sourcing/document/{type}/byType',
            type: API_TYPES.GET,
            paramList: { type: null }
        },
        addDocForFG: {
            url: 'sourcing/document/{designUid}/type/{type}',
            //url: 'sourcing/document/{designUid}/{type}',
            type: API_TYPES.POST,
            paramList: { designUid: null, type: null }
        },
        addDocForFGFromDocList: {
            url: 'sourcing/document/{designUid}/uploadByDesigncode',
            type: API_TYPES.PUT,
            paramList: { designUid: null },
            payload: null
        },
        getFinishGoodDocList: {
            url: 'sourcing/document/{designUid}',
            type: API_TYPES.GET,
            paramList: { designUid: null }
        },
        approveFGDocument: {
            url: 'sourcing/document/{designUid}/{type}/approve',
            type: API_TYPES.PUT,
            paramList: { designUid: null, type: null }
        },
        approveFGDocumentAttachedFile: {
            url: 'sourcing/document/{designcodeUid}/attachFiles',
            type: API_TYPES.PUT,
            paramList: { designcodeUid: null }
        },
        bomAttachFile: {
            url: 'sourcing/document/{bomUid}/attachFiles',
            type: API_TYPES.PUT,
            paramList: { bomUid: null }
        },
        downloadFGDocumentByUid: {
            url: 'sourcing/request/document/{docUid}/{extension}',
            type: API_TYPES.GET,
            paramList: { docUid: null, extension: null }
        },
        downloadRWDocumentByUid: {
            url: 'sourcing/document/{docUid}/{extension}',
            type: API_TYPES.GET,
            paramList: { docUid: null, extension: null }
        },
        getBrandDepartment: {
            url: "sourcing/designCode/brandDepartment",
            type: API_TYPES.GET,
        },
        getcategoryGrouping: {
            url: "sourcing/designCode/categoryGrouping",
            type: API_TYPES.GET,
        },
        getcategoryGroupingByArticleType: {
            url: "sourcing/designCode/categoryGrouping/{articleType}",
            type: API_TYPES.GET,
            paramList: { articleType: null }
        },
        getcategoryByArticleType: {
            url: "sourcing/category/byArticleType",
            type: "GET",
            // paramList: { articleType: null }
        },
        getcategoryGroupByArticleType: {
            url: "sourcing/rawmaterial/articleType",
            type: "GET",
        },
        getcategoryType: {
            url: "sourcing/designCode/categoryType",
            type: API_TYPES.GET,
        },
        getcategoryGroupingByUuid: {
            url: "sourcing/designCode/categoryGrouping/{uuid}",
            type: API_TYPES.GET,
            paramList: { uuid: null }
        },
        createCategoryGrouping: {
            url: 'sourcing/designCode/categoryGrouping',
            type: API_TYPES.POST,
            payload: null,
        },
        updateCategoryGrouping: {
            url: 'sourcing/designCode/categoryGrouping',
            type: API_TYPES.PUT,
            payload: null,
        },
        getRawMaterialTypes: {
            url: 'sourcing/designCode/rawMaterialType',
            type: API_TYPES.GET,
        },
        getRawMaterialTypesBasedOnArticleType: {
            url: 'sourcing/designCode/rawMaterialType/{articleUuid}',
            type: API_TYPES.GET,
            paramList: { articleTypeUuid: null }
        },
        createRawMaterialTypes: {
            url: 'sourcing/designCode/rawMaterialType',
            type: API_TYPES.POST,
            payload: null,
        },
        updateRawMaterialTypes: {
            url: 'sourcing/designCode/rawMaterialType',
            type: API_TYPES.PUT,
            payload: null,
        },
        getRawMaterialSubTypes: {
            url: 'sourcing/designCode/rawMaterialSubType/{rawMaterialType}',
            type: API_TYPES.GET,
            paramList: { rawMaterialType: null }
        },
        importPriceMaster: {
            url: 'sourcing/designCode/price/import',
            type: API_TYPES.POST,
        },
        exportPriceMaster: {
            url: 'sourcing/designCode/price/export',
            type: API_TYPES.POST,
            payload: null
        },
        importBOM: {
            url: 'sourcing/bom/import',
            type: API_TYPES.POST,
        },
        exportBOM: {
            url: 'sourcing/bom/export',
            type: API_TYPES.POST,
        },
        downloadBOMTemplate: {
            url: 'sourcing/bom/template',
            type: API_TYPES.GET,
        },
        downloadPriceMasterTemplate: {
            url: 'sourcing/designCode/price/template',
            type: API_TYPES.GET,
        },
        importRawMaterialList: {
            url: 'sourcing/rawmaterial/import',
            type: 'POST'
        },
        downloadRawMaterialTemplate: {
            url: 'sourcing/rawmaterial/template',
            type: 'GET'
        },
        exportRawMaterial: {
            url: 'sourcing/rawmaterial/export/selected',
            type: API_TYPES.POST,
            payload: null
        },
        addDocForPoShipment: {
            url: 'sourcing/po/shipment/{shipmentNumber}/doc',
            type: API_TYPES.POST,
            paramList: { shipmentNumber: null }
        },
        getPoShipmentDocList: {
            url: 'sourcing/po/shipment/{po}/docList/byShipmentNo',
            type: API_TYPES.GET,
            paramList: { po: null }
        },
        downloadPoShipmentDocumentByUid: {
            url: 'sourcing/po/shipment/{docUid}/doc',
            type: API_TYPES.GET,
            paramList: { docUid: null }
        },
        importFG: {
            url: 'sourcing/designCode/designCode/import',
            type: 'POST'
        },
        downloadFgTemplate: {
            url: 'sourcing/designCode/template',
            type: 'GET'
        },
        exportFG: {
            url: 'sourcing/designCode/export/selected',
            type: API_TYPES.POST,
            payload: null
        },
        addOtherTypeDocForArticle: {
            url: 'sourcing/request/designCode/{designUid}/document',
            type: API_TYPES.POST,
            paramList: { designUid: null, }
        },
        getAttachments: {
            url: 'sourcing/document/',
            type: API_TYPES.GET,
        },
        getArticleTypeListByType: {
            url: "sourcing/designCode/articalType/{type}",
            type: API_TYPES.GET,
            paramList: { type: null }
        },
        getAllArticleTypeList: {
            url: "sourcing/designCode/articalType/all/all",
            type: API_TYPES.GET,
        },
        addDocForRMFromDocList: {
            url: 'sourcing/rawmaterial/{rawMaterialUid}/{designCodeUid}/uploadByRawMaterial',
            type: API_TYPES.PUT,
            paramList: { rawMaterialUid: null, designCodeUid: null },
            payload: null
        },
        updateFGOtherTypeDocument: {
            url: 'sourcing/request/designCode/{docUid}/document/update',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { docUid: null }
        },
        deleteFGDocument: {
            url: 'sourcing/request/{docUid}/doc/delete',
            type: API_TYPES.PUT,
            paramList: { docUid: null }
        },
        getAllFinishedFilterData: {
            url: "sourcing/designCode/filter/all",
            type: "POST"
        }

    },
    reports: {
        bulkFabricInwardReports: {
            url: "sourcing/report/generic/bulk_fabric_inward_report/format/{format}",
            type: "POST",
            paramList: { format: null }
        },
        fgDispatchForAllFactoriesAndCategoriesReports: {
            url: 'sourcing/report/generic/finished_goods_report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        fgDispatchForAllFactoriesAndCategoriesQtyWiseReports: {
            url: 'sourcing/report/generic/finished_goods_qtywise_report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        wipUpdateForFactoriesReports: {
            url: 'sourcing/report/generic/WIP_update_report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        wipUpdateForFactoriesQtyWiseReports: {
            url: 'sourcing/report/generic/WIP_update_Qtywise_report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        leadAvgTimeReports: {
            url: 'sourcing/report/generic/lead_time_average_report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        fobInwardsReports: {
            url: 'sourcing/report/generic/fob_fabric_inward/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        fgDispatchForAllFactoriesAndCategoriesForRMReports: {
            url: 'sourcing/report/generic/finished_goods_dispatch_details_rawmaterial_report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        catWiseRmSamplingReports: {
            url: 'sourcing/report/generic/RM_Sampling_Report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        catWiseFgSamplingReports: {
            url: 'sourcing/report/generic/FG_Sampling_Report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        activityStatusReport: {
            url: 'sourcing/report/generic/activity_status_report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
        catWisePoReports: {
            url: 'sourcing/report/generic/PO_status_report/format/{format}',
            type: "POST",
            paramList: { format: null }
        },
    },
    saleOrders: {
        get: {
            url: 'saleOrder/',
            type: API_TYPES.GET,
            params: null,
        },
        create: {
            url: 'audit',
            type: API_TYPES.POST,
            payload: null,
        },
        update: {
            url: 'audit',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        delete: {
            url: 'audit',
            type: API_TYPES.DELETE,
            id: null,
            paramList: { id: null }
        },
    },
    purchaseOrders: {
        get: {
            url: 'saleOrder/po',
            type: API_TYPES.GET,
            payload: null,
        },
        getByStatus: {
            url: 'saleOrder/po/{id}/status',
            type: API_TYPES.GET,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        getByGroup: {
            url: "saleOrder/po/group",
            type: API_TYPES.GET,
        },
        create: {
            url: 'audit',
            type: API_TYPES.POST,
            payload: null,
        },
        update: {
            url: 'audit',
            type: API_TYPES.PUT,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        delete: {
            url: 'audit',
            type: API_TYPES.DELETE,
            id: null,
            paramList: { id: null }
        },
        getByManufacturer: {
            url: 'saleOrder/po/manufacturer/{manfId}',
            type: API_TYPES.GET,
            paramList: { manfId: null }
        },
        getPoDetailByPoUid: {
            url: 'saleOrder/po/{uuid}',
            type: API_TYPES.GET,
            paramList: { uuid: '' }
        },
        getPoDetailsByPONumber: {
            url: "saleOrder/po/byPoNumber/{poNo}",
            type: API_TYPES.GET,
            paramList: { poNo: "" }
        },
        getDesignCodeDetailsByCode: {
            url: '/designCode/byCode/{code}',
            type: API_TYPES.GET,
            paramList: { code: null }
        }
    },
    mtc: {
        scannedDocuments: {
            url: 'document/',
            type: API_TYPES.GET,
            externalUrl: true
        },
        reProcess: {
            url: 'document/{id}/reproc',
            type: API_TYPES.PUT,
            externalUrl: true,
            id: null,
            paramList: { id: null }
        },
        extract: {
            url: 'document/{id}/ocr',
            type: API_TYPES.PUT,
            externalUrl: true,
            id: null,
            paramList: { id: null }
        },
        getProjects: {
            url: 'proj/',
            type: API_TYPES.GET,
        },
        getCertificates: {
            url: 'mtc/image',
            type: API_TYPES.GET,
        },
        getCertificateTypes: {
            url: 'config/mtcConfig/',
            type: API_TYPES.GET,
        },
        getCertificateType: {
            url: 'config/mtcConfig/{id}',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        uploadCertificate: {
            url: 'mtc/image/file',
            type: API_TYPES.POST,
            payload: null,
        },
        getImage: {
            url: 'mtc/image/{id}',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        getRefData: {
            url: 'config/refdata/',
            type: API_TYPES.GET,
        },
        saveImage: {
            url: 'config/mtcConfig/file',
            type: API_TYPES.POST,
            payload: 'null',
        },
        getImageFile: {
            url: 'config/mtcConfig/file/{id}',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        ocrWithUpdate: {
            url: 'mtc/image/{id}/ocr',
            type: API_TYPES.POST,
            payload: null,
            id: null,
            paramList: { id: null }
        },
        addImageConfi: {
            url: 'mtc/image/{id}/config',
            type: API_TYPES.POST,
            payload: null,
            id: null,
            paramList: { id: null }
        },
        autoOcrWithImageConfig: {
            url: 'automtc/image/{id}/ocr/config',
            type: API_TYPES.POST,
            payload: null,
            externalUrl: true,
            id: null,
            paramList: { id: null }
        },
        ocr: {
            url: 'mtc/image/{id}/ocr',
            type: API_TYPES.PUT,
            payload: null,
            id: null,
            paramList: { id: null }
        },
        autoOcr: {
            url: 'automtc/image/{id}/ocr',
            type: API_TYPES.PUT,
            payload: null,
            externalUrl: true,
            id: null,
            paramList: { id: null }
        },
        getImageResponse: {
            url: 'mtc/image/{id}/response',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        autoOcrReProcess: {
            url: 'automtc/image/{id}/reproc',
            type: API_TYPES.PUT,
            externalUrl: true,
            payload: null,
            id: null,
            paramList: { id: null }
        },
        validateImage: {
            url: 'mtc/image/{id}/validate',
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        sentEmailReport: {
            url: `mtc/{id}/report`,
            type: API_TYPES.GET,
            id: null,
            paramList: { id: null }
        },
        materials: {
            get: {
                url: 'mtc/material',
                type: API_TYPES.GET,
            }
        },


    },
    masterdata: {
        auditor: {
            getauditor: {
                url: 'auditor',
                type: API_TYPES.GET,
                param: null
            },
            add: {
                url: 'auditor',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'auditor/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            getallauditors: {
                url: 'auditor/all',
                type: 'GET'
            },
            getAuditorsByAgency: {
                url: 'auditor/{agencyUid}',
                type: API_TYPES.GET,
                paramList: { agencyUid: null }
            },
            getAuditorsByLab: {
                url: 'auditor/byLab/{labUid}',
                type: API_TYPES.GET,
                paramList: { labUid: null }
            },
            activate: {
                url: 'auditor/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'auditor/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            }
        },
        auditorincharge: {
            get: {
                url: 'incharge',
                type: API_TYPES.GET,
                param: null
            },
            getall: {
                url: 'incharge/all',
                type: API_TYPES.GET,
                param: null
            },
            activate: {
                url: 'incharge/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'incharge/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'incharge',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'incharge/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            }
        },
        customers: {
            get: {
                url: 'customer/all',
                type: API_TYPES.GET,
                param: '@param'
            },
            activate: {
                url: 'customer/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'customer/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'customer',
                type: API_TYPES.POST,
                payload: null,
            },
            update: {
                url: 'customer/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            createcustomers: {
                url: '',
                type: '',
                payload: ''
            },
            updatecustomers: {
                url: '',
                type: '',
                payload: '',
                id: '',
                paramList: { id: null }
            }

        },
        brands: {
            get: {
                url: 'brands/all',
                type: API_TYPES.GET,
                param: null
            },

            create: {
                url: '',
                type: '',
                param: '',
            },
            updatebrands: {
                url: '',
                type: '',
                param: '',
            },
            activate: {
                url: 'brands/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'brands/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'brands',
                type: API_TYPES.POST,
                payload: null,
            },
            update: {
                url: 'brands/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
        },
        articleType: {
            get: {
                url: 'sourcing/designCode/articalType',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/articalType',
                type: API_TYPES.POST,
                param: null
            }
        },
        brandDepartment: {
            get: {
                url: 'sourcing/designCode/brandDepartment',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/brandDepartment',
                type: API_TYPES.POST,
                param: null
            }
        },
        userCategory: {
            get: {
                url: 'sourcing/userCategoryMapping/{userUUId}/getByUser',
                type: API_TYPES.GET,
                paramList: { userUUId: null }
            },
            create: {
                url: 'sourcing/userCategoryMapping/',
                type: API_TYPES.POST,
                param: null
            },
            update: {
                url: 'sourcing/userCategoryMapping/',
                type: API_TYPES.POST,
                param: null
            }
        },
        categoryScreen: {
            get: {
                url: 'sourcing/category/{level}',
                type: API_TYPES.GET,
                paramList: null
            },
            create: {
                url: 'sourcing/category',
                type: API_TYPES.POST,
                param: null
            },
            update: {
                url: 'sourcing/category',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            }
        },
        uom: {
            get: {
                url: 'sourcing/uom',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/uom',
                type: API_TYPES.POST,
                param: null
            }
        },
        fit: {
            get: {
                url: 'sourcing/designCode/fit',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/fit',
                type: API_TYPES.POST,
                param: null
            }
        },
        subBrand: {
            get: {
                url: 'sourcing/brand/subBrand',
                type: API_TYPES.GET,
            },
            create: {
                url: 'sourcing/brand',
                type: API_TYPES.POST,
                param: null
            },
            update: {
                url: 'sourcing/brand',
                type: API_TYPES.PUT,
                payload: null,
            },
        },
        offerMonth: {
            get: {
                url: 'sourcing/designCode/offerMonth',
                type: API_TYPES.GET,
            },
            create: {
                url: 'sourcing/designCode/offerMonth',
                type: API_TYPES.POST,
                param: null
            }
        },
        fabricType: {
            get: {
                url: 'sourcing/designCode/fabricType',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/fabricType',
                type: API_TYPES.POST,
                param: null
            }
        },
        fabricWeave: {
            get: {
                url: 'sourcing/designCode/fabricWave',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/fabricwave',
                type: API_TYPES.POST,
                param: null
            }
        },
        season: {
            get: {
                url: 'sourcing/designCode/season',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/season',
                type: API_TYPES.POST,
                param: null
            }
        },
        fabricContentGroup: {
            get: {
                url: 'sourcing/designCode/fabricContentGroup',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/fabricContentGroup',
                type: API_TYPES.POST,
                param: null
            }
        },
        fabricDesign: {
            get: {
                url: 'sourcing/designCode/fabricDesign',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/fabricDesign',
                type: API_TYPES.POST,
                param: null
            }
        },
        sleeve: {
            get: {
                url: 'sourcing/designCode/Sleeve',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/Sleeve',
                type: API_TYPES.POST,
                param: null
            }
        },
        categoryType: {
            get: {
                url: 'sourcing/designCode/categoryType',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/categoryType',
                type: API_TYPES.POST,
                param: null
            }
        },
        categoryGroup: {
            get: {
                url: 'sourcing/designCode/categoryGrouping',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/categoryGrouping',
                type: API_TYPES.POST,
                param: null
            }
        },
        rawMaterialType: {
            get: {
                url: 'sourcing/designCode/rawMaterialType',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/rawMaterialType',
                type: API_TYPES.POST,
                param: null
            }
        },
        neckline: {
            get: {
                url: 'sourcing/designCode/neckline',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/neckline',
                type: API_TYPES.POST,
                param: null
            }
        },
        color: {
            get: {
                url: 'sourcing/designCode/color',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'sourcing/designCode/color',
                type: API_TYPES.POST,
                param: null
            }
        },
        productType: {
            get: {
                url: 'config/productType/all',
                type: API_TYPES.GET,
                param: null
            },
            activate: {
                url: 'config/productType/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/productType/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'config/productType/',
                type: API_TYPES.POST,
                payload: null,
            },
            update: {
                url: 'config/productType/',
                type: API_TYPES.PUT,
                payload: null,
            }
        },
        vendor: {
            get: {
                url: 'vendor/all',
                type: API_TYPES.GET,
                param: null
            },
            getvendor: {
                url: 'vendor',
                type: API_TYPES.GET,
                param: null
            },
            add: {
                url: 'vendor',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'vendor/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            activate: {
                url: 'vendor/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'vendor/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            allVendors: {
                url: 'vendor/allVendors',
                type: API_TYPES.GET,
                param: null
            },
            mappedVendors: {
                url: 'vendor/mappedVendors/{sellerUid}',
                type: API_TYPES.GET,
                paramList: { sellerUid: null }
            },
            getFactoryProductTypes: {
                url: 'vendor/{factoryUid}/productTypesMapping',
                type: API_TYPES.GET,
                paramList: { factoryUid: null }
            },
            getFactoryProducts: {
                url: 'vendor/{factoryUid}/products',
                type: API_TYPES.GET,
                paramList: { factoryUid: null }
            }
        },
        seller: {
            get: {
                url: 'seller',
                type: API_TYPES.GET,
                param: null
            },
            getByUid: {
                url: 'seller/{uuid}',
                type: API_TYPES.GET,
                paramList: { uuid: null }
            },
            activate: {
                url: 'seller/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'seller/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            addOrUpdate: {
                url: 'seller',
                type: API_TYPES.POST,
                payload: null
            }
        },
        inspectionAgency: {
            get: {
                url: 'inspectionAgency',
                type: API_TYPES.GET,
                param: null
            },
            getByType: {
                url: 'inspectionAgency/byType/{type}',
                type: API_TYPES.GET,
                paramList: { type: null }
            },
            getByUid: {
                url: 'inspectionAgency/{uuid}',
                type: API_TYPES.GET,
                paramList: { uuid: null }
            },
            activate: {
                url: 'inspectionAgency/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'inspectionAgency/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'inspectionAgency',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'inspectionAgency/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            addFiles: {
                url: 'schedulePlanning/documents/{id}',
                type: API_TYPES.POST,
                payload: null,
                paramList: { id: null }
            },
            getSupportingFiles: {
                url: 'schedulePlanning/getDocuments/{id}',
                type: API_TYPES.DELETE,
                payload: null,
                paramList: { id: null }
            },
        },
        testingLab: {
            get: {
                url: 'testingLab/all',
                type: API_TYPES.GET,
                param: null
            },
            getByRole: {
                url: 'user/role/{role}',
                type: API_TYPES.GET,
                id: null,
                paramList: { role: null }
            },
            activate: {
                url: 'testingLab/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'testingLab/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'testingLab',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'testingLab/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            getInternalLab: {
                url: 'testingLab/internalLabs',
                type: API_TYPES.GET,
                param: null
            }
        },
        factory: {
            get: {
                url: 'factory/all',
                type: API_TYPES.GET,
                param: null
            },
            getByUid: {
                url: 'factory/{uuid}',
                type: API_TYPES.GET,
                paramList: { uuid: null }
            },
            activate: {
                url: 'factory/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'factory/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'factory',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'factory/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            getByVendor: {
                url: 'factory/{id}/vendor',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            },
            addFactory: {
                url: 'vendor/saveAssociatedFactory',
                type: API_TYPES.POST,
                payload: null
            },
        },
        designCodeMaster: {
            get: {
                url: 'config/designCode/',
                type: API_TYPES.GET,
                param: null
            },
            getSellerListByDesignCodes: {
                url: 'seller/mappedSellers/category/{category}',
                type: API_TYPES.GET,
                paramList: { category: '' }
            },
            getVendorListByCategories: {
                url: 'vendor/mappedVendors/category/{category}',
                type: API_TYPES.GET,
                paramList: { category: '' }
            },
            getSellerListByVendorUId: {
                url: 'seller/mappedSellers/{vendorUid}',
                type: API_TYPES.GET,
                paramList: { vendorUid: '' }
            },
            getVendorListBySeller: {
                url: 'vendor/mappedVendors/{sellerUid}',
                type: API_TYPES.GET,
                paramList: { sellerUid: '' }
            },
            updateTestRequests: {
                url: 'testProtocol/testRequests',
                type: API_TYPES.PUT,
                payload: null
            },
            getSmeOrLabListByDesignCodes: {
                url: 'user/mappedUsers/category/{category}/role/{type}',
                type: API_TYPES.GET,
                paramList: { category: '', type: '' }
            },
            getByCode: {
                url: 'config/designCode/{code}',
                type: API_TYPES.GET,
                paramList: { code: null }
            },
            getByBulkDesignCode: {
                url: 'designCode/bulkDesignCode/{codes}',
                type: API_TYPES.GET,
                paramList: { codes: null }
            },
            activate: {
                url: 'designCode/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'designCode/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'designCode',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'designCode/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            getAllDesignCodes: {
                url: 'sourcing/designCode/all',
                type: 'GET'
            },
            getFullDesignCodes: {
                url: 'designCode/all',
                type: 'GET'
            },
            deleteDesignCode: {
                url: 'designCode/delete/{designCodeUid}',
                type: API_TYPES.DELETE,
                paramList: { designCodeUid: null }
            },
        },
        auditorfactory: {
            get: {
                url: 'auditorFactory/all',
                type: API_TYPES.GET,
                param: null
            },
            getfactory: {
                url: 'factory',
                type: API_TYPES.GET,
                param: null
            },
            activate: {
                url: 'auditorFactory/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'auditorFactory/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'auditorFactory/',
                type: API_TYPES.POST,
                payload: null,
            },
            update: {
                url: 'auditorFactory/',
                type: API_TYPES.PUT,
                payload: null,
            }
        },
        category: {
            get: {
                url: 'sourcing/category/{level}',
                type: API_TYPES.GET,
                paramList: { level: null }
            },
            activate: {
                url: 'category/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'category/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            add: {
                url: 'sourcing/category',
                type: API_TYPES.POST,
                payload: null,
            },
            update: {
                url: 'category/{id}',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            getWithoutSourcing: {
                url: 'category/all',
                type: API_TYPES.GET,
                paramList: { level: null }
            },
            addWithoutSourcing: {
                url: 'category',
                type: API_TYPES.POST,
                payload: null,
            },
        },
        subCategory: {
            get: {
                url: 'sourcing/category/{level}',
                type: API_TYPES.GET,
                paramList: { level: null }
            },
            create: {
                url: 'sourcing/subCategory',
                type: API_TYPES.POST,
                payload: null
            }
        },
        facilityCameraInspectionConfig: {
            get: {
                url: 'pkg/facilityCameraInspectionConfig/',
                type: API_TYPES.GET,
                param: null
            },
            activate: {
                url: 'pkg/facilityCameraInspectionConfig/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'pkg/facilityCameraInspectionConfig/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            update: {
                url: 'pkg/facilityCameraInspectionConfig/',
                type: API_TYPES.PUT,
                payload: null,
            },
            create: {
                url: 'pkg/facilityCameraInspectionConfig/',
                type: API_TYPES.POST,
                payload: null,
            },
            download: {
                url: 'pkg/facilityCameraInspectionConfig/download',
                type: API_TYPES.GET,
            }
        },
        fabrics: {
            getfabrics: {
                url: '',
                type: API_TYPES.GET,
                param: null
            }
        },
        timelineactivity: {
            get: {
                url: 'timeline/',
                type: API_TYPES.GET,
                param: null
            }
        },
        customdashboard: {
            getcustomdashboard: {
                url: 'user/dash',
                type: API_TYPES.GET,
                param: null
            }
        },
        schedulePlanning: {
            getSchedulePlanning: {
                url: 'schedulePlanning/criteria/{type}/{from}/{to}',
                type: API_TYPES.GET,
                paramList: { type: null, from: null, to: null }
            },
            getSchedulesPlanningList: {
                url: 'schedulePlanning/planning/{from}/{to}/{type}',
                type: API_TYPES.GET,
                paramList: { from: null, to: null, type: null }
            },
            saveSchedulePlanning: {
                url: 'schedulePlanning',
                type: API_TYPES.POST,
                payload: {}
            },
            updateSchedulePlanning: {
                url: 'schedulePlanning/{schedulePlanningUid}',
                type: API_TYPES.PUT,
                payload: {}
            },
            getSchedulePlanningById: {
                url: 'schedulePlanning/{uuid}',
                type: API_TYPES.GET,
                paramList: { uuid: null }
            },
            getSchedulePlanningByAudiId: {
                url: 'schedulePlanning/byAudit/{uuid}',
                type: API_TYPES.GET,
                paramList: { uuid: null }
            },
        },
        userRole: {
            get: {
                url: 'userRole',
                type: 'GET'
            },
            add: {
                url: 'userRole',
                type: 'POST'
            }
        },
        users: {
            getusers: {
                url: 'user/role/{role}',
                type: API_TYPES.GET,
                paramList: { role: null }
            },
            add: {
                url: 'user/create',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'user/update',
                type: API_TYPES.PUT,
                payload: null
            },
            activate: {
                url: 'auditor/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'auditor/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            getByUid: {
                url: 'user/{uuid}/findbyuid',
                type: API_TYPES.GET,
                paramList: { uuid: null }
            },
        },
        userCategoryMapping: {
            getByCategory: {
                url: 'userCategoryMapping/{uuid}/getByCategory',
                type: API_TYPES.GET,
                paramList: { uuid: null }
            }
        },
        scheduleFiledConfig: {
            getAll: {
                url: 'scheduleFieldConfig/all',
                type: API_TYPES.GET,
            },
            getScheduleConfigByAuditTypeUid: {
                url: 'scheduleFieldConfig/{auditTypeUid}',
                type: API_TYPES.GET,
                paramList: { auditTypeUid: null }
            },
            updateScheduleConfigByAuditTypeUid: {
                url: 'scheduleFieldConfig/update/{auditTypeUid}',
                type: API_TYPES.PUT,
                paramList: { auditTypeUid: null },
                payload: null
            }
        },
        masterDynamics: {
            getAllApi: {
                url: 'apiListConfig/all',
                type: API_TYPES.GET,
            }
        }

    },
    administration: {
        homePageCount: {
            get: {
                url: 'count/{page}',
                type: API_TYPES.GET,
                paramList: { page: null }
            }
        },
        getRolesList: {
            url: 'userRole',
            type: API_TYPES.GET,
        },
        getUserByRole: {
            url: 'user/role/{role}',
            type: API_TYPES.GET,
            paramList: { role: null }
        },
        getAllUsersData: {
            url: '/userCategoryMapping',
            type: API_TYPES.GET,
        },
        getAllUsersByRole: {
            url: 'userCategoryMapping/{role}',
            type: API_TYPES.GET,
            paramList: { role: null }
        },
        organization: {
            get: {
                url: 'config/tenant/',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: "config/product/",
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: "config/tenant/",
                type: API_TYPES.PUT,
                payload: null
            },
            activate: {
                url: 'config/tenant/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/tenant/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },

        },
        state: {
            get: {
                url: 'stateMachine/state',
                type: API_TYPES.GET,
            },
            activate: {
                url: 'config/aql/{id}/activate',
                type: API_TYPES.PUT,
                id: null,
                payload: null,
                paramList: { id: null }
            },
            deActivate: {
                url: 'config/aql/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            addstate: {
                url: 'stateMachine/state',
                type: API_TYPES.POST,
                payload: null,
            },
            updatestate: {
                url: 'stateMachine/state',
                type: API_TYPES.PUT,
                payload: null,
            },
            addstatetransition: {
                url: 'stateMachine/stateTransition',
                type: API_TYPES.POST,
                payload: null,
            },
            updatestatetransition: {
                url: 'stateMachine/stateTransition',
                type: API_TYPES.PUT,
                payload: null,
            },
            getTransition: {
                url: 'stateMachine/state/{id}/transitions',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            }
        },
        auditcategories: {
            get: {
                url: 'config/auditCategory/',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: "config/auditCategory/",
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: "config/auditCategory/",
                type: API_TYPES.PUT,
                payload: null
            },
            activate: {
                url: 'config/auditCategory/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/auditCategory/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            }

        },
        audittypes: {
            get: {
                url: 'config/auditType/grid',
                type: API_TYPES.GET,
            },
            getUnmappedCheckpointCategories: {
                url: 'config/checkpointCategory/unmappedCheckpointCategories',
                type: API_TYPES.GET,
            },
            getaudittype: {
                url: 'config/auditType/',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: "config/auditType/",
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: "config/auditType/",
                type: API_TYPES.PUT,
                payload: null
            },
            activate: {
                url: 'config/auditType/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/auditType/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            getAllCategoryTypes: {
                url: 'config/productType/all',
                type: API_TYPES.GET,
            },
            getAllAuditCategories: {
                url: 'config/auditCategory/',
                type: API_TYPES.GET,
            }
        },
        products: {
            get: {
                url: "config/product/",
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: "config/product/",
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: "config/product/",
                type: API_TYPES.PUT,
                payload: null
            },
            activate: {
                url: 'config/product/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/product/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            }

            //config/product/
        },
        aqlType: {
            get: {
                url: "config/aqlType/",
                type: API_TYPES.GET,
                param: null
            },
        },
        aql: {
            get: {
                url: "config/aql/",
                type: API_TYPES.GET,
                param: null
            },
            getByType: {
                url: "config/aql/aqlType/{uuid}",
                type: API_TYPES.GET,
                paramList: { id: null }
            },
            create: {
                url: "config/aql/",
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: "config/aql/",
                type: API_TYPES.PUT,
                payload: null
            },
            activate: {
                url: 'config/aql/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/aql/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            }
        },
        producttypeaql: {
            get: {
                url: "config/aql/",
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: "config/aql/",
                type: API_TYPES.GET,
                payload: null
            },
            update: {
                url: "config/aql/",
                type: API_TYPES.GET,
                payload: null
            },
            activate: {
                url: 'config/aql/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/aql/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            }
        },
        optiontype: {
            get: {
                url: 'config/checkpoint/optiontype/',
                type: API_TYPES.GET,
            },
            activate: {
                url: 'config/checkpoint/optiontype/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/checkpoint/optiontype/{id}/deactivate',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            create: {
                url: 'config/checkpoint/optiontype/',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'config/checkpoint/optiontype/',
                type: API_TYPES.PUT,
                payload: null,
            },
            downloadOptionTypes: {
                url: 'config/checkpoint/optiontype/download',
                type: API_TYPES.GET,
            }
        },
        attributes: {
            get: {
                url: 'config/attribute/',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'config/attribute/',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'config/attribute/',
                type: API_TYPES.PUT,
                payload: null
            },
            activate: {
                url: 'config/attribute/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/attribute/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            }
        },
        panelties: {
            get: {
                url: 'config/penalty/',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'config/penalty/',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'config/penalty/',
                type: API_TYPES.PUT,
                payload: null
            },
            activate: {
                url: 'config/penalty/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/penalty/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            }
        },
        checkpoints: {
            get: {
                url: "config/checkpoint/",
                type: API_TYPES.GET,
                param: null
            },
            activate: {
                url: 'config/checkpoint/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/checkpoint/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            getOptionTypes: {
                url: 'config/checkpoint/optiontype/',
                type: API_TYPES.GET,
            },
            getAllOptionTypes: {
                url: 'config/checkpoint/optiontype/',
                type: API_TYPES.GET,
            },
            update: {
                url: 'config/checkpoint/',
                type: API_TYPES.PUT,
                payload: null,
            },
            create: {
                url: 'config/checkpoint/',
                type: API_TYPES.POST,
                payload: null,
            },
            export: {
                url: 'config/checkpoint/download',
                type: API_TYPES.GET,
            }
        },
        checkpointscategory: {
            get: {
                url: 'config/checkpointCategory/grid',
                type: API_TYPES.GET,
            },
            getcheckpointscategory: {
                url: 'config/checkpointCategory/',
                type: API_TYPES.GET,
                param: null
            },
            activate: {
                url: 'config/checkpointCategory/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/checkpointCategory/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            getunmappedCheckPoints: {
                url: 'config/checkpoint/unmappedCheckpoints',
                type: 'GET'
            },
            getCheckpointsByCategory: {
                url: 'config/checkpointCategory/{id}/checkpoints',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            },
            getAllCheckpoints: {
                url: 'config/checkpoint/grid',
                type: 'GET'
            },
            updateCheckpointCategory: {
                url: 'config/checkpointCategory/',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            createCheckpointCategory: {
                url: 'config/checkpointCategory/',
                type: API_TYPES.POST,
                payload: null
            },
            activateCheckpointCategory: {
                url: 'config/checkpointCategory/',
                type: API_TYPES.POST,
                payload: null
            },
            deactivateCheckpointCategory: {
                url: 'config/checkpointCategory/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            productCheckpointCategory: {
                url: 'config/checkpointCategory/product/{auditType}',
                type: API_TYPES.GET,
                id: null,
                paramList: { auditType: null }
            },
        },
        defecttypes: {
            get: {
                url: 'config/defectType/',
                type: API_TYPES.GET,
                param: null
            },
            //'config/defectType/'
            activate: {
                url: 'config/defectType/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/defectType/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            update: {
                url: 'config/defectType/',
                type: API_TYPES.PUT,
                payload: null,
            },
            create: {
                url: 'config/defectType/',
                type: API_TYPES.POST,
                payload: null,
            },
            downloadDefects: {
                url: 'config/defectType/download',
                type: API_TYPES.GET,
            }
        },
        defecttypecategory: {
            get: {
                url: 'config/defectTypeCategory/',
                type: API_TYPES.GET,
                param: null
            },
            activate: {
                url: 'config/defectTypeCategory/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/defectTypeCategory/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            create: {
                url: 'config/defectTypeCategory/',
                type: API_TYPES.POST,
                payload: null
            },
            update: {
                url: 'config/defectTypeCategory/',
                type: API_TYPES.PUT,
                payload: null
            },
        },
        configurations: {
            get: {
                url: 'config/',
                type: API_TYPES.GET,
                param: null
            },
            create: {
                url: 'config/',
                type: API_TYPES.POST,
                payload: null,
            },
            update: {
                url: 'config/',
                type: API_TYPES.PUT,
                payload: null,
            },
            activate: {
                url: 'config/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            getAuditCategories: {
                url: 'config/auditCategory/',
                type: API_TYPES.GET,
            },
            getProductAllType: {
                url: 'config/productType/all',
                type: API_TYPES.GET,
            },
            getAllAuditTypes: {
                url: 'config/auditType/grid',
                type: 'GET'
            },
            getAllAttributes: {
                url: 'config/attribute/',
                type: 'GET'
            },
            getAllCheckpointCategories: {
                url: 'config/checkpointCategory/grid',
                type: 'GET'
            },
            getAllDefectTypeCategories: {
                url: 'config/defectTypeCategory/',
                type: 'GET'
            },
            getAllPenalties: {
                url: 'config/penalty/',
                type: 'GET'
            },
            getConfigurationDetails: {
                url: 'config/{id}/details',
                type: API_TYPES.GET,
                paramList: { id: null }
            },
            viewJson: {
                url: 'config/{id}/json',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            },
            viewReportTemplate: {
                url: 'config/{id}/template',
                type: API_TYPES.GET,
                id: null,
                responseType: 'text',
                paramList: { id: null }
            },
            getCategories: {
                url: 'category/all',
                type: 'GET'
            },
            getCategoriesByUser: {
                url: 'category/byUser',
                type: 'GET'
            },
            getCategoryMappedUser: {
                url: 'userCategoryMapping/{categoryUid}/getByCategory',
                type: API_TYPES.GET,
                paramList: { categoryUid: null }
            }
        },
        referenceData: {
            get: {
                url: 'config/refdata/',
                type: API_TYPES.GET,
            },
            activate: {
                url: 'mtcConfig/{id}/activate',
                type: API_TYPES.PUT,
                id: null,
                payload: null,
                paramList: { id: null }
            },
            deActivate: {
                url: 'mtcConfig/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            getMtcRefenceDataById: {
                url: 'config/refdata/{id}',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            },
            saveOrUpdateMtcRefenceData: {
                url: 'config/refdata/',
                type: API_TYPES.PUT,
                id: null,
                paramList: null
            },
            getMtcCertTypeByUid: {
                url: 'config/mtcConfig/{id}',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            },
            saveOrUpdateMtcCertificateType: {
                url: 'config/mtcConfig/',
                type: API_TYPES.PUT,
                payload: null
            },
            export: {
                url: 'config/checkpoint/optiontype/download',
                type: API_TYPES.GET,
            }
        },
        visualReferenceData: {
            get: {
                url: 'pkg/packageInspectionReferenceData/',
                type: API_TYPES.GET,
            },
            activate: {
                url: 'mtcConfig/{id}/activate',
                type: API_TYPES.PUT,
                id: null,
                payload: null,
                paramList: { id: null }
            },
            deActivate: {
                url: 'mtcConfig/{id}',
                type: API_TYPES.DELETE,
                id: null
            },
            getMtcRefenceDataById: {
                url: 'config/refdata/{id}',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            },
            saveOrUpdateMtcCertificateType: {
                url: 'config/mtcConfig/',
                type: API_TYPES.PUT,
                payload: null
            },
        },
        facilities: {
            get: {
                url: 'facility/',
                type: API_TYPES.GET,
            },
            saveOrUpdate: {
                url: 'facility/',
                type: API_TYPES.POST,
                payload: null
            },
            viewCams: {
                url: 'facility/{id}/cam',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            }
        },
        facilityEdge: {
            get: {
                url: 'pkg/facilityEdge/',
                type: API_TYPES.GET,
                param: null
            },
            activate: {
                url: 'pkg/facilityEdge/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'pkg/facilityEdge/{id}',
                type: API_TYPES.DELETE,
                id: null
            },
            updateFacilityEdge: {
                url: 'pkg/facilityEdge/',
                type: API_TYPES.PUT,
                payload: null,
            },
            create: {
                url: 'pkg/facilityEdge/',
                type: API_TYPES.POST,
                payload: null,
            },
            downloadEdges: {
                url: 'pkg/facilityEdge/download',
                type: API_TYPES.GET,
            }
        },
        facilityCamera: {
            get: {
                url: 'pkg/facilityCamera/',
                type: API_TYPES.GET,
                param: null
            },
            activateFacilityCamera: {
                url: 'pkg/facilityCamera/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null
            },
            deactivateFacilityCamera: {
                url: 'pkg/facilityCamera/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            updateFacilityCamera: {
                url: 'pkg/facilityCamera/',
                type: API_TYPES.PUT,
                payload: null,
            },
            updateFacilityCameraConfig: {
                url: 'pkg/facilityCameraConfig/{id}',
                type: API_TYPES.PUT,
                payload: null,
            },
            create: {
                url: 'pkg/facilityCamera/',
                type: API_TYPES.POST,
                payload: null,
            },
            downloadCameras: {
                url: 'pkg/facilityCamera/download',
                type: API_TYPES.GET,
            }
        },

        facilityNotifier: {
            get: {
                url: 'pkg/facilityNotifier/',
                type: API_TYPES.GET,
                param: null
            },
            activate: {
                url: 'pkg/facilityNotifier/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'pkg/facilityNotifier/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            },
            update: {
                url: 'pkg/facilityNotifier/',
                type: API_TYPES.PUT,
                payload: null,
            },
            create: {
                url: 'pkg/facilityNotifier/',
                type: API_TYPES.POST,
                payload: null,
            },
            downloadNotifiers: {
                url: 'pkg/facilityNotifier/download',
                type: API_TYPES.GET,
            }
        },
        cameraZone: {
            getCames: {
                url: 'facility/cam',
                type: API_TYPES.GET,
            },
            getCamByUid: {
                url: 'facility/cam/{id}',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            },
            saveOrUpdateFacilityCam: {
                url: 'facility/cam',
                type: API_TYPES.POST,
                payload: null
            },
            uploadCamImage: {
                url: 'facility/{id}/sampleFile',
                type: API_TYPES.POST,
                id: null,
                payload: null,
                paramList: { id: null }
            },
            getSampleFile: {
                url: 'facility/{id}/sampleFile',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            }
        },
        cadApp: {
            getCadApp: {
                url: 'cad/appBundle',
                type: API_TYPES.GET,
            },
        },
        equipment: {
            get: {
                url: 'config/equipment/',
                type: API_TYPES.GET,
            },
            create: {
                url: 'config/equipment/',
                type: API_TYPES.PUT,
                payload: null
            },
            activate: {
                url: 'config/mtcConfig/{id}/activate',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            deactivate: {
                url: 'config/mtcConfig/{id}',
                type: API_TYPES.DELETE,
                id: null,
                paramList: { id: null }
            }
        },
        schedulePermission: {
            get: {
                url: 'permissions',
                type: API_TYPES.GET,
            },
            update: {
                url: "permissions",
                type: API_TYPES.PUT,
            },
            getAuditTypes: {
                url: "config/auditType/0/sap",
                type: API_TYPES.GET,
            },
            getAuditTypesWithoutSapFlag: {
                url: "config/auditType",
                type: API_TYPES.GET,
            }
        },
        designCodeAttachment: {
            getAttachements: {
                url: "designCode/document/all",
                type: API_TYPES.GET
            },
            uploadAttachment: {
                url: "designCode/{designCode}/{attachmentType}",
                type: API_TYPES.POST,
                paramList: { designCode: '', attachmentType: '' }
            },
            designCodePictureUpload: {
                url: "designCode/picturesUpload",
                type: API_TYPES.POST,
            },
            getAttachmentListByDesignCode: {
                url: "designCode/document/{designCode}",
                type: API_TYPES.GET,
                paramList: { designCode: '' }
            },
            getAttachmentByDesignCodeAndAttachmentType: {
                url: "designCode/{designCode}/{attachmentType}",
                type: API_TYPES.GET,
                paramList: { designCode: '', attachmentType: '' }
            },
        },

    },
    visual: {
        facilityNotifications: {
            getNotifications: {
                url: 'pkg/facilityNotificationHistory/',
                type: API_TYPES.GET,
                param: null
            },
            downloadNotifiers: {
                url: 'pkg/facilityNotificationHistory/download',
                type: API_TYPES.GET,
            }
        },
        packageinspection: {
            get: {
                url: 'pkg/visual/package/',
                type: API_TYPES.GET,
                param: null,
                externalUrl: true
            },
            getVisualRefData: {
                url: 'pkg/visual/package/refData',
                type: API_TYPES.GET,
                externalUrl: true
            },
            addVisualPackage: {
                url: 'pkg/visual/package/image',
                type: API_TYPES.POST,
                payload: null,
                externalUrl: true,
            },
            uploadVisualPackageDimensions: {
                url: 'pkg/visualInspection/{id}/image/dimension/topView',
                type: API_TYPES.POST,
                payload: null,
                id: null,
                paramList: { id: null }
            },
            reProcessInspection: {
                url: 'pkg/visual/package/{id}/reproc',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                externalUrl: true,
                paramList: { id: null }
            },
            extractPackageInspectionColors: {
                url: 'pkg/visual/package/{id}/colors',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                externalUrl: true,
                paramList: { id: null }
            },
            extractPackageInspectionDimensions: {
                url: 'pkg/visual/package/{id}/dimensions',
                type: API_TYPES.POST,
                payload: null,
                id: null,
                externalUrl: true,
                paramList: { id: null }
            },
            ocrPackage: {
                url: 'pkg/visual/package/{id}/ocr',
                type: API_TYPES.PUT,
                payload: null,
                id: null,
                externalUrl: true,
                paramList: { id: null }
            },
            getPackageInspectionResponses: {
                url: 'pkg/visual/package/{id}/response',
                type: API_TYPES.GET,
                id: null,
                externalUrl: true,
                paramList: { id: null }
            },
            validatePackageInspection: {
                url: 'pkg/visual/package/{id}/validate',
                type: API_TYPES.GET,
                id: null,
                externalUrl: true,
                paramList: { id: null }
            },
            getPackageInspectionColors: {
                url: 'pkg/visual/package/{id}/colors',
                type: API_TYPES.GET,
                id: null,
                externalUrl: true,
                paramList: { id: null }
            },
            getDimensions: {
                url: 'pkg/visual/package/{id}/dimensions',
                type: API_TYPES.GET,
                id: null,
                externalUrl: true,
                paramList: { id: null }
            }
        },
        surfaceInspection: {
            get: {
                url: 'pkg/visualInspection/{id}',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            },
            create: {
                url: 'pkg/visualInspection/image/{id}',
                type: API_TYPES.POST,
                payload: null,
                id: null,
                paramList: { id: null }
            },
        },
        labelCheck: {
            get: {
                url: 'pkg/visual/package/response',
                type: API_TYPES.GET,
            },
        }
    },
    videoAnalytics: {
        licensePlate: {
            get: {
                url: 'licPlate/',
                type: API_TYPES.GET,
                externalUrl: true
            },
            extractLicensePlate: {
                url: 'licPlate/{id}/ocr',
                type: API_TYPES.PUT,
                externalUrl: true,
                paylod: null,
                id: null,
                paramList: { id: null }
            },
            reProcesstLicensePlate: {
                url: 'licPlate/{id}/reproc',
                type: API_TYPES.PUT,
                externalUrl: true,
                paylod: null,
                id: null,
                paramList: { id: null }
            },
            getValidationMessages: {
                url: 'licPlate/{id}/validate',
                type: API_TYPES.GET,
                externalUrl: true,
                paylod: null,
                id: null,
                paramList: { id: null }
            }
        }
    },
    documentManagement: {
        docManagement: {
            get: {
                url: 'docmgmt/doc/',
                type: API_TYPES.GET,
                externalUrl: true
            },
            getTree: {
                url: 'docmgmt/doc/{id}/tree',
                type: API_TYPES.GET,
                id: null,
                paramList: { id: null }
            }
        }
    },
    testingModule: {
        testRequests: {
            get: {
                url: 'testProtocol/testRequests/all',
                type: API_TYPES.GET,
            },
            getTestRequestsById: {
                url: 'testProtocol/testRequests/{uid}',
                type: API_TYPES.GET,
                paramList: { uid: null }
            },
            getTestRequestsByAuditId: {
                url: 'testProtocol/testRequests/byAudit/{auditId}',
                type: API_TYPES.GET,
                paramList: { auditId: null }
            },
            exportTRF: {
                url: '',
                type: API_TYPES.GET,
            },
            cancelTRF: {
                url: 'testProtocol/testRequests/{uuid}/cancel',
                type: API_TYPES.PUT,
                paramList: { uuid: null }
            }

        }
    },
    testRequests: {
        protocol: {
            create: {
                url: 'testProtocol/',
                type: API_TYPES.POST,
            },
            get: {
                url: 'testProtocol/',
                type: API_TYPES.GET,
                externalUrl: false
            },
            getProtocol: {
                url: 'testProtocol/{uid}',
                type: API_TYPES.GET,
                externalUrl: true,
                paramList: { id: null }
            },
            getProtocolRequirement: {
                url: 'testProtocol/requirement',
                type: API_TYPES.GET,
                externalUrl: true
            },
            getProtocolRequirementByProductType: {
                url: 'testProtocol/requirement/productType/{productTypeUid}',
                type: API_TYPES.GET,
                externalUrl: true,
                paramList: { id: null }
            },
            getIncidents: {
                url: 'testProtocol/incidents',
                type: API_TYPES.GET,
            },
            getPolicyDetailsByUid: {
                url: 'testProtocol/{policyUid}',
                type: API_TYPES.GET,
                paramList: { policyUid: null }
            },
            updateCheckpointByUid: {
                url: 'config/checkpoint/',
                type: API_TYPES.PUT
            },
            executeCheckpoint: {
                url: 'testProtocol/autoExecute',
                type: API_TYPES.GET
            },
        }
    },
    sampling: {
        getCollectionRequests: {
            url: 'sourcing/request/{collectionRequestUid}/details',
            type: API_TYPES.GET,
            paramList: { collectionRequestUid: '' }
        },
        createSampleRequest: {
            url: 'sourcing/sampleRequest',
            type: API_TYPES.POST,
            payload: null
        },
        getRequests: {
            url: 'sourcing/sampleRequest',
            type: API_TYPES.GET,
        },
        getResponses: {
            url: 'sourcing/sampleRequest/response',
            type: API_TYPES.GET,
        },
        getRequestDetailsById: {
            url: 'sourcing/sampleRequest/{sampleRequestUid}',
            type: API_TYPES.GET,
            paramList: { sampleRequestUid: null }
        },
        getResponseByRequesById: {
            url: 'sourcing/sampleRequest/response/{sampleRequestUid}/byrequest',
            type: API_TYPES.GET,
            paramList: { sampleRequestUid: null }
        },
        getResponseDetailsById: {
            url: 'sourcing/sampleRequest/response/{sampleRequestUid}',
            type: API_TYPES.GET,
            paramList: { sampleRequestUid: null }
        },
        acceptSample: {
            url: 'sourcing/sampleRequest/response/vendor/accept',
            type: API_TYPES.PUT,
        },
        rejectSample: {
            url: "sourcing/sampleRequest/response/vendor/reject",
            type: API_TYPES.PUT,
        },
        dispatchSample: {
            url: "sourcing/sampleRequest/response/vendor/dispatched",
            type: API_TYPES.PUT,
        },
        receivedSample: {
            url: "sourcing/sampleRequest/response/sampleReceived",
            type: API_TYPES.PUT,
        },
        feedbackSample: {
            url: "sourcing/sampleRequest/response/feedbacks",
            type: API_TYPES.POST,
        },
        getdesignCodefeedbackSample: {
            url: 'sourcing/sampleRequest/designcode/{designCodeUid}/feedbacks',
            type: API_TYPES.GET,
            paramList: { designCodeUid: null }
        },
        getRawMaterialfeedbackSample: {
            url: 'sourcing/sampleRequest/rawMaterial/{rawMaterialUid}/feedbacks',
            type: API_TYPES.GET,
            paramList: { rawMaterialUid: null }
        },
        sampleSubmitForApproval: {
            url: "sourcing/sampleRequest/response/submit",
            type: API_TYPES.PUT,
        },
        approveIfApproveRequired: {
            url: "sourcing/sampleRequest/response/approve",
            type: API_TYPES.PUT,
        },
        rejectIfApproveRequired: {
            url: "sourcing/sampleRequest/response/rejected",
            type: API_TYPES.PUT,
        },
        dropIfApproveRequired: {
            url: "sourcing/sampleRequest/response/drop",
            type: API_TYPES.PUT,
        },
        saveOrUpdateSampleFeedback: {
            url: "sourcing/sampleRequest/response/feedback",
            type: API_TYPES.PUT,
        },
        getRejectedFeedback: {
            url: "sourcing/quotation/response/{poNUmber}/getPOFeedback",
            type: API_TYPES.GET,
            paramList: { poNUmber: null }
        }

    },
    userManagement: {
        getUsersByRole: {
            url: 'user/role/{role}',
            type: API_TYPES.GET,
            paramList: { role: '' }
        },
    },
    PO: {
        getPOList: {
            url: 'sourcing/po/getAllPoByUser',
            type: "GET",
        },
        getPODetailsByUid: {
            url: 'sourcing/po/{uid}',
            type: API_TYPES.GET,
            paramList: { uid: null }
        },
        getPODetailsByPoNumber: {
            url: 'sourcing/po/{poNumber}/byNumber',
            type: API_TYPES.GET,
            paramList: { poNumber: null }
        },
        getDesignCodeActivityByPoNumber: {
            url: 'sourcing/activity/designcode/{poNumber}/byPoNumber',
            type: API_TYPES.GET,
            paramList: { poNumber: null }
        },
        importPOList: {
            url: 'sourcing/po/response/{responseUid}/po',
            type: "POST",
            paramList: { responseUid: null }
        },
        importPOShipmentList: {
            url: 'sourcing/po/shipment/import',
            type: API_TYPES.POST,
        },
        getShipmentListByPONumber: {
            url: 'sourcing/po/shipment/{poNumber}/getByPo',
            type: API_TYPES.GET,
            paramList: { poNumber: null }
        },
        closeShipment: {
            url: 'sourcing/po/shipment/close/articleWise',
            type: API_TYPES.PUT,
        },
        closeGRN: {
            url: 'sourcing/po/grn/close/articleWise',
            type: API_TYPES.PUT,
        },
        closeArticle: {
            url: 'sourcing/po/article/close/articleWise',
            type: API_TYPES.PUT,
        },
        closePo: {
            url: 'sourcing/po/closePo/byArticles',
            type: API_TYPES.POST,
        },
        saveShipment: {
            url: 'sourcing/po/shipment',
            type: API_TYPES.POST,
        },
        updateShipmentGRN: {
            url: 'sourcing/po/shipment/grn',
            type: API_TYPES.PUT,
        },
        submitPOByNumber: {
            url: 'sourcing/po/poNumber/{poNumber}/submit',
            type: API_TYPES.PUT,
            paramList: { poNumber: null }
        },
        getSizeCodesDetailsForFg: {
            url: 'sourcing/po/{designcodeUid}/ByDesigncode',
            type: API_TYPES.GET,
            paramList: { designcodeUid: null }

        },

        acceptPOByNumber: {
            url: 'sourcing/po/poNumber/{poNumber}/accept',
            type: API_TYPES.PUT,
            paramList: { poNumber: null }
        },
        rejectPOByNumber: {
            url: 'sourcing/po/poNumber/{poNumber}/reject',
            type: API_TYPES.PUT,
            paramList: { poNumber: null }
        },
        closeArticlesByPoUid: {
            url: 'sourcing/po/{poUid}/closePo',
            type: API_TYPES.PUT,
            paramList: { poUid: null }
        },
        getShipmentDetailsByPonumber: {
            url: 'sourcing/po/{poNumber}/shipmentCodeWise',
            type: API_TYPES.GET,
            paramList: { poNumber: null }
        },
        getShipmentDetailsByShipmentnumber: {
            url: 'sourcing/po/shipment/{shipmentNumber}/articleWise',
            type: API_TYPES.GET,
            paramList: { shipmentNumber: null }
        },
        getPoDetailsByShipmentCode: {
            url: 'sourcing/po/{poNumber}/{shipmentCode}/byShipmentCode',
            type: API_TYPES.GET,
            paramList: { shipmentCode: null, poNumber: null }
        },
        deletePOByNumber: {
            url: 'sourcing/po/poNumber/{poNumber}/delete',
            type: API_TYPES.PUT,
            paramList: { poNumber: null }
        },
        getShipmentList: {
            url: 'sourcing/po/shipment',
            type: API_TYPES.GET,
        },
        // getPoDetailsByShipmentNumber:{
        //     url:'sourcing/po/shipment/{shipmentNo}/byShipmentNo',
        //     type:'GET',
        //     paramList :{shipmentNo :null}
        // }
        getShipmentDetailsByShipmentNo: {
            url: 'sourcing/po/shipment/{shipmentNo}/byShipmentNo',
            type: API_TYPES.GET,
            paramList: { shipmentNo: null }
        },
        getShipmentSizeWiseData: {
            url: 'sourcing/po/shipment/{shipmentNo}/{designcodeUid}/ByDesigncode',
            type: API_TYPES.GET,
            paramList: { shipmentNo: null, designcodeUid: null }
        },
        getActivitiesByPo: {
            url: 'sourcing/productionActivityConfig/{processType}',
            type: API_TYPES.GET,
            paramList: { processType: null }
        },
        downloadPOTemplate: {
            url: 'sourcing/po/downloadTemplate',
            type: API_TYPES.GET,
        },
        downloadPurchaseOrderTemplate: {
            // url: 'config/downloadTemplate/PurchaseOrder',
            url: 'config/downloadTemplate/{PurchaseOrder}',
            paramList: { PurchaseOrder: null },
            type: API_TYPES.GET,
        },
        deletePo: {
            url: 'saleOrder/po/delete/{purchaseOrderNo}',
            type: API_TYPES.DELETE,
            paramList: { purchaseOrderNo: null }
        },
        downloadPOShipmentTemplate: {
            url: 'sourcing/po/shipment/downloadTemplate',
            type: API_TYPES.GET,
        },
        exportPO: {
            url: 'sourcing/po/export',
            type: API_TYPES.POST,
            payload: null
        },
        exportShipment: {
            url: 'sourcing/po/shipment/export',
            type: API_TYPES.POST,
            payload: null
        },
        getDailyProductionTillDateDataByPo: {
            url: 'sourcing/productionActivityStatus/{poNumber}/getUpdatesTillDate/{date}',
            type: API_TYPES.GET,
            paramList: { poNumber: null, date: null }
        },
        getDailyProductionByDateDataByPo: {
            url: 'sourcing/productionActivity/{poNumber}/{date}/getByDate',
            type: API_TYPES.GET,
            paramList: { poNumber: null, date: null }
        },
        getDailyProductionDataByPo: {
            url: 'sourcing/productionActivity/{poNumber}/byPoNumber',
            type: API_TYPES.GET,
            paramList: { poNumber: null }
        },
        updateDailyProductionDataByPo: {
            url: 'sourcing/productionActivity/{poNumber}/update',
            type: API_TYPES.PUT,
            paramList: { poNumber: null }
        },
        updatePoDetails: {
            url: 'sourcing/po/{poNumber}/updateComments',
            type: API_TYPES.PUT,
            payload: null,
            paramList: { poNumber: null }
        },
        getTASummaryByDesignCode: {
            url: 'sourcing/activity/{poNumber}/designcode/{designCodeUid}/summary/byDesigCode',
            type: API_TYPES.GET,
            paramList: { poNumber: null, designCodeUid: null }
        },
        downloadPOReport: {
            url: 'sourcing/po/{poNumber}/generateReport',
            type: API_TYPES.GET,
            paramList: { poNumber: null }
        },
        getShipmentDetailsByArticleNumber: {
            url: 'sourcing/po/shipment/poNumber/{poNumber}/{productCode}/getByProductCode',
            type: API_TYPES.GET,
            paramList: { poNumber: null, productCode: null }
        },
        updatePORejectFeedback: {
            url: 'sourcing/po/poNumber/updateFeedback',
            type: API_TYPES.PUT,
        },
        startPOActivity: {
            url: 'sourcing/currentActivity/start',
            type: API_TYPES.POST,
        },
        getInvoiceList: {
            url: 'sourcing/currentActivity/summary',
            type: API_TYPES.GET
        },
        getPendingInvoiceList: {
            url: 'sourcing/activity/pendingActivities',
            type: API_TYPES.GET
        },
        vieBomDetails: {
            url: "sourcing/bom/{finishedGoodsId}/byFinishedGoods",
            type: "GET",
            paramList: { finishedGoodsId: null }
        }

    },

    TechSpecification: {
        getList: {
            url: 'sourcing/tech/specifications/config/{productTypeUid}/byProductType',
            type: API_TYPES.GET,
            paramList: { productTypeUid: null }
        },
        getListByGroup: {
            url: 'sourcing/tech/specifications/config/byGroupUid',
            type: API_TYPES.POST,
        },
        getListDataByGroupUid: {
            url: 'sourcing/tech/specifications/config/byGroupUid',
            type: API_TYPES.POST,
        },
        addConfiguration: {
            url: 'sourcing/tech/specifications/config',
            type: API_TYPES.POST,
        },
        getConfigurationByUid: {
            url: 'sourcing/tech/specifications/config/{uuid}',
            paramList: { uuid: null },
            type: API_TYPES.GET,
        },
        updateConfiguration: {
            url: 'sourcing/tech/specifications/config',
            type: API_TYPES.PUT,
        },
        deleteConfiguration: {
            url: 'sourcing/tech/specifications/config/{configUuid}/delete',
            type: API_TYPES.DELETE,
            paramList: { configUuid: null }
        },
        getProductTypeByDesignCodeUid: {
            url: 'sourcing/request/designCode/{designCodeUid}',
            type: API_TYPES.GET,
            paramList: { designCodeUid: null }
        },
        getSpecificationsByUid: {
            url: 'sourcing/tech/specifications/{responseUid}/response/{designCodeUid}/byResponseDesignCode',
            type: API_TYPES.GET,
            paramList: { responseUid: '', designCodeUid: '' }
        },
        getSpecGroupList: {
            url: 'sourcing/spec/group',
            type: API_TYPES.GET,
        },
        downloadSpecGroupTemplate: {
            url: 'sourcing/spec/group/template',
            type: API_TYPES.GET,
        },
        downloadSpecConfigTemplate: {
            url: 'sourcing/tech/specifications/config/template',
            type: API_TYPES.GET,
        },
        getSpecGroupListByCategory: {
            url: 'sourcing/spec/group/{categoryUid}/byCategory ',
            type: API_TYPES.GET,
            paramList: { categoryUid: '' }
        },
        addSpecGroup: {
            url: 'sourcing/spec/group',
            type: API_TYPES.POST,
        },
        updateSpecGroup: {
            url: 'sourcing/spec/group',
            type: API_TYPES.POST,
        },
        deleteSpecGroup: {
            url: 'sourcing/spec/group/{uuid}/delete',
            type: API_TYPES.DELETE,
            paramList: { uuid: null }
        },
        exportGroup: {
            url: 'sourcing/impex/TechnicalSpecificationGroup/export',
            type: API_TYPES.GET,
        },
        exportGroupTechSpecification: {
            url: 'sourcing/impex/TechnicalSpecificationsConfig/export',
            type: API_TYPES.GET,
        },
        mapCategoeyToGroup: {
            url: 'sourcing/roadmap/{roadmapuuid}/mapGroup',
            type: API_TYPES.PUT,
            paramList: { roadmapuuid: null }
        },
        getMasterSpecsUOM: {
            url: 'sourcing/general/uom',
            type: API_TYPES.GET,
        },
        getExistingGroupList: {
            url: 'sourcing/spec/group/byUids',
            type: API_TYPES.POST,
        },
        getExistingSpecificationList: {
            url: 'sourcing/tech/specifications/config/section/byGroupUid',
            type: API_TYPES.POST,
        }
    },

    qualityDashBoard: {
        getStatsByAllAuditType: {
            url: 'dashboard/statsByAllAuditType',
            type: API_TYPES.POST,
        },
        getDesignCodeWiseDataForTable: {
            url: 'dashboard/designCodeWise',
            type: API_TYPES.POST,
        },
        getStatsByAuditType: {
            url: 'dashboard/statsByAuditType',
            type: API_TYPES.POST,
        },
        getDefectCount: {
            url: 'dashboard/defectCount',
            type: API_TYPES.POST,
        },
        getCheckpointSummary: {
            url: 'dashboard/checkpointDefectSummary',
            type: API_TYPES.POST,
        },
        getDashBoardFilterList: {
            url: 'masterTenantFilters',
            type: API_TYPES.GET,
        },
        getOptionListForFilters: {
            url: 'dashboard/designCode/allFilterData',
            type: API_TYPES.POST,
        },
        getTopNDefects: {
            url: 'dashboard/topDefectsCount',
            type: API_TYPES.POST,
        },
        getNotificationList: {
            url: 'audit/actionables/{userId}',
            type: API_TYPES.GET,
            paramList: { userId: null }
        }

    },
    warranty: {
        getAllWarranties: {
            type: API_TYPES.GET,
            url: "digitize_warranty/all",

        },
        acceptWarranty: {
            type: API_TYPES.PUT,
            url: "digitize_warranty/approve/{warrantyNumber}",
            paramList: { warrantyNumber: '' }
        },
        rejectWarranty: {
            type: API_TYPES.PUT,
            url: "digitize_warranty/reject/{warrantyNumber}",
            paramList: { warrantyNumber: '' }
        }
    },
    policyDashboard: {
        getOption: {
            type: API_TYPES.POST,
            url: "dashboard/statsByOptionValues",
        },
        classificationDocumentUpload: {
            type: API_TYPES.POST,
            url: "documentClassification/documentUpload",
        },
        getDocumentClassificationList: {
            type: API_TYPES.GET,
            url: "documentClassification",
        }
    },
    artwork: {
        getDocRefereAll: {
            type: API_TYPES.GET,
            url: "sourcing/docreference/getAll",
        },
        getDocRefereByUid: {
            type: API_TYPES.GET,
            url: "sourcing/docreference/getByUuid/{uuid}",
            paramList: { uuid: null }
        }
    }


}

export let APP_CONFIG = new InjectionToken<IAppConfig>('app.config');