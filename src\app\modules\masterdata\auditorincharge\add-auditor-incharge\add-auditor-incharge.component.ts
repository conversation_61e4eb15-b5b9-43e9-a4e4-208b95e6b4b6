import { ChangeDetectorRef, Component, EventEmitter, Inject, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { CountryList } from 'app/core/qinspect/_common/country';
import { GenericList } from 'app/core/qinspect/_common/list';
import { Util } from 'app/core/qinspect/_common/util';
import { Qi<PERSON>uditor } from 'app/core/qinspect/_model/auditor.model';
import { QiAuditorIncharge } from 'app/core/qinspect/_model/auditorincharge.model';
import { countryList, FORM_CONTROL_TYPES, HTTP_STATUS, TABLE_ACTION_TYPES } from 'app/enum-constants';
import { BuildFormUtilityService } from 'app/services/build-form-utility.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';
import * as sha512 from 'js-sha512';
import { ReplaySubject } from 'rxjs';
import { distinctUntilChanged, takeUntil } from 'rxjs/operators';

import { AUDITOR_INCHARGE_FORM_MODEL } from './auditor-incharge-form-model';

@Component({
  selector: 'add-auditor-incharge',
  templateUrl: './add-auditor-incharge.component.html',
  styleUrls: ['./add-auditor-incharge.component.scss']
})
export class AddAuditorInchargeComponent implements OnInit, OnDestroy {
  rec: QiAuditorIncharge = new QiAuditorIncharge("");
  @Output() close: EventEmitter<any> = new EventEmitter();
  status: string;
  hdr: string = 'Add Auditor Incharge';
  ctryList: CountryList;
  aicList: GenericList<QiAuditorIncharge>;
  auditorleads: QiAuditorIncharge[] = [];
  auditors: any;
  tenantUid = this._commonService.getTenantUid();
  auditorsList: QiAuditor[] = [];
  auditor: QiAuditor[] = [];
  timeoutIdForScroll: any;

  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  auditorInchargeForm: FormGroup;
  public fields: any[] = [];
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  countryList: any[] = countryList;
  resetPasswordList: any[] = [{ label: "Set password same as username", value: "same" },
  { label: "Create new password", value: "new" }
  ];
  filteredPasswordList: any[];
  roleList: any[] = [{ label: "AUDITOR", value: "AUDITOR" }, { label: "AUDITORINCHARGE", value: "AUDITORINCHARGE" }];
  constructor(private invokeService: InvokeService,
    private cdRef: ChangeDetectorRef,
    private _commonService: CommonService,
    private _buildFormUtilityService: BuildFormUtilityService,
    public dialogRef: MatDialogRef<AddAuditorInchargeComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any) { }


  public ngOnInit(): void {
    this.status = this.data.status;
    this.aicList = new GenericList<QiAuditorIncharge>();
    this.getAuditors();
    this.getAuditorsIncharge();
    this.buildForm();
    this.configureForm();
  }

  private buildForm(): void {
    this.filteredPasswordList = this.resetPasswordList;
    let formFields = this._buildFormUtilityService.getFormControlsFields(AUDITOR_INCHARGE_FORM_MODEL);
    this.fields = this._buildFormUtilityService.getFieldsList();
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'role', this.roleList);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'country', this.countryList);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'password', this.filteredPasswordList);
    this.auditorInchargeForm = new FormGroup(formFields);
  }

  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        this.hdr = "Add Auditor Incharge";
        this.auditorInchargeForm.get('setPasswordAsUserName').setValue(true);
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Auditor Incharge";
        this.auditorInchargeForm.get('code').disable();
        this.setDataForm();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = 'View Auditor Incharge';
        this.setDataForm();
        this.auditorInchargeForm.disable();
        break;
    }
    this.onFormChanges();
  }

  private onFormChanges(): void {
    this.auditorInchargeForm.get('role').setValue(['AUDITOR', 'AUDITORINCHARGE']);
    this.auditorInchargeForm.get('role').disable();
    if (this.tenantUid == 'godrej') {
      if (this.status == TABLE_ACTION_TYPES.CREATE) {
        this.auditorInchargeForm.get('setPasswordAsUserName').disable();
      }
    }

    this.auditorInchargeForm.get('resetPassword').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          if (response) {
            this.rec.resetPassword = 0;
            this.fields = this._buildFormUtilityService.showHideField(this.fields, 'password', true, this.auditorInchargeForm);
          } else {
            this.rec.resetPassword = '';
            this.fields = this._buildFormUtilityService.showHideField(this.fields, 'password', false, this.auditorInchargeForm);
            this.hideNewPasswordField();
          }
        }
      });

    this.auditorInchargeForm.get('password').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          if (response) {
            this.rec.resetPassword = true;
            if (response === 'Create new password') {
              this.rec.setPasswordAsUserName = this.auditorInchargeForm.get('setPasswordAsUserName').setValue(false);
              this.showNewPasswordField();
            }
            else {
              this.rec.setPasswordAsUserName = this.auditorInchargeForm.get('setPasswordAsUserName').setValue(true);
              this.hideNewPasswordField();
            }
          }
        }
      });

  }

  private hideNewPasswordField(): void {
    this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "newPassword", { required: false });
    this.auditorInchargeForm.get("newPassword").clearValidators();
    this.auditorInchargeForm.get("newPassword").setValue('');
    this.auditorInchargeForm.updateValueAndValidity();
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'newPassword', false, this.auditorInchargeForm);
    this.cdRef.detectChanges();
  }

  private showNewPasswordField(): void {
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'newPassword', true, this.auditorInchargeForm);
    this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "newPassword", { required: true });
    this.auditorInchargeForm.get("newPassword").setValidators(Validators.required);
    this.auditorInchargeForm.updateValueAndValidity();
    let el = document.getElementById('lastEle');
    this.timeoutIdForScroll = setTimeout(() => {
      el.scrollIntoView();
    }, 10)
    this.cdRef.detectChanges();
  }


  private setDataForm(): void {
    this.rec = this.data?.data;
    this.rec.resetPassword = this.auditorInchargeForm.get('resetPassword').setValue(false);
    this.auditorInchargeForm = this._buildFormUtilityService.setFormControlsValues(this.auditorInchargeForm, this.rec, this.fields);
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'setPasswordAsUserName', false, this.auditorInchargeForm);
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'resetPassword', true, this.auditorInchargeForm);
    this.auditorInchargeForm.get("role").setValue(this.rec.role.split(","));
    this.auditorInchargeForm.get("role").disable();
  }

  private getAuditors(): void {
    this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.auditor.getallauditors).pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.auditorsList = response;
            this.prepareAuditorsList();
          } else {
            this._commonService.error("Failed to get Data")
          }
        }, error: err => {
          this._commonService.handleError(err);
        }
      })
  }

  private prepareAuditorsList(): void {
    this.auditorsList.forEach(item => {
      if (item.inchargeUid) {
        item.inChargeName = this.auditor.find(ai => ai.uuid == item.inchargeUid)?.fullName;
      }
    });
    this.auditors = this.auditorsList;
    this.cdRef.detectChanges();
  }

  private getAuditorsIncharge(): void {
    this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.auditorincharge.get).pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.auditorleads = response;
            this.prepareAuditorsInCharge();
          } else {
            this._commonService.error("Failed to get Data")
          }
        }, error: err => {
          this._commonService.handleError(err);
        }
      })
  }

  private prepareAuditorsInCharge(): void {
    this.aicList.setList2(this.auditorleads, 'firstName', 'lastName', 'userName', 'uuid');
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'inchargeUid', this.aicList.list);
    this.cdRef.detectChanges();
  }

  setResetPassword(e) {
    let val = 0
    if (e.target?.checked) {
      val = 1
    }
    this.rec.resetPassword = val;
  }

  setToggleValue(e) {
    let val = 0
    if (e.checked) {
      val = 1
    }
    this.rec.setPasswordAsUserName = val;
  }

  changeResetPwdValue(e) {
    if (e.value == "same") {
      this.rec.setPasswordAsUserName = true;
    } else {
      this.rec.setPasswordAsUserName = false;
    }
  }

  public closeAddAuditor(): void {
    this.dialogRef.close(true);
  }

  public checkRole(): boolean {
    if (this.rec.role != undefined && this.rec.role.length == 0) {
      return true;
    }
    if (this.rec.role != undefined && !this.rec.role.includes("AUDITORINCHARGE")) {
      return false;
    } else {
      return true;
    }
  }

  public onSave(): void {
    this.rec.code = this.auditorInchargeForm.get('code').value;
    this.rec.firstName = this.auditorInchargeForm.get('firstName').value;
    this.rec.lastName = this.auditorInchargeForm.get('lastName').value;
    this.rec.role = this.auditorInchargeForm.get('role').value.toString();
    this.rec.inchargeUid = this.auditorInchargeForm.get('inchargeUid').value;
    this.rec.designation = this.auditorInchargeForm.get('designation').value;
    this.rec.contactNo = this.auditorInchargeForm.get('contactNo').value;
    this.rec.email = this.auditorInchargeForm.get('email').value;
    this.rec.addressLine1 = this.auditorInchargeForm.get('addressLine1').value;
    this.rec.altemail = this.auditorInchargeForm.get('altemail').value;
    this.rec.city = this._commonService.trimEdgeSpaces(this.auditorInchargeForm.get('city').value);
    this.rec.state = this._commonService.trimEdgeSpaces(this.auditorInchargeForm.get('state').value);
    this.rec.pincode = this.auditorInchargeForm.get('pincode').value;
    this.rec.country = this.auditorInchargeForm.get('country').value;
    this.rec.setPasswordAsUserName = this.auditorInchargeForm.get('setPasswordAsUserName').value;
    this.rec.mappedWith = this.rec.inchargeUid;

    if (this.data.data && this.data.data.uuid) {
      let password: string = this.auditorInchargeForm.get('newPassword').value;
      if (password) {
        this.rec.password = this.tenantUid == 'godrej' ? sha512.sha512(password) : password;
      } else
        this.rec.password = sha512.sha512(this.rec.userName);
    } else {
      this.rec.password = sha512.sha512(this.rec.code);
    }
    if (this.rec.uuid) {
      this._commonService.openDialogForConfirmation(`Are you sure you want to update Auditor Incharge: ${this.rec.userName} ?`).subscribe({
        next: response => {
          if (response) {
            APP_UI_CONFIG.masterdata.auditorincharge.update.paramList.id = this.rec?.uuid;
            this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.auditorincharge.update, null, this.rec.uuid, Util.clone(this.rec)).subscribe({
              next: response => {
                if (response && response.code == HTTP_STATUS.SUCCESS) {
                  this._commonService.success(`${this.rec.userName} updated successfully.`);
                  this.dialogRef.close(true);
                } else {
                  this._commonService.error(`Failed to Update ${this.rec.userName}.`);
                }
              }, error: error => {
                this._commonService.handleError(error);
              }
            })
          }
        }
      })
    } else {
      this._commonService.openDialogForConfirmation(`Are you sure you want to create Auditor Incharge: ${this.rec.firstName} ?`).subscribe({
        next: response => {
          if (response) {
            this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.auditorincharge.add, '', '', Util.clone(this.rec)).subscribe({
              next: response => {
                if (response && response.code == HTTP_STATUS.SUCCESS) {
                  this._commonService.success(`${this.rec.firstName} created successfully.`);
                  this.dialogRef.close(true);
                } else {
                  this._commonService.handleError(response);
                }
              }, error: error => {
                this._commonService.handleError(error);
              }
            })
          }
        }
      })
    }
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(true);
    this.$destroyed.complete();
    clearTimeout(this.timeoutIdForScroll);
  }

}
