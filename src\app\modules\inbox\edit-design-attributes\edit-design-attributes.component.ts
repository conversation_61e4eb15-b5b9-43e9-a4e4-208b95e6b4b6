import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { TA_ACTIVITY_STATUS, TA_INBOX_ITEMS_STATUS } from 'app/core/qinspect/_common/AppConstants';
import {
  DesigncodeActivityComments,
  DesigncodeActivityDetail,
} from 'app/core/qinspect/_model/DesigncodeActivityDetail.model';
import { DesigncodeActivityDetailAttribute } from 'app/core/qinspect/_model/DesigncodeActivityDetailAttribute.model';
import { CloseTimelineInboxItemList, TimelineInboxItem } from 'app/core/qinspect/_model/TimelineInboxItem.model';
import { QiUser } from 'app/core/qinspect/_model/user.model';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { FORM_CONTROL_TYPES } from 'app/enum-constants';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { CustomDateTimeTooltipComponent } from 'app/shared/custom-date-time-tooltip/custom-date-time-tooltip.component';
import { ImageViewerDialogComponent } from 'app/shared/image-viewer-dialog/image-viewer-dialog.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { ImgIconComponent } from 'app/shared/tfl-table-search-grid/grid-supporting-components/img-icon/img-icon.component';
import { environment } from 'environments/environment';
import { FileSystemFileEntry } from 'ngx-file-drop';
import { FileSaverService } from 'ngx-filesaver';

import { INBOX_LABEL_CONFIGURATION } from './inbox-label-configuration';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'edit-design-attributes',
  templateUrl: './edit-design-attributes.component.html',
  styleUrls: ['./edit-design-attributes.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class EditDesignAttributesComponent implements OnInit {
  @Output() onClose: EventEmitter<any> = new EventEmitter();
  user: QiUser;
  inboxItem: TimelineInboxItem;
  inboxItemUid: string
  fieldAttributes: DesigncodeActivityDetailAttribute[] = []
  actionAttributes: DesigncodeActivityDetailAttribute[] = []
  basicAuth = this.globals.basicAuth;
  files: any[] = [];
  documentsList: any[] = [];
  comments: string = '';
  commentsList: any[] = [];
  documentList: any[] = [];
  showStatusDetails: boolean = false;
  isShowInboxAction: boolean = true;
  maxDate: Date = new Date();
  showFormHeader: boolean = false;
  @Input() status: number = 1;
  @Input() uuid: string = '';
  @Input() title: string = '';
  @Input() type: string = '';
  @Input() inboxItems: TimelineInboxItem[] = [];
  @Input() path: string = '';
  @Input() customParams: string = '';
  TA_INBOX_ITEMS_STATUS: any;
  public TA_ACTIVITY_STATUS = TA_ACTIVITY_STATUS;
  FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  configurationList: any[] = [];

  attachmentsColumnDefs2: any = [
    {
      headerName: "Type", field: "fileName", maxWidth: 70,
      cellRendererFramework: ImgIconComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data
    },
    { headerName: "Name", field: "fileName", sortable: true, unSortIcon: true, minWidth: 200 },
    {
      headerName: "File Size", field: "fileSize", sortable: true, unSortIcon: true, minWidth: 100,
      valueGetter: params => this.commonService.calculateFileSize(params.data.fileSize)
    },
    {
      headerName: "Attachment Type", field: "docType", sortable: true, minWidth: 100,
    },
    {
      headerName: "Created Date & Time", field: "createdTime", sortable: true, minWidth: 100,
      cellRendererFramework: CustomDateTimeTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data
    },
    {
      headerName: 'Actions',
      maxWidth: 100,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: params => {
        return { actions: this.prepareActionIconButtons2(params.data), onAction: action => this.onActions(action) }
      }
    }
  ]


  constructor(
    private _authService: AuthService,
    // private activityService: ActivityService,
    public _invokeService: InvokeService,
    public commonService: CommonService,
    public _router: Router,
    public globals: Globals,
    private http: HttpClient,
    public route: ActivatedRoute,
    private sanitizer: DomSanitizer,
    public dialog: MatDialog,
    public fileSaverService: FileSaverService,
  ) {

  }
  requestMap: any;
  ngOnInit() {
    this.inboxItemUid = this.uuid;
    this.user = this._authService.getLoggedInUser();
    this.initData();
    this.showStatusDetails = this.commonService.getTenantConfig().tenantUid != 'demo' ? false : true;
    if (this.status == TA_INBOX_ITEMS_STATUS.PENDING) {
      this.isShowInboxAction = true;
    } else {
      this.isShowInboxAction = false;
    }
  }

  initData2() {
    this.inboxItem = new TimelineInboxItem()
    this.inboxItem.name = "aa"
    let item = new DesigncodeActivityDetail()
    let attr1 = new DesigncodeActivityDetailAttribute()
    attr1.type = "text"
    attr1.name = "text box"
    attr1.description = "text box description"
    // attr1.attributeValue = '';

    let attr2 = new DesigncodeActivityDetailAttribute()
    attr2.type = "dropdown"
    attr2.name = "dropdown box"
    attr2.description = "drop down description"
    attr2.attributeValue = '';

    let attr3 = new DesigncodeActivityDetailAttribute()
    attr3.type = "checkbox"
    attr3.name = "checkbox box"
    attr3.description = "checkbox description"

    let attr4 = new DesigncodeActivityDetailAttribute()
    attr4.type = "file"
    attr4.name = "checkbox box"
    attr4.description = "file description"

    let attr5 = new DesigncodeActivityDetailAttribute()
    attr5.type = "date"
    attr5.name = "checkbox box"
    attr5.description = "file description"

    let attr6 = new DesigncodeActivityDetailAttribute()
    attr6.type = "toggle"
    attr6.name = "checkbox box"
    attr6.description = "file description"

    // attr3.attributeValue = '';


    item.attributes = [attr1, attr2, attr3, attr4, attr5, attr6]
    this.inboxItem.designcodeActivityDetail = item
  }

  public initData(): void {
    APP_UI_CONFIG.activity.getInboxItemDetails.paramList['id'] = this.inboxItemUid;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.getInboxItemDetails).subscribe(res => {
      this.inboxItem = res;
      if (this.inboxItem.activity) {
        if (this.inboxItem.activity.requestMap) {
          this.requestMap = JSON.parse(this.inboxItem.activity.requestMap)[0];
        }
        this.prepareActivityLabelConfiguration()
      }
      this.fieldAttributes = []
      this.actionAttributes = [];
      if (!this.showStatusDetails) {
        this.commentsList = this.inboxItem?.designcodeActivityDetail?.comments ? this.inboxItem?.designcodeActivityDetail?.comments : [];
        this.documentList = this.inboxItem?.designcodeActivityDetail?.documents ? this.inboxItem?.designcodeActivityDetail?.documents : [];
        if (this.inboxItem?.designcodeActivityDetail?.attributes != null) {
          this.showFormHeader = this.inboxItem?.designcodeActivityDetail?.attributes.some(item => (item.type != 'APPROVE' && item.type != 'REJECT'))
          for (var attr of this.inboxItem.designcodeActivityDetail.attributes) {
            if (attr.type.toUpperCase() == 'APPROVE' || attr.type.toUpperCase() == 'REJECT' || attr.type.toUpperCase() == 'BUTTON' || attr.type.toUpperCase() == 'SUBMIT' || attr.type.toUpperCase() == 'FAIL') {
              this.actionAttributes.push(attr);
            } else {
              this.fieldAttributes.push(attr)
            }
          }
        }
      }
      else {
        if (this.inboxItem?.activity?.attributes != null) {
          this.showFormHeader = this.inboxItem?.activity?.attributes.some(item => (item.type != 'APPROVE' && item.type != 'REJECT'))
          for (var atr of this.inboxItem.activity.attributes) {
            if (atr.type.toUpperCase() == 'APPROVE' || atr.type.toUpperCase() == 'REJECT' || atr.type.toUpperCase() == 'BUTTON' || atr.type.toUpperCase() == 'SUBMIT' || atr.type.toUpperCase() == 'FAIL') {
              this.actionAttributes.push(atr);
            } else {
              this.fieldAttributes.push(atr)
            }
          }
        }
      }
    });
  }

  private prepareActivityLabelConfiguration(): void {
    if (this.inboxItem.activity) {
      let processType = this.inboxItem?.activity?.processType ? this.inboxItem?.activity?.processType : 'FOOD';
      let activityList: any = INBOX_LABEL_CONFIGURATION[processType];
      this.configurationList = activityList[this.inboxItem?.activity?.activityMasterCode];
    }
  }

  public onFileChanged(event: any): void {
    this.files = event;
  }

  public InspectionStatus(): any {
    let inspectionStatus: any;
    switch (this.inboxItem?.activity?.inspectionStatus) {
      case TA_ACTIVITY_STATUS.DELETED:
        inspectionStatus = 'Deleted'
        break;
      case TA_ACTIVITY_STATUS.SCHEDULED:
        inspectionStatus = 'Scheduled'
        break;
      case TA_ACTIVITY_STATUS.SUBMITTED:
        inspectionStatus = 'Submitted'
        break;
      case TA_ACTIVITY_STATUS.ABORTED:
        inspectionStatus = 'Aborted'
        break;
      case TA_ACTIVITY_STATUS.REVOKED:
        inspectionStatus = 'Revoked'
        break;
      case TA_ACTIVITY_STATUS.FAILED:
        inspectionStatus = 'Failed'
        break;
      case TA_ACTIVITY_STATUS.DRAFT:
        inspectionStatus = 'Draft'
        break;
      case TA_ACTIVITY_STATUS.DRAFT_COMPLETED:
        inspectionStatus = 'Draft Completed'
        break;
      case TA_ACTIVITY_STATUS.EXPIRED:
        inspectionStatus = 'Expired'
        break;
      case TA_ACTIVITY_STATUS.SAVE_OR_UPDATE:
        inspectionStatus = 'Save Or Update'
        break;
      case TA_ACTIVITY_STATUS.PREVIEW:
        inspectionStatus = 'Preview'
        break;
      case TA_ACTIVITY_STATUS.STARTED:
        inspectionStatus = 'Started'
        break;
      case TA_ACTIVITY_STATUS.CONFIRMED:
        inspectionStatus = 'Confirmed'
        break;
      case TA_ACTIVITY_STATUS.REVIEWED:
        inspectionStatus = 'Reviewed'
        break;
      case TA_ACTIVITY_STATUS.PARTIALLY_SUBMITTED:
        inspectionStatus = 'Partially Submitted'
        break;
      case TA_ACTIVITY_STATUS.EDITED:
        inspectionStatus = 'Edited'
        break;
      case TA_ACTIVITY_STATUS.DIVERSION:
        inspectionStatus = 'Diversion'
        break;
      case TA_ACTIVITY_STATUS.L1_REVIEW:
        inspectionStatus = 'L1 Review'
        break;
      case TA_ACTIVITY_STATUS.L1_REVIEWED:
        inspectionStatus = 'L1 Reviewed'
        break;
      case TA_ACTIVITY_STATUS.L2_REVIEW:
        inspectionStatus = 'L2 Review'
        break;
      case TA_ACTIVITY_STATUS.L2_REVIEWED:
        inspectionStatus = 'L2 Reviewed'
        break;
      case TA_ACTIVITY_STATUS.AUDITOR_SUBMITTED:
        inspectionStatus = 'Auditor Submitted'
        break;
      case TA_ACTIVITY_STATUS.PARTIALLY_SAVED:
        inspectionStatus = 'Partially Saved'
        break;
      default:
        break
    }
    return inspectionStatus;
  }
  public onValidationFields(item): void {
    if (!this.showStatusDetails) {
      if (this.inboxItem.designcodeActivityDetail.attributes != null) {
        const isValid = this.inboxItem.designcodeActivityDetail.attributes?.every(item1 => {
          if (item1.type == 'APPROVE' || item1.type == 'REJECT' || item1.type == 'LABEL') {
            return true;
          } else {
            if (item1.required == 1) {
              if (item1.type == 'FILE') {
                return (item1.files && item1.files.length > 0) ? true : false;
              } else {
                if (item1.code?.trim()?.toLowerCase() === 'sample qty') {
                  if (!!item1?.attributeValue) {
                    const isValidNumber = /^[+]?\d+(\.\d+)?$/.test(item1?.attributeValue);
                    const numericValue = parseFloat(item1?.attributeValue);
                    if (numericValue <= 0) {
                      return false;
                    }
                    if (isValidNumber) {
                      return true;
                    }
                    else {
                      return false;
                    }
                  } else {
                    return false;
                  }
                }
                if (item1.code?.trim()?.toLowerCase() == 'fabric code' || item1.code?.trim()?.toLowerCase() == 'received fabric') {
                  if (item1.code) {
                    if (this.inboxItem.designcodeActivityDetail?.fabricCode) {
                      return true;
                    } else {
                      this.commonService.error("Please check Fabric, Entered Fabric Code Not Matching");
                    }
                  } else {
                    return false;
                  }
                } else {
                  return item1.attributeValue ? true : false;
                }

              }
            } else {
              return true;
            }
          }
        });

        if (isValid) {
          this.onSave(item);
        } else {
          this.inboxItem.designcodeActivityDetail.attributes?.every(item1 => {
            if (item1.code?.trim()?.toLowerCase() === 'sample qty' && (item1.attributeValue?.length > 0)) {
              this.commonService.error("Sample qty must be a number and it should be greater than 0.");
            } else {
              this.commonService.error("Please fill mandatory fields");
            }
          })
          return;
        }
      }
    } else {
      if (this.inboxItem.activity.attributes != null) {
        const isValid = this.inboxItem.activity.attributes?.every(item1 => {
          if (item1.type == 'APPROVE' || item1.type == 'REJECT' || item1.type == 'LABEL') {
            return true;
          } else {
          }
        });

        if (isValid) {
          this.onSave(item);
        } else {
          this.commonService.error("Please fill mandatory fields");
          return;
        }
      }
    }
  }

  public onSave(item): void {
    let statusText: string = "Submitted";
    if (!this.showStatusDetails) {
      this.uploadFiles()
    }
    if (item.type.toUpperCase() == 'APPROVE') {
      statusText = item.name.toLowerCase() == 'submit' ? 'Submitted' : item.name.toLowerCase() == 'approve' ? 'Approved' : item.name;
      if (!this.showStatusDetails) {
        (!this.showStatusDetails) ? this.inboxItem.designcodeActivityDetail.status = 2 : this.inboxItem.activity.status = 2;
        this.inboxItem.status = 2
      } else {
        this.inboxItem.activity.status = 2
        this.inboxItem.status = 2
      }
    } else if (item.type.toUpperCase() == 'REJECT') {
      statusText = item.name + 'ed';
      (!this.showStatusDetails) ? this.inboxItem.designcodeActivityDetail.status = -1 : this.inboxItem.activity.status = -1;
      this.inboxItem.status = 2
    }
    else if (item.type.toUpperCase() == 'FAIL') {
      statusText = item.name + 'ed';
      (!this.showStatusDetails) ? this.inboxItem.designcodeActivityDetail.status = 3 : this.inboxItem.activity.status = 3;
      this.inboxItem.status = 2
    }



    this.commonService.openDialogForConfirmation(`Are you sure you want to submit?`)
      .subscribe({
        next: confirm => {
          if (confirm) {
            if (this.inboxItems != null && this.inboxItems.length > 0) {
              let payload = new CloseTimelineInboxItemList()
              payload.refItem = this.inboxItem
              payload.items = this.inboxItems
              payload.items.splice(0, 1)
              APP_UI_CONFIG.activity.updateInboxItemsDetails.paramList['id'] = this.inboxItemUid
              APP_UI_CONFIG.activity.updateInboxItemsDetails.payload = payload
              this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.updateInboxItemsDetails, '', '', payload).subscribe({
                next: response => {
                  if (response.code == 200) {
                    (!this.showStatusDetails) ? this.commonService.success(`${this.inboxItem.designcodeActivityDetail.activityName} has been ${statusText} successfully.`) : this.commonService.success(`${this.inboxItem.activity.activityMasterCode} has been ${statusText} successfully.`);;
                    this.onClose.emit(true);
                  } else {
                    this.commonService.handleError(response)
                  }
                }, error: error => {
                  this.commonService.handleError(error)
                }
              });
            }
            else {
              APP_UI_CONFIG.activity.updateInboxItemDetails.paramList['id'] = this.inboxItemUid
              APP_UI_CONFIG.activity.updateInboxItemDetails.payload = this.inboxItem
              this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.updateInboxItemDetails, '', '', this.inboxItem).subscribe({
                next: response => {
                  if (response.code == 200) {
                    (!this.showStatusDetails) ? this.commonService.success(`${this.inboxItem.designcodeActivityDetail.activityName} has been ${statusText} successfully.`) : this.commonService.success(`${this.inboxItem.activity.activityMasterCode} has been ${statusText} successfully.`);;
                    this.onClose.emit(true);
                  } else {
                    this.commonService.handleError(response)
                  }
                }, error: error => {
                  this.commonService.handleError(error)
                }
              });
            }
          }
        }
      })
  }

  public uploadFiles(): void {
    if (this.inboxItem.designcodeActivityDetail.attributes != null) {
      for (var attr of this.inboxItem.designcodeActivityDetail.attributes) {
        if (attr.type.toUpperCase() == 'FILE' && attr.files != null) {
          for (const droppedFile of attr.files) {
            const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
            const formData = new FormData()
            fileEntry.file((file: File) => {
              formData.append('file', file)
            });
            formData.append('docType', JSON.stringify(attr.code));
            formData.append('activityUid', JSON.stringify(this.inboxItem.designcodeActivityDetail.activityUid));
            APP_UI_CONFIG.activity.uploadDesigncodeDocs.paramList.designcodeUid = this.inboxItem.designcodeActivityDetailUid;
            this._invokeService.uploadAttachments(APP_UI_CONFIG.activity.uploadDesigncodeDocs, formData).subscribe(res => {
            })
          }
        }
      }
    }
  }

  public getValue(value, index): void {
    if (this.inboxItem.designcodeActivityDetail.attributes[index].type.toUpperCase() == 'FILE') {
      this.inboxItem.designcodeActivityDetail.attributes[index].files = value
    }
    else if (this.inboxItem.designcodeActivityDetail.attributes[index].type.toUpperCase() == 'COMMENT') {
      let comments: DesigncodeActivityComments = {};
      comments.activityTemplateUid = this.inboxItem.designcodeActivityDetail['activityTemplateUid'];
      comments.designCodeUid = this.inboxItem.designcodeActivityDetail['designCodeUid'];
      comments.designCodeActivityUid = this.inboxItem.designcodeActivityDetail['designCodeActivityUid'];
      comments.designCodeActivityDetailUid = this.inboxItem['designcodeActivityDetailUid'];
      comments.comment = this.comments;
      this.inboxItem.designcodeActivityDetail.comments = [];
      this.inboxItem.designcodeActivityDetail.comments.push(comments);
    } else {
      this.inboxItem.designcodeActivityDetail.attributes[index].attributeValue = value;
    }
  }
  public deleteFile(value, index): void {
    if (this.inboxItem.designcodeActivityDetail.attributes[index].type.toUpperCase() == 'FILE') {
      this.inboxItem.designcodeActivityDetail.attributes[index].files = []
    }
  }

  public navigatePO() {
    this.commonService.navigate("/schedules/purchaseorder/");
    this.close();
  }

  public navigateToShipmentPage(inboxItem): void {
    let currentActivityName: string = 'confirm shipping complete';
    let poStatus: any = 6;
    if (inboxItem.name.toLowerCase() == 'confirm shipping complete') {
      currentActivityName = 'Confirm Shipping Complete';
    } else if (inboxItem?.code?.toLowerCase() == 'grn completed') {
      currentActivityName = 'Acknowledge GRN Completion';
    } else if (inboxItem?.code?.toLowerCase() == 'close article') {
      currentActivityName = 'Close Article';
    }
    this._router.navigate([`po/shipment-by-po/''/${inboxItem.purchaseOrderNo}/''`], { queryParams: { currentActivityName: currentActivityName, poStatus: poStatus} })
    this.close();
  }

  public navigateQuotaion() {
    this.commonService.navigate("/activity/quotationResponse/");
    this.close();
  }

  public navigateToRFQ2() {
    this.commonService.navigate("sourcing/quotation/quotationReq/");
    this.close();
  }

  public navigatePath() {
    let id = JSON.parse(this.customParams);
    this.commonService.navigate("/activity/create-edit-quotation-response/" + id.uuid);
    // if(this.path == "activity/create-edit-quotation-response/")
    //   this.commonService.navigate("/activity/quotationResponse/");
    // else
    //   this.commonService.navigate("/" + this.path);
    this.close();
  }

  private getDesignCodeDocuments(row): void {
    APP_UI_CONFIG.activity.viewActivityDocument.paramList.docUid = row.docUid;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.viewActivityDocument).subscribe(res => {
      this.documentsList = res;
    });
  }

  public prepareActionIconButtons2(row: any): any {
    let icons: any[] = [];
    icons.push({ type: 'view', icon: 'view', title: 'View', data: row });
    return icons;
  }

  private onActions(action) {
    switch (action.type) {
      case 'view':
        this.onViewDocument(action.data)
        break;

    }
  }

  private onViewDocument(data): void {
    let headers = new HttpHeaders({ 'Content-Type': 'application/csv' })
      .set("Authorization", this.basicAuth).set('Client',this.commonService.getTenantUid());;
    this.http.get(environment.apiUrl + 'sourcing/activity/' + data.docUid + '/doc', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      if (data.fileType && this.isImage(data.fileType)) {
        this.viewShipmentImage(res.body, data.docType);
      } else
        this.fileSaverService.save((<any>res).body, data.docType + data.fileType);
    });

  }

  public viewShipmentImage(imageData: Blob, docType: string) {
    if (imageData) {
      const urlCreator = window.URL;
      const image = {};
      image['name'] = docType + ' IMAGE';
      image['pic'] = this.sanitizer.bypassSecurityTrustUrl(
        urlCreator.createObjectURL(imageData));
      const dialogRef = this.dialog.open(ImageViewerDialogComponent, {
        data: [image],
      });
    }
  }

  public isImage(name: string): boolean {
    let isImage: boolean = false;
    let extension = name.toLowerCase();
    switch (extension) {
      case '.png':
        isImage = true;
        break;
      case '.jpg':
        isImage = true;
        break;
      case '.jpeg':
        isImage = true;
        break;
      case '.svg':
        isImage = true;
        break;
      case '.webp':
        isImage = true;
        break;
      default:
        isImage = false;
        break;
    }
    return isImage;
  }

  public onDownloadInspectionSummary(inboxItem): void {
    // this.commonService.navigate(`/master/inspectionSummary`);
    let reportUrl = URL_CONSTANTS.BASE_URL + URL_CONSTANTS.AUDIT_GETREPORT + '/' + inboxItem.activity?.auditId + '.pdf';
    window.open(reportUrl, '_blank');
  }

  public onViewInspectionSummary(inboxItem): void {
    if (inboxItem.activity.inspectionStatus == 17 || inboxItem.activity.inspectionStatus == 6 || inboxItem.activity.inspectionStatus == 19 || inboxItem.activity.inspectionStatus == 23 || inboxItem.activity.inspectionStatus == 1) {
      this._router.navigate(['/schedules/reviewProcess/edit/' + inboxItem.activity?.auditId])
    }
    else {
      this.commonService.warning('Report may not generated yet.');
    }
  }

  public openPoDetailsPage(purchaseOrderNo: string) {
    if (!!purchaseOrderNo)
      this.commonService.navigate(`po/poById/${purchaseOrderNo}/poDetails`);
  }

  public close(): void {
    this.onClose.emit(true);
    //this.dialogRef.close();
  }

  public isDueDateOver(plannedEndDate): boolean {
    return new Date() > new Date(plannedEndDate)
  }

  public navigateToPoDetails(poNumber): void {
    this.commonService.navigate(`po/poById/${poNumber}/''`);
  }

  public navigateToSamplingDetails(): void {
    const parsedParams = JSON.parse(this.inboxItem.customParams);
    if (this.inboxItem?.purchaseOrderNo) {
      this.commonService.navigate(`po/poById/${this.inboxItem.purchaseOrderNo}/''`);
    } else {
      this.commonService.navigate(`npd/sampling/view-sample-response/${parsedParams.uuid}`);
    }
  }

  public onUpdateGRN(item): void {
    sessionStorage.setItem("inboxItem", JSON.stringify(item))
    // this.commonService.navigate(`shipment/shipment-list/${item.activity.code}`);
    let requestCodeList = JSON.parse(item?.activity?.requestMap);
    let poNumber = requestCodeList.filter(item => item.requestCode)[0].requestCode;
    this.commonService.navigate(`po/shipment-by-po/${item.activity.code}/${poNumber}/${item.uuid}`)
  }

}
