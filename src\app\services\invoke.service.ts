import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { saveAs } from 'file-saver';
import { ToastrService } from 'ngx-toastr';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { CommonService } from './common.service';

@Injectable({
  providedIn: 'root'
})
export class InvokeService {
  constructor(private http: HttpClient,
    private _commonService: CommonService,
    public _toastr: ToastrService,) { }
  public serviceInvocation(context, params?, id?, payload?): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() }) //this._commonService.getTenantUid()
      .set("Authorization", this._commonService.getBasicAuth());
    let apiResponse: Observable<any> = new Observable();
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    if (context.type == "GET") {
      // getData(datatype: string, callback: Function = null, params: { key: string, value: string }[] = null, extension: string = null) {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let param = params;
      if (param != null && param != undefined && param.length != 0) {
        let p = "?1=1";
        for (let i in param) {
          p += i ? "&" : "";
          p += param[i].key + "=" + param[i].value;
        }
        url += p;
      }
      apiResponse = this.http.get(url, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }
    if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let param = params;
      if (param != null && param != undefined && param.length != 0) {
        let p = "?1=1";
        for (let i in param) {
          p += i ? "&" : "";
          p += param[i].key + "=" + param[i].value;
        }
        url += p;
      }

      let payload1: any = {};
      payload1 = payload;
      apiResponse = this.http.post(url, payload1, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }

    if (context.type == "DELETE") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let param = params;
      if (param != null && param != undefined && param.length != 0) {
        let p = "?1=1";
        for (let i in param) {
          p += i ? "&" : "";
          p += param[i].key + "=" + param[i].value;
        }
        url += p;
      }

      apiResponse = this.http.delete(url, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }
    if (context.type == "PUT") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let param = params;
      if (param != null && param != undefined && param.length != 0) {
        let p = "?1=1";
        for (let i in param) {
          p += i ? "&" : "";
          p += param[i].key + "=" + param[i].value;
        }
        url += p;
      }
      apiResponse = this.http.put(url, payload, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }

    return apiResponse;
  }


  public getContent(context, responseType, params?, payload?): Observable<any> {
    let apiResponse: Observable<any> = new Observable();
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() }) //environment.tenantUid
      .set("Authorization", this._commonService.getBasicAuth());
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    let url1 = this.rePlaceString(context);
    let url = baseUrl + url1;
    let param = params;
    let queryParams;
    if (param != null && param != undefined && param.length != 0) {
      queryParams = "?1=1";
      for (let i in param) {
        queryParams += i ? "&" : "";
        queryParams += param[i].key + "=" + param[i].value;
      }
    }
    if (context.type == "GET") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let param = params;
      if (param != null && param != undefined && param.length != 0) {
        let p = "?1=1";
        for (let i in param) {
          p += i ? "&" : "";
          p += param[i].key + "=" + param[i].value;
        }
        url += p;
      }
      apiResponse = this.http.get(url, { headers: headers, responseType: responseType }).pipe(catchError(error => this.handleError(error)))
    }
    if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let param = params;
      if (param != null && param != undefined && param.length != 0) {
        let p = "?1=1";
        for (let i in param) {
          p += i ? "&" : "";
          p += param[i].key + "=" + param[i].value;
        }
        url += p;
      }

      let payload1: any = {};
      payload1 = payload;
      apiResponse = this.http.post(url, payload1, { headers: headers, responseType: responseType }).pipe(catchError(error => this.handleError(error)))
    }

    return apiResponse;

  }

  downloadFile(context: any, params: any, fileName: String, payload?: any): any {
    let headers = new HttpHeaders({ 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    if (context.type == "GET") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let param = params;
      if (param != null && param != undefined && param.length != 0) {
        let p = "?";
        for (let i in param) {
          p += i ? "&" : "";
          p += param[i].key + "=" + param[i].value;
        }
        url += p;
      }
      this.http.get(url, { headers: headers, responseType: 'blob' as 'json' }).subscribe(response => {
        if (response) {
          this.saveToFileSystem(fileName, response);
        } else {
          this._toastr.error("Failed to download file");
        }
      }, (error: HttpErrorResponse) => {
        if (error) {
          this._toastr.error(error?.message);
        } else {
          this._toastr.error("Failed to download file");
        }
      })
    }
    if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      this.http.post(url, payload, { headers: headers, 'responseType': 'text' }).subscribe(response => {
        if (response) {
          this.saveToFileSystem(fileName, response);
        } else {
          this._toastr.error("Failed to download file");
        }
      }, (error: HttpErrorResponse) => {
        if (error) {
          this._toastr.error(error?.message);
        } else {
          this._toastr.error("Failed to download file");
        }
      })
    }
  }

  saveToFileSystem(filename, response) {
    //const filename = "orderitems.csv";//parts[1].split('=')[1];
    const blob = new Blob([response], { type: 'text/plain' });
    saveAs(blob, filename);
  }

  public getImage(url): Observable<any> {
    let httpheaders = new HttpHeaders({ 'Client': this._commonService.getTenantUid() }).set("Authorization", this._commonService.getBasicAuth());

    return this.http.get(environment.apiUrl + url, { headers: httpheaders, responseType: 'blob' as 'json' });
  }

  uploadImage(context: any, params?, id?, payload?): Observable<any> {
    let headers = new HttpHeaders({ 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());
    let apiResponse: Observable<any> = new Observable();
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    if (context.type == "PUT") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this.http.put(url, context.payload, { headers: headers, });
    }
    if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this.http.post(url, context.payload, { headers: headers });
    }
    return apiResponse;
  }

  public getImageBase64(context: any): Observable<any> {
    let httpheaders = new HttpHeaders({ 'Client': this._commonService.getTenantUid() }).set("Authorization", this._commonService.getBasicAuth());
    let headers = { headers: httpheaders };
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    return this.http.get<any>(baseUrl + context.url, headers);
  }
  public getPreviewImageBase64(context: any): Observable<any> {
    let httpheaders = new HttpHeaders({ 'Client': this._commonService.getTenantUid() }).set("Authorization", this._commonService.getBasicAuth());
    let headers = { headers: httpheaders };
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    return this.http.get<any>(baseUrl + context.url, headers)
  }

  public addDocumentWithJsonResponse(context) {
    let headers = new HttpHeaders({ 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());
    let apiResponse: Observable<any> = new Observable();
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    if (context.type == "PUT") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      apiResponse = this.http.put(url, context.payload, { headers: headers, responseType: 'json' });
    }
    if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this.http.post(url, context.payload, { headers: headers, responseType: 'json' });
    }
    return apiResponse;
  }

  public rePlaceString(context: any): string {
    let res: string = context.url;
    let res1 = res;
    if (context?.paramList) {
      for (var key of Object.keys(context?.paramList)) {
        if (res.search(key) != -1) {
          res = res.replace(`{${key}}`, context.paramList[key]);
        } else {
          res1 = res;
        }
      }
      res1 = res;
    } else {
      res1 = res;
    }
    return res1;
  }

  public createUser(payload): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.post<any[]>(environment.apiUrl + 'user/create', JSON.stringify(payload), {
      headers: headers
    });
  }

  public getUserByCode(usrCode: string): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());

    return this.http.get<any[]>(environment.apiUrl + 'user/' + usrCode + '/findbycode', {
      headers: headers
    });
  }



  public handleError(errorResponse: HttpErrorResponse) {
    let errorMessage: any = {};
    if (errorResponse.status == 0) {
      let errorMessage1 = 'Could not connect to network. please check and try again later.';
      this._toastr.error(errorMessage1);
    }
    // else if (errorResponse.status == 500) {
    //   let errorMessage1 = 'Internal Server Error';
    //   this._toastr.error(errorMessage1);
    // }
    else {
      if (errorResponse.error instanceof ErrorEvent) {
        // client-side error
        errorMessage = `${errorResponse.error}`;
        // this.showNotificationMessage(errorMessage);
      } else {
        // server-side error
        errorMessage = `${errorResponse.error}`;
        // this.showNotificationMessage(errorMessage);

      }
    }
    return throwError(errorMessage);
  }

  public getLabs(context, params?, id?, payload?): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() }) //this._commonService.getTenantUid()
      .set("Authorization", `Basic ${btoa('admin' + ':' + 'admin')}`);
    let apiResponse: Observable<any> = new Observable();
    let baseUrl = environment.apiUrl;
    if (context.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    if (context.type == "GET") {
      // getData(datatype: string, callback: Function = null, params: { key: string, value: string }[] = null, extension: string = null) {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let param = params;
      if (param != null && param != undefined && param.length != 0) {
        let p = "?";
        for (let i in param) {
          p += i ? "&" : "";
          p += param[i].key + "=" + param[i].value;
        }
        url += p;
      }
      apiResponse = this.http.get(url, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }
    if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      let payload1: any = {};
      payload1 = payload;
      apiResponse = this.http.post(url, payload1, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }

    if (context.type == "DELETE") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;

      apiResponse = this.http.delete(url, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }
    if (context.type == "PUT") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this.http.put(url, payload, { headers: headers }).pipe(catchError(error => this.handleError(error)))
    }

    return apiResponse;
  }

  public uploadAttachments(context, payload): Observable<any> {
    let headers = new HttpHeaders({ 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());
    let apiResponse: Observable<any> = new Observable();
    let baseUrl = environment.apiUrl;
    if (context?.externalUrl) {
      baseUrl = environment.apiUrl;
    }
    if (context.type == "PUT") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this.http.put(url, payload, { headers: headers });
    }
    if (context.type == "POST") {
      let url1 = this.rePlaceString(context);
      let url = baseUrl + url1;
      apiResponse = this.http.post(url, payload, { headers: headers });
    }
    return apiResponse;
  }

}


