import { Injectable, OnInit } from '@angular/core';
import { FuseNavigationItem } from '@fuse/components/navigation';
import { FuseMockApiService } from '@fuse/lib/mock-api';
import { ROLE } from 'app/enum-constants';
import { environment } from 'environments/environment';
import { cloneDeep } from 'lodash-es';

import { AuthService } from '../../app/services/auth.service';
import { UI_CONFIGURATION } from './ui-configuration';



@Injectable({
    providedIn: 'root'
})
export class NavigationMockApi implements OnInit {
    private _navigation: any[] = [];
    /**
     * Constructor
     */
    constructor(private _fuseMockApiService: FuseMockApiService,
        private _authService: AuthService,) {
        this.clearNavigation();
        const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
        if (tenantUid) {
            environment.tenantUid = tenantUid;
            this.loadConfig(tenantUid)
        } else {
            this.loadConfig(environment.tenantUid)
        }
    }
    public ngOnInit(): void {


    }


    private loadConfig(client: string) {
        if (UI_CONFIGURATION[client]) {
            this._navigation = UI_CONFIGURATION[client].menu;
        } else {
            this._navigation = UI_CONFIGURATION['stage'].menu;
        }
        this.registerHandlers();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Register Mock API handlers
     */
    registerHandlers(): any {
        return {
            navigation: {
                navigation: cloneDeep(this.PrepareMenuConfiguration()),
            }
        }
    }

    clearNavigation(): any {
        this._navigation = [];
    }

    private isInArray(value, array): boolean {
        return (array.indexOf(value) != -1);
    }
    private prepareChildren(array: any[]): any[] {
        const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
        let listArray: any[] = array.filter(item => (this.isInArray(tenantUid, item.permission)))
        return listArray;
    }


    public prepareMenu(navigation: any): any[] {
        let menus: any[] = this.filterArrayByRoleExistence(navigation);
        menus?.forEach((item, index) => {
            if (menus[index].children) {
                menus[index].children = this.filterArrayByRoleExistence(menus[index].children);
                menus[index].children?.forEach((item1, index1) => {
                    if (menus[index].children[index1].children) {
                        menus[index].children[index1].children = this.filterArrayByRoleExistence(menus[index].children[index1].children);
                    }
                });
            }
        });

        return menus;
    }

    private checkMenuItem(item: any, user: any): any {
        const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
        if (tenantUid == 'amazon') {
            if (this._authService.hasLoggedUserPermitted(ROLE.AGENCY) || this._authService.hasLoggedUserPermitted(ROLE.AUDITOR) || this._authService.hasLoggedUserPermitted(ROLE.REVIEWER)) {
                if (item.id == 'auditHead') {
                    if (!user.type?.toLowerCase()?.split(',')?.includes('audit') && !this._authService.hasLoggedUserPermitted(ROLE.OPERATIONS))
                        item.hidden = true;
                }
                // else if(item.id == 'inspectionHead') {    
                //     if(!user.type?.toLowerCase()?.split(',')?.includes('inspection'))
                //         item.hidden = true;
                // }
                else if (item.id == 'testingHead') {
                    // if (!user.type?.toLowerCase()?.split(',')?.includes('testing') )
                    //     item.hidden = true;
                }
            }
        }
        return item;
    }

    private checkRoleExist(userRole: any[], permission: any[]) {
        return permission.some(elem => userRole.some(obj => obj.toUpperCase() === elem.toUpperCase()));
    }

    private filterArrayByRoleExistence(childrenItems: any[]) {
        let user: any = this._authService.getLoggedInUser();
        return childrenItems?.filter(elem => this.checkRoleExist(user.role.split(","), elem.role));
    }



    public configMenu(item: any[]): any[] {
        let navigation: any[] = item;
        let user: any = this._authService.getLoggedInUser();
        item?.forEach(item => {
            if (user.role.split(',').filter(r => item.role.includes(r))?.length > 0)
                item.hidden = false;
            else
                item.hidden = true;
            if (item.children) {
                this.configMenu(item.children);
            }
            item = this.checkMenuItem(item, user);
        })
        return navigation;
    }

    public PrepareMenuConfiguration(): any[] {
        let navigation: any[] = this.prepareMenu(this._navigation);
        return navigation;
    }
}
