import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, EventEmitter, Inject, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { TechnicalSpecifications } from 'app/core/qinspect/_model/TechSpecification.model';
import { TechnicalSpecificationsConfig } from 'app/core/qinspect/_model/TechSpecificationConfig.model';
import { FORM_CONTROL_TYPES, HTTP_STATUS, ROLE, TABLE_ACTION_TYPES } from 'app/enum-constants';
import { QuotationStatus } from 'app/modules/sourcing/sourcing.configuraiton';
import { BuildFormUtilityService } from 'app/services/build-form-utility.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { FileSaverService } from 'ngx-filesaver';
import { ReplaySubject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'add-edit-specifications',
  templateUrl: './add-edit-specifications.component.html',
})
export class AddEditSpecificationsComponent implements OnInit, OnDestroy {
  hdr: string = 'Add Specifications';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  public techSpecificationForm: FormGroup;
  fields: any[] = [];
  documentsList: any[] = [];
  addFiles: any[] = [];
  rec: TechnicalSpecifications = new TechnicalSpecifications();
  status: string;
  techSpecificationConfigList: any[] = [];
  designCodeResponse: any;
  quotationResponse: any;
  quotationResponseList: any[] = [];
  productType: any;
  isApprove: boolean = false;
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  isCreateSpecification: boolean = false;
  selectedIndex: number = 0;
  public extension: string = '.pdf';
  newSpecification: any = {
    inputType: FORM_CONTROL_TYPES.TEXT,
    specificationGroup: '',
    specificationName: '',
    sectionOtherName: '',
    value: '',
    uom: '',
    sectionName: '',
    maxLength: 255,
    isNew: true,
    isUpdated: true
  };
  isCopySpecification: boolean = false;
  designCodeList: any[] = [];
  copySpecificationDesignCodes: any[] = [];
  public copySpecification: any = {
    original: '',
    selectedDesignCodes: ''
  };
  pptReportDocUid: any;
  showReportUpload: any;
  allDesignCodes: any[] = [];
  public copyPPT: any = {
    original: '',
    selectedDesignCodes: ''
  };
  specificationGroupUids: any;
  roadmapDetails: any;
  alreadySavedSpecs: any[] = [];
  specsUnitMasterConfigurationList: any[] = [];

  techSpecificationConfigOrdinalList: any = {};
  @Output() onCopySpec: EventEmitter<any> = new EventEmitter();
  constructor(
    private _buildFormUtilityService: BuildFormUtilityService,
    public _commonService: CommonService,
    public dialogRef: MatDialogRef<AddEditSpecificationsComponent>,
    public _invokeService: InvokeService,
    private _httpClient: HttpClient,
    public _fileSaverService: FileSaverService,
    @Inject(MAT_DIALOG_DATA) public data: any

  ) { }


  public ngOnInit(): void {
    this.designCodeResponse = this.data.data.designCodeData;
    this.specificationGroupUids = this.data.data.specificationGroupUids;
    this.roadmapDetails = this.data.data.roadmapDetails;
    this.designCodeList = this.data?.data?.responseData?.designcodes
    this.copySpecification.original = this.designCodeResponse.code;
    this.copyPPT.original = this.designCodeResponse.code;
    if (this._commonService.isProductCompliance()) {
      this.copySpecificationDesignCodes = this.data?.data?.responseData[0]?.designcodes.filter(item => item?.productTypeUid == this.designCodeResponse.productTypeUid && item.code != this.designCodeResponse.code && item.code != '00-For Reference' && (item.status == QuotationStatus.ADDITIONALSPECS))
    } else {
      this.copySpecificationDesignCodes = this.designCodeList?.filter(item => item.productTypeUid == this.designCodeResponse.productTypeUid && item.code != this.designCodeResponse.code && item.code != '00-For Reference' && (item.status == QuotationStatus.RFQ1APPROVED || item.status == QuotationStatus.ADDITIONALSPECS));
    }
    this.allDesignCodes = Util.clone(this.copySpecificationDesignCodes);
    this.allDesignCodes.push(this.designCodeResponse);
    if (this.data.data.responseData.length >= 1 && this._commonService.getUseRole().includes(ROLE.SME)) {
      this.quotationResponse = this.data.data.responseData.filter(designcode => designcode.status != -1)?.find(item => item.designcodes.find(designcodesData => designcodesData.code == this.designCodeResponse.code));
    } else {
      this.quotationResponse = this.data.data.responseData;
      this.showReportUpload = this.designCodeResponse.status < QuotationStatus.RFQ2APPROVED;
    }
    this.quotationResponseList = this.data.data.quotationResponseList;
    this.status = this.data.status;
    this.getMasterSpecUnit();
    this.getTechSpecificationsByProductUid();
    this.getSellerContactInfo();
    this.configureForm();
    this.getFileList();
  }
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        this.hdr = `Add Specifications (${this.designCodeResponse.code})`;

        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = `View Specifications (${this.designCodeResponse.code})`;
        // this.techSpecificationForm.disable();
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = `Edit Specifications (${this.designCodeResponse.code})`;
        break;
      case 'APPROVE':
        this.hdr = `Approve/View Specifications (${this.designCodeResponse.code})`;
        this.isApprove = true;
        this._commonService.isProductCompliance() ? this.techSpecificationForm?.enable() : this.techSpecificationForm.disable();
        break;
      default:
        break;
    }
  }

  //Not using for now
  // private getProductTypeByDesignCode(): void {
  //   APP_UI_CONFIG.TechSpecification.getProductTypeByDesignCodeUid.paramList.designCodeUid = this.designCodeResponse.designCodeUid;
  //   this._invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getProductTypeByDesignCodeUid)
  //     .pipe(takeUntil(this.$destroyed))
  //     .subscribe({
  //       next: response => {
  //         if (response) {
  //           this.productType = response.filter(item => item.requestType == SamplingProcessStatus.COLLECTION_REQUEST)?.[0].productTypeUid;
  //           this.getTechSpecificationsByProductUid(this.productType);
  //         } else {
  //           this._commonService.error("No Product Found for this Design code")
  //         }
  //       },
  //       error: error => {

  //       }
  //     })
  // }


  public buildTechSpecificationConfigOrdinalList(): void {
    this.techSpecificationConfigOrdinalList = {};
    const seenSectionNames = new Set();

    this.techSpecificationConfigList.forEach(item => {
      let topGroupTitleLabel = (item.specificationGroup && item.sectionName) ? `${item.specificationGroup}-${item.sectionName}` : (item.specificationGroup && !item.sectionName) ? item.specificationGroup : (!item.specificationGroup && item.sectionName) ? item.sectionName : '';

      if (item.specificationGroup && !seenSectionNames.has(topGroupTitleLabel.toLowerCase())) {
        this.techSpecificationConfigOrdinalList[topGroupTitleLabel.toLowerCase()] = item.ordinal || '';
        seenSectionNames.add(topGroupTitleLabel.toLowerCase());
      }
    });

    const values = Object.values(this.techSpecificationConfigOrdinalList);
    const numericValues: number[] = values.filter(value => typeof value === "number") as unknown as number[];
    const maxValue = Math.max(...numericValues);
    const keys = Object.keys(this.techSpecificationConfigOrdinalList);
    const emptyKeys = keys.filter(key => this.techSpecificationConfigOrdinalList[key] === "");
    let currentValue = maxValue + 1;
    emptyKeys.forEach(key => {
      this.techSpecificationConfigOrdinalList[key] = currentValue;
      currentValue++;
    });
  }

  //Get master specs by groupid
  private getTechSpecificationsByProductUid(): void {
    let payload = {};
    payload['groupUid'] = this.specificationGroupUids;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getListByGroup, '', '', payload)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response && response.length > 0) {
            this.techSpecificationConfigList = response;
            this.buildTechSpecificationConfigOrdinalList();
            if (this.status != TABLE_ACTION_TYPES.CREATE) {
              this.getAlreadyAddedSpecification(response);
            } else {
              this.prepareFormModel(response, false);
            }
          } else {
            this._commonService.error("Please wait for compliance to map specification group");
          }
        },
        error: error => {

        }
      })
  }
  private getMasterSpecUnit(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getMasterSpecsUOM).subscribe({
      next: response => {
        if (response) {
          this.specsUnitMasterConfigurationList = response;
        }
      },
      error: error => {

      }
    });
  }

  sellerDetails: any;
  private getSellerContactInfo(): void {
    APP_UI_CONFIG.masterdata.users.getByUid.paramList.uuid = this.roadmapDetails.sellerUid
    this._invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.users.getByUid).subscribe({
      next: response => {
        if (response) {
          this.sellerDetails = response;
        }
      },
      error: error => {

      }
    });
  }

  public viewSpecAttachment(specItem): void {
    Object.keys(this.alreadySavedSpecs).forEach(item => {
      if (this.alreadySavedSpecs[item].specificationName === specItem.specificationName && this.alreadySavedSpecs[item].value) {
        APP_UI_CONFIG.sourcingRoadmap.getDocumentsByDocUid.paramList.docUid = this.alreadySavedSpecs[item].value;
        let url = this._invokeService.rePlaceString(APP_UI_CONFIG.sourcingRoadmap.getDocumentsByDocUid);
        window.open(environment.apiUrl + url, '_blank').focus();
      }
    }
    )
  }


  private checkInputType(specificationName, isUnitFor?): any {
    let spec: any;
    if (isUnitFor) {
      if (specificationName.toLowerCase().includes("unit_")) {
        spec = this.techSpecificationConfigList?.find(spec => spec?.unitFor == specificationName?.replace(/unit_/gi, ""))
      } else {
        spec = this.techSpecificationConfigList?.find(spec => spec?.unitFor == specificationName)
      }
    } else {
      spec = this.techSpecificationConfigList.find(spec => spec.specificationName == specificationName);
    }
    if (spec && spec.inputType) {
      let specType: any;
      if (spec.inputType?.toLowerCase() == FORM_CONTROL_TYPES.SINGLE_SELECT.toLowerCase()) {
        specType = FORM_CONTROL_TYPES.SINGLE_SELECT;
      }
      else if (spec.inputType?.toLowerCase() == FORM_CONTROL_TYPES.MULTI_SELECT.toLowerCase()) {
        specType = FORM_CONTROL_TYPES.MULTI_SELECT;
      } else {
        specType = spec.inputType.toLowerCase();
      }
      return specType;
    } else {
      return FORM_CONTROL_TYPES.TEXT.toLowerCase();
    }
  }

  private showFileUpload(): boolean {
    return this.designCodeResponse.status == QuotationStatus.ADDITIONALSPECS || this.designCodeResponse.status == QuotationStatus.RFQ1APPROVED
  }

  private showFileView(): boolean {
    return this.techSpecificationConfigList.some(item =>
      Object.keys(this.alreadySavedSpecs).some(item1 =>
        item.inputType?.toLowerCase() === 'file' &&
        item.specificationName === this.alreadySavedSpecs[item1].specificationName &&
        !!this.alreadySavedSpecs[item1].value
      )
    );
  }


  private prepareUnitForControl(unitForSpecItem: any): any {
    let fieldConfig1: any;
    if (unitForSpecItem.keyword && (unitForSpecItem.inputType?.toLowerCase() == FORM_CONTROL_TYPES.SINGLE_SELECT || unitForSpecItem.inputType?.toLowerCase() == FORM_CONTROL_TYPES.MULTI_SELECT)) {
      let specUOMItemData: any[] = JSON.parse(this.specsUnitMasterConfigurationList?.find(specUnit => specUnit?.keyword == unitForSpecItem?.keyword)?.jsonConfig);
      let specUOMList: any[] = specUOMItemData.map(filteredUOM => ({ label: filteredUOM.value, value: filteredUOM.value, raw: filteredUOM }));
      fieldConfig1 = {
        type: this.checkInputType(unitForSpecItem.unitFor, true),
        value: unitForSpecItem.value,
        label: '',
        placeholder: `Unit For ${unitForSpecItem.unitFor}`,
        show: true,
        showLabel: false,
        topGroupTitle: false,
        topGroupTitleLabel: (unitForSpecItem.specificationGroup && unitForSpecItem.sectionName) ? `${unitForSpecItem.specificationGroup}-${unitForSpecItem.sectionName}` : (unitForSpecItem.specificationGroup && !unitForSpecItem.sectionName) ? unitForSpecItem.specificationGroup : (!unitForSpecItem.specificationGroup && unitForSpecItem.sectionName) ? unitForSpecItem.sectionName : '',
        // tooltip: unitForSpecItem?.uom || '',
        unitControl: true,
        rightSideGap: true,
        onChange: (event) => { },
        rules: {
          required: (unitForSpecItem.mandatory && unitForSpecItem.mandatory == 1) ? true : false,
        }
      };
      fieldConfig1['ordinal'] = this.techSpecificationConfigOrdinalList[fieldConfig1.topGroupTitleLabel.toLowerCase()];
      fieldConfig1['options'] = specUOMList;
      return fieldConfig1;
    }
    if (unitForSpecItem.list) {
      let listMappedData = unitForSpecItem.list.split(",").map(listData => ({
        label: listData.trim(),
        value: listData.trim(),
      }));
      fieldConfig1 = {
        type: this.checkInputType(unitForSpecItem.unitFor, true),
        value: unitForSpecItem.value,
        label: '',
        placeholder: `Unit For ${unitForSpecItem.unitFor}`,
        show: true,
        showLabel: false,
        topGroupTitle: false,
        topGroupTitleLabel: (unitForSpecItem.specificationGroup && unitForSpecItem.sectionName) ? `${unitForSpecItem.specificationGroup}-${unitForSpecItem.sectionName}` : (unitForSpecItem.specificationGroup && !unitForSpecItem.sectionName) ? unitForSpecItem.specificationGroup : (!unitForSpecItem.specificationGroup && unitForSpecItem.sectionName) ? unitForSpecItem.sectionName : '',
        unitControl: true,
        // tooltip: unitForSpecItem?.uom || '',
        rightSideGap: true,
        onChange: (event) => { },
        rules: {
          required: (unitForSpecItem.mandatory && unitForSpecItem.mandatory == 1) ? true : false,
        },
        options: listMappedData,
      }
      fieldConfig1['ordinal'] = this.techSpecificationConfigOrdinalList[fieldConfig1.topGroupTitleLabel.toLowerCase()];
      return fieldConfig1;
    }

    else {
      fieldConfig1 = {
        type: this.checkInputType(unitForSpecItem.unitFor, true),
        value: unitForSpecItem.value,
        label: '',
        placeholder: `Unit For ${unitForSpecItem.unitFor}`,
        show: true,
        showLabel: false,
        topGroupTitle: false,
        topGroupTitleLabel: (unitForSpecItem.specificationGroup && unitForSpecItem.sectionName) ? `${unitForSpecItem.specificationGroup}-${unitForSpecItem.sectionName}` : (unitForSpecItem.specificationGroup && !unitForSpecItem.sectionName) ? unitForSpecItem.specificationGroup : (!unitForSpecItem.specificationGroup && unitForSpecItem.sectionName) ? unitForSpecItem.sectionName : '',
        // tooltip: unitForSpecItem?.uom ? unitForSpecItem?.uom : '',
        unitControl: true,
        rightSideGap: true,
        onChange: '',
        rules: {
          required: unitForSpecItem.mandatory && unitForSpecItem.mandatory == 1 ? true : false,
          maxLength: unitForSpecItem?.characterLength ? unitForSpecItem?.characterLength : 255,
        }
      }
      fieldConfig1['ordinal'] = this.techSpecificationConfigOrdinalList[fieldConfig1.topGroupTitleLabel.toLowerCase()];
      return fieldConfig1;
    }
  }

  private replacePlaceholders(template: string, seller): string {
    return template
      ?.replace(/{sellerName}/g, seller?.firstName)
      ?.replace(/{contactInformation}/g, seller?.addressLine1);
  }

  private prepareFormModel(technicalSpecifications: any[], isAddMoreSpec?): void {
    let FORM_MODAL_OBJECT = {};
    let sortedConfiguration: any[] = technicalSpecifications.sort((a, b) => {
      return a.ordinal > b.ordinal ? 1 : (a.ordinal < b.ordinal ? -1 : 0);
    });
    let specsList: any[] = sortedConfiguration.filter(item => !item.unitFor);

    let specsUnitForList: any[] = sortedConfiguration.filter(item => item.unitFor);
    specsList.forEach(item => {
      if (item.keyword && (item.inputType?.toLowerCase() == FORM_CONTROL_TYPES.SINGLE_SELECT || item.inputType?.toLowerCase() == FORM_CONTROL_TYPES.MULTI_SELECT)) {
        let unitForSpecItem = specsUnitForList.find(unitForItem => unitForItem.unitFor?.toLowerCase() == item.specificationName.toLowerCase());
        let specUOMItemData: any[] = JSON.parse(this.specsUnitMasterConfigurationList.find(specUnit => specUnit.keyword == item.keyword).jsonConfig);
        let specUOMList: any[] = specUOMItemData.map(filteredUOM => ({ label: filteredUOM.value, value: filteredUOM.uuid, raw: filteredUOM }));
        let fieldConfig = {
          type: this.checkInputType(item.specificationName, false),
          value: item.value,
          label: item.uom ? `${item.specificationName}(${item.uom})` : `${item.specificationName}`,
          placeholder: item.specificationName,
          show: true,
          showLabel: true,
          // tooltip: item?.uom || '',
          topGroupTitle: true,
          topGroupTitleLabel: (item.specificationGroup && item.sectionName) ? `${item.specificationGroup}-${item.sectionName}` : (item.specificationGroup && !item.sectionName) ? item.specificationGroup : (!item.specificationGroup && item.sectionName) ? item.sectionName : '',
          unitPresent: unitForSpecItem ? true : false,
          options: specUOMList,
          rightSideGap: unitForSpecItem ? false : true,
          onChange: (event) => { },
          rules: {
            required: item.mandatory && item.mandatory == 1 ? true : false,
          },
        };
        fieldConfig['ordinal'] = this.techSpecificationConfigOrdinalList[fieldConfig.topGroupTitleLabel.toLowerCase()];
        FORM_MODAL_OBJECT[item.specificationName] = fieldConfig;

        if (unitForSpecItem) {
          FORM_MODAL_OBJECT[`unit_${unitForSpecItem.unitFor}`] = this.prepareUnitForControl(unitForSpecItem);
        }


      }
      else if (item.list) {
        let listMappedData = item.list.split(",").map(listData => ({
          label: listData.trim(),
          value: listData.trim(),
        }));
        let unitForSpecItem1 = specsUnitForList.find(unitForItem => unitForItem.unitFor?.toLowerCase() == item.specificationName.toLowerCase())
        let fieldConfig = {
          type: this.checkInputType(item.specificationName, false),
          value: item.value,
          label: item.uom ? `${item.specificationName}(${item.uom})` : `${item.specificationName}`,
          placeholder: item.specificationName,
          show: true,
          showLabel: true,
          topGroupTitle: true,
          topGroupTitleLabel: (!!item.specificationGroup && item.sectionName) ? `${item.specificationGroup}-${item.sectionName}` : (item.specificationGroup && !item.sectionName) ? item.specificationGroup : (!item.specificationGroup && item.sectionName) ? item.sectionName : '',
          unitPresent: unitForSpecItem1 ? true : false,
          // tooltip: item?.uom || '',
          rightSideGap: unitForSpecItem1 ? false : true,
          onChange: (event) => { },
          rules: {
            required: item.mandatory && item.mandatory == 1 ? true : false,
          },
          options: listMappedData,
        };
        fieldConfig['ordinal'] = this.techSpecificationConfigOrdinalList[fieldConfig.topGroupTitleLabel.toLowerCase()];
        FORM_MODAL_OBJECT[item.specificationName] = fieldConfig;

        if (unitForSpecItem1) {
          FORM_MODAL_OBJECT[`unit_${unitForSpecItem1.unitFor}`] = this.prepareUnitForControl(unitForSpecItem1);
        }
      }
      else if (item.inputType?.toLowerCase() == FORM_CONTROL_TYPES.FILE) {
        let fieldConfig = {
          type: FORM_CONTROL_TYPES.FILE,
          value: item.value,
          label: item.uom ? `${item.specificationName}(${item.uom})` : `${item.specificationName}`,
          placeholder: item.specificationName,
          show: true,
          showLabel: true,
          topGroupTitle: true,
          topGroupTitleLabel: (item.specificationGroup && item.sectionName) ? `${item.specificationGroup}-${item.sectionName}` : (item.specificationGroup && !item.sectionName) ? item.specificationGroup : (!item.specificationGroup && item.sectionName) ? item.sectionName : '',
          unitPresent: false,
          showUpload: this.showFileUpload(),
          showView: this.showFileView(),
          // tooltip: item?.uom ? item?.uom : '',
          rightSideGap: true,
          onChange: (event) => this.fileUploadApiIntegration(event),
          onView: () => this.viewSpecAttachment(item),
          rules: {
            required: item.mandatory && item.mandatory == 1 ? true : false,
          }
        }
        fieldConfig['ordinal'] = this.techSpecificationConfigOrdinalList[fieldConfig.topGroupTitleLabel.toLowerCase()];
        FORM_MODAL_OBJECT[item.specificationName] = fieldConfig;
      }
      else {
        if (item.specificationName.toLowerCase() == 'model year') {
          let optionsList: any[] = [];
          let modelYear = item.value ? item.value : new Date().getFullYear();
          for (let i = 0; i < 10; i++) {
            optionsList.push({ label: Number(modelYear) + i, value: Number(modelYear) + i })
          }
          for (let i = 10; i > 0; i--) {
            optionsList.unshift({ label: Number(modelYear) - i, value: Number(modelYear) - i })
          }
          let unitForSpecItem3 = specsUnitForList.find(unitForItem => unitForItem.unitFor?.toLowerCase() == item.specificationName.toLowerCase())
          let fieldConfig = {
            type: FORM_CONTROL_TYPES.SINGLE_SELECT,
            value: item.value,
            label: item.uom ? `${item.specificationName}(${item.uom})` : `${item.specificationName}`,
            placeholder: item.specificationName,
            show: true,
            showLabel: true,
            topGroupTitle: true,
            topGroupTitleLabel: (item.specificationGroup && item.sectionName) ? `${item.specificationGroup}-${item.sectionName}` : (item.specificationGroup && !item.sectionName) ? item.specificationGroup : (!item.specificationGroup && item.sectionName) ? item.sectionName : '',
            unitPresent: unitForSpecItem3 ? true : false,
            // tooltip: item?.uom ? item?.uom : '',
            rightSideGap: unitForSpecItem3 ? false : true,
            onChange: (event) => { },
            options: optionsList,
            rules: {
              required: item.mandatory && item.mandatory == 1 ? true : false,
            }
          }
          fieldConfig['ordinal'] = this.techSpecificationConfigOrdinalList[fieldConfig.topGroupTitleLabel.toLowerCase()];
          FORM_MODAL_OBJECT[item.specificationName] = fieldConfig;

          if (unitForSpecItem3) {
            FORM_MODAL_OBJECT[`unit_${unitForSpecItem3.unitFor}`] = this.prepareUnitForControl(unitForSpecItem3);
          }
        } else {
          let unitForSpecItem2 = specsUnitForList.find(unitForItem => unitForItem.unitFor?.toLowerCase() == item.specificationName.toLowerCase())
          let fieldConfig = {
            type: this.checkInputType(item.specificationName, false),
            value: item.value,
            label: item.uom ? `${item.specificationName}(${item.uom})` : `${item.specificationName}`,
            placeholder: item.specificationName,
            show: true,
            showLabel: true,
            topGroupTitle: true,
            topGroupTitleLabel: (item.specificationGroup && item.sectionName) ? `${item.specificationGroup}-${item.sectionName}` : (item.specificationGroup && !item.sectionName) ? item.specificationGroup : (!item.specificationGroup && item.sectionName) ? item.sectionName : '',
            unitPresent: unitForSpecItem2 ? true : false,
            // tooltip: item?.uom ? item?.uom : '',
            pTooltip: '',
            tooltipPosition: 'bottom',
            rightSideGap: unitForSpecItem2 ? false : true,
            onChange: (event) => {
            },
            rules: {
              required: item.mandatory && item.mandatory == 1 ? true : false,
              maxLength: item?.characterLength ? item?.characterLength : 255,
            }
          }
          fieldConfig['ordinal'] = this.techSpecificationConfigOrdinalList[fieldConfig.topGroupTitleLabel.toLowerCase()];
          if (!!fieldConfig.value && !isAddMoreSpec) {
            fieldConfig.value = this.replacePlaceholders(fieldConfig.value, this.sellerDetails);
          }
          FORM_MODAL_OBJECT[item.specificationName] = fieldConfig;

          if (unitForSpecItem2) {
            FORM_MODAL_OBJECT[`unit_${unitForSpecItem2.unitFor}`] = this.prepareUnitForControl(unitForSpecItem2);
          }
        }

      }
    })
    let FILTERED_FORM_MODAL_OBJECT = Object.keys(FORM_MODAL_OBJECT)
      .filter(key => key !== 'undefined')
      .reduce((acc, key) => {
        acc[key] = FORM_MODAL_OBJECT[key];
        return acc;
      }, {});
    this.buildForm(FILTERED_FORM_MODAL_OBJECT);
    if (this.status == 'VIEW') {
      this.techSpecificationForm.disable()
    }

  }

  getControlsData: any
private buildForm(FORM_MODAL_OBJECT) {
  this.getControlsData = FORM_MODAL_OBJECT;

  const formGroupFields = this._buildFormUtilityService.getFormControlsFields(FORM_MODAL_OBJECT);
  const allFields = this._buildFormUtilityService.getFieldsList();
  this.getpToolTip(allFields);

  //  Group fields by topGroupTitleLabel
  const groupMap = new Map<string, any[]>();
  allFields.forEach(field => {
    const label = field.topGroupTitleLabel?.trim().toLowerCase() || 'ungrouped';
    if (!groupMap.has(label)) {
      groupMap.set(label, []);
    }
    groupMap.get(label)?.push(field);
  });

  //  Sort each group by ordinal
  const sortedFields: any[] = [];
  const seenGroups = new Set<string>();

  groupMap.forEach((groupFields, label) => {
    groupFields.sort((a, b) => {
      if (a.ordinal === undefined) return 1;
      if (b.ordinal === undefined) return -1;
      return a.ordinal - b.ordinal;
    });

    // only one section title per group
    groupFields.forEach((field, index) => {
      field.topGroupTitle = index === 0;
      sortedFields.push(field);
    });
  });

  // Apply final result
  this.fields = sortedFields;
  this.techSpecificationForm = new FormGroup(formGroupFields);

  if (this.status === TABLE_ACTION_TYPES.CREATE && this.designCodeResponse?.copiedSpec) {
    this.setCopyDataToFormForCreate();
  }
}







  private getpToolTip(fields): void {
    fields.filter(item => {
      if (item.fieldName == 'Warranty Claims') {
        if (this.status == TABLE_ACTION_TYPES.CREATE) {
          item.pTooltip = this.replacePlaceholders(item.value, this.sellerDetails)
        } else {
          Object.keys(this.alreadySavedSpecs).forEach(savedSpec => {
            if (this.alreadySavedSpecs[savedSpec].specificationName === item.fieldName && this.alreadySavedSpecs[savedSpec].value) {
              item.pTooltip = this.alreadySavedSpecs[savedSpec].value;
            }
          })
        }
      }
    })
  }
  getSavedSpecUid: any;
  private getAlreadyAddedSpecification(allSpecsList): void {

    APP_UI_CONFIG.TechSpecification.getSpecificationsByUid.paramList.responseUid = this.designCodeResponse?.quotationResponseUid;
    APP_UI_CONFIG.TechSpecification.getSpecificationsByUid.paramList.designCodeUid = this.designCodeResponse?.uuid;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getSpecificationsByUid)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response && response.length > 0) {
            this.getSavedSpecUid = response[0];
            this.alreadySavedSpecs = JSON.parse(response[0].specificationsMap);
            this.getFileUid(this.alreadySavedSpecs, allSpecsList)
            this.prepareAndSetNewFormFields(this.alreadySavedSpecs);
          } else {
            this._commonService.error("No Specifications Found");
          }
        },
        error: error => {

        }
      })
  }

  private getFileUid(savedSpecs, specsList): void {
    Object.keys(savedSpecs).forEach(savedItem => {
      specsList.filter(item => {
        if (item.inputType.toLowerCase() == 'file' && savedSpecs[savedItem].specificationName === item.specificationName && savedSpecs[savedItem].value) {
          this.specFileUid = savedSpecs[savedItem].value;
        }
      })

    }
    )
  }
  private prepareAndSetNewFormFields(alreadySavedSpecs: any[]): void {
    let newSpec: any[] = Object.keys(alreadySavedSpecs).filter(key => alreadySavedSpecs[key].isNew).map(key1 => ({
      "code": "TPS Common Attributes_001",
      "specificationGroupCode": alreadySavedSpecs[key1].groupName,
      "specificationGroup": alreadySavedSpecs[key1].groupName,
      "specificationName": alreadySavedSpecs[key1].specificationName,
      "characterLength": 255,
      "mandatory": 0,
      "inputType": alreadySavedSpecs[key1].inputType,
      "sectionName": alreadySavedSpecs[key1].sectionName,
      "groupName": alreadySavedSpecs[key1].groupName,
      "uom": alreadySavedSpecs[key1].uom,
      "value": alreadySavedSpecs[key1].value,
      "isNew": true,
      "isUpdated": true,
    }));
    this.techSpecificationConfigList = this.techSpecificationConfigList.concat(newSpec);
    this.prepareFormModel(this.techSpecificationConfigList, false);
    setTimeout(() => {
      this.setSpecificationData(this.alreadySavedSpecs);
    }, 500);

  }
  private setSpecificationData(alreadyDesignCodeSpec: any): void {
    this.setDataToForm(alreadyDesignCodeSpec)
  }

  private setCopyDataToFormForCreate(): void {
    Object.keys(this.designCodeResponse.copiedSpec).forEach((key) => {
      const specConfig = this.techSpecificationConfigList.find(
        (spec) => spec.specificationName === key || spec?.unitFor === key.replace(/unit_/gi, "")
      );
      if (specConfig?.inputType?.toLowerCase() === FORM_CONTROL_TYPES.DATE?.toLowerCase()) {
        this.techSpecificationForm?.controls[key]?.setValue(new Date(Number(this.designCodeResponse.copiedSpec[key])));
      }
      else if (specConfig?.inputType?.toLowerCase() === FORM_CONTROL_TYPES.MULTI_SELECT?.toLowerCase()) {
        if (Array.isArray(this.designCodeResponse.copiedSpec[key])) {
          this.techSpecificationForm?.controls[key]?.setValue(this.designCodeResponse.copiedSpec[key]);
        } else {
          this.techSpecificationForm?.controls[key]?.setValue(this.designCodeResponse.copiedSpec[key]?.split(","));
        }
      }
      else {
        this.techSpecificationForm?.controls[key]?.setValue(this.designCodeResponse.copiedSpec[key]);

      }
    });
  }
  private setDataToForm(alreadyDesignCodeSpec: any): void {
    if (this.designCodeResponse.copiedSpec) {
      Object.keys(this.designCodeResponse.copiedSpec).forEach((key) => {
        const specConfig = this.techSpecificationConfigList.find(
          (spec) => spec.specificationName === key || spec?.unitFor === key.replace(/unit_/gi, "")
        );
        if (specConfig?.inputType?.toLowerCase() === FORM_CONTROL_TYPES.DATE?.toLowerCase()) {
          this.techSpecificationForm?.controls[key]?.setValue(new Date(Number(this.designCodeResponse.copiedSpec[key])));
        }
        else if (specConfig?.inputType?.toLowerCase() === FORM_CONTROL_TYPES.MULTI_SELECT?.toLowerCase()) {
          if (Array.isArray(this.designCodeResponse.copiedSpec[key])) {
            this.techSpecificationForm?.controls[key]?.setValue(this.designCodeResponse.copiedSpec[key]);
          } else {
            this.techSpecificationForm?.controls[key]?.setValue(this.designCodeResponse.copiedSpec[key]?.split(","));
          }
        }
        else {
          this.techSpecificationForm?.controls[key]?.setValue(this.designCodeResponse.copiedSpec[key]);

        }
      });

    }
    else {
      Object.keys(alreadyDesignCodeSpec).forEach((key) => {
        if (this.techSpecificationConfigList.find(spec => spec.specificationName == key)?.inputType?.toLowerCase() == FORM_CONTROL_TYPES.DATE?.toLowerCase()) {
          this.techSpecificationForm.controls[key].setValue(new Date(Number(alreadyDesignCodeSpec[key].value)));
        } else if (this.techSpecificationConfigList.find(spec => (spec.specificationName == key) || spec?.unitFor == key.replace(/unit_/gi, ""))?.inputType?.toLowerCase() == FORM_CONTROL_TYPES.MULTI_SELECT?.toLowerCase()) {
          alreadyDesignCodeSpec[key].value?.indexOf(",") > -1 ? this.techSpecificationForm.controls[key]?.setValue(alreadyDesignCodeSpec[key].value?.split(",")) : this.techSpecificationForm.controls[key]?.setValue(alreadyDesignCodeSpec[key].value?.split(","))
        }
        else {
          this.techSpecificationForm.controls[key]?.setValue(alreadyDesignCodeSpec[key].value);
        }
      })
    }
  }

  private getSpecificationValueBySpecificationName(specificationName): any {
    return this.techSpecificationConfigList.find(item => item.specificationName == specificationName);
  }

  private checkIfSpecUpdate(specificationName): any {
    return this.getSavedSpecUid?.uuid || null;
  }

  // PPT Report
  public getFileList(): void {
    APP_UI_CONFIG.quotation.getAllFilesBasedOnDesignCode.paramList.designCodeUid = this.data?.data?.designCodeData?.uuid;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getAllFilesBasedOnDesignCode)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.documentsList = response;
        },
        error: error => {

        }
      })
  }

  public setAddedFiles(files: NgxFileDropEntry[]) {
    this.addFiles = files;
  }

  public uploadPPTReport(): void {
    this._commonService.openDialogForConfirmation(this.documentsList.length > 0 ? `Are you sure you want to replace old Test Report with new Test Report?` : `Are you sure you want to upload the Test Report?`).subscribe({
      next: confirm => {
        if (confirm) {
          if (this.copyPPT.selectedDesignCodes.length > 0) {
            if (!(this.copyPPT.selectedDesignCodes.includes(this.copyPPT.original))) {
              this.copyPPT.selectedDesignCodes.push(this.copyPPT.original);
            }
            this.copyPPT.selectedDesignCodes.forEach((designCode, index) => {
              this.createAndUpdatePptUpload(designCode, false, index);
            })
          } else {
            this.createAndUpdatePptUpload(this.copyPPT.original, false, 0);
          };
        }
      }
    })
  }

  private findDesignCodeUid(designCode,): any {
    return this.allDesignCodes.find(item => item.code == designCode).designCodeUid;
  }

  private findDesignCodeUidinSelectedDesignCode(designCode,): any {
    return this.allDesignCodes.find(item => item.code == designCode).designCodeUid;
  }
  documentsListData: any
  private createAndUpdatePptUpload(designCode, isFromSingle: boolean, index: number): void {
    let foundItem = this.allDesignCodes.find(item => item.code == designCode)
    //update PPT
    if (foundItem.pptReportDocUid) {
      APP_UI_CONFIG.quotation.getAllFilesBasedOnDesignCode.paramList.designCodeUid = foundItem.uuid;
      this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getAllFilesBasedOnDesignCode)
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            this.documentsListData = response;
            let designcodeWisePptUpload = this.documentsListData.find(item => item.designCodeUid == this.findDesignCodeUidinSelectedDesignCode(designCode));
            const designcodeWisePptUploadAsString = JSON.stringify(designcodeWisePptUpload);
            this.addFiles.forEach((droppedFile, index) => {
              if (droppedFile.fileEntry.isFile) {
                const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
                fileEntry.file((file: File) => {
                  const formData = new FormData();
                  formData.append('file', file, droppedFile.relativePath);
                  formData.append('json', designcodeWisePptUploadAsString);
                  APP_UI_CONFIG.quotation.updateFile.paramList.docUid = this.documentsListData.find(item => item.designCodeUid == this.findDesignCodeUidinSelectedDesignCode(designCode))?.docUid
                  this._invokeService.uploadAttachments(APP_UI_CONFIG.quotation.updateFile, formData).subscribe({
                    next: res => {
                      if (res.code == 200) {
                        if (isFromSingle) {
                          this._commonService.success('PPT Attachment Updated successfully');
                        } else {
                          if (index == this.copyPPT.selectedDesignCodes.length - 1) {
                            this._commonService.success('PPT Attachment Updated successfully');
                          }
                        }

                        this.selectedIndex = 1;
                      }
                    },
                    error: error => {
                      this._commonService.handleError(error);
                    }

                  })
                });
              } else {
                const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
              }
            });
          },
          error: error => {

          }
        })
    }
    //save PPT
    else {
      let designcodeWisePptUpload = this.data.data.responseData.designcodes.find(item => item.code == designCode);
      const designcodeWisePptUploadAsString = JSON.stringify(designcodeWisePptUpload);
      this.addFiles.forEach((droppedFile, index) => {
        if (droppedFile.fileEntry.isFile) {
          const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
          fileEntry.file((file: File) => {
            const formData = new FormData();
            formData.append('file', file, droppedFile.relativePath);
            formData.append('docType', 'PPT');
            formData.append('json', designcodeWisePptUploadAsString);
            this._invokeService.uploadAttachments(APP_UI_CONFIG.quotation.createPptUpload, formData).subscribe({
              next: res => {
                if (res.code == 200) {
                  if (isFromSingle) {
                    this._commonService.success('PPT Attachment created successfully');
                  } else {
                    if (index == this.copyPPT.selectedDesignCodes.length - 1) {
                      this._commonService.success('PPT Attachment created successfully');
                    }
                  }
                  this.selectedIndex = 1;
                  //by this view PPT icon enable/disable will work
                }
              },
              error: error => {
                this._commonService.handleError(error);
              }

            })
          });
        } else {
          const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
        }
      });
    }
  }



  //Specifications
  public onSave(): void {
    this.saveSpecificationApiIntegration(true);
  }

  private getConfigList(specName): any {
    this.techSpecificationConfigList.filter(item => {
      if (specName == item.specificationName) {
        return item
      }
    })
  }
  specFileUid: any;
  public fileUploadApiIntegration(event): void {
    const formData = new FormData();
    formData.append('file', event, event.name);
    formData.append(
      'json',
      JSON.stringify({ name: event.name, docType: 'OTHERS' })
    );

    const designUid = this.designCodeResponse.designCodeUid || this.designCodeResponse.uuid;
    APP_UI_CONFIG.sourcingRoadmap.uploadAttachmentsByDesignCodeUid.paramList.designUid = designUid;

    this._invokeService
      .uploadAttachments(
        APP_UI_CONFIG.sourcingRoadmap.uploadAttachmentsByDesignCodeUid,
        formData
      )
      .subscribe({
        next: (res) => {
          this._commonService.success('Attachment has been uploaded successfully');
          this.specFileUid = res.uuid;
        },
        error: (error) => {
          this._commonService.handleError(error);
        },
      });
  }


  getSpecsData: any;
  private saveSpecificationApiIntegration(showToast?): void {
    this.onCopySpec.emit({
      specsConfigurationList: this.techSpecificationConfigList,
      specsUnitMasterConfigurationList: this.specsUnitMasterConfigurationList,
      spec: this.techSpecificationForm?.value,
      designcodeList: this.copySpecification.original,
      alreadySavedSpecs: this.alreadySavedSpecs,
      specFileUid: this.specFileUid
    });

   

    showToast ? this._commonService.warning("TPS saved temporarily; data will lost if not submitted.") : '';
    this.dialogRef.close(true);
  }
  private getMultiplier(matchingTechItem, value): any {
    let specItem = this.specsUnitMasterConfigurationList.find(item => item?.keyword == matchingTechItem?.keyword)?.jsonConfig;
    if (specItem) {
      return JSON.parse(specItem)?.find(item1 => item1.value == value)?.multiplier
    } else {
      return '';
    }
  }

  private getValue(data: any): any {
    const matchingItem = this.techSpecificationConfigList.find(
      (item) => (item?.specificationName === data?.specificationName && item.inputType?.toLowerCase() == 'file')
    );
    if (matchingItem) {
      return this.specFileUid;
    }
    return data.value;
  }



  //on approve
  public onApprove(): void {
    this._commonService.openDialogForConfirmation(`Are you sure you want to approve technical specifications?`)
      .subscribe(confirm => {
        if (confirm) {
          this.saveSpecificationApiIntegration(false);
          let selectedQuotationResponse = this.quotationResponseList?.find(o => o.vendorUid == this.designCodeResponse.vendorUid);
          let { request, ...quotationResponse } = selectedQuotationResponse;
          quotationResponse['designcodes'] = [this.designCodeResponse];
          this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.approveQuotation, null, null, [quotationResponse]).subscribe({
            next: response => {
              if (response && response.code == HTTP_STATUS.SUCCESS) {
                this._commonService.success(`${this.designCodeResponse.code} TPS approved successfully`);
                setTimeout(() => {
                  window.location.reload();
                }, 200);
                this.dialogRef.close(true);
              } else {
                this._commonService.handleError(response);
              }
            }, error: error => {
              this._commonService.handleError(error);
            }
          });
        }
      })
  }



  public onCreateSpecification(): void {
    this.isCreateSpecification = true;
    this.getExistingGroupList();
    setTimeout(() => {
      this.selectedIndex = (this._commonService.isProductCompliance()) ? 1 : 2;
    }, 500);
  }
  existingGroupList: any[] = [];
  private getExistingGroupList(): void {
    let payload = {
      "uids": this.specificationGroupUids.split(",")
    }
    this._invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getExistingGroupList, '', '', payload).subscribe({
      next: response => {
        this.existingGroupList = response;
      }, error: error => {
        this._commonService.handleError(error);
      }
    });
  }

  existingSecList: any[] = [];
  public getExistingSectionList(): void {
    let payload = {
      "groupUid": this.newSpecification.specificationGroup.uuid
    }
    this._invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getExistingSpecificationList, '', '', payload).subscribe({
      next: response => {
        this.existingSecList = response?.filter(item => item);
        this.existingSecList.push("Other");
      }, error: error => {
        this._commonService.handleError(error);
      }
    });

  }

  public onAddSpecification(): void {
    this.techSpecificationConfigList.forEach(item => {
      if (item.unitFor) {
        item.value = this.techSpecificationForm.controls['unit_' + item.unitFor]?.value;
      } else {
        item.value = this.techSpecificationForm.controls[item.specificationName]?.value;
      }
    })
    this.techSpecificationConfigList.push({
      inputType: FORM_CONTROL_TYPES.TEXT,
      specificationName: this.newSpecification.specificationName,
      value: this.newSpecification.value,
      uom: this.newSpecification.uom,
      specificationGroup: this.newSpecification.specificationGroup?.name || '',
      groupName: this.newSpecification.specificationGroup?.name || '',
      sectionName: this.newSpecification.sectionName ? this.newSpecification.sectionName == "Other" ? `${this.newSpecification.sectionOtherName}` : `${this.newSpecification.sectionName}` : '',
      maxLength: 255,
      isNew: true,
      isUpdated: true
    });

    this.prepareFormModel(this.techSpecificationConfigList, true);
    if (this.status != TABLE_ACTION_TYPES.CREATE) {
      this.setSpecificationData(this.alreadySavedSpecs);
    }
    this.isCreateSpecification = false;
    this.newSpecification.specificationGroup = '';
    this.newSpecification.specificationName = '';
    this.newSpecification.sectionOtherName = '';
    this.newSpecification.sectionName = '';
    this.newSpecification.value = '';
    this.newSpecification.uom = '';
    this.selectedIndex = (this._commonService.isProductCompliance()) ? 0 : 1;
  }


  public onCopy(): void {
    this.isCopySpecification = true;
  }

  public onCopySpecification(): void {
    this.copySpecification.selectedDesignCodes.push(this.copySpecification.original)
    this._commonService.success(`TPS copied successfully`);
    this.onCopySpec.emit({
      specsConfigurationList: this.techSpecificationConfigList,
      specsUnitMasterConfigurationList: this.specsUnitMasterConfigurationList,
      spec: this.techSpecificationForm?.value,
      designcodeList: this.copySpecification.selectedDesignCodes,
      specFileUid: this.specFileUid
    });
    this.dialogRef.close(true);

  }

  public onClose(): void {
    this.dialogRef.close(false);
  }

  private preparePayload(designCode): any[] {
    let formValues: any = this.techSpecificationForm.value;
    let payload: TechnicalSpecifications[] = [];
    Object.keys(formValues).forEach(key => {
      let rec: TechnicalSpecifications = new TechnicalSpecifications();
      let spec: TechnicalSpecificationsConfig = this.getSpecificationValueBySpecificationName(key)
      rec.specificationName = key;
      rec.uom = spec?.uom || null;
      rec.productTypeUid = designCode.productTypeUid;
      rec.productTypeName = designCode.productTypeName;
      rec.specificationName = key;
      rec.value = this.checkInputType(key) == FORM_CONTROL_TYPES.DATE?.toLowerCase() ? +new Date(formValues[key]) : formValues[key];
      rec.designCodeUid = designCode.designCodeUid;
      rec.quotationResponseDesigncodeUid = designCode.uuid;
      rec.quotationResponseUid = this.designCodeResponse?.quotationResponseUid;
      (rec.value && !!rec) ? payload.push(rec) : '';
    });
    return payload;
  }

  public closeCopy(): void {
    this.isCopySpecification = false
    this.copySpecification.selectedDesignCodes = [];
  }


  public viewAttachments(): void {
    let docUid = this.documentsList[0].docUid;
    let fileExtension = this.documentsList[0]?.fileExtension
    let headers = new HttpHeaders()
      .set("Authorization", this._commonService.getBasicAuth()).set('Client', this._commonService.getTenantUid());;
    this._httpClient.get(environment.apiUrl + `sourcing/quotation/response/${docUid}/doc`, {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this._fileSaverService.save((<any>res).body, fileExtension + fileExtension);
    });
  }

  public prepareWorkOrerExportData(): any[] {
    let specList = [];
    Object.keys(this.techSpecificationForm.value).forEach(key => {
      let rowObject: any = {};
      if (this.techSpecificationConfigList.find(spec => spec.specificationName)?.inputType?.toLowerCase() == FORM_CONTROL_TYPES.DATE?.toLowerCase()) {
        rowObject = { 'Specification Name': key, 'Value': this._commonService.dateFormat(this.techSpecificationForm.controls[key]?.value) }
      }
      if (this.techSpecificationConfigList.find(spec => (spec?.specificationName == key))?.inputType?.toLowerCase() == FORM_CONTROL_TYPES.MULTI_SELECT.toLowerCase()) {
        rowObject = { 'Specification Name': key, 'Value': this.techSpecificationForm.controls[key]?.value?.join(), 'Unit': '' }
      }
      else {
        let specName = this.techSpecificationConfigList.find(item => item.specificationName == key);
        let unitSpecName = this.techSpecificationConfigList.find(item => item?.unitFor == key);

        if (specName?.specificationName == unitSpecName?.unitFor) {
          if (unitSpecName?.inputType?.toLowerCase() == FORM_CONTROL_TYPES.MULTI_SELECT?.toLowerCase()) {
            rowObject = { 'Specification Name': unitSpecName?.unitFor, 'Value': this.techSpecificationForm.controls[unitSpecName?.unitFor]?.value, 'Unit': this.techSpecificationForm.controls['unit_' + unitSpecName?.unitFor]?.value?.join() }

          } else {
            rowObject = { 'Specification Name': specName?.specificationName, 'Value': this.techSpecificationForm.controls[specName?.specificationName]?.value, 'Unit': this.techSpecificationForm.controls['unit_' + unitSpecName?.unitFor]?.value }
          }
        }
        else {
          rowObject = { 'Specification Name': specName?.specificationName, 'Value': this.techSpecificationForm.controls[specName?.specificationName]?.value, 'Unit': specName?.uom || '' }
        }
      }
      specList.push(rowObject)
    });
    let filteredSpecsList = specList.filter(
      item => item['Specification Name'] !== undefined && item['Specification Name'] !== null && item['Specification Name'].trim() !== ''
    );
    return filteredSpecsList;
  }
  public exportToCSV() {
    let exportData = this.prepareWorkOrerExportData();
    import("xlsx").then(xlsx => {
      const worksheet = xlsx.utils.json_to_sheet(exportData);
      const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
      const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
      this._commonService.saveAsCSVFile(excelBuffer, `${this.designCodeResponse.code}_Specifications`);
    });
  }

  public downloadPDF(): void {
    let exportData = this.prepareWorkOrerExportData();
    import("xlsx").then(xlsx => {
      const worksheet = xlsx.utils.json_to_sheet(exportData);
      const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
      const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
      this._commonService.saveAsPDFFile(excelBuffer, `${this.designCodeResponse.code}_Specifications`);
    });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }

}
