<tfl-table-search-grid style="width: 100%; height: 100%" [tableHeadingName]="'Request Inspection'"
  [addButtonLabel]="'Add'" [showGlobalSearchFilter]="true" [showFilter]="false" [showRefresh]="true"
  [showImport]="false" [rowData]="scheduleList" [showExport]="false" [showAdd]="isVendor || isSeller || isAdmin"
  [showFilterList]="false" [ShowFromToDateFilters]="true" [fromDate]="defaultfromDate" [toDate]="defaultToDate"
  [tableColumnsDef]="columnDefs" [showCsvExport]="true" [rowClassRules]="gridOptions?.rowClassRules"
  [receiveUrlsNotificaiton]="sendUrlsNotification.asObservable()" (emitAddEvent)="getAddEvent($event)"
  (onRefresh)="getRefreshEvent($event)" (emitFilterEvent)="getFilterEvent($event)"
  (emitImportEvent)="getImportEvent($event)" (emitExportEvent)="getExportEvent($event)"
  (emitRowClickEvent)="getRowClickEvent($event)" (emitOnGetDate)="getOnGetDate($event)"
  (emitActionEvent)="getActions($event)" (emitPostResponseEvent)="getPostReponse($event)"></tfl-table-search-grid>