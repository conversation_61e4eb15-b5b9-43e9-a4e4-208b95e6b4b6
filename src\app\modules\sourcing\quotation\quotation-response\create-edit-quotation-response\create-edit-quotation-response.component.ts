import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { CostingMode, designCodeFeedback } from 'app/core/qinspect/_common/AppConstants';
import { Util } from 'app/core/qinspect/_common/util';
import { QuotationResponse } from 'app/core/qinspect/_model/QuotationResponse.model';
import { RoadmapMaster } from 'app/core/qinspect/_model/RoadmapMaster.model';
import { TechnicalSpecifications } from 'app/core/qinspect/_model/TechSpecification.model';
import { QiUser } from 'app/core/qinspect/_model/user.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { FORM_CONTROL_TYPES, HTTP_STATUS, ROLE, TABLE_ACTION_TYPES } from 'app/enum-constants';
import { ICON_LIST } from 'app/icon-list';
import {
  AddAttachmentsByDesigncodeDocumentComponent,
} from 'app/modules/sourcing/roadmap/add-attachments-by-designcode/add-attachments-by-designcode.component';
import { prepareDesignCodesStatus, QuotationStatus, SOURCINGMENUTYPE } from 'app/modules/sourcing/sourcing.configuraiton';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { TflChatboxComponent } from 'app/shared/tfl-chatbox/tfl-chatbox.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import {
  MultiDropdownComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/multi-dropdown/multi-dropdown.component';
import {
  NumericEditorComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/numeric-editor/numeric-editor.component';
import {
  SingleDropdownComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/single-dropdown/single-dropdown.component';
import { Console } from 'console';
import { environment } from 'environments/environment';
import { NgxFileDropEntry } from 'ngx-file-drop';
import { FileSaverService } from 'ngx-filesaver';
import { forkJoin, Observable, ReplaySubject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';

import { AddEditSpecificationsComponent } from '../add-edit-specifications/add-edit-specifications.component';

@Component({
  selector: 'create-edit-quotation-response',
  templateUrl: './create-edit-quotation-response.component.html',
})
export class CreateEditQuotationResponseComponent implements OnInit, OnDestroy {
  @ViewChild('fileUpload') fileUpload: any;
  @ViewChild('gridDesigncode') gridDesigncode: any;


  hdr: string = 'EOI Response';
  public files: NgxFileDropEntry[] = []; ō
  status: string = '';
  user: QiUser
  responseDocs: any[] = [];
  quotationResponseUid: string;
  quotationResponse: QuotationResponse;
  designCodeList: any[] = [];
  isOpenCosting: boolean;
  isClosedCosting: boolean;
  showAddEditDocuments: boolean = false;
  showAddEditDesigncodeDetails: boolean = false;
  showTable = true
  selectedRoadmap: any
  selectedDesigncode: any
  roadmapMaster: RoadmapMaster = new RoadmapMaster();
  showAddEditSpecifications: boolean = false;
  allCurrencyList: any[] = [];
  isAllDesignCodesQuotationSubmitted: boolean = false;
  isQuotationButtonShow: boolean = true;
  isBiddingQuotationButtonShow: boolean = true;
  itemIncludedInCosting: any[] = [];
  itemExcludedInCosting: any[] = [];
  isCopyPriceDialogEnabled: boolean = false;
  isLab: boolean = false;
  public copyPrices: any = {
    original: '',
    selectedDesignCodes: ''
  };
  copyPricesDesignCodes: any[] = [];
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  quotationRequestColumns: any[] = [];
  quotationRequests: QuotationResponse[] = [];

  basicAuth = this.globals.basicAuth;
  columnDefs: any[] = [];
  groupWiseColumns: any[] = [];
  groupWiseRequests: any[] = [];
  menuType: string;

  constructor(
    public dialog: MatDialog,
    public globals: Globals,
    private http: HttpClient,
    private _authService: AuthService,
    public _invokeService: InvokeService,
    public commonService: CommonService,
    public route: ActivatedRoute,
    private _router: Router,
    public fileSaverService: FileSaverService,
  ) {
    this.user = this._authService.getLoggedInUser();
  }

  public ngOnInit(): void {
    this.hdr = this.commonService.isVendor() ? 'EOI Response' : 'Bidding Response';
    this.quotationResponse = new QuotationResponse();
    this.quotationResponseUid = this.route.snapshot.params["uuid"];
    this.menuType = JSON.parse(sessionStorage.getItem("menuType"));
    this.getCurrencyList();
    this.getListItemForIncludedAndExcludedInCosting();
    this.getResponseDetailsById();
  }

  private prepareQuotationResponseColumns(): void {
    this.quotationRequestColumns = [
      { headerName: "Vendor Name", field: "vendorName", sortable: true, wrapText: false, minWidth: 150, unSortIcon: true, hide: this.commonService.isLab() },
      { headerName: "Lab Name", field: "labName", sortable: true, wrapText: false, minWidth: 150, unSortIcon: true, hide: this.commonService.isVendor() },
      { headerName: "Collection Name", field: "collectionName", sortable: true, unSortIcon: true, autoHeight: false, minWidth: 180, },
      { headerName: "Brand", field: "brand", sortable: true, unSortIcon: true, autoHeight: false },
      { headerName: "GL Name", field: "glName", sortable: true, unSortIcon: true, },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, wrapText: false, minWidth: 130,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Status", field: "status", unSortIcon: true, sortable: false, wrapText: false,
        pinned: 'right', minWidth: 150,
        cellRenderer: (params) => { return this.prepareDesignCodesStatusLabWiseAndVendorWise(params.data) }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtonsByResponse(params.data), onAction: (action) => this.onResponseAction(action) }
        }
      }
    ];
  }

  public prepareActionIconButtonsByResponse(row: any): any {
    let iconsList: any[] = [];
    if (this._authService.isSupplier(this.user)) {
      if (row.status == QuotationStatus.REJECTED) {
        iconsList.push({ type: 'view_reject_comments_response', title: "Add/View Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
      }
      else if (row.status == QuotationStatus.ADDITIONALSPECS && this.menuType == SOURCINGMENUTYPE.TPS) {
        iconsList.push({ type: 'feedback_specs_response', title: "Specs Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
      }
      else if (row.status == QuotationStatus.BIDDINGREJECTED) {
        iconsList.push({ type: 'view_reject_comments_response', title: "Add/View Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
      }
    }
    return iconsList;
  }

  private onResponseAction(action): void {
    switch (action.type) {
      case "view_reject_comments_response":
        this.getFeedbackListByRequest(action.data);
        break;
      case "feedback_specs_response":
        this.getFeedbackListByRequest(action.data);
        break;
      default:
        break;
    }
  }

  private prepareColumnOpenCosting(): any[] {
    //for open costing
    return [
      {
        headerName: "DID No", field: "code", sortable: true, wrapText: false, unSortIcon: true, pinned: 'left', minWidth: 135,
      },

      {
        headerName: "Production Lead Time(in Days)*", field: "productionLeadTime", sortable: false, minWidth: 150,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent,
        cellRendererParams: params => {
          return {
            label: "Ex.5",
            minFractionDigits: 0,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }

        }
      },
      {
        headerName: 'Currency',
        field: 'currency', minWidth: 100,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: SingleDropdownComponent,
        cellRendererParams: params => {
          return {
            values: this.allCurrencyList,
            label: "",
            defaultValue: params.data.currency ? params.data.currency : 'INR',
            isEditable: this.isFieldEditable(params.data),
            onChange: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: "MOQ*", field: "moq", sortable: false, minWidth: 90,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent,
        cellRendererParams: params => {
          return {
            label: "Ex.3000",
            minFractionDigits: 0,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }

        }
      },
      {
        headerName: "Production Cost*", field: "productionProposedPrice", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, maxWidth: 140,
        cellRendererParams: params => {
          return {
            label: "Ex.3000.66",
            minFractionDigits: 2,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: "Packaging Cost*", field: "packingProposedPrice", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, minWidth: 140,
        cellRendererParams: params => {
          return {
            label: "Ex.3000.88",
            minFractionDigits: 2,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: "Shipping Cost*", field: "shipmentProposedPrice", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, minWidth: 140,
        cellRendererParams: params => {
          return {
            label: "Ex.3000.99",
            minFractionDigits: 2,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: "Margin %*", field: "marginProposedPrice", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, minWidth: 100,
        cellRendererParams: params => {
          return {
            label: "Ex.10.88",
            minFractionDigits: 2,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: "Total Cost", field: "totalProposedPrice", sortable: false, editable: false, minWidth: 100
      },
      {
        headerName: "Tax %*", field: "taxProposedPrice", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, maxWidth: 90,
        cellRendererParams: params => {
          return {
            label: "Ex.12",
            minFractionDigits: 2,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }

        }
      }, {
        headerName: "Category", field: "glName", sortable: false, wrapText: false, minWidth: 130,
        cellRenderer: params => {
          return this.roadmapMaster.glName;
        }
      },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, wrapText: false, minWidth: 130,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Brand", field: "brand", sortable: false, wrapText: false, minWidth: 100,
        cellRenderer: params => {
          return this.roadmapMaster.brand;
        }
      },
      {
        headerName: "Final Cost with Tax", field: "finalCostWithTax", sortable: false, editable: false, pinned: 'right', maxWidth: 150,
      },
      {
        headerName: "Status", field: "status", unSortIcon: true, sortable: false, wrapText: false,
        pinned: 'right', minWidth: 180,
        cellRenderer: (params) => { return this.prepareDesignCodesStatus(params) }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
        }
      }
    ];
  }
  private prepareColumnClosedCosting(): any[] {
    //for closed costing
    return [
      { headerName: "DID No", field: "code", sortable: false, unSortIcon: true, wrapText: false, pinned: 'left' },
      {
        headerName: "Category", field: "glName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.glName;
        }
      },
      {
        headerName: "Production Lead Time(in Days)*", field: "productionLeadTime", sortable: false, minWidth: 150,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent,
        cellRendererParams: params => {
          return {
            label: "Ex.5",
            minFractionDigits: 0,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }

        }
      },
      {
        headerName: 'Currency', maxWidth: 100,
        field: 'currency',
        cellRendererFramework: SingleDropdownComponent,
        cellClass: 'ag-grid-custom-input',
        cellRendererParams: params => {
          return {
            values: this.allCurrencyList,
            label: "",
            defaultValue: params.data.currency ? params.data.currency : 'INR',
            isEditable: this.isFieldEditable(params.data),
            onChange: (params) => this.updateColumnValues(params)
          }

        },
      },
      {
        headerName: "FOB*", field: "fob", sortable: false, unSortIcon: true, maxWidth: 80,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent,
        cellRendererParams: params => {
          return {
            label: "Ex.10.88",
            minFractionDigits: 2,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }
        }

      },
      {
        headerName: "MOQ*", field: "moq", sortable: false,
        cellClass: 'ag-grid-custom-input', maxWidth: 80,
        cellRendererFramework: NumericEditorComponent,
        cellRendererParams: params => {
          return {
            label: "Ex.1000",
            minFractionDigits: 0,
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: "Items Included in costing*", field: "scope", sortable: false, minWidth: 200,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: MultiDropdownComponent,
        cellRendererParams: params => {
          return {
            values: this.itemIncludedInCosting,
            label: "",
            isEditable: this.isFieldEditable(params.data),
            onChange: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: "Items excluded in costing*", field: "outOfScope", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: MultiDropdownComponent, minWidth: 200,
        cellRendererParams: params => {
          return {
            values: this.itemExcludedInCosting,
            label: "",
            isEditable: this.isFieldEditable(params.data),
            onChange: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Brand", field: "brand", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.brand;
        }
      },
      {
        headerName: "Status", field: "status", unSortIcon: true, sortable: false, wrapText: false,
        pinned: 'right', minWidth: 180,
        cellRenderer: (data) => { return this.prepareDesignCodesStatus(data) }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
        }
      }
    ];
  }
  private prepareColumnLab(): any[] {
    //for lab
    return [
      {
        headerName: "Group", field: "groupName", sortable: false, unSortIcon: true, wrapText: false, pinned: 'left', minWidth: 100
      },
      { headerName: "DID No", field: "code", sortable: false, unSortIcon: true, wrapText: false, pinned: 'left' },
      {
        headerName: "Category", field: "glName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.glName;
        }
      },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Brand", field: "brand", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.brand;
        }
      },
      {
        headerName: "Required Samples*", field: "labSamplesRequired", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, minWidth: 200,
        cellRendererParams: params => {
          return {
            label: "Ex.10",
            defaultValue: params.data.labSamplesRequired ? params.data.labSamplesRequired : '',
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: "No Of Days For Lab Testing*", field: "noOfDaysForLabTesting", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, minWidth: 200,
        cellRendererParams: params => {
          return {
            label: "Ex.10",
            defaultValue: params.data.noOfDaysForLabTesting ? params.data.noOfDaysForLabTesting : '',
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }
        }
      },
      {
        headerName: 'Currency*',
        field: 'currency', minWidth: 100,
        cellRendererFramework: SingleDropdownComponent,
        cellClass: 'ag-grid-custom-input',
        cellRendererParams: params => {
          return {
            values: this.allCurrencyList,
            label: "",
            defaultValue: params.data.currency ? params.data.currency : '',
            isEditable: this.isFieldEditable(params.data),
            onChange: (params) => this.updateColumnValues(params)
          }
        },
        valueGetter: params => {
          return 'INR';
        }
      },
      {
        headerName: "Total Testing Cost*", field: "labTestingTotalPrice", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, minWidth: 150,
        cellRendererParams: params => {
          return {
            label: "Ex.3000.78",
            defaultValue: params.data.labTestingTotalPrice ? params.data.labTestingTotalPrice : '',
            isEditable: this.isFieldEditable(params.data),
            OnInput: (params) => this.updateColumnValues(params)
          }
        }
      },

      {
        headerName: "Status", field: "status", unSortIcon: true, sortable: false, wrapText: false,
        pinned: 'right', minWidth: 100,
        cellRenderer: (data) => { return this.prepareDesignCodesStatus(data.data) }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
        }
      }
    ];
  }
  private prepareGroupWiseColumnLab(): any[] {
    //for lab
    return [
      {
        headerName: "Group", field: "groupName", sortable: false, unSortIcon: true, wrapText: false, pinned: 'left', maxWidth: 100
      },
      {
        headerName: "Category", field: "glName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.glName;
        }
      },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Brand", field: "brand", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.brand;
        }
      },
      {
        headerName: "Required Samples*", field: "labSamplesRequired", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, minWidth: 200,
        cellRendererParams: params => {
          return {
            label: "Ex.10",
            defaultValue: params.data.labSamplesRequired ? params.data.labSamplesRequired : '',
            isEditable: this.prepareGroupEditFields(params),
            OnInput: (params) => this.updateGroupWiseColumnValues(params)
          }
        }
      },
      {
        headerName: "No Of Days For Lab Testing*", field: "noOfDaysForLabTesting", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, minWidth: 200,
        cellRendererParams: params => {
          return {
            label: "Ex.10",
            defaultValue: params.data.noOfDaysForLabTesting ? params.data.noOfDaysForLabTesting : '',
            isEditable: this.prepareGroupEditFields(params),
            OnInput: (params) => this.updateGroupWiseColumnValues(params)
          }
        }
      },
      {
        headerName: 'Currency*',
        field: 'currency', minWidth: 100,
        cellRendererFramework: SingleDropdownComponent,
        cellClass: 'ag-grid-custom-input',
        cellRendererParams: params => {
          return {
            values: this.allCurrencyList,
            label: "",
            defaultValue: params.data.currency ? params.data.currency : '',
            isEditable: this.prepareGroupEditFields(params),
            onChange: (params) => this.updateGroupWiseColumnValues(params)
          }
        },
        valueGetter: params => {
          return 'INR';
        }
      },
      {
        headerName: "Total Testing Cost*", field: "labTestingTotalPrice", sortable: false,
        cellClass: 'ag-grid-custom-input',
        cellRendererFramework: NumericEditorComponent, minWidth: 150,
        cellRendererParams: params => {
          return {
            label: "Ex.3000.78",
            defaultValue: params.data.labTestingTotalPrice ? params.data.labTestingTotalPrice : '',
            isEditable: this.prepareGroupEditFields(params),
            OnInput: (params) => this.updateGroupWiseColumnValues(params)
          }
        }
      },
      {
        headerName: "Status", field: "status", unSortIcon: true, sortable: false, wrapText: false,
        pinned: 'right', minWidth: 100,
        cellRenderer: (data) => { return this.prepareDesignCodesStatus(data.data) }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 150,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
        }
      },
      // {
      //   headerName: 'Actions',
      //   pinned: 'right',
      //   minWidth: 200,
      //   cellRendererFramework: CellActionComponent,
      //   cellRendererParams: (params) => {
      //     return { actions: params.data.code != '00-For Reference' ? [{ type: "VIEW_GROUPWISE_DETAILS", title: "View DesignCodes", icon: ICON_LIST.VIEW, data: params.data }] : [], onAction: (action) => this.onGroupWiseAction(action) }
      //   }
      // }
    ];
  }
  isShowGroupWiseDesignCodes: boolean = false;
  groupWiseDesignCodes: any[] = [];
  groupWiseColumnDefs: any[] = [];
  private onGroupWiseAction(action): void {
    switch (action.type) {
      case 'VIEW_GROUPWISE_DETAILS':
        this.isShowGroupWiseDesignCodes = true;
        this.groupWiseDesignCodes = action.data?.designCodes;
        this.groupWiseColumnDefs = this.GroupWiseDesignColumns();
        break;

      default:
        break;
    }
  }

  private GroupWiseDesignColumns(): any[] {
    return [
      {
        headerName: "Group", field: "groupName", sortable: false, unSortIcon: true, wrapText: false, pinned: 'left', maxWidth: 100
      },
      { headerName: "DID No", field: "code", sortable: false, unSortIcon: true, wrapText: false, pinned: 'left' },
      {
        headerName: "Category", field: "glName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.glName;
        }
      },
      {
        headerName: "Sub Category", field: "subCatName", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.subCatName;
        }
      },
      {
        headerName: "Brand", field: "brand", sortable: false, unSortIcon: true, wrapText: false,
        cellRenderer: params => {
          return this.roadmapMaster.brand;
        }
      },
      {
        headerName: "Reference ASINs", field: "referenceAsin", sortable: true, unSortIcon: true, minWidth: 300,
        cellRenderer: (params) => {
          return this.commonService.prepareReferenceASINsLinks(this.roadmapMaster?.referenceAsin);
        }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: (params.data.testProtocolUid && !this.commonService.getUseRole().includes(ROLE.VENDOR)) ? [{ type: "VIEW_TEST_PROTOCOL", title: "View Protocol", icon: ICON_LIST.MICROSCOPE, data: params.data }, { type: 'view_attachments', title: "View Attachments", icon: 'view_file_blue', data: params.data }] : [], onAction: (action) => this.onAction(action) }
        }
      }


    ];
  }

  private prepareGroupEditFields(params): boolean {
    if (params.data.code == '00-For Reference') {
      return false;
    } else if (this.quotationResponse.labUid && this.isBiddingQuotationButtonShow) {
      return true;
    } else {
      return false;
    }
  }
  enableSubmitTps: any;
  private getResponseDetailsById(): void {
    APP_UI_CONFIG.quotation.getResponseDetails.paramList.responseUid = this.quotationResponseUid;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getResponseDetails)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.quotationResponse = response;
            this.quotationRequests = [response];
            this.getQuotationStatus();
            this.getRoadmapDetailsByRoadmapUid();
            this.isOpenCosting = this.quotationResponse.costingMode == 1;
            this.isClosedCosting = this.quotationResponse.costingMode == 0;
            this.designCodeList = this.commonService.sortByAlphanumeric(this.quotationResponse?.designcodes, 'code', 'code').map(item => ({
              groupName: this.quotationResponse.groupedDesignCodes?.find(designCode => designCode.designCodeUid == item.designCodeUid)?.groupName || '',
              labReference: this.prepareReferenceAsign(item),
              ...item
            }))


            this.enableSubmitTps = this.designCodeList.some(item => item.status == QuotationStatus.RFQ1APPROVED || item.status == QuotationStatus.ADDITIONALSPECS)
            if (this.menuType == SOURCINGMENUTYPE.TPS) {
              this.designCodeList = this.designCodeList.filter(item => item.status != -1)
            }
            this.designCodeList.forEach(item => {
              if (!item['currency']) {
                item['currency'] = 'INR';
              }
            });
            this.prepareGroupWiseRequest();
            this.prepareReferenceData()
            if (this.quotationResponse.status != 0) {
              //this.fillTable(false)
            }
          }
        },
        error: error => {
          this.commonService.handleError(error);
        }
      })
  }

  private prepareReferenceAsign(eachDesignCode): void {
    this.quotationResponse.designcodemaster.find(item => item.uuid == eachDesignCode.designcodeUid)?.referenceCode || '';
  }

  private prepareGroupWiseRequest(): void {
    this.groupWiseRequests = [];
    this.designCodeList.forEach(eachDesignCode => {
      let index = this.groupWiseRequests.findIndex(item => item.groupName == eachDesignCode.groupName);
      if (index > -1) {
        //this.groupWiseRequests[index]["labSamplesRequired"] = this.groupWiseRequests[index]["labSamplesRequired"] + this.checkNull(eachDesignCode.labSamplesRequired);
        // this.groupWiseRequests[index]["noOfDaysForLabTesting"] = this.groupWiseRequests[index]["noOfDaysForLabTesting"] + this.checkNull(eachDesignCode.noOfDaysForLabTesting);
        // this.groupWiseRequests[index]["labSamplesRequired"] = this.groupWiseRequests[index]["labSamplesRequired"] + this.checkNull(eachDesignCode.labSamplesRequired);
        //this.groupWiseRequests[index]["labTestingTotalPrice"] = this.groupWiseRequests[index]["labTestingTotalPrice"] + this.checkNull(eachDesignCode.labTestingTotalPrice);
        this.groupWiseRequests[index]['designCodes'].push(eachDesignCode);
      } else {
        let groupDesignCode = {
          groupName: eachDesignCode.groupName,
          glName: eachDesignCode.glName,
          subCatName: eachDesignCode.subCatName,
          brand: eachDesignCode.brand,
          labSamplesRequired: this.checkNull(eachDesignCode.labSamplesRequired),
          noOfDaysForLabTesting: this.checkNull(eachDesignCode.noOfDaysForLabTesting),
          currency: eachDesignCode.currency,
          labTestingTotalPrice: this.checkNull(eachDesignCode.labTestingTotalPrice),
          designCodes: [eachDesignCode]
        }

        this.groupWiseRequests.push(groupDesignCode);
      }
    })

  }
  //Set columns definitons based on costingMode
  private setColumnDefs(costingMode): void {
    this.prepareQuotationResponseColumns();
    if (costingMode == 0) {
      this.columnDefs = this.prepareColumnClosedCosting();
      this.isQuotationButtonShow = this.designCodeList.some(item => (item.status == QuotationStatus.CREATED || item.status == QuotationStatus.REJECTED || item.status == QuotationStatus.PENDING || item.status == QuotationStatus.QuotationSubmitted));
    }
    else if (costingMode == 1) {
      this.columnDefs = this.prepareColumnOpenCosting();
      this.isQuotationButtonShow = this.designCodeList.some(item => (item.status == QuotationStatus.CREATED || item.status == QuotationStatus.REJECTED || item.status == QuotationStatus.PENDING || item.status == QuotationStatus.QuotationSubmitted));
    }
    else if (costingMode == 2) {
      this.columnDefs = this.prepareColumnLab();
      this.groupWiseColumns = this.prepareGroupWiseColumnLab();
      this.isBiddingQuotationButtonShow = this.designCodeList.some(item => (item.status == QuotationStatus.CREATED || item.status == QuotationStatus.PENDING || item.status == QuotationStatus.BIDDINGREJECTED || item.status == QuotationStatus.QuotationSubmitted));
    }
  }
  private isFieldEditable(row): boolean {
    if (this.commonService.getUseRole()?.includes(ROLE.VENDOR) || this.commonService.getUseRole()?.includes(ROLE.ADMIN)) {
      return row.status == QuotationStatus.CREATED || row.status == QuotationStatus.REJECTED || row.status == QuotationStatus.PENDING || row.status == QuotationStatus.QuotationSubmitted;
    }
    else if (this.commonService.getUseRole()?.includes(ROLE.LAB) || this.commonService.getUseRole()?.includes(ROLE.ADMIN)) {
      return row.status == QuotationStatus.CREATED || row.status == QuotationStatus.BIDDINGREJECTED || row.status == QuotationStatus.PENDING || row.status == QuotationStatus.QuotationSubmitted;;
    }
  }
  //Get currency list
  private getCurrencyList(): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getCurrencyList)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            response?.forEach(currency => {
              this.allCurrencyList.push({ code: currency, name: currency });
            })
          }
        }, error: error => {
          this.commonService.handleError(error);
        }
      });
  }

  //Get list for items to exclude and include in coasting
  private getListItemForIncludedAndExcludedInCosting(): void {
    APP_UI_CONFIG.sourcingRoadmap.getAllDataByKeyWord.paramList.keyWord = 'COST INCLUDING';
    APP_UI_CONFIG.quotation.getAllDataByKeyWord.paramList.keyWord = 'COST EXCLUDING';
    const costIncluded$ = this._invokeService.serviceInvocation(APP_UI_CONFIG.sourcingRoadmap.getAllDataByKeyWord);
    const costExcluded$ = this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getAllDataByKeyWord);

    forkJoin([costIncluded$, costExcluded$])
      .pipe(takeUntil(this.$destroyed))
      .subscribe(results => {
        let costIncluded: any[] = results[0];
        let costExcluded: any[] = results[1];
        this.itemIncludedInCosting = costIncluded.map(item => ({ code: item.name, name: item.name }));
        this.itemExcludedInCosting = costExcluded.map(item => ({ code: item.name, name: item.name }));
      }), (err: any) => {
        this.commonService.handleError(err);
      };
  }


  public returnEditable(params): boolean {
    if (this.quotationResponse.status == QuotationStatus.CREATED) {
      return true;
    } else {
      return true;
    }
  }
  public returnSpecificationsEditable(params): boolean {
    if (this.quotationResponse.status == QuotationStatus.CREATED) {
      return true;
    } else {
      return true;
    }
  }

  private prepareReferenceData(): void {
    if (this.quotationResponse['labUid']) {
      let labData = { isNotEditable: true, viewStatus: false, code: '00-For Reference', productionLeadTime: '10', currency: 'INR', labSamplesRequired: 10, noOfDaysForLabTesting: 20, labTestingTotalPrice: 10000.56 }
      this.designCodeList.unshift(labData);
      this.groupWiseRequests.unshift(labData);
      if (this.commonService.getUseRole()?.includes(ROLE.LAB)) {
        this.hdr = 'Submit/View Bidding';
      } else {
        this.hdr = 'View Bidding';
      }
    }
    if (this.isOpenCosting) {
      let openCostData = { isNotEditable: true, viewStatus: false, code: '00-For Reference', productionLeadTime: '10', currency: 'INR', moq: 2000, productionProposedPrice: 3000.55, packingProposedPrice: 2000.75, shipmentProposedPrice: 3000.75, marginProposedPrice: 10, totalProposedPrice: 5500.75, taxProposedPrice: 12 }
      this.designCodeList.unshift(openCostData);
      if (this.commonService.getUseRole()?.includes(ROLE.VENDOR)) {
        this.hdr = this.menuType == SOURCINGMENUTYPE.QUOTATION ? 'Submit/View EOI' : 'Submit/View TPS';
      } else {
        this.hdr = this.menuType == SOURCINGMENUTYPE.QUOTATION ? 'Submit/View EOI' : 'View TPS';
      }
    }
    if (this.isClosedCosting) {
      let closedCostingData = { isNotEditable: true, viewStatus: false, code: '00-For Reference', productionLeadTime: '10', currency: 'INR', moq: 2000, fob: 3000.75, scope: 'Packing Cost', outOfScope: 'Product Cost' }
      this.designCodeList.unshift(closedCostingData);
      if (this.commonService.getUseRole()?.includes(ROLE.VENDOR)) {
        this.hdr = this.menuType == SOURCINGMENUTYPE.QUOTATION ? 'Submit/View EOI' : 'Submit/View TPS';
      } else {
        this.hdr = this.menuType == SOURCINGMENUTYPE.QUOTATION ? 'Submit/View EOI' : 'View TPS';
      }
    }

  }

  private getRoadmapDetailsByRoadmapUid(): void {
    APP_UI_CONFIG.sourcingRoadmap.getByUid.paramList.roadMapUid = this.quotationResponse['roadmapUid'];
    this._invokeService.serviceInvocation(APP_UI_CONFIG.sourcingRoadmap.getByUid)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.roadmapMaster = response;
            this.setColumnDefs(this.quotationResponse['costingMode']);
          }
        }
      })
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];
    if (row.code != '00-For Reference') {
      if (this.menuType == SOURCINGMENUTYPE.QUOTATION) {
        iconsList.push({ type: 'view_attachments', title: "View Attachments", icon: 'view_file_blue', data: row });
        if (this.commonService.getUseRole().includes(ROLE.VENDOR) || this.commonService.getUseRole().includes(ROLE.ADMIN)) {
          if (row.status == QuotationStatus.REJECTED) {
            iconsList.push({ type: 'view_reject_comments', title: "Add/View Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
          }
          if (this.checkIfEveryValueExist(row) && (row.status == QuotationStatus.PENDING || row.status == QuotationStatus.QuotationSubmitted || row.status == QuotationStatus.REJECTED)) {
            iconsList.push({ type: "COPY_PRICES", title: "In order to copy prices to other ASINs click here", icon: ICON_LIST.COPY_DATA_BLUE, data: row });
          }
        }
      }
      else if (this.menuType == SOURCINGMENUTYPE.TPS) {
        if (this.commonService.isVendor()) {
          iconsList.push({ type: 'add_attachments', title: "Add/View Attachments", icon: ICON_LIST.ADD_DOC_BLUE, data: row, });
        } else {
          iconsList.push({ type: 'view_attachments', title: "View Attachments", icon: 'view_file_blue', data: row });
        }
        if (this.commonService.getUseRole().includes(ROLE.VENDOR) || this.commonService.getUseRole().includes(ROLE.ADMIN)) {
          if (row.status >= QuotationStatus.RFQ2APPROVED) {
            iconsList.push({ type: 'view_specs', title: "View Specs", icon: 'view', data: row });
          }
          if (row.status == QuotationStatus.RFQ1APPROVED) {
            iconsList.push({ type: 'add_specs', title: "Add Specs", icon: 'plus-circle_blue', data: row });
            if (row?.pptReportDocUid) {
              iconsList.push({ type: 'view_ppt', title: "View Test Report", icon: ICON_LIST.REPORT_BLUE, data: row });
            }
          }
          if (row.status == QuotationStatus.ADDITIONALSPECS) {
            iconsList.push({ type: 'feedback_specs', title: "Specs Feedback", icon: row.isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row });
            iconsList.push({ type: 'view_edit_specs', title: "Edit/View Specs", icon: 'view', data: row });
          }
          if (row?.pptReportDocUid && (row.status == QuotationStatus.ADDITIONALSPECS || row.status == QuotationStatus.BIDDINGREREQUESTSTARTED || row.status == QuotationStatus.RFQ2APPROVED)) {
            iconsList.push({ type: 'view_ppt', title: "View Test Report", icon: ICON_LIST.REPORT_BLUE, data: row });
          }
        }
      }
      else if (this.menuType == SOURCINGMENUTYPE.BIDDING) {
        iconsList.push({ type: "VIEW_GROUPWISE_DETAILS", title: "View DesignCodes", icon: ICON_LIST.VIEW, data: row })
        let rowStatus = row.designCodes.find(item => item.code)?.status
        if (rowStatus == QuotationStatus.BIDDINGREJECTED && (this.commonService.getUseRole().includes(ROLE.LAB) || this.commonService.getUseRole().includes(ROLE.ADMIN))) {
          iconsList.push({ type: 'view_reject_comments', title: "Add/View Feedback", icon: row?.designCodes[0].isChatHistoryExist ? ICON_LIST.MESSAGE_BLUE_HIGHLIGHT_GREEN : ICON_LIST.MESSAGE_BLUE, data: row?.designCodes[0] });
        }
      }
    }
    return iconsList;
  }

  public onAction(action): void {
    switch (action.type) {
      case 'add_specs':
        const dialogRef = this.dialog.open(AddEditSpecificationsComponent, {
          minWidth: '90%',
          width: '90%',
          data: { data: { designCodeData: action.data, responseData: this.quotationResponse, specificationGroupUids: this.roadmapMaster.specificationGroupUids, roadmapDetails: this.roadmapMaster }, status: TABLE_ACTION_TYPES.CREATE, isCopied: false },
          disableClose: true,
        });
        dialogRef.componentInstance.onCopySpec.subscribe(response => {
          if (response) {
            this.designCodeList.forEach(designCodeItem => {
              if (response.designcodeList?.includes(designCodeItem.code)) {
                designCodeItem['copiedSpec'] = response.spec;
                designCodeItem['specConfigList'] = response.specsConfigurationList;
                designCodeItem['specsUnitMasterConfigurationList'] = response.specsUnitMasterConfigurationList;
                designCodeItem['specFileUid'] = response.specFileUid;
              } else {
                return;
              }
            })
          }
        });
        dialogRef.afterClosed().subscribe(response => {
        });
        break;
      case 'view_edit_specs':
        const dialogRefViewEdit = this.dialog.open(AddEditSpecificationsComponent, {
          minWidth: '90%',
          width: '90%',
          data: { data: { designCodeData: action.data, responseData: this.quotationResponse, specificationGroupUids: this.roadmapMaster.specificationGroupUids, roadmapDetails: this.roadmapMaster }, status: TABLE_ACTION_TYPES.EDIT, isCopied: false },
          disableClose: true,
        });
        dialogRefViewEdit.componentInstance.onCopySpec.subscribe(response => {
          if (response) {
            this.designCodeList.forEach(designCodeItem => {
              if (response.designcodeList?.includes(designCodeItem.code)) {
                designCodeItem['copiedSpec'] = response.spec;
                designCodeItem['specConfigList'] = response.specsConfigurationList;
                designCodeItem['specsUnitMasterConfigurationList'] = response.specsUnitMasterConfigurationList;
                designCodeItem['specFileUid'] = response.specFileUid;
              } else {
                return;
              }
            })
          }
        });
        dialogRefViewEdit.afterClosed().subscribe(response => {
        });
        break;
      case 'view_specs':
        this.commonService.openDialog(AddEditSpecificationsComponent, { designCodeData: action.data, responseData: this.quotationResponse, specificationGroupUids: this.roadmapMaster.specificationGroupUids, roadmapDetails: this.roadmapMaster }, TABLE_ACTION_TYPES.VIEW, '90%').subscribe(res => {
          if (res) {
            this.onRefresh();
          }
        });
        break;
      case 'feedback_specs':
        this.getFeedbackList(action.data);
        break;
      case 'view_ppt':
        this.getFileList(action.data);
        break;
      case 'view_attachments':
        this.commonService.openDialog(AddAttachmentsByDesigncodeDocumentComponent, { isDocumentUpload: false, designCode: action.data, roadMap: { uuid: '' } }, '', '50%').subscribe(res => {
          if (res) {
          }
        });
        break;
      case 'add_attachments':
        this.onAddAttachments(action.data)
        // this.commonService.openDialog(AddAttachmentsByDesigncodeDocumentComponent, { isDocumentUpload: false, designCode: action.data, roadMap: { uuid: '' } }, '', '50%').subscribe(res => {
        //   if (res) {
        //   }
        // });
        break;
      case 'view_reject_comments':
        this.getFeedbackList(action.data);
        break;
      case 'COPY_PRICES':
        this.isCopyPriceDialogEnabled = true;
        this.copyPrices.original = action?.data;
        this.copyPrices.selectedDesigncode = [];
        this.copyPricesDesignCodes = this.designCodeList.filter(item => (item.uuid) && (item.uuid != action.data.uuid));
        this.isLab = this.copyPricesDesignCodes.some(item => item.labName && item.labUid);
        break;
      case 'VIEW_TEST_PROTOCOL':
        const url = this._router.serializeUrl(
          this._router.createUrlTree(['testing/testProtocol/edit/' + action.data.testProtocolUid])
        );
        window.open(url, '_blank');
        break;

      case 'VIEW_GROUPWISE_DETAILS':
        this.isShowGroupWiseDesignCodes = true;
        this.groupWiseDesignCodes = action.data?.designCodes;
        this.groupWiseColumnDefs = this.GroupWiseDesignColumns();
        break;
      default:
      // code block
    }
  }

  private onAddAttachments(action): void {
    this.commonService.openDialog(AddAttachmentsByDesigncodeDocumentComponent, { isDocumentUpload: true, designCode: action, roadMap: this.selectedRoadmap }, '', '50%')
      .pipe(takeUntil(this.$destroyed))
      .subscribe(res => {
        if (res) {
        }
      });
  }

  public onCopyPrices(): void {
    if (this.quotationResponse['labUid']) {
      this.designCodeList.forEach(item => {
        if (item.code != this.copyPrices.original.code && this.copyPrices.selectedDesignCodes.find(selectedD => selectedD == item.code) && (item.status == QuotationStatus.PENDING)) {
          item['productionLeadTime'] = this.copyPrices.original?.productionLeadTime;
          item['currency'] = this.copyPrices.original?.currency;
          item['labSamplesRequired'] = this.copyPrices.original?.labSamplesRequired;
          item['noOfDaysForLabTesting'] = this.copyPrices.original?.noOfDaysForLabTesting;
          item['labTestingTotalPrice'] = this.copyPrices.original?.labTestingTotalPrice;
        }
      })
    }
    if (this.isOpenCosting) {
      this.designCodeList.forEach(item => {
        if (item.code != this.copyPrices.original.code && this.copyPrices.selectedDesignCodes.find(selectedD => selectedD == item.code) && (item.status == QuotationStatus.PENDING || item.status == QuotationStatus.QuotationSubmitted || item.status == QuotationStatus.REJECTED)) {
          item['productionLeadTime'] = this.copyPrices.original?.productionLeadTime;
          item['currency'] = this.copyPrices.original?.currency;
          item['moq'] = this.copyPrices.original?.moq;
          item['productionProposedPrice'] = this.copyPrices.original?.productionProposedPrice;
          item['packingProposedPrice'] = this.copyPrices.original?.packingProposedPrice;
          item['shipmentProposedPrice'] = this.copyPrices.original?.shipmentProposedPrice;
          item['marginProposedPrice'] = this.copyPrices.original?.marginProposedPrice;
          item['totalProposedPrice'] = this.copyPrices.original?.totalProposedPrice;
          item['taxProposedPrice'] = this.copyPrices.original?.taxProposedPrice;
          item['finalCostWithTax'] = this.copyPrices.original?.finalCostWithTax;
        }
      })
    } else {
      this.designCodeList.forEach(item => {
        if (item.code == this.copyPrices.original.code) {
          item.scope = typeof (this.copyPrices.original?.scope) == 'string' ? this.copyPrices.original?.scope : this.copyPrices.original?.scope.toString();
          item.outOfScope = typeof (this.copyPrices.original?.outOfScope) == 'string' ? this.copyPrices.original?.outOfScope : this.copyPrices.original?.outOfScope.toString();
        }
        if (item.code != this.copyPrices.original.code && this.copyPrices.selectedDesignCodes.find(selectedD => selectedD == item.code) && (item.status == QuotationStatus.PENDING || item.status == QuotationStatus.QuotationSubmitted || item.status == QuotationStatus.REJECTED)) {
          item['productionLeadTime'] = this.copyPrices.original?.productionLeadTime;
          item['currency'] = this.copyPrices.original?.currency;
          item['fob'] = this.copyPrices.original?.fob;
          item['moq'] = this.copyPrices.original?.moq;
          item['scope'] = typeof (this.copyPrices.original?.scope) == 'string' ? this.copyPrices.original?.scope : this.copyPrices.original?.scope.toString();
          item['outOfScope'] = typeof (this.copyPrices.original?.outOfScope) == 'string' ? this.copyPrices.original?.outOfScope : this.copyPrices.original?.outOfScope.toString();
        }
      });
    }
    this.isCopyPriceDialogEnabled = false;
    this.columnDefs = [];
    this.copyPrices.selectedDesignCodes = [];
    setTimeout(() => {
      this.setColumnDefs(this.quotationResponse['costingMode']);
    }, 100);

  }

  private viewAttchments(documentsList): void {
    let docUid = documentsList.find(item => item.docUid)?.docUid;
    let fileExtension = documentsList.find(item => item.docUid)?.fileExtension
    let headers = new HttpHeaders()
      .set("Authorization", this.commonService.getBasicAuth()).set('Client', this.commonService.getTenantUid());
    this.http.get(environment.apiUrl + `sourcing/quotation/response/${docUid}/doc`, {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, fileExtension + fileExtension);
    });
  }

  //get docUid from here to get view attachments
  private getFileList(row): void {
    APP_UI_CONFIG.quotation.getAllFilesBasedOnDesignCode.paramList.designCodeUid = row?.uuid;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getAllFilesBasedOnDesignCode)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response.length > 0) {
            this.viewAttchments(response);
          } else {
            // this.commonService.error("PPT not uploaded")
          }
        },
        error: error => {

        }
      })
  }

  //Get list of feedback on specs
  private getFeedbackList(designCode): void {
    APP_UI_CONFIG.quotation.getFeedbackByDesignCode.paramList.designcodeUid = designCode.uuid;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.getFeedbackByDesignCode)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          if (designCode.status == QuotationStatus.REJECTED || designCode.status == QuotationStatus.DROPPED) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.QUOTATIONFEEDBACK);
            this.saveOrUpdateFeedback(designCode, feedbackForQuotationReject);
          }
          if (designCode.status == QuotationStatus.ADDITIONALSPECS) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.SPECSFEEDBACK);
            this.saveOrUpdateFeedback(designCode, feedbackForQuotationReject);
          }
          if (designCode.status == QuotationStatus.BIDDINGREJECTED) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.BIDDINGFEEDBACK);
            this.saveOrUpdateFeedback(designCode, feedbackForQuotationReject);
          }
        }, error: error => {
          this.commonService.handleError(error);
          this.saveOrUpdateFeedback(designCode, []);
        }
      });

  }
  //Share feedback
  private saveOrUpdateFeedback(designCode, feedbackList): void {
    this.openDialogForFeedback(feedbackList, 'Share Feedback').subscribe(res => {
      if (res) {
        let feedbacks: any = {};
        if (designCode.status == QuotationStatus.REJECTED || designCode.status == QuotationStatus.DROPPED) {
          feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : designCodeFeedback.QUOTATIONFEEDBACK;
        }
        if (designCode.status == QuotationStatus.ADDITIONALSPECS) {
          feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : designCodeFeedback.SPECSFEEDBACK;
        }
        if (designCode.status == QuotationStatus.BIDDINGREJECTED) {
          feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : designCodeFeedback.BIDDINGFEEDBACK;
        }
        feedbacks['feedback'] = res;
        feedbacks["designCodeUid"] = designCode.uuid;
        feedbacks['userRole'] = this.commonService.getUseRole();
        feedbacks['userUid'] = this.commonService.getCurrentUser()?.uuid;
        feedbacks['userName'] = this.commonService.getCurrentUser()?.userName;
        const designCodeResponse = designCode;
        designCodeResponse['feedbacks'] = [feedbacks];
        let feedbackPayload = this.quotationResponse;

        [designCodeResponse].forEach(item => {
          if (item['scope'] && item['outOfScope']) {
            item['scope'] = typeof (item?.scope) == 'string' ? item?.scope : item?.scope?.toString();
            item['outOfScope'] = typeof (item?.outOfScope) == 'string' ? item?.outOfScope : item?.outOfScope?.toString();
          }
        });
        feedbackPayload['designcodes'] = [designCodeResponse];
        delete feedbackPayload.request;
        this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.updateFeedbackByResponse, null, null, feedbackPayload)
          .pipe(takeUntil(this.$destroyed))
          .subscribe({
            next: res => {
              this.commonService.success(`Feedback shared successfully`);
              this.onRefresh();
            }, error: error => {
              this.commonService.handleError(error);
            }
          });
      }
    })
  }

  private openDialogForFeedback(data, header?, isDisabled = false): Observable<any> {
    const dialogRef = this.dialog.open(TflChatboxComponent, {
      width: '50%',
      data: { data: data, header: header, isDisabled: isDisabled },
      disableClose: true,
    });
    return dialogRef.afterClosed()
  }

  public calcTotal(rowData) {
    if (this.quotationResponse['costingMode'] == CostingMode.OPEN) {
      if (rowData.moq && rowData.productionProposedPrice && rowData.packingProposedPrice && rowData.shipmentProposedPrice && rowData.marginProposedPrice) {
        let total = this.checkNull(rowData.moq) + this.checkNull(rowData.productionProposedPrice) + this.checkNull(rowData.packingProposedPrice) + this.checkNull(rowData.shipmentProposedPrice);
        rowData.totalProposedPrice = total + ((this.checkNull(rowData.marginProposedPrice) / 100) * total);
        rowData.finalCostWithTax = (((this.checkNull(rowData.taxProposedPrice) / 100) * rowData.totalProposedPrice) + rowData.totalProposedPrice)?.toFixed(2);
        this.designCodeList.forEach(item => {
          if (item.uuid == rowData.uuid) {
            item.totalProposedPrice = rowData?.totalProposedPrice;
            item.finalCostWithTax = Number(rowData.finalCostWithTax);
          }
        });
      }
    }
    else if (this.quotationResponse['costingMode'] == CostingMode.CLOSED) {
      if (rowData.fob && rowData.moq) {
        this.designCodeList.forEach(item => {
          if (item.uuid == rowData.uuid) {
            item.fob = Number(rowData.moq);
            item.moq = Number(rowData.moq);
          }
        });
      }

    }
    else if (this.quotationResponse['costingMode'] == CostingMode.LAB) {
      if (rowData.labSamplesRequired && rowData.noOfDaysForLabTesting && rowData.labTestingTotalPrice) {
        this.designCodeList.forEach(item => {
          if (item.uuid == rowData.uuid) {
            item.labSamplesRequired = Number(rowData.labSamplesRequired);
            item.noOfDaysForLabTesting = Number(rowData.noOfDaysForLabTesting);
            item.labTestingTotalPrice = Number(rowData.labTestingTotalPrice);
          }
        });
      }
    }
    this.setColumnDefs(this.quotationResponse['costingMode']);
  }

  private checkNull(value): number {
    if (value == null || value == '' || value == ' ') {
      return 0
    }
    return parseFloat(value)
  }


  public submit(status): void {
    let gridData: any[] = this.gridDesigncode.getGridData()?.filter(item => item.code != '00-For Reference');
    let gridDataForSubmit: any[] = gridData?.filter(item => (item.status == QuotationStatus.CREATED || item.status == QuotationStatus.REJECTED || item.status == QuotationStatus.PENDING));
    gridData.forEach(item => {
      if (item['scope'] && item['outOfScope']) {
        item['scope'] = typeof (item?.scope) == 'string' ? item?.scope : item?.scope?.toString();
        item['outOfScope'] = typeof (item?.outOfScope) == 'string' ? item?.outOfScope : item?.outOfScope?.toString();
      }
    });
    this.quotationResponse.designcodes = gridData;
    let isValid = this.quotationResponse.designcodes.every(item => this.checkIfEveryValueExist(item));
    if (isValid) {
      this.quotationResponse.status = status;
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'Are you sure you want to submit quotation?'
      });
      dialogRef.afterClosed().subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.createOrUpdateResponse, null, null, this.quotationResponse)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                if (response) {
                  this.commonService.success("Quotation response submitted successfully");
                  this.close();
                } else {
                  this.commonService.handleError(response);
                }
              },
              error: error => {
                this.commonService.handleError(error);
              }
            })
        }
      })
    } else {
      this.commonService.error("Make sure all the details are entered properly");
    }
  }

  public submitBiddingRequest(): void {
    let gridData: any[] = this.designCodeList?.filter(item => item.code != '00-For Reference');
    let gridDataForSubmit: any[] = gridData?.filter(item => (item.status == QuotationStatus.CREATED || item.status == QuotationStatus.BIDDINGREJECTED));
    gridDataForSubmit.forEach(designCode => {
      designCode['status'] = QuotationStatus.BIDDINGREREQUESTSTARTED;
    })
    this.quotationResponse.designcodes = gridData;
    let isValid = this.quotationResponse.designcodes.every(item => this.checkLabIfEveryValueExist(item));
    if (isValid) {
      this.commonService.openDialogForConfirmation("Are you sure you want to submit for bidding?").subscribe({
        next: confirm => {
          if (confirm) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.createOrUpdateResponse, null, null, this.quotationResponse)
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: res => {
                  this.commonService.success("Bidding response submitted successfully");
                  this.close();
                }, error: error => {
                  this.commonService.handleError(error);
                }
              });
          }
        }
      })
    } else {
      this.commonService.error("Make sure all the details are entered properly");
    }

  }

  private checkLabIfEveryValueExist(el) {
    if (el.currency && el.labSamplesRequired && el.noOfDaysForLabTesting && el.labTestingTotalPrice) {
      return true;
    } else {
      return false;
    }
  }

  private checkIfEveryValueExist(row) {
    if (this.quotationResponse['labUid']) {
      if (row.currency && row.labSamplesRequired && row.noOfDaysForLabTesting && row.labTestingTotalPrice) {
        return true;
      } else {
        return false;
      }
    }

    if (this.isOpenCosting) {
      if (row.productionLeadTime > 0 && row.currency && row.moq && row.productionProposedPrice && row.packingProposedPrice && row.shipmentProposedPrice && row.marginProposedPrice && row.totalProposedPrice && row.taxProposedPrice) {
        return true;
      } else {
        return false;
      }
    } else {
      if (row.productionLeadTime > 0 && row.currency && row.fob && row.moq && row.scope && row.outOfScope) {
        return true;
      } else {
        return false;
      }
    }

  }

  public close(): void {
    this.commonService.historyBack();
  }


  public getUpdatedSpecs(data): void {
    this.commonService.saveSpecsTemporary(data);
    this.showAddEditSpecifications = false;
  }

  private updateGroupWiseColumnValues(params): void {
    let groupWise: any[] = this.groupWiseRequests;
    groupWise.forEach(item => {
      if (item.groupName == params.data.groupName) {
        item[params.colDef.field] = params.value;
        item.designCodes?.forEach(chilDesignCode => {
          chilDesignCode[params.colDef.field] = params.value;

        })
      }
    });
    this.groupWiseRequests = groupWise;
  }
  public updateDesignCodesColumnsByGroup(chilDesignCode, fieldName, value): void {
    this.designCodeList.forEach(item => {
      if (item.uuid == chilDesignCode.uuid) {
        item[fieldName] = value;
      }
    });
    this.setColumnDefs(this.quotationResponse['costingMode']);
  }
  public updateColumnValues(params): void {
    this.designCodeList.forEach(item => {
      if (item.uuid == params.data.uuid) {
        item[params.colDef.field] = params.value;
      }
    });
    if (this.quotationResponse?.costingMode == 2) {
      this.designCodeList.forEach(item => {
        if (item.groupName == params.data.groupName) {
          item[params.colDef.field] = params.value;
        }
      });
      this.setColumnDefs(this.quotationResponse['costingMode']);
    } else {

    }
    this.calcTotal(params.data);
  }

  public onRefresh(): void {
    this.getResponseDetailsById();
  }

  quotationStatus: string = '';
  public getQuotationStatus(): any {
    if (+new Date(Number(this.quotationResponse?.quotationEndDate)) < +new Date()) {
      this.quotationStatus = this.quotationResponse?.costingMode == 2 ? 'Bidding Closed' : 'Quotation Closed';
    } else {
      this.quotationStatus = this.quotationResponse?.costingMode == 2 ? 'Bidding Open' : 'Quotation Open';
    }
    return this.quotationStatus;
  }


  //Get list of feedback on specs
  private getFeedbackListByRequest(response): void {
    let requestType: any
    if (this.menuType == SOURCINGMENUTYPE.QUOTATION) {
      requestType = 'QUOTATION';
    } else if (this.menuType == SOURCINGMENUTYPE.TPS) {
      requestType = 'TPS';
    } else {
      requestType = 'BIDDING';
    }
    let urlObject = {
      url: `sourcing/quotation/response/${response.uuid}/feedback/byResponse?1=1&requestType=${requestType}`,
      type: 'GET'
    }
    this._invokeService.serviceInvocation(urlObject)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          if ((response.status == QuotationStatus.REJECTED || response.status == QuotationStatus.DROPPED) && this.menuType == SOURCINGMENUTYPE.QUOTATION) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.QUOTATIONFEEDBACK);
            this.saveOrUpdateFeedbackByRequest(response, feedbackForQuotationReject);
          }
          if ((response.status == QuotationStatus.ADDITIONALSPECS) && this.menuType == SOURCINGMENUTYPE.TPS) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.SPECSFEEDBACK);
            this.saveOrUpdateFeedbackByRequest(response, feedbackForQuotationReject);
          }
          if (response.status == QuotationStatus.BIDDINGREJECTED && this.menuType == SOURCINGMENUTYPE.BIDDING) {
            let feedbackForQuotationReject = res?.filter(item => item.feedbacCategoryType == designCodeFeedback.BIDDINGFEEDBACK);
            this.saveOrUpdateFeedbackByRequest(response, feedbackForQuotationReject);
          }
        }, error: error => {
          this._invokeService.handleError(error);
          this.saveOrUpdateFeedbackByRequest(response, []);
        }
      });

  }
  //Share feedback
  private saveOrUpdateFeedbackByRequest(response, feedbackList): void {
    this.openDialogForFeedbackRequest(feedbackList, 'Share Feedback').subscribe(res => {
      if (res) {
        let feedbacks: any = {};
        if (response.status == QuotationStatus.REJECTED || response.status == QuotationStatus.DROPPED) {
          feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : designCodeFeedback.QUOTATIONFEEDBACK;
        }
        if (response.status == QuotationStatus.ADDITIONALSPECS) {
          feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : designCodeFeedback.SPECSFEEDBACK;
        }
        if (response.status == QuotationStatus.BIDDINGREJECTED || response.status == QuotationStatus.BIDDINGDROPPED) {
          feedbacks['feedbacCategoryType'] = feedbackList[0]?.feedbacCategoryType ? feedbackList[0]?.feedbacCategoryType : designCodeFeedback.BIDDINGFEEDBACK;
        }
        feedbacks['feedback'] = res;
        feedbacks["responseUid"] = response.uuid;
        feedbacks['userRole'] = this.commonService.getUseRole();
        feedbacks['userUid'] = this.commonService.getCurrentUser()?.uuid;
        feedbacks['userName'] = this.commonService.getCurrentUser()?.userName;
        const quotationResponse = response;
        quotationResponse['feedbacks'] = [feedbacks];
        this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.updateFeedbackByResponse, null, null, quotationResponse)
          .pipe(takeUntil(this.$destroyed))
          .subscribe({
            next: res => {
              this.commonService.success(`Feedback shared successfully`);
              this.onRefresh();
            }, error: error => {
              this.commonService.handleError(error);
            }
          });
      }
    })
  }

  public prepareDesignCodesStatus = (row) => {
    let status: string = '';
    if (this.menuType == SOURCINGMENUTYPE.QUOTATION) {
      switch (row.status) {
        case QuotationStatus.CREATED:
          status = this.commonService.prepareSourcingStatus("Quotation Created", "#ffa500");
          break;
        case QuotationStatus.PENDING:
          status = this.commonService.prepareSourcingStatus("Quotation Pending", "#64748b");
          break;
        case QuotationStatus.QuotationSubmitted:
          status = this.commonService.prepareSourcingStatus("Quotation Submitted", "#0000ff");
          break;
        case QuotationStatus.REJECTED:
          status = this.commonService.prepareSourcingStatus("Under Negotiation", "#f97316");
          break;
        case QuotationStatus.DROPPED:
          status = this.commonService.prepareSourcingStatus("Quotation Rejected", "#f97316");
          break;
        case QuotationStatus.RFQ1APPROVED:
          status = this.commonService.prepareSourcingStatus("Quotation Approved", "#22c55e");
          break;
        default:
          if (row?.data?.code != '00-For Reference') {
            status = this.commonService.prepareSourcingStatus("Quotation Approved", "#22c55e");
          }
      }
    }
    else if (this.menuType == SOURCINGMENUTYPE.TPS) {
      switch (row.status) {
        case QuotationStatus.RFQ1APPROVED:
          status = this.commonService.prepareSourcingStatus("Specs Pending", "#009688");
          break;

        case QuotationStatus.ADDITIONALSPECS:
          status = this.commonService.isProductCompliance() ? this.commonService.prepareSourcingStatus("Specs Received", "#64748b") : this.commonService.isVendor() ? this.commonService.prepareSourcingStatus("Specs Submitted", "#20c997") : this.commonService.prepareSourcingStatus("In-Progress", "#ffa500");;
          break;

        case QuotationStatus.RFQ2APPROVED:
          status = this.commonService.prepareSourcingStatus("Specs Approved", "#22c55e");
          break;
        case QuotationStatus.BIDDINGREREQUESTSTARTED:
          status = this.commonService.isVendor() ? this.commonService.prepareSourcingStatus("Specs Approved", "#22c55e") : this.commonService.prepareSourcingStatus("Bidding Started", "#ffa500");
          break;
        default:
          if (row?.data?.code != '00-For Reference') {
            status = this.commonService.prepareSourcingStatus("Specs Approved", "#22c55e");
          }
      }
    } else if (this.menuType == SOURCINGMENUTYPE.BIDDING) {
      let rowData: any;
      if (!!row.designcodes && typeof (row.designcodes) == 'object') {
        rowData = row.designcodes.find(item => item.status == QuotationStatus.PENDING || item.status == QuotationStatus.CREATED ||
          item.status == QuotationStatus.QuotationSubmitted || item.status == QuotationStatus.BIDDINGREJECTED ||
          item.status == QuotationStatus.BIDDINGDROPPED || item.status == QuotationStatus.BIDDINGRAPPROVED)
      }
      if (!!row.designCodes && typeof (row.designCodes) == 'object') {
        rowData = row.designCodes.find(item => item.status == QuotationStatus.PENDING || item.status == QuotationStatus.CREATED ||
          item.status == QuotationStatus.QuotationSubmitted || item.status == QuotationStatus.BIDDINGREJECTED ||
          item.status == QuotationStatus.BIDDINGDROPPED || item.status == QuotationStatus.BIDDINGRAPPROVED)
      }
      if (!!rowData) {
        switch (rowData?.status) {
          case QuotationStatus.CREATED:
            status = this.commonService.prepareSourcingStatus("In-Progress", "#ffa500");
            break;
          case QuotationStatus.PENDING:
            status = this.commonService.isProductCompliance() ? this.commonService.prepareSourcingStatus("Bidding Pending", "#64748b") : this.commonService.isLab() ? this.commonService.prepareSourcingStatus("Bidding Pending", "#64748b") : this.commonService.prepareSourcingStatus("In-Progress", "#ffa500");;
            break;
          case QuotationStatus.QuotationSubmitted:
            status = this.commonService.prepareSourcingStatus("Bidding Submitted", "#0000ff");
            break;
          case QuotationStatus.BIDDINGREJECTED:
            status = this.commonService.prepareSourcingStatus("Under Negotiation", "#f97316");
            break;
          case QuotationStatus.BIDDINGDROPPED:
            status = this.commonService.prepareSourcingStatus("Bidding Rejected", "#f97316");
            break;
          case QuotationStatus.BIDDINGRAPPROVED:
            status = this.commonService.prepareSourcingStatus("Bidding Approved", "#22c55e");
            break;
          default:
            if (row.code != '00-For Reference') {
              status = this.commonService.prepareSourcingStatus("Bidding Approved", "#22c55e");
            }
        }
      }
    } else {
    }
    return status;

  }

  public prepareDesignCodesStatusLabWiseAndVendorWise = (row) => {
    let status: string = '';
    if (this.menuType == SOURCINGMENUTYPE.QUOTATION) {
      switch (row.status) {
        case QuotationStatus.CREATED:
          status = this.commonService.prepareSourcingStatus("Quotation Created", "#ffa500");
          break;
        case QuotationStatus.PENDING:
          status = this.commonService.prepareSourcingStatus("Quotation Pending", "#64748b");
          break;
        case QuotationStatus.QuotationSubmitted:
          status = this.commonService.prepareSourcingStatus("Quotation Submitted", "#0000ff");
          break;
        case QuotationStatus.REJECTED:
          status = this.commonService.prepareSourcingStatus("Under Negotiation", "#f97316");
          break;
        case QuotationStatus.DROPPED:
          status = this.commonService.prepareSourcingStatus("Quotation Rejected", "#f97316");
          break;
        case QuotationStatus.RFQ1APPROVED:
          status = this.commonService.prepareSourcingStatus("Quotation Approved", "#22c55e");
          break;
        default:
          if (row?.data?.code != '00-For Reference') {
            status = this.commonService.prepareSourcingStatus("Quotation Approved", "#22c55e");
          }
      }
    }
    else if (this.menuType == SOURCINGMENUTYPE.TPS) {
      switch (row.status) {
        case QuotationStatus.RFQ1APPROVED:
          status = this.commonService.prepareSourcingStatus("Specs Pending", "#009688");
          break;

        case QuotationStatus.ADDITIONALSPECS:
          status = this.commonService.isProductCompliance() ? this.commonService.prepareSourcingStatus("Specs Received", "#64748b") : this.commonService.isVendor() ? this.commonService.prepareSourcingStatus("Specs Submitted", "#20c997") : this.commonService.prepareSourcingStatus("In-Progress", "#ffa500");;
          break;

        case QuotationStatus.RFQ2APPROVED:
          status = this.commonService.prepareSourcingStatus("Specs Approved", "#22c55e");
          break;
        case QuotationStatus.BIDDINGREREQUESTSTARTED:
          status = this.commonService.isVendor() ? this.commonService.prepareSourcingStatus("Specs Approved", "#22c55e") : this.commonService.prepareSourcingStatus("Bidding Started", "#ffa500");
          break;
        default:
          if (row?.data?.code != '00-For Reference') {
            status = this.commonService.prepareSourcingStatus("Specs Approved", "#22c55e");
          }
      }
    } else if (this.menuType == SOURCINGMENUTYPE.BIDDING) {
      let rowData: any;
      if (!!row.designcodes && typeof (row.designcodes) == 'object') {
        rowData = row.designcodes.find(item => item.status == QuotationStatus.PENDING || item.status == QuotationStatus.CREATED ||
          item.status == QuotationStatus.QuotationSubmitted || item.status == QuotationStatus.BIDDINGREJECTED ||
          item.status == QuotationStatus.BIDDINGDROPPED || item.status == QuotationStatus.BIDDINGRAPPROVED)
      }
      if (!!row.designCodes && typeof (row.designCodes) == 'object') {
        rowData = row.designCodes.find(item => item.status == QuotationStatus.PENDING || item.status == QuotationStatus.CREATED ||
          item.status == QuotationStatus.QuotationSubmitted || item.status == QuotationStatus.BIDDINGREJECTED ||
          item.status == QuotationStatus.BIDDINGDROPPED || item.status == QuotationStatus.BIDDINGRAPPROVED)
      }
      if (!!rowData) {
        switch (row?.status) {
          case QuotationStatus.CREATED:
            status = this.commonService.prepareSourcingStatus("In-Progress", "#ffa500");
            break;
          case QuotationStatus.PENDING:
            status = this.commonService.isProductCompliance() ? this.commonService.prepareSourcingStatus("Bidding Pending", "#64748b") : this.commonService.isLab() ? this.commonService.prepareSourcingStatus("Bidding Pending", "#64748b") : this.commonService.prepareSourcingStatus("In-Progress", "#ffa500");;
            break;
          case QuotationStatus.QuotationSubmitted:
            status = this.commonService.prepareSourcingStatus("Bidding Submitted", "#0000ff");
            break;
          case QuotationStatus.BIDDINGREJECTED:
            status = this.commonService.prepareSourcingStatus("Under Negotiation", "#f97316");
            break;
          case QuotationStatus.BIDDINGDROPPED:
            status = this.commonService.prepareSourcingStatus("Bidding Rejected", "#f97316");
            break;
          case QuotationStatus.BIDDINGRAPPROVED:
            status = this.commonService.prepareSourcingStatus("Bidding Approved", "#22c55e");
            break;
          default:
            if (row.code != '00-For Reference') {
              status = this.commonService.prepareSourcingStatus("Bidding Approved", "#22c55e");
            }
        }
      }
    } else {

    }

    return status;

  }

  public submitTps(): void {
    this.prepareSpecsPayload();
  }

  private checkInputType(masterSpecList: any[], specificationName, isUnitFor?): any {
    let spec: any;
    if (isUnitFor) {
      if (specificationName.toLowerCase().includes("unit_")) {
        spec = masterSpecList?.find(spec => spec?.unitFor == specificationName?.replace(/unit_/gi, ""))
      } else {
        spec = masterSpecList?.find(spec => spec?.unitFor == specificationName)
      }
    } else {
      spec = masterSpecList.find(spec => spec.specificationName == specificationName);
    }
    if (spec && spec.inputType) {
      let specType: any;
      if (spec.inputType?.toLowerCase() == FORM_CONTROL_TYPES.SINGLE_SELECT.toLowerCase()) {
        specType = FORM_CONTROL_TYPES.SINGLE_SELECT;
      }
      else if (spec.inputType?.toLowerCase() == FORM_CONTROL_TYPES.MULTI_SELECT.toLowerCase()) {
        specType = FORM_CONTROL_TYPES.MULTI_SELECT;
      } else {
        specType = spec.inputType.toLowerCase();
      }
      return specType;
    } else {
      return FORM_CONTROL_TYPES.TEXT.toLowerCase();
    }
  }
  private getMultiplier(specsUnitMasterConfigurationList: any[], matchingTechItem, value): any {
    let matchSepcItem = specsUnitMasterConfigurationList.find(specItem => specItem.keyword == matchingTechItem?.keyword);
    if (matchSepcItem) {
      let data = JSON.parse(matchSepcItem.jsonConfig);
      return data.find(item1 => item1.value == value)?.multiplier || '';
    }
  }
  specFileUid: any;
  private getValue(designCodeItem, data: any): any {
    this.specFileUid = designCodeItem['specFileUid'];
    const matchingItem = designCodeItem.specConfigList.find(
      (item) => (item?.specificationName === data?.specificationName && item.inputType?.toLowerCase() == 'file')
    );
    if (matchingItem) {
      return  this.specFileUid;
    }
    return data.value;
  }
  private prepareSpecification(designCodeItem): any {
    if (designCodeItem) {
      let specifications: TechnicalSpecifications = {};
      let specList: TechnicalSpecifications[] = [];
      let rec: TechnicalSpecifications = new TechnicalSpecifications();
      Object.keys(designCodeItem.copiedSpec).forEach(key => {
        rec.specificationName = key;
        let isUnitFor: boolean = (key.toLowerCase().includes("unit_")) ? true : false;
        rec.value = this.checkInputType(designCodeItem.specConfigList, key, isUnitFor) == FORM_CONTROL_TYPES.MULTI_SELECT?.toLowerCase() ? designCodeItem.copiedSpec[key]?.toString() : this.checkInputType(designCodeItem.specConfigList, key, isUnitFor) == FORM_CONTROL_TYPES.DATE?.toLowerCase() ? +new Date(designCodeItem.copiedSpec[key]) : designCodeItem.copiedSpec[key];
        rec.isUpdated = true;
        specList.push(Util.clone(rec));
      });
      specList.forEach(specItem => {
        let matchingTechItem;
        if (specItem.specificationName.toLowerCase().includes("unit_")) {
          matchingTechItem = designCodeItem.specConfigList.find((techItem: any) => techItem.unitFor == specItem.specificationName.split("_")[1]);
        } else {
          matchingTechItem = designCodeItem.specConfigList.find((techItem: any) => techItem.specificationName == specItem.specificationName);
        }
        if (matchingTechItem) {
          specItem.code = matchingTechItem.code;
          specItem.groupName = matchingTechItem.specificationGroup;
          specItem.uuid = matchingTechItem.uuid;
          specItem.groupCode = matchingTechItem.specificationGroupCode;
          specItem.isNew = matchingTechItem.isNew;
          specItem.sectionName = matchingTechItem.sectionName;
          specItem.uom = matchingTechItem.uom;
        }

      });
      specList.forEach((data) => {
        specifications[data.specificationName] = {
          specificationName: data.specificationName,
          value: this.getValue(designCodeItem, data),
          isUpdated: data.isUpdated,
          isNew: data.isNew,
          sectionName: data.sectionName,
          code: data.code,
          groupName: data.groupName,
          uuid: data.uuid,
          groupCode: data.groupCode,
          multiplier: (data.value) ? data.specificationName.toLowerCase().includes("unit_") ? this.getMultiplier(designCodeItem.specsUnitMasterConfigurationList, designCodeItem.specConfigList.find((techItem: any) => techItem.unitFor == data.specificationName.split("_")[1]), data.value) : '' : '',
          uom: data.uom
        };
      });
      return specifications;
    } else {
      return;
    }
  }
  private checkIfSpecUpdate(designCodeItem): any {
    return designCodeItem.technicalSpecifications[0].uuid;
  }

  private prepareSpecsPayload(): void {
    if (this.designCodeList.some(item => item.copiedSpec)) {
      let payload = this.designCodeList.filter(item => item.copiedSpec)?.map(designCodeItem => ({
        code: designCodeItem.code,
        productTypeName: designCodeItem.productTypeName,
        productTypeUid: designCodeItem.productTypeUid,
        quotationResponseUid: designCodeItem.quotationResponseUid,
        quotationResponseDesigncodeUid: designCodeItem.uuid,
        designCodeUid: designCodeItem.designCodeUid,
        categoryUid: designCodeItem.categoryUid,
        uuid: designCodeItem.technicalSpecifications ? this.checkIfSpecUpdate(designCodeItem) : null,
        specifications: this.prepareSpecification(designCodeItem)
      }))

      this._invokeService.serviceInvocation(APP_UI_CONFIG.quotation.saveTechSpecifications, null, null, Util.clone(payload))
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            if (response && response.code == HTTP_STATUS.SUCCESS) {
              this.commonService.success("TPS(s) Submitted Successfully");
              this.onRefresh();
              this.commonService.navigate('sourcing/quotation/quotationResponse/TPS')
            } else {
              this.commonService.error("Failed To Submit the TPS");
            }
          }, error: error => {
            this.commonService.handleError(error);
          }
        });
    }
    else {
      this.commonService.error("No TPS are found to submit");
    }
  }







  private openDialogForFeedbackRequest(data, header?, isDisabled = false): Observable<any> {
    const dialogRef = this.dialog.open(TflChatboxComponent, {
      width: '50%',
      data: { data: data, header: header, isDisabled: isDisabled },
      disableClose: true,
    });
    return dialogRef.afterClosed()
  }




  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }

}
