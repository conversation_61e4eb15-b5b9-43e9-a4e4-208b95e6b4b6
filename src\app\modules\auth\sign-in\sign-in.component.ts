import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, NgForm, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseAnimations } from '@fuse/animations';
import { FuseAlertType } from '@fuse/components/alert';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { User } from 'app/core/user/user.model';
import { HTTP_STATUS } from 'app/enum-constants';
import { UI_CONFIGURATION } from 'app/navigation/ui-configuration';
import { InvokeService } from 'app/services/invoke.service';
import { SessionTimeoutService } from 'app/services/sessiontimeout.service';
import { PrimeIcons } from 'primeng/api';
import { ReplaySubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { environment } from '../../../../environments/environment';
import { AuthService } from '../../../services/auth.service';
import { CommonService } from '../../../services/common.service';
import { ForgetPasswordDialogComponent } from './forget-password-dialog/forget-password-dialog.component';


@Component({
    selector: 'auth-sign-in',
    templateUrl: './sign-in.component.html',
    styleUrls: ["./sign-in.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: FuseAnimations
})
export class AuthSignInComponent implements OnInit {
    @ViewChild('signInNgForm') signInNgForm: NgForm;

    alert: { type: FuseAlertType, message: string } = {
        type: 'success',
        message: ''
    };
    signInForm: FormGroup;
    showAlert: boolean = false;
    appVersion = environment.Version;
    tenant: string = '';
    headerLogo: any;
    headerStickyLogo: any;
    today: Date = new Date();
    sharedLink: string;
    showOTP: boolean = false;
    authenticationCode: any;
    public qrCodeImageUrl: any;
    private destroyed$: ReplaySubject<boolean> = new ReplaySubject(1);
    showRegister: boolean = false;
    visibleSidebar5;
    uiConfigurationObject: any;
    events1 = [
        {
            status: "Inspection Request",
            date: "June 2024",
            icon: PrimeIcons.CHECK,
            color: "#9C27B0",
            description: ['In Inspection Request ASINs can be added via CSV file import.', 'Download templated , add data and then import csv file.']
        },
        {
            status: "Inspection ",
            date: "June 2024",
            icon: PrimeIcons.CHECK,
            color: "#9C27B0",
            description: []
        },
    ];
    title: string = "Quality360";
    subTitle: string = 'Quality Automation Platform';
    copyrights: string = "3Frames Software Labs Pvt Ltd";
    /**
     * Constructor
     */
    constructor(
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _commonService: CommonService,
        private _invokeService: InvokeService,
        private _formBuilder: FormBuilder,
        private _router: Router,
        private sessionTimeoutService: SessionTimeoutService,
        private _dialog: MatDialog,
        private _domSanitizer: DomSanitizer,
    ) {
        this.loadConfig(environment.tenantUid)
    }

    private loadConfig(client: string) {
        if (UI_CONFIGURATION[client]) {
            this.uiConfigurationObject = UI_CONFIGURATION[client];
        } else {
            this.uiConfigurationObject = UI_CONFIGURATION['stage'];
        }
        window.top.document.title = `${this.uiConfigurationObject?.title}-${this.uiConfigurationObject?.subTitle}`;
        this.title = this.uiConfigurationObject?.title;
        this.subTitle = this.uiConfigurationObject?.subTitle;
        this.copyrights = this.uiConfigurationObject?.copyrights;
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this.signInForm = this._formBuilder.group({
            email: ['', [Validators.required]],
            password: ['', Validators.required],
            rememberMe: ['']
        });
        this.tenant = environment.tenantUid;
        const defaultLogo = './assets/logos/3frames.png';
        this.headerLogo = defaultLogo;
        this.headerStickyLogo = defaultLogo;
        if (this.tenant) {
            const setLogos = (logoPath) => {
                this.headerLogo = logoPath;
                this.headerStickyLogo = logoPath;
            };
            const tenantLogoPath = `./assets/logos/${this.tenant}.png`;
            const img = new Image();
            img.onload = () => {
                setLogos(tenantLogoPath);
            };

            img.onerror = () => {
                setLogos(defaultLogo);
            };
            img.src = tenantLogoPath;
        }
        // this.headerLogo = './assets/logos/' + this.tenant + '.png';
        // this.headerStickyLogo = './assets/logos/' + this.tenant + '.png';
        this.checkImage(this.headerStickyLogo, function () { this.headerStickyLogo = './assets/logos/' + this.tenant + '.png'; }, function () { this.headerStickyLogo = './assets/logos/3frames.png'; });

        // Create the form

        this._activatedRoute.queryParams.subscribe((params) => {
            if (params.returnUrl != undefined && localStorage.getItem('sharedLink') == null) {
                this.sharedLink = params.returnUrl;
                localStorage.setItem('sharedLink', this.sharedLink);
            }
        });

    }

    private checkImage(imageSrc, good, bad) {
        var img = new Image();
        img.onload = good;
        img.onerror = bad;
        img.src = imageSrc;
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Sign in
     */
    loggedInUser;
    public signIn(): void {
        // Return if the form is invalid
        if (this.signInForm.invalid) {
            Object.keys(this.signInForm.controls).forEach(key => {
                this.signInForm.controls[key].markAsDirty();
                this.signInForm.controls[key].markAsTouched();
            });
            this.signInForm.updateValueAndValidity();
            return;
        }
        // Disable the form
        this.signInForm.disable();
        // Hide the alert
        this.showAlert = false;
        //get tenant
        this._authService.getTenant(this.signInForm.controls['email'].value, this.signInForm.controls['password'].value)
            .pipe(takeUntil(this.destroyed$))
            .subscribe({
                next: response => {
                    if (response.status == HTTP_STATUS.SUCCESS) {
                        let data = response.body;
                        environment.tenantUid = data.tenantUid;
                        localStorage.setItem("tenantUid", JSON.stringify(data.tenantUid));
                        this.loginAfterTenant(this.signInForm.controls['email'].value, this.signInForm.controls['password'].value);
                        this.loadConfig(data.tenantUid);
                    } else {
                        this.loginErrorHandling(response);
                    }
                }, error: error => {
                    this.loginErrorHandling(error);
                }
            })
        return;
        // Sign in

    }

    private loginAfterTenant(username, password): void {
        this._authService.login(username, password)
            .pipe(takeUntil(this.destroyed$))
            .subscribe({
                next: response => {
                    if (response) {
                        this.getTenantConfig(response);
                    } else {
                        this.loginErrorHandling(response);
                    }
                }, error: error => {
                    this.loginErrorHandling(error);
                }
            })
    }

    private loginErrorHandling(error): void {
        this.showAlert = true;
        this.signInForm.enable();
        this.signInNgForm.resetForm();

        if (error.status == HTTP_STATUS.UNAUTHORIZED) {
            this.alert = {
                type: 'error',
                message: 'Invalid username or password.'
            };
        } else if (error.status == HTTP_STATUS.NOT_CONTENT) {
            this.alert = {
                type: 'error',
                message: 'No Record Found.'
            };
        } else {
            this.alert = {
                type: 'error',
                message: 'Something Went Wrong Please Try Again After Sometime.'
            };
        }
    }

    public getTenantConfig(user: User): void {
        this._authService.getTenantConfig(user.userName, user.password, environment.tenantUid).subscribe({
            next: tenantConfig => {
                this.checkTenantConfig(user, tenantConfig)
            },
            error: error => {
                this.checkTenantConfig(user, "")
            }

        })
    }
    private checkTenantConfig(user, tenantConfig): void {
        this.setStorage(user, tenantConfig);
        this.loggedInUser = this._authService.getLoggedInUser();
    }

    private setStorage(user: User, tenantConfig): void {
        user['mfaEnable'] = false;
        if (tenantConfig && tenantConfig.enableMfa) {
            user['mfaEnable'] = true;
        }
        this._authService.setLoggeInUser(user);

        sessionStorage.setItem('tenantConfig', JSON.stringify(tenantConfig));
        this._commonService.setBasicAuth('Basic ' + btoa(user.userName + ':' + user.password));

        if (tenantConfig && tenantConfig.enableMfa) {
            this.showOTP = true;
        } else {
            this.showOTP = false;
            this.redirectAfterLogin();
        }

    }



    public onOtpVerification(): void {
        if (this.authenticationCode && this.authenticationCode.length === 6) {
            APP_UI_CONFIG.auth.mfaValidation.paramList.mfaCode = this.authenticationCode;
            this._authService.validateMFACode(this.loggedInUser.userName, this.loggedInUser.password, this.authenticationCode)
                .pipe(takeUntil(this.destroyed$))
                .subscribe({
                    next: response => {
                        if (response == 200) {
                            this._authService.setMfaCode();
                            this.redirectAfterLogin();
                        } else {
                            this._commonService.error("Please enter valid MFA Code")
                        }
                    },
                    error: error => {
                        this._commonService.handleError(error)
                    }

                })
        } else {
            this._commonService.error("Please enter MFA code to login")
        }
    }

    public getQrCodeImage(): any {
        this.qrCodeImageUrl = "";
        this._authService.getQRImage(this.loggedInUser.userName, this.loggedInUser.password).subscribe({
            next: response => {
                this.qrCodeImageUrl = response
            }
        })
    }

    public registerMfaAuthentication(): void {
        this.showRegister = true;
        this.getQrCodeImage();
    }

    private redirectAfterLogin(): void {
        const user = this._authService.getLoggedInUser();
        const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
        const tenantConfig = JSON.parse(sessionStorage.getItem("tenantConfig"));
        let redirectURL = '/auth/sign-in';
        let sharedLink: any = localStorage?.getItem('sharedLink');
        if (sharedLink != undefined && sharedLink != null && sharedLink != '') {
            redirectURL = sharedLink;
            localStorage.removeItem('sharedLink')
            this.navigateTOPath(redirectURL);
            this.startSession();
            return;
        }
        else if (tenantUid == 'twills') {
            if (user && user.role?.toLowerCase() == 'admin') {
                let response = JSON.parse(JSON.stringify(tenantConfig));
                let featuresSet = JSON.parse(response['featuresSet']);
                if (featuresSet) {
                    featuresSet['userCategoryMapEnabled'] = 0;
                    response['featuresSet'] = JSON.stringify(featuresSet);
                    sessionStorage.setItem('tenantConfig', JSON.stringify(response));
                }
            } else {
                this.getUserCategoryByLoggedInUser(user.uuid);
            }

            redirectURL = this.uiConfigurationObject.homePage;
            this.navigateTOPath(redirectURL)
            this.startSession();
            return;
        }
        else {
            if (tenantUid == 'norlanka') {
                if (user?.userName == 'approver') {
                    redirectURL = 'schedules/exceptionSchedules';
                    this.navigateTOPath(redirectURL)
                    this.startSession();
                    return;
                } else {
                    redirectURL = 'dashboard/qualityDashboard';
                    this.navigateTOPath(redirectURL)
                    this.startSession();
                    return;
                }
            }
            if (tenantUid == 'lots') {
                redirectURL = 'dashboard/lotsDashboard';
                this.navigateTOPath(redirectURL)
                this.startSession();
                return;
            }
            else {
                redirectURL = 'home';
                this.navigateTOPath(redirectURL)
                this.startSession();
                return;
            }
        }

    }

    private navigateTOPath(url): void {
        console.log("url:", url);
        this._router.navigateByUrl(url);
    }

    public onCodeChanged(event): void {
        this.authenticationCode = event;
    }
    public onCodeCompleted(event): void {
        // this.authenticationCode = event;
    }

    public getAgencyType(user, redirectURL): void {
        APP_UI_CONFIG.masterdata.inspectionAgency.getByUid.paramList.uuid = user.uuid;
        this._invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.inspectionAgency.getByUid).subscribe(res => {
            this.navigateTOPath(redirectURL)
        });
    }

    private startSession(): void {
        this.sessionTimeoutService.setSesstionTimeout();
        this.sessionTimeoutService.startWatch();
    }



    public showForgetPasswordPage(): void {
        const dialogRef = this._dialog.open(ForgetPasswordDialogComponent, {
            width: '45%',
            data: {},
            disableClose: true
        });
    }

    private getUserCategoryByLoggedInUser(userUid): void {
        APP_UI_CONFIG.masterdata.userCategory.get.paramList['userUUId'] = userUid;
        this._invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.userCategory.get)
            .subscribe({
                next: response => {
                    let userCategoryList = this._commonService.sortTabularList(response || []);
                    sessionStorage.setItem("userCategory", JSON.stringify(userCategoryList))
                },
                error: error => {
                    this._commonService.handleError(error);
                }
            })
    }

    public onOpenTerms(hyperlink): void {
        window.open(hyperlink, "_blank")
    }






}
