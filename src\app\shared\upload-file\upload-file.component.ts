import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { CommonService } from 'app/services/common.service';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';

import { Status } from '../../core/qinspect/_model/status.model';


export interface DialogData {
  apiData: string;
  extensions: string
  dailogHeadig: string;
  message: { success: string, error: string },
  dropFileslabel: string
  formDatakey: any,
  showDownloadTemplateOption: boolean,
  note: string
}

@Component({
  selector: 'app-upload-file',
  templateUrl: './upload-file.component.html',
  styleUrls: ['./upload-file.component.scss']
})
export class UploadFileComponent implements OnInit {
  public files: NgxFileDropEntry[] = [];
  status: Status = new Status();
  headingLabel: string;
  apiData: any;
  messages: DialogData['message'];
  infoMessage: string;
  basicAuth = this.globals.basicAuth;
  dropFileslabel: string;
  formDatakey: string;
  showDownloadTemplateOption: boolean = true;
  extenstions: string = "*";
  constructor(
    public dialogRef: MatDialogRef<UploadFileComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    private http: HttpClient,
    private globals: Globals,
    private toastr: ToastrService,
    private _commonService: CommonService
  ) { }

  public ngOnInit(): void {
    this.headingLabel = this.data.dailogHeadig;
    this.apiData = this.data.apiData;
    this.messages = this.data.message;
    this.dropFileslabel = this.data.dropFileslabel;
    this.extenstions = this.data.extensions || "*";
    this.formDatakey = this.data.formDatakey;
    this.showDownloadTemplateOption = this.data.showDownloadTemplateOption;
    if (this.data.note) {
      this.infoMessage = this.data.note;
    } else if (this.data.extensions) {
      this.infoMessage = `Please use the ${this.data?.extensions} format to upload the attachment.`
    }
  }

  public fileOver(event) {
    console.log(event);
  }

  public fileLeave(event) {
    console.log(event);
  }

  deleteFile(i) {
    this.files.splice(i, 1)
    this.status = new Status()
  }

  public setFiles(files: NgxFileDropEntry[]) {
    this.files = files
  }

  public uploadFiles(): void {
    for (const droppedFile of this.files) {
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          const formData = new FormData()
          formData.append('file', file, droppedFile.relativePath);
          if (this.formDatakey) {
            formData.append(this.formDatakey, JSON.stringify({}))
          }
          let headers = new HttpHeaders()
            .set("Authorization", this._commonService.getBasicAuth()).set('Client', this._commonService.getTenantUid());
          let baseUrl = environment.apiUrl;
          if (this.apiData.externalUrl) {
            baseUrl = environment.apiUrl;
          }
          if (this.apiData.type == 'POST') {
            this.http.post(baseUrl + this.apiData.url, formData, { headers: headers, responseType: 'json' })
              .subscribe(data => {
                this.status = data as Status
                if (Object.keys(data).length == 0 || this.status.code == '200') {
                  this.toastr.success(this.status.message ? this.status.message : this.messages.success);
                  this.files = [];
                  this.dialogRef.close(data);
                } else {
                  this.toastr.warning(this.status.message);
                }
              }, error => {
                if (error) {
                  this.toastr.error(error.error);
                } else {
                  this.toastr.error(this.status.message);
                }
              })
          }
          if (this.apiData.type == 'PUT') {
            this.http.put(baseUrl + this.apiData.url, formData, { headers: headers, responseType: 'json' })
              .subscribe(data => {
                this.status = data as Status
                if (this.status.code != '500' || !this.status) {
                  this.toastr.success(this.messages.success);
                  this.files = [];
                  this.dialogRef.close(data);
                } else {
                  this.status.message = "";
                  this.toastr.warning(this.status.message);
                }
              }, error => {
                if (error) {
                  this.toastr.error(error.error);
                } else {
                  this.toastr.error(this.messages.error);
                }
              })
          }

        });
      } else {
        // It was a directory (empty directories are added, otherwise only files)
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
        console.log(droppedFile.relativePath, fileEntry);
      }
    }
  }

}
