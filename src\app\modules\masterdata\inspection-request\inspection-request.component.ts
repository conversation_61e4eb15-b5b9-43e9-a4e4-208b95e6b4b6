import { DatePipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { FilterList } from 'app/core/qinspect/_model/filterList.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { User } from 'app/core/user/user.model';
import { ROLE } from 'app/enum-constants';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { DashboardService } from 'app/services/dashboard.service';
import { InvokeService } from 'app/services/invoke.service';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import _ from 'lodash';
import moment from 'moment';
import { FileSaverService } from 'ngx-filesaver';

@Component({
  selector: 'app-inspection-request',
  templateUrl: './inspection-request.component.html',
  styleUrls: ['./inspection-request.component.scss']
})
export class InspectionRequestComponent extends BaseTableSharedComponent {
  isVendor: boolean = false;
  isSeller: boolean = false;
  isAdmin: boolean = false;

  isAgency: boolean = false;
  userInfo: User;
  scheduleList: any[] = [];
  filtersList: FilterList[] = [
    { id: "fromDate", type: 'date', label: 'From', defaultValue: this.defaultfromDate, value: this.defaultfromDate },
    { id: "toDate", type: 'date', label: 'To', defaultValue: this.defaultToDate, value: this.defaultToDate },
  ];
  gridOptions: any = {};
  columnDefs: any[] = [
    // {
    //   field: '',
    //   // headerCheckboxSelection: true,
    //   checkboxSelection: true,
    //   showDisabledCheckboxes: true,
    //   width: 24,
    //   minWidth: 24,
    // },
    {
      headerName: "ASINs", field: "designCodes", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', tooltipComponentParams: { hide: true },
      width: 200, minWidth: 200,
    },
    {
      headerName: "PO #", field: "poNumbers", tooltipField: ' ', sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
      width: 200, minWidth: 175,
    },

    {
      headerName: "Vendor & Factory Name", field: "vendorName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
      valueGetter: (params: ICellRendererParams) => this.returnVendorFactoryName(params.data), width: 250, minWidth: 250
    },
    // { headerName: "Factory Name", field: "factoryName", tooltipField: ' ', sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    // { headerName: "Factory Name", field: "factoryName", tooltipField: ' ', sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    { headerName: "Agency Name", field: "agencyName", tooltipField: ' ', sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', width: 200, minWidth: 200, },


    {
      headerName: "QTY", field: "totalOfferQuantity", tooltipField: ' ', sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
      width: 125, minWidth: 100,
      cellRendererParams: (params: ICellRendererParams) => params.data
    },
    { headerName: "Status", field: "statusText", tooltipField: ' ', width: 175, minWidth: 175, sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    {
      headerName: "Inspection Date", field: "plannedScheduleStartTimeText", width: 150, minWidth: 150, tooltipField: '', sortable: true, unSortIcon: true,
      filter: 'agTextColumnFilter', valueGetter: (data) => {
        if (data?.data?.statusText == "REQUEST CONFIRMED") {
          return data?.data?.auditDateText?.substring(0, 10)
        }
        else {
          return data?.data?.plannedScheduleStartTimeText?.substring(0, 10)
        }
      },
    },
    { headerName: "Inspection Number", field: "auditNumber", tooltipField: ' ', sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    { headerName: "Inspection Type ", field: "auditTypeLabel", tooltipField: ' ', sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    {
      headerName: 'Actions',
      pinned: 'right',
      maxWidth: 180,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: (params) => {
        return { actions: this.prepareActionIconButtons(params.data) }
      }
    }
  ];

  constructor(dialog: MatDialog,
    invokeService: InvokeService,
    _commonService: CommonService,
    _authService: AuthService,
    _dashboardService: DashboardService,
    _datepipe: DatePipe,
    _router: Router,
    globals: Globals,
    http: HttpClient,
    fileSaverService: FileSaverService,
    changeDetectorRef: ChangeDetectorRef,
    route: ActivatedRoute) {
    super(dialog, invokeService, _commonService, _authService, _dashboardService, _datepipe, _router, globals, http, fileSaverService, changeDetectorRef, route)
  }


  ngOnInit(): void {

    this.defaultfromDate.setDate(this.defaultToDate.getDate() - 30);
    this.setDates(this.defaultfromDate, this.defaultToDate);

    this.userInfo = JSON.parse(localStorage.getItem("currentUser"));
    if (this._authService.hasLoggedUserPermitted(ROLE.VENDOR) || this._authService.hasLoggedUserPermitted(ROLE.FACTORY))
      this.isVendor = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.SELLER))
      this.isSeller = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.AGENCY))
      this.isAgency = true;
    if (this._authService.hasLoggedUserPermitted(ROLE.ADMIN)) {
      this.isAdmin = true;
    }
    // APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulePlanning.paramList.type = 'INSPECTION';
    // APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulePlanning.paramList.from = moment(this.defaultfromDate).startOf("day").valueOf();
    // APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulePlanning.paramList.to = moment(this.defaultToDate).endOf("day").valueOf();
    // super.setBasUrl(APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulePlanning);
    // super.init();
    APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulesPlanningList.paramList.from = moment(this.defaultfromDate).startOf("day").valueOf();
    APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulesPlanningList.paramList.to = moment(this.defaultToDate).endOf("day").valueOf();
    this.serviceInvocation()
  }
  public getOnGetDate(event: any): void {
    this.FromToDate.from = event.from;
    this.FromToDate.to = event.to;

    APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulesPlanningList.paramList.from = moment(this.FromToDate.from).startOf("day").valueOf();
    APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulesPlanningList.paramList.to = moment(this.FromToDate.to).endOf("day").valueOf();

    this.serviceInvocation();
    this.gridOptions = {
      rowClassRules: {
        'bg-primary-300': params => params.data?.statusText == 'REQUEST RAISED',
      }
    }
  }

  private serviceInvocation(): void {
    APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulesPlanningList.paramList.type = 'INSPECTION';
    this.subscription.add(
      this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.schedulePlanning.getSchedulesPlanningList, null, null)
        .subscribe({
          next: response => {
            this.scheduleList = response
          },
          error: error => {
            this._commonService.handleError(error);
          }
        }))
  }

  private calculateOrderedQuantity(row: any): number {
    let orderedQuantity = 0;
    row.asin?.forEach(asin => {
      orderedQuantity += asin.orderQuantity;
    });
    return orderedQuantity;
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];
    iconsList.push({ type: 'update', title: "View", icon: 'view', data: row });
    if ((this.isAgency) && row.statusText.toLowerCase() == 'request confirmed')
      iconsList.push({ type: 'update', title: "Edit", icon: 'edit', data: row });
    return iconsList;
  }

  public getAddEvent(event): void {
    this._router.navigate(['/master/inspectionRequest/create'], { queryParams: { mode: 'add' } })
  }

  private getAllASINCodes(row: any): string {
    let asinCodes = '';
    row.asin?.forEach(asin => {
      if (asinCodes != '')
        asinCodes += ', ' + asin.code;
      else
        asinCodes = asin.code;
    });
    return asinCodes;
  }

  private getPONumberAsString(row: any): string {
    return _.uniq(row.asin?.map(x => x.poNumber)).join(", ");
  }

  private returnVendorFactoryName(row: any): string {
    return `${row.vendorName} | ${row.factoryName}`
  }

  public getActions(event): void {
    let actionData: any = event;
    this.selectedData = actionData.data;

    if (actionData.status == 'update')
      this._router.navigate(['/master/inspectionRequest/edit/' + this.selectedData.uuid], { queryParams: { mode: 'edit' } })
    else
      this._router.navigate(['/master/inspectionRequest/edit/' + this.selectedData.uuid])
  }


}
