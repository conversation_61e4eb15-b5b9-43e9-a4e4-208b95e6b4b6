import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { DesigncodeActivityDetailAttribute } from 'app/core/qinspect/_model/DesigncodeActivityDetailAttribute.model';
import { TimelineInboxItem } from 'app/core/qinspect/_model/TimelineInboxItem.model';
import { QiUser } from 'app/core/qinspect/_model/user.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';
import { FileSaverService } from 'ngx-filesaver';
import { TreeNode } from 'primeng/api';


@Component({
  selector: 'app-document-tree',
  templateUrl: './document-tree.component.html',
  styleUrls: ['./document-tree.component.scss']
})
export class DocumentTreeComponent implements OnInit {
  @Output() close: EventEmitter<any> = new EventEmitter();
  user: QiUser;
  inboxItem: TimelineInboxItem
  inboxItemUid: string
  fieldAttributes: DesigncodeActivityDetailAttribute[] = []
  actionAttributes: DesigncodeActivityDetailAttribute[] = []
  basicAuth = this.globals.basicAuth;


  documents: TreeNode<any>[] = [];
  selectedNodes: TreeNode[];


  cols: any[];

  constructor(
    private _authService: AuthService,
    // private activityService: ActivityService,
    public _invokeService: InvokeService,
    public commonService: CommonService,
    private fileSaverService: FileSaverService,
    public _router: Router,
    public globals: Globals,
    private http: HttpClient,
    public route: ActivatedRoute
    ) {

  }

  ngOnInit() {
    this.inboxItemUid = this.route.snapshot.params["uuid"];
    this.user = this._authService.getLoggedInUser();
    this.initData()
    this.cols = [
    { field: 'name', header: 'Name' },
    { field: 'size', header: 'Size' },
    { field: 'type', header: 'Type' },
    ];
  }

  initData() {
    APP_UI_CONFIG.documentManagement.docManagement.getTree.paramList['id'] = "*"
    this._invokeService.serviceInvocation(APP_UI_CONFIG.documentManagement.docManagement.getTree).subscribe(res => {
      this.documents = res
    });

  }

  save(item){
    this.uploadFiles()
    if(item.type.toUpperCase() == 'APPROVE'){
      this.inboxItem.designcodeActivityDetail.status = 2
      this.inboxItem.status = 2
    }else if(item.type.toUpperCase() == 'REJECT'){
      this.inboxItem.designcodeActivityDetail.status = -1
      this.inboxItem.status = 2
    }
    APP_UI_CONFIG.activity.updateInboxItemDetails.paramList['id'] = this.inboxItemUid
    APP_UI_CONFIG.activity.updateInboxItemDetails.payload = this.inboxItem
    this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.updateInboxItemDetails,'','',this.inboxItem ).subscribe(res => {

    });
  }

  import(item){
    this._router.navigate(["schedules/docUploadArticle"])
  }

  add(item){
    this._router.navigate(["/documentManagement/importDocument/"+item.uuid])
  }

  deleteFolder(item){
    this._router.navigate(["/documentManagement/importDocument/"+item.uuid])
  }

  public uploadFiles(): void {
    if(this.inboxItem.designcodeActivityDetail.attributes != null){
      for(var attr of this.inboxItem.designcodeActivityDetail.attributes){
        if(attr.type.toUpperCase() == 'FILE' && attr.files != null ){
          for (const file of attr.files) {
            const formData = new FormData()
            formData.append('file', file, file.name);
            // if (this.formDatakey) {
            //   formData.append(this.formDatakey, JSON.stringify({}))
            // }
            let headers = new HttpHeaders()
            .set("Authorization", this.basicAuth).set('Client', this.commonService.getTenantUid()); 
            let baseUrl = environment.apiUrl;
            this.http.post(baseUrl + 'sourcing/activity/designcode/'+this.inboxItem.designcodeActivityDetailUid+'/doc', formData, { headers: headers, responseType: 'json' })
            .subscribe(data=> {
              console.log(data)
            }, error => {

            })
          }
        }
      }
    }
  }
  

  public closeApproval() {
    this.close.emit(true);
  }
  public getValue(value, index): void {
    if(this.inboxItem.designcodeActivityDetail.attributes[index].type.toUpperCase() == 'FILE'){
      this.inboxItem.designcodeActivityDetail.attributes[index].files = value
    }else{
      this.inboxItem.designcodeActivityDetail.attributes[index].attributeValue = value;
    }
    console.log("inboxItem changes :", this.inboxItem)
  }

  generateDoc(row, format){
    let headers = new HttpHeaders({ 'Content-Type': 'application/csv' })
      .set("Authorization", this.basicAuth).set('Client', this.commonService.getTenantUid()); 
    this.http.get(environment.apiUrl + 'docmgmt/compose/'+row.uuid+'/format', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      if(format == 'pdf')
        this.fileSaverService.save((<any>res).body, row.name+'.pdf');
      else
        this.fileSaverService.save((<any>res).body, row.name+'.docx');
    });
  }

  initData2(){
    // let doc1 = {"name": "aa"}
    // documents.push(doc1)
    // this.documents = documents

    this.http.get("assets/treetable.json").subscribe(data =>{
      console.log(data)
      this.documents = (data as any).data as TreeNode[];
    })
  }

}
