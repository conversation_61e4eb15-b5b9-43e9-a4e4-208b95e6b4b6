import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { QiCategory } from 'app/core/qinspect/_model/category.model';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-add-edit-category',
  templateUrl: './add-edit-category.component.html',
  styleUrls: ['./add-edit-category.component.scss']
})
export class AddEditCategoryComponent implements OnInit {

  hdr: string = 'Add Category';
  rec: QiCategory = new QiCategory();
  editRec: boolean = false;
  loading: boolean = false;
  codeinpt: boolean = false;
  btnflag: boolean = false;
  constructor(
    public invokeService: InvokeService,
    public toaster: ToastrService,
    public dialogRef: MatDialogRef<AddEditCategoryComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _commonService: CommonService,
  ) { }

  ngOnInit(): void {
    if (this.data?.data?.uuid) {
      if (this.data.status == 'update')
        this.onEdit(this.data.data);
      else if (this.data.status == 'view') {
        this.onView(this.data.data);
      }
    } else {
      this.openAddRecord();
    }
  }

  public onSave() {
    this.loading = true;
    if (this.rec.uuid != null || this.rec.id != null) {
      APP_UI_CONFIG.masterdata.category.update.paramList['id'] = this.rec.uuid;
      this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.category.update, null, this.rec.uuid, Util.clone(this.rec)).subscribe(res => {
        this.processResponse(res);
      }, (err: any) => {
        this._commonService.handleError(err);
      })

    } else {
      this.rec.level = 0;
      let categoryListApi = this._commonService.getTenantUid() == 'twills' ? APP_UI_CONFIG.masterdata.category.add : APP_UI_CONFIG.masterdata.category.addWithoutSourcing;
      this.invokeService.serviceInvocation(categoryListApi, null, null, Util.clone(this.rec)).subscribe(res => {
        this.processResponse(res);
      }, (err: any) => {
        this._commonService.handleError(err);
      })
    }
  }
  processResponse(data: any) {
    this.loading = false;
    if (data.code == "200") {
      this.toaster.success(data.message)
      this.dialogRef.close(true);
    }
    else {
      this.toaster.warning(data.message);
      this.dialogRef.close(true);
    }
  }

  onEdit(event) {
    const usrole = JSON.parse(localStorage.getItem('currentUser'));
    this.hdr = 'Update Category';
    if (usrole.role == 'ADMIN') {
      this.editRec = false;
      this.btnflag = true;
    }
    else {
      this.hdr = 'View Category';
      this.editRec = true;
      this.btnflag = false;
    }
    this.codeinpt = true;
    this.rec = <QiCategory>Util.clone(event); //event.data is selected row data
  }

  openAddRecord() {
    const usrole = JSON.parse(localStorage.getItem('currentUser'));
    this.rec = new QiCategory("");
    this.editRec = false;
    this.codeinpt = false;
    this.hdr = 'Add Category';
    if (usrole.role == 'ADMIN') {
      this.editRec = false;
      this.btnflag = true;
    }
    else {
      this.editRec = true;
      this.btnflag = false;
    }

  }

  onView(event) {
    this.editRec = true;
    const usrole = JSON.parse(localStorage.getItem('currentUser'));

    if (usrole.role == 'ADMIN') {
      this.btnflag = true;
      this.hdr = 'View Category';
    }
    else {
      this.hdr = 'View Category';
      this.btnflag = false;
    }

    this.codeinpt = true;
    this.rec = <QiCategory>Util.clone(event); //event.data is selected row data
  }

}
