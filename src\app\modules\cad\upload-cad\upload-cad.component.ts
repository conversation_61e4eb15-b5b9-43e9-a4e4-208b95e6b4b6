import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { CadAppBundle } from 'app/core/qinspect/_model/CadAppBundle.model';
import { CadDimension } from 'app/core/qinspect/_model/CadDimension.model';
import { CadDocument } from 'app/core/qinspect/_model/CadDocument.model';
import { CadDocumentContent } from 'app/core/qinspect/_model/CadDocumentContent.model';
import { headersConfigurations } from 'app/core/qinspect/_model/headersConfigurations.model';
import { MtcReferenceData } from 'app/core/qinspect/_model/MtcReferenceData.model';
import { Status } from 'app/core/qinspect/_model/status.model';
import { InvokeService } from 'app/services/invoke.service';
import { MTCHeadersConfigurationsService } from 'app/services/mtcHeadersConfig.service';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Subscription } from 'rxjs';

import { CommonService } from '../../../services/common.service';

// import { Message } from 'primeng/primeng';
// import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-upload-cad',
  templateUrl: './upload-cad.component.html',
  styleUrls: ['./upload-cad.component.scss']
})
export class UploadCadComponent implements OnInit, OnDestroy {
  refDataList: MtcReferenceData[] = [];
  selectedRefData: MtcReferenceData;
  stepIndex: any = 0;
  showFileUploadErrorMessage: boolean = false;
  public files: NgxFileDropEntry[] = [];
  status: Status = new Status();
  subscription: Subscription = new Subscription();
  supplierOrigin: string = ''
  supplierOrigins: any[] = [];
  mtcImageUid: string

  showUploadCad: boolean = false;

  cadDocumentUid: string
  imgPreview: false
  cadImage: any
  showSpinner: boolean = false;
  displayImageinView: boolean = false;
  sendCurrentDataToImageCrop: BehaviorSubject<any> = new BehaviorSubject(null);
  isImageDataSaved: BehaviorSubject<any> = new BehaviorSubject(null);

  rec: CadDocument;
  dimensions: CadDimension[] = [];
  selectedBundle: CadAppBundle
  bundles: CadAppBundle[] = [];
  images: CadDocument[] = [];
  bacRows: CadDocument[] = [];
  content: CadDocumentContent[] = [];

  subscriptions: Subscription[] = [];
  buttonsConfigArray: headersConfigurations[] = [];
  miscButtonsConfigArray: headersConfigurations[] = [];


  constructor(private _router: Router,
    private _invokeService: InvokeService,
    private http: HttpClient,
    private toaster: ToastrService,
    private _MTCHeadersConfigurationsService: MTCHeadersConfigurationsService,
    private cdRef: ChangeDetectorRef,
    private _commonService: CommonService) {
    this.supplierOrigins = [
      { name: 'World except Europe', code: '' },
      { name: 'Europe', code: '.' },
    ]
  }

  ngOnInit(): void {
    this._getAppBundles();
    this.getDocuments();
    this.buttonsConfiguraitons();
  }
  public goto(): void {
    this._router.navigate(['cad/cadDocuments'])
  }


  public buttonsConfiguraitons(): void {
    this.buttonsConfigArray = this._MTCHeadersConfigurationsService.headersConfigurations()
    this.miscButtonsConfigArray = this._MTCHeadersConfigurationsService.miscHeadersConfigurations();
  }
  getDocuments() {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.cadDesign.getCad).subscribe(data => {
      this.refDataList = data;
      this.cdRef.detectChanges();
    }, error => {
      // this.toaster.error(error.message);
    });
  }

  _getAppBundles() {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.cadApp.getCadApp).subscribe(data => {
      this.bundles = data;
      this.cdRef.detectChanges();
    }, error => {
      // this.toaster.error(error.message);
    });
  }
  onRefDataChange(event) {

  }
  public onFileChanged(event: any): void {
    this.files = event;
  }

  public uploadCadFile(): void {
    for (const droppedFile of this.files) {
      this.showSpinner = true;
      if (droppedFile.fileEntry.isFile) {

        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          console.log(droppedFile.relativePath, file);
          let fileType = file.type;
          console.log("fileType:", fileType);
          const formData = new FormData()
          formData.append('file', file, droppedFile.relativePath)
          if (this.selectedBundle != null) {
            this.rec.code = this.selectedBundle.code
            this.rec.name = this.selectedBundle.name
            this.rec.appBundleUid = this.selectedBundle.uuid
          } else {
            this.rec = new CadDocument()
          }
          formData.append('img', JSON.stringify(this.rec))
          let headers = new HttpHeaders()
            .set("Authorization", this._commonService.getBasicAuth())
            .set("Client", this._commonService.getTenantUid());

          this.http.post(environment.apiUrl + 'cad/document', formData,
            { headers: headers, responseType: 'json' })
            .subscribe(data => {
              this.showSpinner = false;
              this.status = data as Status
              this.toaster.success('Uploaded CAD Document for processing');
              if (fileType) {
                this.getImage(this.status.entityUid)
              }else{
                this.goto();
              }
              this.cadDocumentUid = this.status.entityUid
              this.getDocuments()
              if (this.status.code != '500') {
                this.showUploadCad = false;
                this.files = []
              } else {
                this.status.message = "";
              }
            })
        });
      } else {
        // It was a directory (empty directories are added, otherwise only files)
        this.showSpinner = false;
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
        console.log(droppedFile.relativePath, fileEntry);
      }
    }
  }

  public uploadCadFile2() {
    const fileEntry = this.files[0].fileEntry as FileSystemFileEntry;
    // fileEntry.file((file: File) => {
    //   const formData = new FormData();
    //   formData.append('file', file);
    //   let fileType = file.type;
    //   formData.append('img', JSON.stringify(this.rec));
    //   APP_UI_CONFIG.mtc.uploadCertificate.payload = formData;
    //   this.subscriptions.add(this._invokeService.uploadImage(APP_UI_CONFIG.cadDesign.uploadCad)
    //     .subscribe((res: any) => {          
    //           this.status = res as Status
    //           this.toaster.success('Uploaded CAD Document for processing');
    //           if (fileType) {
    //             this.getImage(this.status.entityUid)
    //           }
    //           this.cadDocumentUid = this.status.entityUid
    //           this.getDocuments()
    //           if (this.status.code != '500') {
    //             this.files = []
    //           } else {
    //             this.status.message = "";
    //           }          

    //     }));
    // })
  }

  public SaveAllBoxesData(data) {
    //    this.blocksData = data;

    this.subscriptions.push(this._invokeService.serviceInvocation(APP_UI_CONFIG.cadDesign.saveBlocks, null, this.rec.uuid, data).subscribe(res => {
      let response: any = res;
      this.toaster.success(response.message);
      this.isImageDataSaved.next(true);
      //        this.showUploadImageModal = false;
      this.getDocuments()
    }, error => {
      this.toaster.error(error.message);
    }))
  }


  onBundleChange(event) {
    //this.selectedBundle = event
  }

  public getImage(uuid) {
    this.showSpinner = true;
    APP_UI_CONFIG.cadDesign.preview.paramList.id = uuid;
    let url = `cad/${uuid}/preview`
    this._invokeService.getImage(url).subscribe(data => {
      this.showSpinner = false;
      this.stepIndex = 1;
      let currentConfigData = {
        status: 'edit',
        data: {},
        image: data,
        gettingFrom: 'cad',
        uuid: uuid
      }
      this.displayImageinView = true;
      setTimeout(() => {
        this.sendCurrentDataToImageCrop.next(currentConfigData);
        this.isImageDataSaved.next(false);
      }, 100);
    });
  }

  public ngOnDestroy(): void {
    this.subscription.unsubscribe();
    this.subscriptions.forEach((subscription) => subscription.unsubscribe())
  }

}
