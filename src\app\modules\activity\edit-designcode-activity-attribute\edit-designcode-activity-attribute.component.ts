import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { DesigncodeActivityDetail } from 'app/core/qinspect/_model/DesigncodeActivityDetail.model';
import { DesigncodeActivityDetailAttribute } from 'app/core/qinspect/_model/DesigncodeActivityDetailAttribute.model';
import { CloseTimelineInboxItemList, TimelineInboxItem } from 'app/core/qinspect/_model/TimelineInboxItem.model';
import { QiUser } from 'app/core/qinspect/_model/user.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { AuthService } from 'app/services/auth.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { environment } from 'environments/environment';

@Component({
  selector: 'edit-designcode-activity-attribute',
  templateUrl: './edit-designcode-activity-attribute.component.html',
  styleUrls: ['./edit-designcode-activity-attribute.component.scss']
})
export class EditDesigncodeActivityAttributeComponent implements OnInit {
  @Output() close: EventEmitter<any> = new EventEmitter();
  @Input() isDialog: boolean = false
  user: QiUser;
  inboxItem: TimelineInboxItem
  inboxItemUid: string
  fieldAttributes: DesigncodeActivityDetailAttribute[] = []
  actionAttributes: DesigncodeActivityDetailAttribute[] = []
  basicAuth = this.globals.basicAuth;
  @Input() uuid: string = '';
  @Input() title: string = '';
  @Input() type: string = '';
  @Input() inboxItems: TimelineInboxItem[] = [];
  @Input() path: string = '';
  @Input() customParams: string = '';


  constructor(
    private _authService: AuthService,
    // private activityService: ActivityService,
    public _invokeService: InvokeService,
    public commonService: CommonService,
    public _router: Router,
    public globals: Globals,
    private http: HttpClient,
    public route: ActivatedRoute,
    public dialogRef: MatDialogRef<EditDesigncodeActivityAttributeComponent>
  ) {

  }

  ngOnInit() {
    if (this.isDialog) {
      this.inboxItemUid = this.uuid;
    } else {
      this.inboxItemUid = this.route.snapshot.params["uuid"];
    }

    this.user = this._authService.getLoggedInUser();
    this.initData()
  }

  initData2() {
    this.inboxItem = new TimelineInboxItem()
    this.inboxItem.name = "aa"
    let item = new DesigncodeActivityDetail()
    let attr1 = new DesigncodeActivityDetailAttribute()
    attr1.type = "text"
    attr1.name = "text box"
    attr1.description = "text box description"
    // attr1.attributeValue = '';

    let attr2 = new DesigncodeActivityDetailAttribute()
    attr2.type = "dropdown"
    attr2.name = "dropdown box"
    attr2.description = "drop down description"
    attr2.attributeValue = '';

    let attr3 = new DesigncodeActivityDetailAttribute()
    attr3.type = "checkbox"
    attr3.name = "checkbox box"
    attr3.description = "checkbox description"

    let attr4 = new DesigncodeActivityDetailAttribute()
    attr4.type = "file"
    attr4.name = "checkbox box"
    attr4.description = "file description"

    let attr5 = new DesigncodeActivityDetailAttribute()
    attr5.type = "date"
    attr5.name = "checkbox box"
    attr5.description = "file description"

    let attr6 = new DesigncodeActivityDetailAttribute()
    attr6.type = "toggle"
    attr6.name = "checkbox box"
    attr6.description = "file description"

    // attr3.attributeValue = '';


    item.attributes = [attr1, attr2, attr3, attr4, attr5, attr6]
    this.inboxItem.designcodeActivityDetail = item
  }

  initData() {
    APP_UI_CONFIG.activity.getInboxItemDetails.paramList['id'] = this.inboxItemUid
    this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.getInboxItemDetails).subscribe(res => {
      this.inboxItem = res
      this.fieldAttributes = []
      this.actionAttributes = []
      if (this.inboxItem.designcodeActivityDetail.attributes != null) {
        for (var attr of this.inboxItem.designcodeActivityDetail.attributes) {
          if (attr.type.toUpperCase() == 'APPROVE' || attr.type.toUpperCase() == 'REJECT' || attr.type.toUpperCase() == 'BUTTON' || attr.type.toUpperCase() == 'SUBMIT') {
            this.actionAttributes.push(attr)
          } else {
            this.fieldAttributes.push(attr)
          }
        }
      }
    });

  }

  save(item) {
    this.uploadFiles()
    if (item.type.toUpperCase() == 'APPROVE') {
      this.inboxItem.designcodeActivityDetail.status = 2
      this.inboxItem.status = 2
    } else if (item.type.toUpperCase() == 'REJECT') {
      this.inboxItem.designcodeActivityDetail.status = -1
      this.inboxItem.status = 2
    }
    if (this.inboxItems != null && this.inboxItems.length > 0) {
      let payload = new CloseTimelineInboxItemList()
      payload.refItem = this.inboxItem
      payload.items = this.inboxItems
      payload.items.splice(0, 1)
      APP_UI_CONFIG.activity.updateInboxItemsDetails.paramList['id'] = this.inboxItemUid
      APP_UI_CONFIG.activity.updateInboxItemsDetails.payload = payload
      this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.updateInboxItemsDetails,'','',payload).subscribe(res => {
        this.commonService.success('Task has been closed successfully.');
        this.isDialog = false;
        this.dialogRef.close(true);
        //this.commonService.navigate("/dashboard/inbox")
      });
    } else {
      APP_UI_CONFIG.activity.updateInboxItemDetails.paramList['id'] = this.inboxItemUid
      APP_UI_CONFIG.activity.updateInboxItemDetails.payload = this.inboxItem
      this._invokeService.serviceInvocation(APP_UI_CONFIG.activity.updateInboxItemDetails,'','',this.inboxItem).subscribe(res => {
        this.commonService.success('Task has been closed successfully.');
        this.isDialog = false;
        this.dialogRef.close(true);
        // this.commonService.navigate("/dashboard/inbox")
      });
    }
  }

  public uploadFiles(): void {
    if (this.inboxItem.designcodeActivityDetail.attributes != null) {
      for (var attr of this.inboxItem.designcodeActivityDetail.attributes) {
        if (attr.type.toUpperCase() == 'FILE' && attr.files != null) {
          for (const file of attr.files) {
            const formData = new FormData()
            formData.append('file', file, file.name);
            // if (this.formDatakey) {
            //   formData.append(this.formDatakey, JSON.stringify({}))
            // }
            let headers = new HttpHeaders()
              .set("Authorization", this.basicAuth).set('Client', this.commonService.getTenantUid());
            let baseUrl = environment.apiUrl;
            this.http.post(baseUrl + 'sourcing/activity/designcode/' + this.inboxItem.designcodeActivityDetailUid + '/doc', formData, { headers: headers, responseType: 'json' })
              .subscribe(data => {
              }, error => {

              })
          }
        }
      }
    }
  }


  public closeApproval() {
    this.close.emit(true);
  }
  public getValue(value, index): void {
    if (this.inboxItem.designcodeActivityDetail.attributes[index].type.toUpperCase() == 'FILE') {
      this.inboxItem.designcodeActivityDetail.attributes[index].files = value
    } else {
      this.inboxItem.designcodeActivityDetail.attributes[index].attributeValue = value;
    }
    console.log("inboxItem changes :", this.inboxItem)
  }

  public navigatePO() {
    this.commonService.navigate("/schedules/purchaseorder/");
    this.closeDailog();
  }

  public navigateQuotaion() {
    this.commonService.navigate("/activity/quotationResponse/");
    this.closeDailog();
  }

  public navigateToRFQ2() {
    this.commonService.navigate("sourcing/quotation/quotationReq/");
    this.closeDailog();
  }

  public navigatePath() {
    let id = JSON.parse(this.customParams);
    this.commonService.navigate("/activity/create-edit-quotation-response/" + id.uuid);
    // if(this.path == "activity/create-edit-quotation-response/")
    //   this.commonService.navigate("/activity/quotationResponse/");
    // else
    //   this.commonService.navigate("/" + this.path);
    this.closeDailog();
  }


  public closeDailog(): void {
    this.isDialog = false;
    this.dialogRef.close();
  }

}

