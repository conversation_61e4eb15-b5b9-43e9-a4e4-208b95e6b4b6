import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { UploadFileComponent } from 'app/shared/upload-file/upload-file.component';
import { environment } from 'environments/environment';
import { ReplaySubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';


@Component({
  selector: 'app-vendor-list',
  templateUrl: './vendor-list.component.html',
  styleUrls: ['./vendor-list.component.scss']
})
export class VendorListComponent extends BaseTableSharedComponent {


  private destroyed$: ReplaySubject<boolean> = new ReplaySubject(1);
  vendorList: any[] = [];
  filteredVendorList: any[] = []
  statusList: any[] = [
    { uuid: "IN PROGRESS", name: "IN PROGRESS" },
    { uuid: "REQUEST RAISED", name: "REQUEST RAISED" },
    { uuid: "PRE-PRODUCTION", name: "PRE-PRODUCTION" },
    { uuid: "ACTIVE", name: "ACTIVE" }

  ];
  hdr: string = "Vendor List";
  reverse: boolean = true;


  selectedStatus: string;
  //declarations:
  tenantUid = this._commonService.getTenantUid();

  columnDefs: any[] = [,
    {
      headerName: "Name", field: "name", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
    },
    {
      headerName: "Contact Name", field: "mainContactName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
    },
    {
      headerName: "Business Line", field: "businessLine", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
    },
    {
      headerName: "Requested Date", field: "createdTime", tooltipField: '', sortable: true, unSortIcon: true,
      filter: 'agTextColumnFilter',
      cellRendererFramework: CustomDateTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data,
    },
    { headerName: "City", field: "city", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', },
    // { headerName: "Green Channel Enabled", field: "greenChannelEnabledStatus", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', },
    {
      headerName: "Status",
      field: "vendorBoardingStatus",
      sortable: true, unSortIcon: true,
      filter: 'agTextColumnFilter',
    },
    // 
    {
      headerName: 'Actions',
      pinned: 'right',
      maxWidth: 100,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: (params) => {
        return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
      }
    }

  ];
  tenantConfig: any;


  public ngOnInit(): void {
    localStorage.removeItem('vpf-form-data');
    this.tenantConfig = JSON.parse(localStorage.getItem('user'));
    this.onRefresh();
  }


  public init(): void {
    localStorage.removeItem('vpf-form-data');
    this.onRefresh();
    super.init();
  }


  public prepareActionIconButtons(row: any): any {
    this.status = null;
    let iconsList: any[] = [];
    if (row.deleted == 0) {
      iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });
      //iconsList.push({ type: 'edit', title: 'Enable as Green Channel', icon: 'edit', data: row })
    }
    if ((row.vendorBoardingStatus == "ACTIVE" || row.vendorBoardingStatus == "PRE-PRODUCTION") && this.tenantConfig.role.includes("OPERATIONS")) {
      iconsList.push({ type: 'editable', title: "Make Editable", icon: 'edit', data: row });
    }
    return iconsList;
  }
  private prepareFilteredVendorList(vendorData: any[]): void {
    vendorData.sort((a, b) => (a.greenChannelEnabled > b.greenChannelEnabled) ? 1 : ((b.greenChannelEnabled > a.greenChannelEnabled) ? -1 : 0));
    this.filteredVendorList = vendorData.map(item => ({ greenChannelEnabledStatus: item.greenChannelEnabled == 1 ? 'Yes' : 'No', ...item }))
  }

  public getVendorList(): void {
    this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.vendor.allVendors)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: response => {
          this.vendorList = response;
          this.filteredVendorList = this.vendorList;
          this.prepareFilteredVendorList(response);
        },
        error: error => {
          this._commonService.error(error)
        }
      })
  }

  public onRefresh(): void {
    this.getVendorList();
  }
  public onAction(event): void {
    let actionData: any = event;
    this.selectedData = actionData.data;
    if (actionData.type == 'edit') {
      this._router.navigate([`master/vendor-list/edit/${this.selectedData.uuid}`]);
    } else {
      if (actionData.type == 'view') {
        this._router.navigate(['/vendor/vpfForm'],
          {
            queryParams: { uuid: this.selectedData.uuid, status: 'view' }
          });
      }
    }
    if (actionData.type == 'editable') {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'This will make VPF editable, are you sure?',
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          console.log(result);
          this._dashboardService.makeVPFEditable(this.selectedData.uuid).subscribe(res => {
            if (res.code == 200) {
              this._router.navigate(['/vendor/vpfForm'],
                {
                  queryParams: { uuid: this.selectedData.uuid }
                });
            }
          });
        }
      });
    }

  }

  public getCloseEvent() {
    super.closeAddEdit();
  }

  public onStatusChange(): void {
    if (this.selectedStatus) {
      this.filteredVendorList = this.vendorList.filter(item => item.vendorBoardingStatus?.toLowerCase() == this.selectedStatus?.toLowerCase())
    }
  }

  public getImportEvent(event): void {
    this.openImportDialog();
  }

  private openImportDialog(): void {
    const dialogRef = this.dialog.open(UploadFileComponent, {
      width: '70%',
      data: { dailogHeadig: 'Import Vendor', apiData: { 'url': 'vendor/master/bulk_imports', type: "POST" }, message: { success: 'Vendor Imported successfully', error: 'Import failed' }, dropFileslabel: 'Vendors' },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.init();
      }
    });
  }

  public ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.complete();
  }

}
