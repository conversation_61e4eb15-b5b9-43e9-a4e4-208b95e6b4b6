{"name": "@quality_360/quality_automation_platform", "version": "12.0.0", "private": true, "scripts": {"ng": "ng", "start": "node --max_old_space_size=8192 node_modules/@angular/cli/bin/ng serve", "build": "ng build --prod", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "qinspect-prod": "node --max_old_space_size=150124 node_modules/@angular/cli/bin/ng build --prod --build-optimizer", "qinspect-stage": "ng build --prod=false"}, "dependencies": {"@agentepsilon/decko": "^2.0.1", "@amcharts/amcharts5": "^5.1.4", "@amcharts/amcharts5-fonts": "^5.0.1", "@amcharts/amcharts5-geodata": "^5.0.2", "@angular-material-components/datetime-picker": "^5.1.0", "@angular-material-components/moment-adapter": "^2.0.1", "@angular/animations": "11.2.10", "@angular/cdk": "11.2.9", "@angular/common": "11.2.10", "@angular/compiler": "11.2.10", "@angular/core": "11.2.10", "@angular/forms": "11.2.10", "@angular/material": "11.2.9", "@angular/material-moment-adapter": "11.2.9", "@angular/platform-browser": "11.2.10", "@angular/platform-browser-dynamic": "11.2.10", "@angular/router": "11.2.10", "@fortawesome/angular-fontawesome": "^0.7.0", "@fortawesome/fontawesome-svg-core": "^6.1.1", "@fortawesome/free-solid-svg-icons": "^6.1.1", "@kolkov/angular-editor": "^1.0.0", "@ng-bootstrap/ng-bootstrap": "^9.1.3", "@ng-idle/core": "11.1.0", "@ng-idle/keepalive": "11.0.3", "@ng-select/ng-select": "^6.1.0", "@swimlane/ngx-charts": "^18.0.1", "@tinymce/tinymce-angular": "^4.0.0", "ag-grid-angular": "^26.2.0", "ag-grid-community": "^26.2.1", "angular-archwizard": "^7.0.0", "angular-code-input": "^1.6.0", "bootstrap": "^5.1.1", "core-js": "^3.25.0", "crypto-js": "3.3.0", "d3": "^5.12.0", "d3-selection": "^1.4.0", "d3-x3d": "^2.0.4", "events": "^3.3.0", "file-saver": "^2.0.5", "highlight.js": "10.7.2", "html2canvas": "^1.4.1", "jquery": "^3.6.0", "js-sha512": "^0.8.0", "jspdf": "^2.5.2", "jszip": "^3.10.1", "lodash-es": "4.17.21", "moment": "2.29.1", "ng-wizard": "^1.3.0", "ng2-pdf-viewer": "^7.0.0", "ngx-capture": "^0.10.1", "ngx-file-drop": "^11.2.0", "ngx-filesaver": "^9.0.0", "ngx-image-zoom": "^0.6.0", "ngx-markdown": "11.1.2", "ngx-papaparse": "^5.1.0", "ngx-perfect-scrollbar": "6.3.0", "ngx-toastr": "^11.3.0", "pdfmake": "^0.2.16", "popper.js": "^1.16.1", "primeicons": "^4.1.0", "primeng": "^11.0.0", "rrule": "2.6.8", "rxjs": "6.6.7", "tslib": "2.1.0", "tui-calendar": "1.15.1", "tui-date-picker": "^4.3.1", "tui-time-picker": "^2.1.4", "uuid": "^3.4.0", "web-animations-js": "2.3.2", "xlsx": "^0.17.4", "zone.js": "0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "0.1102.9", "@angular/cli": "11.2.9", "@angular/compiler-cli": "11.2.10", "@angular/language-service": "11.2.10", "@tailwindcss/aspect-ratio": "0.2.0", "@tailwindcss/line-clamp": "0.2.0", "@tailwindcss/typography": "0.4.0", "@types/chroma-js": "2.1.3", "@types/crypto-js": "3.1.47", "@types/d3": "^5.7.2", "@types/highlight.js": "10.1.0", "@types/jasmine": "3.6.8", "@types/lodash": "4.14.168", "@types/lodash-es": "4.17.4", "@types/node": "12.20.6", "autoprefixer": "10.2.5", "chroma-js": "2.1.1", "codelyzer": "6.0.1", "jasmine-core": "3.6.0", "jasmine-spec-reporter": "5.0.2", "karma": "6.1.2", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.0.3", "karma-jasmine": "4.0.1", "karma-jasmine-html-reporter": "1.5.4", "lodash": "4.17.21", "postcss": "8.2.10", "protractor": "7.0.0", "tailwindcss": "2.1.1", "ts-node": "8.3.0", "tslint": "6.1.3", "typescript": "4.1.5"}}