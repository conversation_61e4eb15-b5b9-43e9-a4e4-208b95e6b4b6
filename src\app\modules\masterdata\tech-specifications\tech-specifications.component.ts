import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { TechnicalSpecificationsGroup } from 'app/core/qinspect/_model/SpecificationGroup.model';
import { TechnicalSpecificationsConfig } from 'app/core/qinspect/_model/TechSpecificationConfig.model';
import {
  FORM_CONTROL_TYPES,
  FORM_FIELDS_CONSTANTS_VALUES,
  HTTP_STATUS,
  inputTypeList,
  ROLE,
  TABLE_ACTION_TYPES,
} from 'app/enum-constants';
import { ICON_LIST } from 'app/icon-list';
import { BuildFormUtilityService } from 'app/services/build-form-utility.service';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { UploadFileComponent } from 'app/shared/upload-file/upload-file.component';
import { ReplaySubject } from 'rxjs';
import { distinctUntilChanged, takeUntil } from 'rxjs/operators';

import { TECH_SPEC_FORM_MODEL } from './new-techSpecification-form.model';

@Component({
  selector: 'app-tech-specifications',
  templateUrl: './tech-specifications.component.html'
})
export class TechSpecificationsComponent implements OnInit, OnDestroy {
  specificationList: any[] = [];
  columnDefs: any[] = [];
  techSpecificationList: any[] = [];
  filtersListData: any[] = [];
  productTypeList: any[] = [];
  selectedProductTypeUid: string;
  showForm: boolean = false;
  status: string = TABLE_ACTION_TYPES.CREATE;
  dialogHeaderText: string;
  rec: TechnicalSpecificationsConfig = new TechnicalSpecificationsConfig();
  hdr: string = 'Technical Specifications Configuration';
  techSpecGroupData: TechnicalSpecificationsGroup = new TechnicalSpecificationsGroup();

  public techSpecificationForm: FormGroup;
  public fields: any[] = [];
  private yesOrNoList = [{ label: "Yes", value: 1 }, { label: "No", value: 0 }];
  private keywordList = [{ label: "Area", value: "Area" }, { label: "Speed", value: "Speed" },
  { label: "Temperature", value: "Temperature" }, { label: "Time", value: "Time" },
  { label: "Volume", value: "Volume" }, { label: "Weight", value: "Weight" },
  { label: "Length", value: "Length" }, { label: "Period", value: "Period" }
  ];

  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);

  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;

  constructor(
    public invokeService: InvokeService,
    public _commonService: CommonService,
    public _buildFormUtilityService: BuildFormUtilityService,
    private cdRef: ChangeDetectorRef,
    public dialog: MatDialog,) { }

  public ngOnInit(): void {
    this.onRefresh();
  }



  public onRefresh(): void {
    this.techSpecGroupData = JSON.parse(localStorage.getItem('techSpecificationGroupData'));
    this.hdr = 'Technical Specifications Configuration - ' + this.techSpecGroupData.name;
    this.getTechSpecificationsByProductUid();
    this.prepareTableColumns();
    this.getExistingSectionList();
  }


  private prepareTableColumns(): void {
    this.columnDefs = [
      { headerName: "Specification Name", field: "specificationName", sortable: true, unSortIcon: true, wrapText: false },
      { headerName: "Specification Code", field: "code", sortable: true, unSortIcon: true, wrapText: false },
      { headerName: "Section Name", field: "sectionName", sortable: true, unSortIcon: true, wrapText: false },
      { headerName: "Unit For", field: "unitFor", sortable: true, unSortIcon: true, wrapText: false },
      { headerName: "UOM", field: "uom", sortable: true, unSortIcon: true, wrapText: false },
      { headerName: "Input Type", field: "inputType", sortable: true, unSortIcon: true, wrapText: false },
      { headerName: "mandatory", field: "mandatoryText", sortable: true, unSortIcon: true, wrapText: false },
      { headerName: "Character Length", field: "characterLength", sortable: true, unSortIcon: true, wrapText: false },
      { headerName: "Uom Code", field: "keyword", sortable: true, unSortIcon: true, wrapText: false },
      { headerName: "List of Values", field: "list", sortable: true, unSortIcon: true, wrapText: false },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 180,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
        }
      }
    ];
  }

  private onFormChanges(option): void {
    let specUnitData = this.techSpecificationList.find(item => item.unitFor == this.techSpecificationForm.get("specificationName").value);
    if (this.techSpecificationForm.get("uom").value) {
      this._commonService.warning("Single UOM is already created , can't create UOM standard unit");
      this.techSpecificationForm.get("isUnitRequired").setValue('No');
      return
    } else {
      if (option?.label == 'Yes') {
        this.showkeywordField();
        if (specUnitData && this.status == TABLE_ACTION_TYPES.EDIT) {
          this.techSpecificationForm.get("keyword").setValue(specUnitData?.keyword);
        }
      }
      else {
        this.hidekeywordField();
        if (this.status == TABLE_ACTION_TYPES.EDIT) {
          this.deleteSpecUnit(specUnitData)
        }
      }
    }
  }

  private configureUom(event): void {
    if (this.techSpecificationForm.get("isUnitRequired").value == 'Yes') {
      this._commonService.warning("UOM standard unit is already created , cant create single UOM");
      this.techSpecificationForm.get("uom").setValue('');
      return
    }
  }

  private deleteSpecUnit(specUnitData): void {
    if (specUnitData) {
      this._commonService.openDialogForConfirmation(`Are you sure you want to delete the UOM specification for ${specUnitData.unitFor}?`)
        .subscribe({
          next: confirm => {
            if (confirm) {
              APP_UI_CONFIG.TechSpecification.deleteConfiguration.paramList.configUuid = specUnitData.uuid;
              this.invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.deleteConfiguration, null, null, null).subscribe({
                next: response => {
                  if (response && response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success("Specification Deleted Successfully");
                    this.onRefresh();
                  } else {
                    this._commonService.handleError(response)
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
            }
          }
        })
    }
  }

  private configureInputType(inputType, field): void {
    this.fields.map(item => {
      if (item.label == 'Character Length') {
        if (inputType.value == FORM_CONTROL_TYPES.NUMBER || inputType.value == FORM_CONTROL_TYPES.TEXT || inputType.value == FORM_CONTROL_TYPES.TEXTAREA) {
          this.techSpecificationForm.get("characterLength").enable();
          item.rules.required = true;
          if (this.status == TABLE_ACTION_TYPES.CREATE) {
            this.techSpecificationForm.get("characterLength").setValue('0');
          } else {
            let savedSpec = this.techSpecificationList.find(item => item.specificationName == this.techSpecificationForm.get("specificationName").value);
            this.techSpecificationForm.get("characterLength").setValue(savedSpec.characterLength);
          }
        } else {
          this.techSpecificationForm.get("characterLength").disable();
          this.techSpecificationForm.get("characterLength").setValue('0');
          item.rules.required = false;
        }
      }

      if (inputType.value == FORM_CONTROL_TYPES.SINGLE_SELECT || inputType.value == FORM_CONTROL_TYPES.MULTI_SELECT) {
        if (item.label == 'List of Values') {
          this.techSpecificationForm.get("list").enable();
          item.rules.required = true;
        }
      } else {
        this.techSpecificationForm.get("list").disable();
        this.techSpecificationForm.get("list").setValue('');

      }
    })
  }

  public onClose(): void {
    this._commonService.navigate(`master/specGroup`);
  }
  private showkeywordField(): void {
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'keyword', true, this.techSpecificationForm);
    this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "keyword", { required: true });
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'keyword', this.keywordList);
    this.techSpecificationForm.get("keyword").setValidators(Validators.required);
    this.techSpecificationForm.updateValueAndValidity();
    this.cdRef.detectChanges();
  }

  private hidekeywordField(): void {
    this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "keyword", { required: false });
    this.techSpecificationForm.get("keyword").clearValidators();
    this.techSpecificationForm.updateValueAndValidity();
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'keyword', false, this.techSpecificationForm);
    this.cdRef.detectChanges();
  }

  private hideIsUnitRequiredField(): void {
    this.fields = this._buildFormUtilityService.setFieldsValidation(this.fields, "isUnitRequired", { required: false });
    this.techSpecificationForm.get("isUnitRequired").clearValidators();
    this.techSpecificationForm.get("isUnitRequired").setValue('');
    this.techSpecificationForm.updateValueAndValidity();
    this.fields = this._buildFormUtilityService.showHideField(this.fields, 'isUnitRequired', false, this.techSpecificationForm);
    this.cdRef.detectChanges();
  }

  public setFilterList(): void {
    if (this._commonService.getUseRole() != ROLE.LAB) {
      this.filtersListData = [
        {
          label: 'Product Type',
          type: 'dropdown',
          defaultValue: this.productTypeList[0],
          showLabelName: '',
          list: this.productTypeList,
        },
      ]
    }
    this.selectedProductTypeUid = this.productTypeList[0]?.uuid;

  }

  private getProductTypeList(): void {
    this.invokeService.serviceInvocation(APP_UI_CONFIG.request.getProductTypeList)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.productTypeList = response?.map(item => ({ name: item.name, uuid: item.uuid, label: item.name, value: item.name }));
            // this.setFilterList();
            this.onRefresh();
          }
        },
        error: error => {
          // this._commonService.handleError(error);
        }
      })
  }

  public onFilter(event): any {
    this.selectedProductTypeUid = event[0]?.value?.uuid;
    this.onRefresh();
  }

  private getTechSpecificationsByProductUid(): void {
    this.invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getListDataByGroupUid, null, null, Util.clone({ "groupUid": this.techSpecGroupData.uuid }))
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            this.techSpecificationList = this._commonService.sortByDateLatest(response, 'createdTime', 'createdTime')?.map(item => ({ createdTimeText: this._commonService.dateTimeFormat(item.createdTime), mandatoryText: item.mandatory == 1 ? 'Yes' : 'No', mandatory: !!item.mandatory ? item.mandatory : 0, ...item }))
          }
        },
        error: error => {

        }
      })
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];
    iconsList.push({ type: TABLE_ACTION_TYPES.VIEW, title: ICON_LIST.VIEW, icon: ICON_LIST.VIEW, data: row });
    iconsList.push({ type: TABLE_ACTION_TYPES.EDIT, title: ICON_LIST.EDIT, icon: ICON_LIST.EDIT, data: row });
    if (!row.unitFor) {
      iconsList.push({ type: TABLE_ACTION_TYPES.DELETE, title: ICON_LIST.DELETE, icon: ICON_LIST.DELETE, data: row });
    }
    return iconsList;
  }

  public onAction(action): void {
    switch (action.type) {
      case TABLE_ACTION_TYPES.VIEW:
        this.prepareFormModel();

        if (!action.data.unitFor) {
          this.getConfigByUid(action)
        }
        this.showForm = true;
        this.dialogHeaderText = 'View Technical Specification';
        this.status = TABLE_ACTION_TYPES.VIEW;
        this.rec = action.data;
        this.techSpecificationForm.get("specificationGroupCode").setValue(this.techSpecGroupData.code);
        this.techSpecificationForm.get("categoryName").setValue(this.techSpecGroupData.category);
        // this.hideIsUnitRequiredField();
        this.techSpecificationForm.disable();
        if (this.rec.keyword || this.rec.unitFor) {
          this.showkeywordField();
        }
        this.setDataToForm();
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.prepareFormModel();
        if (!action.data.unitFor) {
          this.getConfigByUid(action)
        }
        this.techSpecificationForm.enable();
        this.showForm = true;
        this.techSpecificationForm.get("categoryName")?.disable();
        this.techSpecificationForm.get("specificationGroupCode")?.disable();
        this.techSpecificationForm.get("code")?.disable();
        // this.techSpecificationForm.get("specificationName")?.disable();
        this.dialogHeaderText = 'Update Technical Specification';
        this.status = TABLE_ACTION_TYPES.EDIT;
        this.rec = action.data;
        this.techSpecificationForm.get("specificationGroupCode").setValue(this.techSpecGroupData.code);
        this.techSpecificationForm.get("categoryName").setValue(this.techSpecGroupData.category);
        // this.hideIsUnitRequiredField();
        if (this.rec.keyword || this.rec.unitFor) {
          this.checkUpdateFormforUnitType();
        }
        this.setDataToForm();
        break;
      case TABLE_ACTION_TYPES.DELETE:
        this.onDeleteConfiguration(action.data)
        break;
      default:
      // code block
    }
  }
  savedConfigList: any;
  private getConfigByUid(row): void {
    let specKeyWord = this.techSpecificationList.find(item => item.unitFor == row.data.specificationName)
    APP_UI_CONFIG.TechSpecification.getConfigurationByUid.paramList.uuid = row.data.uuid
    this.invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getConfigurationByUid)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.savedConfigList = response;
          if (response?.unit?.unitFor) {
            this.techSpecificationForm.get("isUnitRequired").setValue('Yes');
            this.techSpecificationForm.get("keyword")?.setValue(specKeyWord.keyword);
            this.setDataToForm();
            this.showkeywordField();
          } else {
            this.techSpecificationForm.get("isUnitRequired").setValue('No');
          }
        }, error: error => {
          this._commonService.handleError(error);
        }
      })
  }
  private checkUpdateFormforUnitType(): void {
    this.techSpecificationForm.disable();
    this.showkeywordField();
    this.techSpecificationForm.get("keyword")?.enable();
  }


  //Form
  public onAddSpecification(): void {
    this.prepareFormModel();
    this.rec = new TechnicalSpecificationsConfig();
    this.techSpecificationForm.enable();
    this.dialogHeaderText = 'Add Technical Specification';
    this.techSpecificationForm.reset();
    this.status = TABLE_ACTION_TYPES.CREATE;
    this.showForm = true;
    this.techSpecificationForm.get("specificationGroupCode").setValue(this.techSpecGroupData.code);
    this.techSpecificationForm.get("categoryName").setValue(this.techSpecGroupData.category);
    this.techSpecificationForm.get("categoryName")?.disable();
    this.techSpecificationForm.get("specificationGroupCode")?.disable();

  }

  //build form

  private prepareFormModel(): void {
    const TECH_SPEC_FORM_MODEL = {
      categoryName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        disabled: true,
        label: "Category",
        placeholder: '',
        show: true,
        rules: {
          required: false,
          maxLength: 250,
        }
      },
      specificationGroupCode: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        disabled: true,
        label: "Group Code",
        placeholder: '',
        show: true,
        rules: {
          required: false,
          maxLength: 250,
        }
      },
      code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        disabled: false,
        label: "Specification Code",
        placeholder: 'Specification Code',
        show: true,
        rules: {
          required: true,
          maxLength: 50,
        }
      },
      specificationName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        disabled: false,
        label: "Specification Name",
        placeholder: 'Specification Name',
        show: true,
        rules: {
          required: true,
          maxLength: 255,
        }
      },
      inputType: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        disabled: false,
        label: "Input Type",
        placeholder: 'Input Type',
        onChange: (event, field) => this.configureInputType(event, field),
        show: true,
        rules: {
          required: true,
          maxLength: 255,
        }
      },
      uom: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        disabled: false,
        label: "Single UOM",
        placeholder: 'Single UOM',
        show: true,
        onChange: (event) => this.configureUom(event),
        tooltip: 'If Standard UOMs are not reqd',
        rules: {
          required: false,
          maxLength: 255,
          pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE
        }
      },
      characterLength: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        disabled: true,
        label: "Character Length",
        placeholder: 'Character Length',
        show: true,
        rules: {
          required: true,
          pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN
        }
      },
      mandatory: {
        type: FORM_CONTROL_TYPES.RADIO,
        options: [],
        value: "",
        label: "Mandatory",
        placeholder: 'Mandatory',
        disabled: false,
        onChange: (option) => '',
        show: true,
        rules: {
          required: true,
        },
      },
      sectionName: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        disabled: false,
        label: "Section Name",
        placeholder: 'Section Name',
        onChange: (option) => this.onSectionNameChanges(option),
        show: true,
      },
      sectionOtherName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        disabled: true,
        label: "Section Name (Other)",
        placeholder: 'Section Name (Other)',
        show: false,
        rules: {
          required: false,
          // pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN
        }
      },
      list: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        disabled: true,
        label: "List of Values",
        placeholder: 'List of Values',
        show: true,
        tooltip: 'Separate values by comma',
        rules: {
          required: false,
          maxLength: 255,
        }
      },
      isUnitRequired: {
        type: FORM_CONTROL_TYPES.RADIO,
        options: [],
        value: "",
        label: "UOM Standard Units Required?",
        placeholder: 'UOM Standard Units Required?',
        disabled: false,
        show: true,
        onChange: (option) => this.onFormChanges(option),
        rules: {
          required: false,
        },
      },
      keyword: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        disabled: false,
        label: "UOM Code",
        onChange: (event, field) => '',
        placeholder: 'Select UOM Code',
        show: false,
        rules: {
          required: false,
          maxLength: 255,
        }
      }
    }
    this.buildForm(TECH_SPEC_FORM_MODEL);
  }
  private buildForm(TECH_SPEC_FORM_MODEL) {
    const formGroupFields = this._buildFormUtilityService.getFormControlsFields(TECH_SPEC_FORM_MODEL);
    this.fields = this._buildFormUtilityService.getFieldsList();
    this.techSpecificationForm = new FormGroup(formGroupFields);

    let inputTypeLists = inputTypeList.filter(item => item.value != 'radio' && item.value != 'checkbox' && item.value != 'text-area' && item.value != 'date')
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'inputType', inputTypeLists);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'mandatory', this.yesOrNoList);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'isUnitRequired', this.yesOrNoList);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'keyword', this.keywordList);
    this.fields = this._buildFormUtilityService.setOptionsToDropDown(this.fields, 'sectionName', this.existingSecList);

    this.techSpecificationForm.get("specificationGroupCode").setValue(this.techSpecGroupData.code);
    this.techSpecificationForm.get("categoryName").setValue(this.techSpecGroupData.category);
  }

  private onSectionNameChanges(section): void {
    if (section.value == 'Other') {
      this.fields.map(item => {
        if (item.label == 'Section Name (Other)') {
          item.show = true;
          item.rules.required = true;
        }
      })
    }

  }

  existingSecList: any[] = [];
  private getExistingSectionList(): void {
    let payload = {
      "groupUid": this.techSpecGroupData.uuid
    }
    this.invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getExistingSpecificationList, '', '', payload).subscribe({
      next: response => {
        this.existingSecList = response?.filter(item => item);
        this.existingSecList.push("Other");
      }, error: error => {
        this._commonService.handleError(error);
      }
    });

  }

  private setDataToForm(): void {
    if (!this.rec.unitFor && (this.rec.inputType.toLowerCase() == FORM_CONTROL_TYPES.SINGLE_SELECT.toLowerCase() || this.rec.inputType.toLowerCase() == FORM_CONTROL_TYPES.MULTI_SELECT.toLowerCase()) && this.status != TABLE_ACTION_TYPES.VIEW) {
      this.techSpecificationForm.get("list").enable();
    } else {
      this.techSpecificationForm.get("list").disable();

    }

    this.rec.inputType = this.rec.inputType?.toLowerCase();
    this.techSpecificationForm = this._buildFormUtilityService.setFormControlsValues(this.techSpecificationForm, this.rec, this.fields);
    let savedSpec = this.techSpecificationList.find(item => item.specificationName == this.techSpecificationForm.get("specificationName").value);
    if (savedSpec.mandatory == 1) {
      this.techSpecificationForm.get("mandatory").setValue('Yes');
    } else {
      this.techSpecificationForm.get("mandatory").setValue('No');
    }
    if (savedSpec?.unitFor) {
      if (savedSpec?.keyword) {
        this.techSpecificationForm.get("isUnitRequired").setValue('Yes');
      }
    }
  }
  public onSpecConfigDownloadTemplate(): void {
    this.invokeService.downloadFile(APP_UI_CONFIG.TechSpecification.downloadSpecConfigTemplate, '', `Spec_Config_Template.csv`, '');
  }

  public onSubmit(addMore: boolean): void {
    if (this.techSpecificationForm.get("inputType").value == FORM_CONTROL_TYPES.NUMBER || this.techSpecificationForm.get("inputType").value == FORM_CONTROL_TYPES.TEXTAREA || this.techSpecificationForm.get("inputType").value == FORM_CONTROL_TYPES.TEXT)
      if (this.techSpecificationForm.get("characterLength").value < 1) {
        this._commonService.error("Character Length should be greater than 1");
        return;
      }
    this.rec = this._buildFormUtilityService.mapFormFieldsDataToModel(this.techSpecificationForm, this.rec, this.fields)
    this.rec.mandatory = (this.techSpecificationForm.get("mandatory").value == 'Yes') ? 1 : 0;
    this.rec.characterLength = Number(this.rec.characterLength);
    if (this.rec.uuid) {
      let payload: any[] = this.prepareUpdatePayLoad();
      this.invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.updateConfiguration, null, null, payload)
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            if (response.code == HTTP_STATUS.SUCCESS) {
              this._commonService.success(`${this.rec.specificationName} Specification Updated Successfully.`);
              this.onRefresh();
              if (!addMore) {
                this.prepareFormModel();
                this.showForm = false;
              }
            } else {
              this._commonService.handleError(response);
            }
          }, error: error => {
            this._commonService.handleError(error);
          }
        })
    }
    else {
      let payload: any[] = this.preparePayLoad();
      this.invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.addConfiguration, null, null, Util.clone(payload))
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            if (response.code == HTTP_STATUS.SUCCESS) {
              this._commonService.success('New Specification Added Successfully');
              this.onRefresh();
              this.onAddSpecification();
              if (addMore) {
                this.getExistingSectionListAfterAdd();
              } else {
                this.prepareFormModel();
                this.showForm = false;
              }
            } else {
              this._commonService.handleError(response);
            }
          }, error: error => {
            this._commonService.handleError(error);
          }
        })
    }
  }

  private getExistingSectionListAfterAdd(): void {
  let payload = {
    "groupUid": this.techSpecGroupData.uuid
  };

  this.invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.getExistingSpecificationList, '', '', payload)
    .pipe(takeUntil(this.$destroyed))
    .subscribe({
      next: response => {
        this.existingSecList = response?.filter(item => item);
        this.existingSecList.push("Other");
        this.onAddSpecification();
      },
      error: error => {
        this._commonService.handleError(error);
      }
    });
}

  matchingItem = [];
  private prepareUpdatePayLoad(): any[] {
    let payload: any[] = [];
    if (this.techSpecificationForm.get("isUnitRequired").value == 'Yes') {
      if (!this.rec.unitFor) {
        if (this.techSpecificationList.find(item => item.unitFor == this.savedConfigList?.specificationName)) {
          let specUnitData = this.techSpecificationList.filter(
            item => item.unitFor === this.savedConfigList.specificationName
          );
          let mappedSpecUnit = specUnitData.map(item => ({
            ...item,
            keyword: this.rec.keyword,
            characterLength: 0,
            unitFor: this.rec.specificationName,
            mandatory: this.rec.mandatory,
            uuid: specUnitData[0].uuid
          }));
          delete this.rec.keyword;
          this.rec.sectionName = this.rec.sectionName ? this.rec.sectionName == "Other" ? `${this.rec.sectionOtherName}` : `${this.rec.sectionName}` : '';
          payload.push(this.rec)
          return payload.concat(mappedSpecUnit[0]);


        } else {
          let createSpecUnit: any = {};
          createSpecUnit['specificationName'] = 'Unit';
          createSpecUnit['inputType'] = 'single-select';
          createSpecUnit['characterLength'] = 0;
          createSpecUnit['unitFor'] = this.rec.specificationName;
          createSpecUnit['code'] = this.rec.code + '_unit';
          createSpecUnit['keyword'] = this.rec.keyword;
          createSpecUnit['mandatory'] = this.rec.mandatory;
          createSpecUnit['sectionName'] = this.rec.sectionName ? this.rec.sectionName == "Other" ? `${this.rec.sectionOtherName}` : `${this.rec.sectionName}` : '';
          createSpecUnit['specificationGroup'] = this.rec.specificationGroup;
          createSpecUnit['specificationGroupCode'] = this.rec.specificationGroupCode;
          createSpecUnit['specificationGroupUid'] = this.rec.specificationGroupUid;
          createSpecUnit['uom'] = this.rec.uom;

          delete this.rec.keyword;
          payload.push(this.rec)
          return payload.concat(createSpecUnit);
        }

      } else if (this.rec.unitFor && this.rec.keyword) {
        this.rec.sectionName = this.rec.sectionName ? this.rec.sectionName == "Other" ? `${this.rec.sectionOtherName}` : `${this.rec.sectionName}` : '';
        payload.push(this.rec);
        let specUnitData = this.techSpecificationList.filter(
          item => item.specificationName === this.rec.unitFor
        );
        return payload.concat(specUnitData[0]);
      }

    } else {
      delete this.rec.keyword;
      this.rec.sectionName = this.rec.sectionName ? this.rec.sectionName == "Other" ? `${this.rec.sectionOtherName}` : `${this.rec.sectionName}` : '';
      let payloadData: any = this.rec
      return [payloadData]
    }

  }

  private preparePayLoad(): any[] {
    let payload: any[] = [];
    this.rec.sectionName = this.rec.sectionName ? this.rec.sectionName == "Other" ? `${this.rec.sectionOtherName}` : `${this.rec.sectionName}` : '';
    let obj = {
      characterLength: this.rec.characterLength,
      code: this.rec.code,
      inputType: this.rec.inputType,
      mandatory: this.rec.mandatory,
      sectionName: this.rec.sectionName,
      specificationGroupCode: this.rec.specificationGroupCode,
      specificationName: this.rec.specificationName,
      uom: this.rec.uom,
      specificationGroup: this.techSpecGroupData.name,
      specificationGroupUid: this.techSpecGroupData.uuid,
      list: this.rec.list
    }
    payload.push(obj)
    const copiedObj = JSON.parse(JSON.stringify(obj));
    if (this.techSpecificationForm.get("isUnitRequired").value == 'Yes') {
      copiedObj['keyword'] = this.rec.keyword;
      copiedObj['specificationName'] = 'Unit';
      copiedObj['inputType'] = 'single-select';
      copiedObj['characterLength'] = 0;
      copiedObj['unitFor'] = this.rec.specificationName;
      copiedObj['code'] = this.rec.code + '_unit';
      delete copiedObj.list;
      payload.push(copiedObj)
    }
    return payload;
  }

  private onDeleteConfiguration(configData): void {
    this._commonService.openDialogForConfirmation(`Are you sure want to delete ${configData?.specificationName} ?`)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: confirm => {
          if (confirm) {
            APP_UI_CONFIG.TechSpecification.deleteConfiguration.paramList.configUuid = configData.uuid;
            this.invokeService.serviceInvocation(APP_UI_CONFIG.TechSpecification.deleteConfiguration, null, null, null).subscribe({
              next: response => {
                if (response && response.code == HTTP_STATUS.SUCCESS) {
                  this._commonService.success("Specification Deleted Successfully");
                  this.onRefresh();
                } else {
                  this._commonService.handleError(response)
                }
              }, error: error => {
                this._commonService.handleError(error);
              }
            });
          }
        }
      })
  }
  public onExport(event): void {
    this.invokeService.downloadFile(APP_UI_CONFIG.TechSpecification.exportGroupTechSpecification, '', 'Tech_Specification.csv', null);
  }

  public getImportEvent(): void {
    const dialogRef = this.dialog.open(UploadFileComponent, {
      width: '70%',
      data: { extenstions: 'csv', dailogHeadig: 'Import Tech Specification', apiData: { 'url': 'sourcing/impex/TechnicalSpecificationsConfig/import', type: "POST" }, message: { success: 'Imported successfully', error: 'Import failed' }, dropFileslabel: 'Tech Specification' },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.onRefresh();
      }
    });
  }

  public onCloseForm(): void {
    this.showForm = false;
  }
  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }

}
