import { ChangeDetectorRef, Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { CountryList } from 'app/core/qinspect/_common/country';
import { Util } from 'app/core/qinspect/_common/util';
import { QiVendor } from 'app/core/qinspect/_model/vendor.model';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'addedit-vendor',
  templateUrl: './addedit-vendor.component.html',
  styleUrls: ['./addedit-vendor.component.scss']
})
export class AddeditVendorComponent implements OnInit {

  @Output() close: EventEmitter<any> = new EventEmitter();
  status: string;
  hdr: string = 'Add Vendor';
  ctryList: CountryList;
  editRec: boolean = false;
  btnflag: boolean = false;
  codeinpt: boolean = false;
  rec: QiVendor;
  isMandatory: boolean = false;
  loading: boolean;
  addRec: boolean = false;

  constructor(private invokeService: InvokeService,
    private cdRef: ChangeDetectorRef,
    private toaster: ToastrService,
    private _commonService: CommonService,
    public dialogRef: MatDialogRef<AddeditVendorComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any) { }

  ngOnInit(): void {
    this.isMandatory = this._commonService.getTenantConfig().tenantUid != 'twills' ? true : false;
    const usrole = JSON.parse(localStorage.getItem('currentUser'));
    this.ctryList = new CountryList();
    this.status = this.data.status;
    if (this.data?.data && this.data.data.uuid) {
      if (this.status == 'update')
        this.onEdit(this.data.data);
      else if (this.status = "view") {
        this.onView(this.data.data);
      }
    } else {
      this.openAddRecord();
    }
  }

  onEdit(data) {
    this.addRec = false;
    const usrole = JSON.parse(localStorage.getItem('currentUser'));
    if (usrole.role == 'ADMIN') {
      this.hdr = 'Update Vendor';
      this.editRec = false;
      this.btnflag = true;
    }
    else {
      this.hdr = 'View Vendor';
      this.editRec = true;
      this.btnflag = false;
    }
    this.codeinpt = true;
    this.rec = <QiVendor>Util.clone(data);
    this.ctryList.setCountry(this.rec.country);
  }

  public openAddRecord() {
    this.addRec = true;
    const usrole = JSON.parse(localStorage.getItem('currentUser'));
    this.rec = new QiVendor("");
    this.editRec = false;
    this.codeinpt = false;
    this.hdr = 'Add Vendor';
    this.ctryList.setCountry(this.rec.country);
    if (usrole.role == 'ADMIN') {
      this.editRec = false;
      this.btnflag = true;
    }
    else {
      this.editRec = true;
      this.btnflag = false;
    }
  }

  public onSave() {
    this.loading = true;
    this.rec.country = this.ctryList.selectedCtry;
    if (this.rec.uuid != null || this.rec.id != null) {
      APP_UI_CONFIG.masterdata.vendor.update.paramList['id'] = this.rec.uuid;
      this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.vendor.update, null, this.rec.uuid, Util.clone(this.rec)).subscribe(res => {
        this.processResponse(res);
      }, (err: any) => {
        this._commonService.handleError(err);
      })

    } else {
      this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.vendor.add, null, null, Util.clone(this.rec)).subscribe(res => {
        this.processResponse(res);
      }, (err: any) => {
        this._commonService.handleError(err);
      })
    }
  }

  processResponse(data) {
    this.loading = false;
    if (data.code == "200") {
      this.toaster.success(data.message);
      this.closeAddVendor();
    }
    else {
      // this.toaster.warning(data.message);
    }
    this.cdRef.detectChanges();
  }

  public closeAddVendor(): void {
    this.dialogRef.close(true);
  }

  onView(event) {
    this.addRec = false;
    this.editRec = true;
    const usrole = JSON.parse(localStorage.getItem('currentUser'));
    this.codeinpt = true;
    this.rec = <QiVendor>Util.clone(event);
    this.ctryList.setCountry(this.rec.country);
    if (usrole.role == 'ADMIN') {
      this.hdr = 'View Vendor';
      this.btnflag = true;
    }
    else {
      this.hdr = 'View Vendor';
      this.btnflag = false;
    }

  }

  setToggleValue(e) {
    let val = 0
    if (e.target.checked) {
      val = 1
    }
    this.rec.setPasswordAsUserName = val;
  }
  setResetPassword(e) {
    let val = 0
    if (e.target.checked) {
      val = 1
    }
    this.rec.resetPassword = val;
  }
  changeResetPwdValue(e) {
    if (e.value == "same") {
      this.rec.setPasswordAsUserName = true;
    } else {
      this.rec.setPasswordAsUserName = false;
    }
  }

  

}
