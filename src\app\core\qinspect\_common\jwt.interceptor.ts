import { <PERSON><PERSON>p<PERSON><PERSON>, <PERSON><PERSON>p<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { AuthService } from '../../../services/auth.service';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
    constructor(private _authService: AuthService) { }

    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        // add authorization header with jwt token if available
        let currentUser = this._authService.getLoggedInUser();
        const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
        if (currentUser && currentUser.token) {
            request = request.clone({
                setHeaders: {
                    Authorization: `${currentUser.token}`,
                    'Client': `${tenantUid}`
                }
            });
        }

        return next.handle(request);
    }
}