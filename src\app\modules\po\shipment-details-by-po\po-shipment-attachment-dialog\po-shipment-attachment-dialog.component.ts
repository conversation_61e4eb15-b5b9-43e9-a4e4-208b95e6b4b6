import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { shipmentDocumentList } from 'app/core/qinspect/_model/ShipmentDocumentList.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import { ImageViewerDialogComponent } from 'app/shared/image-viewer-dialog/image-viewer-dialog.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { ImgIconComponent } from 'app/shared/tfl-table-search-grid/grid-supporting-components/img-icon/img-icon.component';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { FileSaverService } from 'ngx-filesaver';

@Component({
  selector: 'app-po-shipment-attachment-dialog',
  templateUrl: './po-shipment-attachment-dialog.component.html',
  styleUrls: ['./po-shipment-attachment-dialog.component.scss']
})
export class PoShipmentAttachmentDialogComponent implements OnInit {
  hdr: string = 'Attachment';
  files: any[] = [];
  selectedDocType: string = '';
  documentsList: any[] = [];
  disableSubmitButton: boolean = false;
  basicAuth = this.globals.basicAuth;
  disablePackingListOption: boolean = false;
  disableInvoiceOption: boolean = false;
  disableLrDetailsOption: boolean = false;
  isAllDocTypeUploaded: boolean = false;
  allFiles: NgxFileDropEntry[] = [];


  @Input() isShowAddViewAttachments: boolean = false;
  @Input() isShowOnlyViewAttachments: boolean = false;
  @Input() isDocumentShow: boolean = true;
  @Input() set uploadedFiles(files) {
    this.files = files;
    if (this.files.length > 0) {
      this.setFiles();
    }
  }
  @Output() onUploadFiles: EventEmitter<any> = new EventEmitter();

  attachmentsColumnDefs: any = [
    {
      headerName: "Type", field: "fileType", maxWidth: 70,
      cellRendererFramework: ImgIconComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data
    },
    { headerName: "Attachment Type", field: "docType", sortable: true, minWidth: 150 },
    { headerName: "Name", field: "name", sortable: true, minWidth: 200 },
    { headerName: "Attachment Type", field: "docType", sortable: true, minWidth: 200, hide: this.isDocumentShow },
    {
      headerName: "Created On", field: "createdTime", sortable: true,
      cellRendererFramework: CustomDateTooltipComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data
    },
    // {
    //   headerName: "File Size", field: "fileSize", sortable: true, valueGetter: params => {
    //     return this._commonService.calculateFileSize(params.data.fileSize)
    //   }
    // },
    {
      headerName: 'Actions',
      minWidth: 150, maxWidth: 200,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: params => {
        return { actions: this.prepareActionIconButtons(params.data), }
      }
    }
  ]

  constructor(public dialogRef: MatDialogRef<PoShipmentAttachmentDialogComponent>,
    private _invokeService: InvokeService,
    public _commonService: CommonService,
    public http: HttpClient,
    public fileSaverService: FileSaverService,
    public globals: Globals,
    private sanitizer: DomSanitizer,
    public dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,) { }

  ngOnInit(): void {
    if (this.isShowOnlyViewAttachments) {
      this.hdr = 'Shipment Attachment List';
    }
    this.getShipmentDocuments();
  }

  public prepareActionIconButtons(row: any): any {
    let icons: any[] = [];
    icons.push({ type: 'view', icon: 'view', title: 'View', data: row });
    return icons;
  }

  public onTypeClick(type: string): void {
    this.selectedDocType = type;
  }

  public onAction(event): void {
    switch (event.status) {
      case 'view':
        this.downloadShipmentDoc(event.data);
        break;
      default:
        break;
    }
  }

  public viewShipmentImage(imageData: Blob, docType: string) {
    if (imageData) {
      const urlCreator = window.URL;
      const image = {};
      image['name'] = docType + ' IMAGE';
      image['pic'] = this.sanitizer.bypassSecurityTrustUrl(
        urlCreator.createObjectURL(imageData));
      const dialogRef = this.dialog.open(ImageViewerDialogComponent, {
        data: [image],
      });
    }
  }

  public downloadShipmentDoc(data: any): void {
    let headers = new HttpHeaders()
      .set("Authorization",this._commonService.getBasicAuth()).set('Client', this._commonService.getTenantUid());;
    this.http.get(environment.apiUrl + 'sourcing/po/shipment/' + data.docUid + '/doc', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      if (data.fileType && this.isImage(data.fileType)) {
        this.viewShipmentImage(res.body, data.docType);
      } else
        this.fileSaverService.save((<any>res).body, data.docType + data.fileType);
    });
  }

  private getShipmentDocuments(): void {
    APP_UI_CONFIG.finishGoodsDesignCode.getPoShipmentDocList.paramList.po = this.dialogData?.data;
    this._invokeService.serviceInvocation(APP_UI_CONFIG.finishGoodsDesignCode.getPoShipmentDocList).subscribe({
      next: res => {
        this.documentsList = res;
        if (res) {
          this.getUploadedDocTypeForShipment(res);
        }
      }, error: error => {
        this._commonService.handleError(error);
      }
    })
  }

  public getUploadedDocTypeForShipment(shipmentData: shipmentDocumentList[]): void {
    this.disablePackingListOption = false;
    this.disableInvoiceOption = false;
    this.disableLrDetailsOption = false;
    this.isAllDocTypeUploaded = false;

    shipmentData?.forEach((item) => {
      switch (item?.docType) {
        case 'PACKINGLIST':
          this.disablePackingListOption = true;
          break;
        case 'INVOICE':
          this.disableInvoiceOption = true;
          break;
        case 'LR DETAILS':
          this.disableLrDetailsOption = true;
          break;
        default:
          break;
      }
    });

    if (this.disableInvoiceOption && this.disablePackingListOption && this.disableLrDetailsOption) {
      this.isShowOnlyViewAttachments = true;
      this.isAllDocTypeUploaded = true;
      this.hdr = 'Shipment Attachment List';
    }
  }

  public onRefresh(): void {
    this.getShipmentDocuments();
  }

  public onFileChanged(event): void {
    if (this.selectedDocType) {
      let findIndex = this.files.findIndex(item => item.docType == this.selectedDocType);
      if (findIndex > -1) {
        let file = event;
        file[0]['docType'] = this.selectedDocType;
        this.files[findIndex] = file[0];
        this.setFiles();
      } else {
        let file = event;
        file[0]['docType'] = this.selectedDocType;

        this.files = this.files.concat(file);
        this.setFiles();
      }
    } else {
      this._commonService.error("Please select category type to upload")
    }
    this.onUploadFiles.emit(this.files);
  }

  private setFiles(): void {
    this.allFiles = this.files;
  }

  public deleteFile(i): void {
    this.files.splice(i, 1)
  }

  public checkExistedFile(categoryType): boolean {
    return this.files.some(item => item.docType == categoryType);
  }

  public onSubmit(): void {
    const formData = new FormData()
    this.files.forEach((droppedFile, index) => {
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          formData.append('file', file, droppedFile.relativePath);
          formData.append('docType', droppedFile['docType']);
        });
        APP_UI_CONFIG.finishGoodsDesignCode.addDocForPoShipment.paramList.shipmentNumber = this.dialogData?.data;
        this._invokeService.uploadAttachments(APP_UI_CONFIG.finishGoodsDesignCode.addDocForPoShipment, formData).subscribe({
          next: res => {
            if (res && res.code == 200) {
              this.selectedDocType = '';
              this.files = [];
              this.allFiles = [];
              this.documentsList = [];
              this._commonService.success("File saved successfully");
              this.getShipmentDocuments();
            } else {
              this._commonService.handleError('');
            }
          },
          error: error => {
            this._commonService.handleError(error);
          }
        })
      } else {
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
      }
    });
  }

  public switchHeaders(event: any): void {
    if (event.index == 0) {
      this.disableSubmitButton = false;
    } else if (event.index == 1) {
      this.disableSubmitButton = true;
      this.getShipmentDocuments();
    }
  }

  public isImage(name: string): boolean {
    let isImage: boolean = false;
    let extension = name.toLowerCase();
    switch (extension) {
      case '.png':
        isImage = true;
        break;
      case '.jpg':
        isImage = true;
        break;
      case '.jpeg':
        isImage = true;
        break;
      case '.svg':
        isImage = true;
        break;
      case '.webp':
        isImage = true;
        break;
      default:
        isImage = false;
        break;
    }
    return isImage;
  }


}

