<div class="main-grids-page-wrapper" *ngIf="!showDynamicForm">
    <div *ngIf="!fromCalendar" class="edit-inspection-wrapper-header">
        <tfl-page-header [headerName]="hdr" [showBackIcon]="true" (close)="close()"></tfl-page-header>
    </div>
    <p-divider class="mt-1"></p-divider>
    <div *ngIf="tentUid=='demo' || tentUid=='norlanka' " class="mr-3 toogle-des">
        <mat-slide-toggle color="primary" [(ngModel)]="isByShipment" (change)="onClearSelectedItem()">
            Create Schedule by Shipment
        </mat-slide-toggle>
    </div>

    <form #fshp="ngForm" *ngIf="isByShipment" class="padding-card" novalidate>
        <div>
            <div *ngIf="rec" class="tfl-flex tfl-flex-column">
                <div class="row form-row">
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="auditDate">Audit Date</label><span class="requiredField">*</span>
                            </div>
                            <div class="col-md-8">
                                <p-calendar [(ngModel)]="auditDt" [showTime]="true" [showSeconds]="true"
                                    [showIcon]="editRec ? false : true" name="auditDt" hourFormat="12"
                                    [minDate]="schdMinDt" [readonlyInput]="true" placeholder="Choose a date"
                                    required></p-calendar>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1"></div>
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="shipmentNo">Shipment No.<span class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-6">
                                <input pInputText id="shipmentNo1" name="shipmentNo1" [(ngModel)]="rec.shipmentNo"
                                    placeholder="Shipment Number" required />
                            </div>
                            <div class="col-md-2">
                                <button type="button" (click)="getByShipment()" class="go-button">Get
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="shipmentDto.code=='200'" class="row form-row">
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="auditTy">Audit Type<span class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="shipmentDto.auditTypes" [(ngModel)]="shipmentSelectAuditType"
                                    placeholder="Select Audit Type" [filter]="true" filterBy="name" optionValue="name"
                                    optionLabel="name" [showClear]="true" name="auditTyShp" [resetFilterOnHide]="true"
                                    (onChange)="onAuditTypeSelect($event,true)" required>
                                </p-dropdown>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1"></div>
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="actFactShp">Actual Factory<span class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="filteredFactoryList"
                                    [(ngModel)]="common.actualFactoryList.selectedItem"
                                    placeholder="Select a {{configParam?.actualFactoryLabel || 'Actual Factory'}}"
                                    name="actFactShp" (onChange)="onActualFactorySelect()" required [filter]="true"
                                    filterBy="label">
                                </p-dropdown>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="shipmentDto.code=='200'" class="row form-row">
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="auditorShp">{{configParam?.auditorLabel ||'Auditor'}}<span
                                        *ngIf="configParam?.auditorMandatory==1" class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="common.auditorList.list"
                                    [(ngModel)]="common.auditorList.selectedItem" placeholder="Select an Auditor"
                                    name="auditorShp" required [filter]="true" filterBy="label"
                                    [disabled]="user.role == 'AUDITOR'"></p-dropdown>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="shipmentDto.code=='200'" class="row form-row">
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="poNumshp">PO Number(s)</label>
                            </div>
                            <div class="col-md-8">
                                <input pInputText class="p-inputtext p-component" disabled name="poNumshp"
                                    [(ngModel)]="rec.purchaseOrderNo" />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-1"></div>
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="factoryshp">Factory</label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="filteredFactoryList"
                                    [(ngModel)]="common.factoryList.selectedItem" placeholder="Select" name="factoryshp"
                                    [filter]="true" filterBy="label" [disabled]="true">
                                </p-dropdown>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="shipmentDto.code=='200'" class="row form-row">
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="suppShp">Supplier</label>
                            </div>
                            <div class="col-md-8">
                                <input pInputText class="p-inputtext p-component" disabled name="suppShp"
                                    [(ngModel)]="shipmentDto.factory.name" />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-1"></div>

                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="poCatShp">Product Category</label>
                            </div>
                            <div class="col-md-8">
                                <input pInputText class="p-inputtext p-component" disabled name="poCatShp"
                                    [(ngModel)]="shipmentDto.productCategory.category" />
                            </div>
                        </div>
                    </div>

                </div>

                <div *ngIf="shipmentDto.code=='200'" class="row form-row">
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="offerQtyShp">Offered Quantity</label>
                            </div>
                            <div class="col-md-8">
                                <input pInputText class="p-inputtext p-component"
                                    [(ngModel)]="rec.designcodes[0].offerQty" name="offerQtyShp" disabled />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1"></div>
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="styleCodeShp">#Style </label>

                            </div>
                            <div class="col-md-8">
                                <input pInputText class="p-inputtext p-component"
                                    [(ngModel)]="rec.designcodes[0].designcode" name="styleCodeShp"
                                    (input)="upperCaseStyleCode($event)" disabled />
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="shipmentDto.code=='200'" class="row form-row">
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="buyerPoShp">Buyer PO</label>
                            </div>
                            <div class="col-md-8">
                                <input pInputText class="p-inputtext p-component" [(ngModel)]="rec.buyerPo"
                                    name="buyerPoShp" disabled />
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <p-divider></p-divider>
            <div
                class="tfl-flex tfl-flex-row tfl-flex-align-center tfl-flex-justify-flex-end form-fotter-button-wrapper">

                <tfl-button-cancel [buttonName]="'Cancel'" (onClick)="close()"></tfl-button-cancel>
                <tfl-button [buttonName]="'Submit'" [isDisabled]="!fshp.form.valid || shipmentDto.code!='200'"
                    (submit)="onShipmentSave()"></tfl-button>

            </div>
        </div>
    </form>

    <form #f="ngForm" *ngIf="!isByShipment" class="padding-card" novalidate>

        <div>
            <div *ngIf="rec" class="tfl-flex tfl-flex-column">

                <div class="row form-row">
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="auditDate">Audit Date</label><span class="requiredField">*</span>
                            </div>
                            <div class="col-md-8">
                                <p-calendar [(ngModel)]="auditDt" [showTime]="true" [showSeconds]="true"
                                    [showIcon]="editRec ? false : true" name="auditDt" hourFormat="12"
                                    [minDate]="schdMinDt" [readonlyInput]="true" placeholder="Choose a date"
                                    required></p-calendar>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1">

                    </div>
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="auditTy">Audit Type<span class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="common.auditTypeList.list"
                                    [(ngModel)]="common.auditTypeList.selectedItem" placeholder="Select Audit Type"
                                    [filter]="true" filterBy="label,value" value="value" [showClear]="true"
                                    name="auditType" [resetFilterOnHide]="true"
                                    (onChange)="onAuditTypeSelect($event,true)" required>
                                </p-dropdown>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="rec.auditType != null && configParam?.poNumberVisible == 1" class="row form-row">
                    <div class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div *ngIf="purchaseOrderNoFetchEnable==1" class="col-md-4">
                                <label for="poNum">{{configParam?.poNumberLabel}}
                                    <span *ngIf="configParam?.poNumberMandatory==1"
                                        class="requiredField">*</span></label>
                            </div>
                            <div *ngIf="purchaseOrderNoFetchEnable==0" class="col-md-4">
                                <label for="poNum">PO Number(s)
                                    <span *ngIf="configParam?.poNumberMandatory==1"
                                        class="requiredField">*</span></label>
                            </div>
                            <div *ngIf="purchaseOrderNoFetchEnable==1" class="col-md-6">
                                <input pInputText class="p-inputtext p-component"
                                    [disabled]="configParam?.poNumberEditable==0" name="poNum"
                                    [(ngModel)]="rec.purchaseOrderNo" [placeholder]="configParam?.poNumberLabel" />
                            </div>
                            <div *ngIf="purchaseOrderNoFetchEnable==1" class="col-md-2">
                                <button type="button" (click)="getByPO()" class="go-button">Get
                                </button>
                            </div>
                            <div *ngIf="purchaseOrderNoFetchEnable==0" class="col-md-8">

                                <input pInputText class="p-inputtext p-component"
                                    [[disabled]="configParam?.poNumberEditable==0" name="poNum"
                                    [(ngModel)]="rec.purchaseOrderNo" />
                            </div>
                        </div>
                    </div>

                </div>

                <div *ngIf="rec.auditType != null && (tenantConfig?.enableAgencySupport>=1 || configParam?.auditorVisible == 1)"
                    class="row form-row">
                    <div *ngIf="tenantConfig?.enableAgencySupport>=1" class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="ia">Inspection Agency<span class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="common.agencyList.list"
                                    [(ngModel)]="common.agencyList.selectedItem" [showClear]="true"
                                    placeholder="Select an Inspection Agency" name="agency" [required]="true"
                                    [filter]="true" filterBy="label">
                                </p-dropdown>
                            </div>
                        </div>
                    </div>

                    <div *ngIf="tenantConfig?.enableAgencySupport>=1" class="col-md-1">

                    </div>

                    <div *ngIf="configParam?.auditorVisible == 1" class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="auditor">{{configParam?.auditorLabel ||'Auditor'}}<span
                                        *ngIf="configParam?.auditorMandatory==1" class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="common.auditorList.list"
                                    [(ngModel)]="common.auditorList.selectedItem" placeholder="Select an Auditor"
                                    name="auditor" [required]="configParam?.auditorMandatory==1" [filter]="true"
                                    filterBy="label" [showClear]="true"
                                    [disabled]="user.role == 'AUDITOR' || configParam?.auditorEditable!=1 "></p-dropdown>
                            </div>
                        </div>
                    </div>

                </div>

                <div *ngIf="rec.auditType != null && configParam?.supportingAuditorsVisible==1" class="row form-row">
                    <div *ngIf="configParam?.supportingAuditorsVisible==1" class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="supportingAuditors">{{configParam?.supportingAuditorsLabel}}<span
                                        *ngIf="configParam?.supportingAuditorsMandatory==1"
                                        class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-multiSelect [options]="common.supportingAuditorList.list"
                                    [(ngModel)]="SupportingAuditors" optionLabel="label" [selectionLimit]="3"
                                    name="audLabel" optionLabel="label" display="chip"
                                    placeholder="{{configParam?.supportingAuditorsLabel}}" optionValue="value"
                                    [style]="{'width':'100%'}" [panelStyle]="{'width':'100%'}"></p-multiSelect>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="rec.auditType != null && (configParam?.vendorVisible==1 || configParam?.factoryVisible==1)"
                    class="row form-row">
                    <div *ngIf="configParam?.vendorVisible==1" class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="vendorId">{{ configParam?.vendorLabel || 'Vendor'}}<span
                                        *ngIf="configParam?.vendorMandatory==1" class="requiredField">*</span></label>
                            </div>
                            <div *ngIf="configParam?.vendorEditable==1" class="col-md-8">
                                <p-dropdown [options]="common.vendorList.list"
                                    [disabled]="configParam?.vendorEditable!=1"
                                    [(ngModel)]="common.vendorList.selectedItem"
                                    placeholder="Select {{ configParam?.vendorLabel || 'Vendor'}}" name="vendor"
                                    [required]="configParam?.vendorMandatory==1" (onChange)="onVendorSelect()"
                                    [filter]="true" filterBy="label">
                                </p-dropdown>
                            </div>
                            <div *ngIf="configParam?.vendorEditable!=1" class="col-md-8">
                                <input pInputText class="p-inputtext p-component" [(ngModel)]="selectedVendor"
                                    name="vendorr" disabled />
                            </div>
                        </div>
                    </div>

                    <div *ngIf="configParam?.vendorVisible==1" class="col-md-1">

                    </div>

                    <div *ngIf="configParam?.factoryVisible==1" class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="factory">{{ configParam?.factoryLabel || 'Factory'}}<span
                                        *ngIf="configParam?.factoryMandatory==1 " class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown
                                    *ngIf="tentUid != 'lots' && (configParam?.vendorVisible==1 || configParam?.vendorMandatory==1)"
                                    [options]="filteredFactoryList" [(ngModel)]="common.factoryList.selectedItem"
                                    placeholder="Select a {{ configParam?.factoryLabel || 'Factory'}}" name="factory"
                                    (onChange)="onFactorySelect()" [filter]="true" filterBy="label"
                                    [required]="configParam?.factoryMandatory==1 "
                                    [disabled]="configParam?.factoryEditable!=1 || !common.vendorList?.selectedItem">
                                </p-dropdown>
                                <p-dropdown *ngIf="tentUid == 'lots' && configParam?.factoryVisible==1"
                                    [options]="common.factoryList.list" [(ngModel)]="common.factoryList.selectedItem"
                                    placeholder="Select a {{ configParam?.factoryLabel || 'Factory'}}" name="factory"
                                    [required]="configParam?.factoryMandatory==1 " (onChange)="onLotsFactorySelect()"
                                    [filter]="true" filterBy="label" [disabled]="configParam?.factoryEditable!=1">
                                </p-dropdown>
                            </div>
                        </div>
                    </div>

                </div>

                <div *ngIf="rec.auditType != null && (configParam?.actualFactoryVisible==1 || configParam?.productTypeVisible==1)"
                    class="row form-row">
                    <div *ngIf="configParam?.actualFactoryVisible==1" class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="vendorId">{{configParam?.actualFactoryLabel || 'Actual Factory'}}<span
                                        *ngIf="configParam?.actualFactoryMandatory==1"
                                        class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown *ngIf="tentUid != 'norlanka'" [options]="filteredFactoryList"
                                    [(ngModel)]="common.actualFactoryList.selectedItem"
                                    placeholder="Select a {{configParam?.actualFactoryLabel || 'Actual Factory'}}"
                                    name="actfactory" (onChange)="onActualFactorySelect()" required [filter]="true"
                                    filterBy="label"
                                    [disabled]="configParam?.actualFactoryEditable!=1 ||common.factoryList.selectedItem">
                                </p-dropdown>
                                <p-dropdown *ngIf="tentUid =='norlanka'" [options]="filteredFactoryList"
                                    [(ngModel)]="common.actualFactoryList.selectedItem"
                                    placeholder="Select a {{configParam?.actualFactoryLabel || 'Actual Factory'}}"
                                    name="actfactory" (onChange)="onActualFactorySelect()" required [filter]="true"
                                    filterBy="label" [disabled]="configParam?.actualFactoryEditable!=1">
                                </p-dropdown>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="configParam?.actualFactoryVisible==1" class="col-md-1"></div>
                    <div *ngIf="configParam?.productTypeVisible==1" class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="prdTy">{{ configParam?.productTypeLabel ||'Product Type'}} <span
                                        *ngIf="configParam?.productTypeMandatory==1"
                                        class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="common.productTypeList.list"
                                    [disabled]="configParam?.productTypeEditable!=1"
                                    [(ngModel)]="common.productTypeList.selectedItem"
                                    placeholder="Select {{ configParam?.productTypeLabel ||'Product Type'}}"
                                    name="producttype" [filter]="true" filterBy="label,value"
                                    [required]="configParam?.productTypeMandatory==1"></p-dropdown>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="rec.auditType != null && (configParam?.categoryVisible==1 || configParam?.customerVisible==1 )"
                    class="row form-row">
                    <div *ngIf="configParam?.categoryVisible==1" class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="prdCat">{{configParam?.categoryLabel ||'Product Category'}}<span
                                        *ngIf="configParam?.categoryMandatory==1" class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="common.categoryList.list"
                                    [disabled]="configParam?.categoryEditable!=1"
                                    [(ngModel)]="common.categoryList.selectedItem"
                                    placeholder="Select {{configParam?.categoryLabel ||'Product Category'}}"
                                    name="category" [filter]="true" filterBy="label,value"></p-dropdown>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="configParam?.categoryVisible==1" class="col-md-1">

                    </div>
                    <div class="col-md-5">
                        <div *ngIf="configParam?.customerVisible==1" class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="customer">{{ configParam?.customerLabel || 'Customer'}}<span
                                        *ngIf="configParam?.customerMandatory==1" class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-dropdown [options]="common.customerList.list"
                                    [disabled]="configParam?.customerEditable!=1"
                                    [(ngModel)]="common.customerList.selectedItem"
                                    placeholder="Select {{ configParam?.customerLabel || 'Customer'}}" name="customer"
                                    [filter]="true" filterBy="label,value"></p-dropdown>
                            </div>
                        </div>
                    </div>
                </div>


                <div *ngIf="configParam?.orderQtyVisible==1 || configParam?.designcodeVisible==1 && (rec.auditType.multiDesignCode==undefined || rec.auditType.multiDesignCode!=1)  "
                    class="row form-row">
                    <div *ngIf="configParam?.orderQtyVisible==1 " class="col-md-5">
                        <div class="row  tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="ordQty">{{ configParam?.orderQtyLabel ||'Ordered Quantity'}}
                                    <span *ngIf="configParam?.orderQtyMandatory==1"
                                        class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-inputNumber [(ngModel)]="rec.designcodes[0].orderQty" name="orderQty"
                                    [disabled]="configParam?.orderQtyEditable!=1 "
                                    [placeholder]="configParam?.orderQtyLabel ||'Ordered Quantity'"
                                    [useGrouping]="false"></p-inputNumber>
                            </div>
                        </div>
                    </div>

                    <div *ngIf="configParam?.orderQtyVisible==1 " class="col-md-1"></div>

                    <div *ngIf="configParam?.designcodeVisible==1 && (rec.auditType.multiDesignCode==undefined || rec.auditType.multiDesignCode!=1) "
                        class="col-md-5">
                        <div class="row  tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="stylecode">{{ configParam?.designcodeLabel || 'Style Code(s)'}}
                                    <span *ngIf="configParam?.designcodeMandatory==1" class="requiredField">*</span>
                                </label>

                            </div>
                            <div class="col-md-8">
                                <input pInputText class="p-inputtext p-component"
                                    [(ngModel)]="rec.designcodes[0].designcode" name="styleCode"
                                    (input)="upperCaseStyleCode($event)"
                                    [disabled]="configParam?.designcodeEditable!=1 "
                                    [placeholder]="configParam?.designcodeLabel || 'Style Code(s)'" />
                            </div>
                        </div>

                    </div>
                </div>

                <div class="row form-row">
                    <div *ngIf="configParam?.offerQtyVisible==1 " class="col-md-5">
                        <div class="row tfl-flex-align-center">
                            <div class="col-md-4">
                                <label for="ordQty">{{ configParam?.offerQtyLabel ||'Offered Quantity'}}
                                    <span *ngIf="configParam?.offerQtyMandatory==1"
                                        class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <p-inputNumber [(ngModel)]="rec.designcodes[0].offerQty" name="offerQty"
                                    [disabled]="configParam?.offerQtyEditable!=1" [useGrouping]="false">
                                </p-inputNumber>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="configParam?.offerQtyVisible==1 " class="col-md-1"></div>
                    <div *ngIf="rec.auditType != null" class="col-md-5">
                        <div *ngIf="configParam?.descriptionVisible==1" class="row">
                            <div class="col-md-4">
                                <label class="mt-4" for="desc">{{ configParam?.descriptionLabel
                                    ||'Description'}}<span *ngIf="configParam?.descriptionMandatory==1"
                                        class="requiredField">*</span></label>
                            </div>
                            <div class="col-md-8">
                                <textarea id="float-input" rows="3" cols="30" pInputTextarea
                                    [(ngModel)]="auditDescription" name="descr"
                                    [disabled]="configParam?.descriptionEditable!=1"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="configParam?.designcodeVisible==1 && rec.auditType.multiDesignCode && rec.auditType.multiDesignCode==1"
                    class="row">

                    <div class="w-full tfl-flex tfl-flex-column mb-4">
                        <div class="form-sub-title mb-2 mt-2">ASIN Information</div>
                        <div class="p-4 tfl-flex tfl-flex-column">
                            <div class="row mt-3">
                                <div class="col-md-2">
                                    <div class="flex flex-column gap-2">
                                        <label for="asinNumber">ASIN Code<span class="requiredField">*</span></label>
                                        <input pInputText id="asinCode" name="asinNumber" [(ngModel)]="asinCodeName"
                                            placeholder="ASIN" />
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="flex flex-column gap-2">
                                        <label for="asinNumber">Ordered Quantity<span
                                                class="requiredField">*</span></label>
                                        <p-inputNumber [(ngModel)]="asinCodeOdrQty" name="orderQty"
                                            [placeholder]="'Ordered Quantity'" [useGrouping]="false"></p-inputNumber>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="flex flex-column gap-2">
                                        <label for="asinNumber">Offered Quantity<span
                                                class="requiredField">*</span></label>
                                        <p-inputNumber [(ngModel)]="asinCodeOfferQty" name="orderQty"
                                            [placeholder]="'Ordered Quantity'" [useGrouping]="false"></p-inputNumber>
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <!-- <button type="button" class="form-submit-button ml-4 mt-4"
                                    [disabled]="asinCodeOfferQty==undefined || asinCodeOdrQty==undefined || asinCodeName==''"
                                    (click)="getAsinCode()">
                                    Add
                                </button> -->
                                    <tfl-button class="ml-4 mt-4" [buttonName]="'Add'" [icon]="'add'"
                                        [isDisabled]="asinCodeOfferQty==undefined || asinCodeOdrQty==undefined || asinCodeName==''"
                                        (submit)="getAsinCode()"></tfl-button>
                                </div>
                            </div>
                            <p-divider class="my-2"></p-divider>
                            <div class="row">
                                <div class="col-md-12 mt-3">
                                    <tfl-table-search-grid #gridDesigncode style="width: 100%;height: 100%;"
                                        [height]="300" [isShowHeader]="false" [rowData]="asinCodeList" [gridRef]="this"
                                        [tableColumnsDef]="columnDefs" (emitActionEvent)="getActions($event)">
                                    </tfl-table-search-grid>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>


            </div>
            <p-divider></p-divider>

            <div
                class="tfl-flex tfl-flex-row tfl-flex-align-center tfl-flex-justify-flex-end form-fotter-button-wrapper">

                <tfl-button-cancel [buttonName]="'Cancel'" (onClick)="close()"></tfl-button-cancel>
                <tfl-button [isDisabled]="!f.form.valid" [buttonName]="'Submit'" (submit)="onSave()"></tfl-button>

            </div>

        </div>

    </form>
</div>
<div class="main-grids-page-wrapper flex flex-col w-full" *ngIf="showDynamicForm && tentUid !=Partners.MADURA">
    <div class="w-full">
        <tfl-page-header [headerName]="hdr" [showBackIcon]="true" (close)="close()"></tfl-page-header>
        <p-divider></p-divider>
    </div>
    <div>
        <form [formGroup]="scheduleForm" autocomplete="off">
            <div class="form-card-padding">
                <div class="required_fields_message mb-4 w-full">
                    IMPORTANT: * Indicates Mandatory Fields.
                </div>
                <div class="dynamic-form-wrapper w-full min-w-0 form-row tfl-flex-justify-space-between ">
                    <ng-container *ngFor="let field of fields" [ngSwitch]="field.type">
                        <div *ngIf="field.show && field?.topGroupTitle" class="w-full dynamic-form-sub-title my-4">
                            {{field.topGroupTitleLabel}}
                        </div>
                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-text class="col-md-5 col-sm-12 col-12" *ngSwitchCase="FORM_CONTROL_TYPES.TEXT"
                                [field]="field" [formName]="scheduleForm"></tfl-dynamic-text>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-number class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.NUMBER" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-number>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-single-select class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.SINGLE_SELECT" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-single-select>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-multi-select class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.MULTI_SELECT" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-multi-select>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-checkbox class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.CHECKBOX" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-checkbox>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-date-picker class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.DATE" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-date-picker>
                        </ng-container>
                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-radio class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.RADIO" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-radio>
                        </ng-container>
                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-text-area *ngSwitchCase="FORM_CONTROL_TYPES.TEXTAREA"
                                class="col-md-5 col-sm-12 col-12" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-text-area>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-text-search *ngSwitchCase="FORM_CONTROL_TYPES.TEXT_SEARCH"
                                class="col-md-5 col-sm-12 col-12" [field]="field" (onClick)="onSeachPo()"
                                [formName]="scheduleForm"></tfl-dynamic-text-search>
                        </ng-container>
                        <ng-container *ngIf="field.rightSideGap">
                            <div class="col-md-5 col-sm-12 col-12"></div>
                        </ng-container>
                    </ng-container>
                </div>
                <div class="w-full flex flex-col gap-2">
                    <div class="tfl-flex tfl-flex-row tfl-flex-justify-space-between tfl-flex-align-center">
                        <div class=" w-full dynamic-form-sub-title">
                            Selected Design Codes
                        </div>
                        <div class="w-full required_fields_message" *ngIf="alreadyInspectionCreatedMessage">
                            <tfl-button [buttonName]="'View Previous Inspections'"
                                (submit)="alreadyInspectionCreated=true"></tfl-button>
                        </div>
                    </div>

                    <tfl-table-search-grid class="w-full" [isShowHeader]="false" [showGlobalSearchFilter]="true"
                        [showFilter]="false" [showRefresh]="true" [height]="'30vh'" [showImport]="false"
                        [rowData]="selectedDesigncodes" [showExport]="false" [showAdd]="false"
                        [tableColumnsDef]="selectedDesignCodeColumnsDef"
                        [fromDate]="defaultfromDate"></tfl-table-search-grid>
                </div>
            </div>
        </form>
    </div>
    <p-divider class="my-2"></p-divider>
    <div class="form-action-wrapper">
        <tfl-button-cancel [buttonName]="'Cancel'" (onClick)="close()"></tfl-button-cancel>
        <tfl-button *ngIf="showSubmitButton" [isDisabled]="scheduleForm.invalid" [buttonName]="'Submit'"
            (submit)="onCreateSchedule()"></tfl-button>
    </div>
</div>
<div class="main-grids-page-wrapper flex flex-col w-full" *ngIf="showDynamicForm && tentUid==Partners.MADURA">
    <div class="w-full">
        <tfl-page-header [headerName]="hdr" [showBackIcon]="true" (close)="close()"></tfl-page-header>
        <p-divider></p-divider>
    </div>
    <div>
        <form [formGroup]="scheduleForm" autocomplete="off">
            <div class="form-card-padding">
                <div class="required_fields_message mb-4 w-full">
                    IMPORTANT: * Indicates Mandatory Fields.
                </div>
                <div class="dynamic-form-wrapper w-full min-w-0 form-row tfl-flex-justify-space-between ">
                    <ng-container *ngFor="let field of fields" [ngSwitch]="field.type">
                        <div *ngIf="field.show && field?.topGroupTitle" class="w-full dynamic-form-sub-title my-4">
                            {{field.topGroupTitleLabel}}
                        </div>
                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-text class="col-md-5 col-sm-12 col-12" *ngSwitchCase="FORM_CONTROL_TYPES.TEXT"
                                [field]="field" [formName]="scheduleForm"></tfl-dynamic-text>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-number class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.NUMBER" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-number>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-single-select class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.SINGLE_SELECT" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-single-select>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-multi-select class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.MULTI_SELECT" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-multi-select>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-checkbox class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.CHECKBOX" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-checkbox>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-date-picker class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.DATE" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-date-picker>
                        </ng-container>
                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-radio class="col-md-5 col-sm-12 col-12"
                                *ngSwitchCase="FORM_CONTROL_TYPES.RADIO" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-radio>
                        </ng-container>
                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-text-area *ngSwitchCase="FORM_CONTROL_TYPES.TEXTAREA"
                                class="col-md-5 col-sm-12 col-12" [field]="field"
                                [formName]="scheduleForm"></tfl-dynamic-text-area>
                        </ng-container>

                        <ng-container *ngIf="field.show">
                            <tfl-dynamic-text-search *ngSwitchCase="FORM_CONTROL_TYPES.TEXT_SEARCH"
                                class="col-md-5 col-sm-12 col-12" [field]="field" (onClick)="onSeachPo()"
                                [formName]="scheduleForm"></tfl-dynamic-text-search>
                        </ng-container>
                        <ng-container *ngIf="field.rightSideGap">
                            <div class="col-md-5 col-sm-12 col-12"></div>
                        </ng-container>
                    </ng-container>
                </div>
            </div>
        </form>
    </div>
    <p-divider class="my-2"></p-divider>
    <div class="form-action-wrapper">
        <tfl-button-cancel [buttonName]="'Cancel'" (onClick)="close()"></tfl-button-cancel>
        <tfl-button *ngIf="showSubmitButton" [isDisabled]="scheduleForm.invalid" [buttonName]="'Submit'"
            (submit)="onCreateSchedule()"></tfl-button>
    </div>
</div>


<ng-container *ngIf="alreadyInspectionCreated">
    <p-dialog [header]="'Previous Inspections'" [(visible)]="alreadyInspectionCreated" [style]="{width: '80%'}"
        [modal]="true" appendTo="body" [autoZIndex]="true">
        <div class="w-full flex flex-col gap-1 notificationListItemWrapper">
            <div class="card inspectionTableWrapper">
                <p-table [columns]="columns" [value]="inspectionTableData" [tableStyle]="{ 'min-width': '30rem' }"
                    [scrollable]="true" scrollHeight="600px" showGridlines styleClass="adjustable-table">
                    <ng-template pTemplate="header" let-columns>
                        <tr>
                            <th *ngFor="let col of columns;let i=index" [ngStyle]="{'width': i==6 ? '40%':'10%'}">
                                {{ col.headerName }}
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-rowData let-columns="columns">
                        <tr>
                            <td *ngFor="let col of columns;let i=index" class="pl-8"
                                [ngStyle]="{'width': i==6 ? '40%':'10%'}">
                                {{ rowData[col.field] }}
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
        </div>
    </p-dialog>
</ng-container>