import { HttpClient, HttpClientModule } from '@angular/common/http';
import { HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InspectionTemplate } from 'app/core/qinspect/_model/InspectionTemplate.model';
import { Status } from 'app/core/qinspect/_model/status.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { CommonService } from 'app/services/common.service';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import moment from 'moment';


@Component({
  selector: 'app-create-edit-import-inspection-template',
  templateUrl: './create-edit-import-inspection-template.component.html',
  styleUrls: ['./create-edit-import-inspection-template.component.scss']
})
export class CreateEditImportInspectionTemplateComponent implements OnInit {

  public files: NgxFileDropEntry[] = [];
  status: Status = new Status();
  basicAuth = this.globals.basicAuth;
  rec: InspectionTemplate;
  hdr:string='Import Inspection Template';
  constructor(
    public dialogRef: MatDialogRef<CreateEditImportInspectionTemplateComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private http: HttpClient,
    private globals: Globals,
    private _commonService: CommonService,
  ) { }

  ngOnInit(): void {
    this.rec = new InspectionTemplate();
    if(this.data.status=='view'){
        this.hdr='View Inspection Template';
        this.rec= this.data.data;
        this.rec['deployedStatus']= this.data.data.deployed==0?'Not Deployed':'Deployed';
        this.rec.createdTimeText =  this.rec.createdTime ? moment(new Date( this.rec.createdTime)).format('MMM Do YYYY hh:mm:ss A') : ""
    }
  }

  public fileOver(event) {
  }

  public fileLeave(event) {
  }

  deleteFile(i) {
    this.files.splice(i, 1)
    this.status = new Status()
  }

  public setFiles(files: NgxFileDropEntry[]) {
    this.files = files
  }

  public uploadFiles(): void {
    for (const droppedFile of this.files) {
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          const formData = new FormData()
          formData.append('file', file, droppedFile.relativePath)
          formData.append('payload', JSON.stringify(this.rec));
          const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
          let headers = new HttpHeaders()
            .set("Authorization", this._commonService.getBasicAuth()).set('Client', this._commonService.getTenantUid())
          this.http.post(environment.apiUrl + 'config/inspectionTemplate/import', formData, { headers: headers, responseType: 'json' })
            .subscribe(data => {
              this.status = data as Status
              if (this.status.code == '200') {
                this._commonService.success(this.status.message);
                this.files = [];
                this.dialogRef.close(true);
              } else {
                this._commonService.warning(this.status.message);
              }
            }, error => {
              if (error) {
                this._commonService.error(error.error);
              } else {
                this._commonService.error(this.status.message);
              }
            })

        });
      } else {
        this._commonService.warning('Please select file')
        // It was a directory (empty directories are added, otherwise only files)
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;

      }
    }
  }
}
