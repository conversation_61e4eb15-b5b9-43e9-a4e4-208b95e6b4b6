import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DEFAULT_INTERRUPTSOURCES, Idle } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { SessionTimeoutComponent } from 'app/shared/session-timeout/session-timeout.component';
import * as moment from 'moment';
import { Subject } from 'rxjs';

import { environment } from '../../environments/environment';

//import { AuthService } from '../auth/auth.service';
//import { ConfirmationService } from 'primeng/primeng';
//import { SessionTimeoutDialogComponent } from '../session-timeout/session-timeout.component';


@Injectable({
    providedIn: 'root'
})
export class SessionTimeoutService {

    idleState = "NOT_STARTED";
    lastPing?: Date = null;

    public _onDestroy = new Subject<void>();
    confirmDialogRef: MatDialogRef<SessionTimeoutComponent>;

    constructor(
        protected http: HttpClient,
        protected router: Router,
        public _matDialog: MatDialog,
        private idle: Idle,
        private keepalive: Keepalive,
        //  private authenticationService: AuthService,

    ) { }

    setSesstionTimeout(): void {
        // set idle parameters
        this.idle.setIdle(environment.sessIdleDefault * 60); // how long can they be inactive before considered idle, in seconds
        this.idle.setTimeout(environment.sessExpireDefault * 60); // how long can they be idle before considered timed out, in seconds
        this.idle.setInterrupts(DEFAULT_INTERRUPTSOURCES); // provide sources that will "interrupt" aka provide events indicating the user is active

        // do something when the user becomes idle
        this.idle.onIdleStart.subscribe(() => {
            this.idleState = "IDLE";
            sessionStorage.setItem('sessionState', this.idleState);
            this._showSessionAboutToTimeoutDialog();
            this.startWarningTime();


        });
        // do something when the user is no longer idle
        this.idle.onIdleEnd.subscribe(() => {
            this.idleState = "NOT_IDLE";
            sessionStorage.setItem('sessionState', this.idleState);
        });
        // do something when the user has timed out
        // this.idle.onTimeout.subscribe(() => {
        //     if (this._newbackendService.hasActiveSession()) {
        //         this._matDialog.closeAll();
        //         this.stopWatch();
        //         this.idleState = "TIMED_OUT";
        //         localStorage.setItem('sessionState', this.idleState);
        //         this.confirmDialogRef.close('logout');
        //     }
        // });
        // do something as the timeout countdown does its thing
        // this.idle.onTimeoutWarning.subscribe(seconds => {
        //     if (this._newbackendService.hasActiveSession()) {
        //         this.countdown = seconds;
        //         this.idle.clearInterrupts();
        //         this._showSessionAboutToTimeoutDialog();
        //         var duration = moment.duration(this.countdown, 'seconds');
        //         if (this.confirmDialogRef) {
        //             this.confirmDialogRef.componentInstance.hdrMessage = "Your session will expire in  " + `${duration.minutes()}m : ${duration.seconds()}s`;
        //         }
        //     }
        // });

        // set keepalive parameters, omit if not using keepalive
        this.keepalive.interval(15); // will ping at this interval while not idle, in seconds
        this.keepalive.onPing.subscribe(() => {
            this.lastPing = new Date();
        }); // do something when it pings

    }


    public startWatch(): void {
        this.clearTimeoutInterval();
        if (this.idle.isRunning()) {
            this.idle.stop();
        }
        // we'll call this method when we want to start/reset the idle process
        // reset any component state and be sure to call idle.watch()

        //if (this._newbackendService.hasActiveSession()) {
        if (localStorage.getItem('user')) {
            this.idle.watch();
            this.idleState = "NOT_IDLE";
            this.lastPing = null;
        }
    }
    stopWatch() {
        this.clearTimeoutInterval();
        this.idle.stop();
        this.idle.clearInterrupts();
        this.idleState = "NOT_IDLE";
        this.lastPing = null;
    }

    userLogout(): void {
        this.clearTimeoutInterval();
        localStorage.removeItem('user');
        localStorage.removeItem('tenant');
        localStorage.removeItem('currentUser');
        localStorage.removeItem('basicAuth');
        this.router.navigate(['auth/sign-out']);
        // this._matDialog.closeAll();
    }


    checkIfUserLoggedIn(): void {
        // if (this._newbackendService.hasActiveSession()) {
        if (localStorage.getItem('user')) {
            this.setSesstionTimeout();
            this.startWatch();
        } else {
            this.stopWatch();
        }
    }


    _showSessionTimeoutDialog() {
        //  this._backendErrorService.showNotificationMessage(ErrorMessages.session_expired);
        alert('Your session has expired. Please Login Again.');
    }

    _showSessionAboutToTimeoutDialog() {
        if (!this.confirmDialogRef) {
            this.confirmDialogRef = this._matDialog.open(SessionTimeoutComponent, {
                panelClass: 'mail-compose-dialog',
                disableClose: true,
                data: { hdrMessage: 'Session Timeout', bodyMessage: "Your session will expire in  " + `${environment.sessExpireDefault}m :00s `, error: false }
            });
            this.confirmDialogRef.afterClosed().subscribe(result => {
                if (result == 'reset') {
                    this.checkIfUserLoggedIn();
                }
                else {
                    this.stopWatch();
                    this.userLogout();
                }
                this.confirmDialogRef = null;
            });
        }
    }

    timeoutInterval: any = null;
    public startWarningTime(): void {
        this.idle.clearInterrupts();
        this.clearTimeoutInterval();
        let currentDate = new Date();
        let newTargetDate = new Date(currentDate.setMinutes(currentDate.getMinutes() + (environment.sessExpireDefault)));
        this.timeoutInterval = setInterval(() => {
            let d = new Date();
            var diffMs = (newTargetDate.getTime() - d.getTime());
            var Seconds_from_T1_to_T2 = diffMs / 1000;
            var Seconds_Between_Dates = Math.abs(Seconds_from_T1_to_T2);
            if (new Date(d) < new Date(newTargetDate)) {
                var duration = moment.duration(Seconds_Between_Dates, 'seconds');
                this.confirmDialogRef.componentInstance.bodyMessage = "Your session will expire in  " + `${duration.minutes()}m : ${duration.seconds()}s`;
            } else {
                this.timeOut();
            }
        }, 1000)
    }

    private clearTimeoutInterval() {
        if (this.timeoutInterval) {
            clearInterval(this.timeoutInterval);
        }
    }

    public timeOut(): void {
        this.clearTimeoutInterval();
        this._matDialog.closeAll();
        this.stopWatch();
        this.idleState = "TIMED_OUT";
        sessionStorage.setItem('sessionState', this.idleState);
        this.confirmDialogRef.close('logout');
    }

}