import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { QiVendor } from 'app/core/qinspect/_model/vendor.model';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { environment } from 'environments/environment';

import {
  DeleteConfirmationDialogComponent,
} from '../../../shared/delete-confirmation-dialog/delete-confirmation-dialog.component';
import { AddeditVendorComponent } from './addedit-vendor/addedit-vendor.component';

@Component({
  selector: 'app-vendor',
  templateUrl: './vendor.component.html',
  styleUrls: ['./vendor.component.scss']
})
export class VendorComponent extends BaseTableSharedComponent {

  //declarations:
  tenantUid = this._commonService.getTenantUid();

  columnDefs: any[] = [
    // {
    //   field: '',
    //   // headerCheckboxSelection: true,
    //   checkboxSelection: true,
    //   showDisabledCheckboxes: true,
    //   width: 33,
    //   minWidth: 33,
    // },
    {
      headerName: "Code",
      field: "code",
      sortable: true,
      unSortIcon: true,
      filter: 'agTextColumnFilter',
    },
    { headerName: "User Name", field: "userName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', },
    {
      headerName: "FullName", field: "fullName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter',
      cellRenderer: (params: ICellRendererParams) => {
        return `<a href="/dashboard/vpfDashboard?uuid=${params.data.uuid}">
      ${params.data.fullName}
    </a>`;
      }
    },
    {
      headerName: "Email",
      field: "email",
      sortable: true,
      unSortIcon: true,
      filter: 'agTextColumnFilter',
    },
    {
      headerName: "City",
      field: "city",
      sortable: true,
      unSortIcon: true,
      filter: 'agTextColumnFilter',
    },
    {
      headerName: 'Actions',
      pinned: 'right',
      maxWidth:180,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: (params) => {
        return { actions: this.prepareActionIconButtons(params.data) }
      }
    }

  ];
  public init(): void {
    super.setBasUrl(APP_UI_CONFIG.masterdata.vendor.get);
    super.init();
  }

  public getAddEvent(event): void {
    this.selectedData = {};
    this.status = 'new';
    this.openDialog(AddeditVendorComponent, '', this.status, '70%');
  }

  public prepareActionIconButtons(row: any): any {
    this.status = null;

    let iconsList: any[] = [];
    iconsList.push({ type: 'view', title: "View", icon: 'view', data: row });

    if (row.deleted == 0) {
      iconsList.push({ type: 'update', title: "Update", icon: 'edit', data: row });
    }
    if (row.deleted == 1) {
      iconsList.push({ type: 'activate', title: "Activate", icon: 'activate', data: row });
    }
    else if (row.deleted == 0) {
      iconsList.push({ type: 'deactivate', title: "Deactivate", icon: 'deactivate', data: row });
    }
    iconsList.push({ type: 'delete', title: "Delete", icon: 'delete', data: row });
    return iconsList;
  }

  public getActions(event): void {
    let actionData: any = event;
    this.selectedData = actionData.data;
    let name = (actionData.data.fullName || actionData.data.code) || ' ';

    if (actionData.status == 'update') {
      this.status = 'update';
      this.openDialog(AddeditVendorComponent, this.selectedData, this.status, '70%');
    }

    if (actionData.status == 'view') {
      this.status = 'view';
      this.openDialog(AddeditVendorComponent, this.selectedData, this.status, '70%');
    }

    if (actionData.status == 'activate') {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'Are you sure you want to activate the vendor ' + name + '?',
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          APP_UI_CONFIG.masterdata.vendor.activate.paramList['id'] = this.selectedData.uuid;
          this.activate(APP_UI_CONFIG.masterdata.vendor.activate, this.selectedData.uuid, this.selectedData);
        }
      });
    }


    if (actionData.status == 'deactivate') {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'Are you sure you want to deactivate the vendor ' + name + '?',
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          APP_UI_CONFIG.masterdata.vendor.deactivate.paramList['id'] = this.selectedData.uuid;
          this.deactivate(APP_UI_CONFIG.masterdata.vendor.deactivate, this.selectedData.uuid);
        }
      });

    }
    if (actionData.status == 'delete') {
      this.onDelete(actionData.data);
    }
  }

  public onDelete(row): void {
    let rec = <QiVendor>Util.clone(row);

    let url = URL_CONSTANTS.VENDOR + '/' + row.uuid;
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '50%',
      data: { apiData: { url: url, data: row } },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        setTimeout(() => {
          this.init();
        }, 1000);
      }
    });
  }

  public getCloseEvent() {
    super.closeAddEdit();
  }

}
