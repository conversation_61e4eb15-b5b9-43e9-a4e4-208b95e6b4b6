export const environment = {
    production: true,
    Title: 'Quality360',
    SubTitle: 'Quality & Compliance Automation Platform',
    CopyRights: '3Frames Software Labs Pvt Ltd.',
    // apiUrl:  'https://norlanka.3frameslab.com/api/',pds.3frameslab.com:9090
    // apiUrl: 'http://qa.3frameslab.com:9093/',
    // apiUrl: 'http://qa.3frameslab.com:9020/',  // elgi
    //apiUrl: 'https://ismart.3frameslab.com/qasrv/', // ismart
    // apiUrl: 'https://lotssrv.3frameslab.com/api/',
    // apiUrl: 'https://ismart.3frameslab.com/qa4inspect/',
    //apiUrl: 'http://raymond.3frameslab.com:9093/',          // Dev Norlanka
    // apiUrl: 'http://aspada.3frameslab.com:9090',
    //apiUrl: 'https://pds.3frameslab.com/api/',  
    //apiUrl: 'https://ismart.3frameslab.com/qaapi/',            // pds url
    // apiUrl: 'http://localhost:8080/',
    //tenantUid: 'norlanka',                                   // norlanka tenent
    // tenantUid: 'twills',                                   // test tenent
    // tenantUid: 'norlanka',                                        // pds tenent
    // tenantUid: 'givaudan',
    //tenantUid: 'titan',
    tenantUid: 'demo',
    //tenantUid: 'lots',                                    // elgi tenent
    // tenantUid: 'licious',                                        // elgi tenent
    //  apiUrl:'http://localhost:9098/',
    // apiUrl: 'http://************:9090',
    //apiUrl: AppConfig.BASE_URL,
    apiUrl: 'https://ismart.3frameslab.com/demoapi/',
    sourcingApiUrl: 'https://ismart.3frameslab.com/demoapi/sourcing/',
    USER: 'user',
    //USER: 'pkg/auth/details', //enable for licious

    Version: 0.4,
    releaseDate: '12-01-2022',
    sessIdleDefault: 30, // 30 mins
    sessExpireDefault: 2, // 5 mins 

    //lots staging uid - ismart
    //horecaTableAuditTypeUid: 'fb305cf6-44a2-4022-b7fa-a7585c695b65', // value to pass in auditTypeUid param for horeca Table payload
    //filterAuditTypeUuid: ['fb305cf6-44a2-4022-b7fa-a7585c695b65'] // to filter the Audit types from dashboard screen's dropdown 

    //lots production uid
    horecaTableAuditTypeUid: '569ec81f-10e1-4c38-92ae-af2b2aca3954', // value to pass in auditTypeUid param for horeca Table payload
    filterAuditTypeUuid: ['569ec81f-10e1-4c38-92ae-af2b2aca3954'] // to filter the Audit types from dashboard screen's dropdown 
};

export const Partners = {
    STAGE: 'stage',
    AMAZON: 'amazon',
    NORLANKA: 'norlanka',
    PRAJ: 'praj',
    GIVAUDAN: 'givaudan',
    TWILLS: 'twills',
    MTR: 'mtr',
    TITAN: 'titan',
    LOTS: 'lots',
    MADURA: 'madura'
}
