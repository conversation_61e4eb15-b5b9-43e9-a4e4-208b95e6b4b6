import { Component } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { PO_PROCESS_TYPES, POListStatusCodes } from 'app/core/qinspect/_common/AppConstants';
import { Util } from 'app/core/qinspect/_common/util';
import { ShipmentData } from 'app/core/qinspect/_model/ShipmentData.model';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { TflErrorListComponent } from 'app/shared/tfl-error-list/tfl-error-list.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import moment from 'moment';
import { FileSystemDirectoryEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { forkJoin, ReplaySubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import {
  PoShipmentAttachmentDialogComponent,
} from '../po-shipment-attachment-dialog/po-shipment-attachment-dialog.component';
import { SizeWiseShipmentComponent } from '../size-wise-shipment/size-wise-shipment.component';

@Component({
  selector: 'po-add-edit-shipment-details',
  templateUrl: './po-add-edit-shipment-details.component.html',
  styleUrls: ['./po-add-edit-shipment-details.component.scss']
})
export class PoAddEditShipmentDetailsComponent extends BaseTableSharedComponent {
  shipmentFormGroup: FormGroup;
  hdr: string = 'Shipment Details';
  sizeWiseQuantity: any[] = [];
  totalQty: any;
  poNumber: string = '';
  poUid: string = '';
  showDetails: boolean = false;
  columnDefs: any[] = [];
  allDesignCodeWithShipmentList: any[] = [];
  designCodeList: any[] = [];
  shipmentRec: ShipmentData = new ShipmentData()
  selectedDesignCodes: any[] = [];
  accessStatus: string = '';
  processType: string = '';
  disabledFields: boolean = false;
  isShowSaveButton: boolean = false;
  isShowupdateButton: boolean = false;
  shipmentDetails: any[] = [];
  shipmentNumber: string = '';
  isViewShipmentDetails: boolean = false;
  isRawMaterialShipment: boolean = false;
  vendorList: any = {};
  allDesignCodes: any[] = [];
  allDesignCodesWithActivities: any[] = [];
  selectionType: string = "multiple";
  metaDataHtml: any;
  shipmentAttachments: any[] = [];
  // minDate: Date = new Date();
  packinglistDocName: string = '';
  invoiceDocName: string = '';
  lrDetailsDocName: string = '';
  totalPoQty: number = 0;
  disableActualGrDate: boolean = false;
  disableRmActualGrDate: boolean = false;
  shipmentCode: any;
  currentActivityName: string = '';
  poStatus: any;
  shipmentCodeData: any;
  vendorCode: string = '';
  designCodeUid: any;
  inboxItem: any;
  maxDate: Date = new Date();

  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  public init(): void {
    this.isShowSaveButton = false;
    this.isShowupdateButton = false;
    this.showDetails = this._commonService.getTenantConfig()?.tenantUid != 'demo' ? true : false;
    this.route.queryParams
      .pipe(takeUntil(this.$destroyed))
      .subscribe(params => {
        this.currentActivityName = params['currentActivityName'];
        this.vendorCode = params['vendorCode'];
        this.poStatus = params['poStatus'];
        this.shipmentCodeData = params['shipmentCode'];
        // this.designCodeUid = params['designCodeUid']
      });
    this.route.paramMap
      .pipe(takeUntil(this.$destroyed))
      .subscribe(paramMap => {
        this.poNumber = paramMap.get('poNumber');
        this.shipmentCode = paramMap.get('uuid');
        this.accessStatus = paramMap.get('status');
        this.shipmentNumber = paramMap.get('shipmentNo');
        this.processType = paramMap.get('processType');

        if (this.processType == PO_PROCESS_TYPES.RAWMATERIAL) {
          this.isRawMaterialShipment = true;
          if (this.poStatus == POListStatusCodes.PO_CLOSED) {
            this.disableRmActualGrDate = true;
          }
        } else {
          this.isRawMaterialShipment = false;
          if (this.currentActivityName?.toLowerCase() == 'close article') {
            this.disableActualGrDate = true;
          }
        }

        this.getPoGetDetailsByPoNumber()
        if (!!this.shipmentNumber && this.shipmentNumber != 'null') {
          this.getShipmentDetailsByShipmentNumber();
        }
        else {
          if ((!!this.shipmentCode) || this.shipmentCodeData) {
            this.getPoDetailsByShipmentCode('', '')
          }
        }
      })
    this.grnEnableBasedOnUser();
    this.inboxItem = JSON.parse(sessionStorage.getItem("inboxItem"));
    super.init();
  }

  private grnEnableBasedOnUser(): void {
    if (this.accessStatus == 'update') {
      if (this.processType != this._commonService.processType.RAWMATERIAL) {
        if (this._commonService.isVendor()) {
          this.hdr = 'View Shipment Details';
          this.isShowSaveButton = false;
          this.disabledFields = true;
        } else if (this._commonService.isSourcing()) {
          this.hdr = 'Shipment Details-View/Update GRN';
          this.disabledFields = true;
          this.isShowupdateButton = true;
          this.selectionType = 'single';
        } else if (this._commonService.isProductManager()) {
          this.disabledFields = true;
        }
      } else {
        if (this._commonService.isVendor()) {
          if (this.vendorList.vendorCode == this._commonService.getCurrentUser().code) {
            this.hdr = 'View Shipment Details';
            this.isShowSaveButton = false;
            this.disabledFields = true;
          } else if (this.vendorList.shipTo == this._commonService.getCurrentUser().code) {
            this.hdr = 'Shipment Details-View/Update GRN';
            this.disabledFields = true;
            this.isShowupdateButton = true;
            this.selectionType = 'single';
          } else {

          }
        }
        else if (this._commonService.isProductManager()) {
          this.disabledFields = true;
        }
      }
    } else if (this.accessStatus == 'view') {
      this.hdr = 'View Shipment Details';
      this.isShowSaveButton = false;
      this.disabledFields = true;
    }
    else {
      if (this._commonService.isVendor() && this.vendorList.vendorCode == this._commonService.getCurrentUser().code) {
        this.hdr = 'Add Shipment Details';
        this.isShowSaveButton = true;
        this.disabledFields = false;
      }
      if (!this.showDetails && this._commonService.iAdmin()) {
        this.hdr = 'Add Shipment Details';
        this.isShowSaveButton = true;
        this.disabledFields = false;
        this.prepareShipmentColumns()
      }
    }

  }

  private getPoDetailsByShipmentCode(row, status): void {
    if (this.showDetails) {
      let shipment_code = (!!this.shipmentCode && this.shipmentCode != 'undefined') ? this.shipmentCode : this.shipmentCodeData;
      let urlObject = {
        url: `sourcing/po/articleWise?1=1&po=${this.poNumber}&shipment_code=${shipment_code}`,
        type: 'GET'
      }
      this.invokeService.serviceInvocation(urlObject)
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            if (response) {
              let modifiedDesignCodes: any[] = response.filter((person, index, selfArray) =>
                index === selfArray.findIndex((p) => (
                  p.designCodeCode === person.designCodeCode && p.shipmentCode === person.shipmentCode
                ))
              );
              this.allDesignCodes = modifiedDesignCodes;
              this.poDetailsByShipemntCodes = modifiedDesignCodes;
              this.getDesigncodeActvityByPoNumber();
              this.grnEnableBasedOnUser();
            }
          }, error: error => {
          }
        })

    } else {
      APP_UI_CONFIG.PO.getPODetailsByPoNumber.paramList.poNumber = this.poNumber;
      this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getPODetailsByPoNumber)
        .pipe(takeUntil(this.$destroyed))
        .subscribe({
          next: response => {
            if (response) {
              // let modifiedDesignCodes: any[] = response.filter((person, index, selfArray) =>
              //   index === selfArray.findIndex((p) => (
              //     p.designCodeCode === person.designCodeCode && p.shipmentCode === person.shipmentCode
              //   ))
              // );
              let allDesignCodes = response.map(({ value: value1, ...rest }) => ({ value1, ...rest }));
              this.vendorList = Util.clone(allDesignCodes.find(item => item.vendorCode));
              this.grnEnableBasedOnUser();
              this.allDesignCodes = allDesignCodes.map(item => ({ toleranceQty: (item.tolerance ? Math.round((item.tolerance / 100) * item.qty) : 0), ...item }))
              this.getDesigncodeActvityByPoNumber();
            }

          }, error: error => {

          }
        })
    }
  }
  poDetailsByShipemntCodes: any;
  allDesignCodesData: any[];
  private getPoGetDetailsByPoNumber(): void {
    APP_UI_CONFIG.PO.getPODetailsByPoNumber.paramList.poNumber = this.poNumber;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getPODetailsByPoNumber)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: reponse => {
          if (reponse) {
            this.allDesignCodesData = reponse;
            let allDesignCodes = reponse.map(({ value: value1, ...rest }) => ({ value1, ...rest }));
            this.vendorList = Util.clone(allDesignCodes.find(item => item.vendorCode));
            this.grnEnableBasedOnUser();
          }
        }, error: error => {

        }
      })
  }
  activityDetails: any;
  private getDesigncodeActvityByPoNumber(): void {
    APP_UI_CONFIG.PO.getDesignCodeActivityByPoNumber.paramList.poNumber = this.poNumber;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getDesignCodeActivityByPoNumber)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          if (response) {
            let activityResponses = response;
            this.activityDetails = response;
            this.mapActivityWithDesignCode(activityResponses);
          }

        }, error: error => {

        }
      })
  }
  private mapActivityWithDesignCode(activityResponses: any[]) {

    let allDesignCodesWithActivities = this.allDesignCodes.map(item => {
      let designCodeActivity = activityResponses.filter(activity => item.designCodeUid == activity.designCodeUid)[0];
      if (designCodeActivity) {
        return { currentActivityName: designCodeActivity.currentActivityName, ...item }
      }
    });
    this.allDesignCodesWithActivities = allDesignCodesWithActivities.filter(item => item);
    // const tenantConfig = JSON.parse(sessionStorage.getItem("tenantConfig"));
    // let shipmentStaus = tenantConfig ? JSON.parse(tenantConfig?.shipmentStatus) : []
    // const rmActivities = shipmentStaus[0]['rm'].split(',').map(item => item.trim().toLowerCase());
    // const fgActivities = shipmentStaus[0]['fg'].split(',').map(item => item.trim().toLowerCase());

    if (this.shipmentNumber != 'null' && this.shipmentNumber != 'undefined' && this.shipmentNumber != '') {
      let designCodeActivity = activityResponses.filter(activity => activity.designCodeUid)[0];

      if (this.poStatus != POListStatusCodes.PO_CLOSED) {
        if (this.processType == PO_PROCESS_TYPES.RAWMATERIAL) {
          this.allDesignCodeWithShipmentList = this.poDetailsByShipemntCodes;
          // this.allDesignCodesWithActivities.filter(item => (
          //   item.status == POStatus.PO_ACCEPTED && rmActivities.includes(item.currentActivityName?.toLowerCase())
          // ));
        } else {
          this.allDesignCodeWithShipmentList = this.poDetailsByShipemntCodes;
          // this.allDesignCodesWithActivities.filter(item => (
          //   item.status == POStatus.PO_ACCEPTED && fgActivities.includes(item.currentActivityName?.toLowerCase())
          // ));
        }
        this.getShipmentListByPoNumber(this.designCodeList);
      } else {
        this.allDesignCodeWithShipmentList = this.allDesignCodesWithActivities;
        this.disableActualGrDate = true;
      }
      this.getPoGetDetailsByShipmentNumber();
    } else {
      if (this.processType == PO_PROCESS_TYPES.RAWMATERIAL) {
        this.designCodeList = this.allDesignCodes
      } else {
        this.designCodeList = this.allDesignCodes;
      }
      this.totalPoQty = this.designCodeList.reduce((a, b) => a + (b.qty ? b.qty : 0), 0);

      // this.getPoGetDetailsByShipmentNumber();
      this.prepareShipmentColumns();
    }
  }
  totalPOQty: any;
  //Get Shipment List by shipment Number
  getShipmentDetailsByShipmentNum: any;
  private getShipmentDetailsByShipmentNumber(): void {
    let urlObject = {
      url: `sourcing/po/shipment/articleWise?1=1&po=${this.poNumber}&shipment_no=${this.shipmentNumber}`,
      type: 'GET'
    }
    this.invokeService.serviceInvocation(urlObject)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: reponse => {
          if (reponse) {
            this.getShipmentDetailsByShipmentNum = reponse;
            this.totalPOQty = this.getShipmentDetailsByShipmentNum.reduce((sum, item) => sum + item.poQty, 0);
            this.getDesigncodeActvityByPoNumber();
            this.grnEnableBasedOnUser();
          }

        }, error: error => {

        }
      })
  }

  //Get Shipment List by PO Number
  private getShipmentListByPoNumber(designCodes): void {
    APP_UI_CONFIG.PO.getShipmentListByPONumber.paramList.poNumber = this.poNumber;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getShipmentListByPONumber)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: shipmentList => {
          if (shipmentList && shipmentList.length > 0) {
            this.mapShipmentDataWithDesignCode(designCodes, shipmentList);
          }

        }, error: error => {
          this.mapShipmentDataWithDesignCode(designCodes, []);
        }
      })
  }

  // new shipment check if already shipped qty

  private mapShipmentDataWithDesignCode(designCodeList, shipmentList): void {
    this.designCodeList.forEach(item => {
      let currentDesignCodeShipment: any[] = shipmentList.filter(shipment => shipment.designCodeUid == item.designCodeUid);
      item['shipmentBookingQty'] = currentDesignCodeShipment.reduce((a, b) => a + (b.shipmentBookingQty ? b.shipmentBookingQty : 0), 0);
      item['receivedQty'] = currentDesignCodeShipment.reduce((a, b) => a + (b.receivedQty ? b.receivedQty : 0), 0);
    });
  }

  private getPoGetDetailsByShipmentNumber(): void {
    APP_UI_CONFIG.PO.getShipmentDetailsByShipmentNo.paramList.shipmentNo = this.shipmentNumber;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getShipmentDetailsByShipmentNo)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: reponse => {
          if (reponse) {
            this.shipmentDetails = reponse;
            if (this.shipmentDetails.length > 0) {
              this.setShipmentDetails();
            }
          }

        }, error: error => {

        }
      })
  }
  private setShipmentDetails(): void {
    this.shipmentRec.shipmentNo = this.shipmentDetails[0].shipmentNo;
    this.shipmentRec.invoiceNumber = this.shipmentDetails[0].invoiceNumber;
    this.shipmentRec.lrNumber = this.shipmentDetails[0].lrNumber;
    if (this.shipmentDetails[0]?.transportPlanningDate != undefined && this.shipmentDetails[0]?.transportPlanningDate != null) {
      this.shipmentRec.transportPlanningDate = new Date(this.shipmentDetails[0].transportPlanningDate);
    }
    if (this.shipmentDetails[0]?.deliveryDate != undefined && this.shipmentDetails[0]?.deliveryDate != null) {
      this.shipmentRec.deliveryDate = new Date(this.shipmentDetails[0].deliveryDate);
    } else {
      this.shipmentRec.deliveryDate = '';
    }
    if (this.shipmentDetails[0]?.actualGRDate != undefined && this.shipmentDetails[0]?.actualGRDate != null) {
      this.shipmentRec.actualGRDate = new Date(this.shipmentDetails[0].actualGRDate);
    }
    else {
    }
    let desginCode: any[] = this.shipmentDetails.map(item => {
      let desingCode = this.allDesignCodeWithShipmentList?.find(shipment => shipment.designCodeUid == item.designCodeUid);
      if (desingCode) {
        if (desingCode.sizeWiseQuantity || desingCode.sizeWiseOrderQty) {
          let sizeWiseQuantity = item?.sizeWiseQuantity?.map(item => {
            let sizeOrder = desingCode?.sizeWiseOrderQty?.find(size => size.size.trim() == item.size.trim())
            return ({ orderQty: sizeOrder.orderQty, ...item })
          })
          return ({ sizeWiseQuantity: sizeWiseQuantity, grnUpdated: item.grnUpdated, shipmentBookingQty: item.shipmentBookingQty ? item.shipmentBookingQty : this.calculateOrderQty(item), receivedQty: this.calculateReceivedQty(item), ...desingCode })
        } else {
          return ({ grnUpdated: item.grnUpdated, shipmentBookingQty: item.shipmentBookingQty ? item.shipmentBookingQty : this.calculateOrderQty(item), receivedQty: this.calculateReceivedQty(item), ...desingCode })
        }
      }
    });
    if (!!this.shipmentNumber && this.shipmentNumber != 'null') {
      this.designCodeList = this.getShipmentDetailsByShipmentNum;
    } else {
      this.designCodeList = desginCode.filter(designCode => designCode);
    }

    // 
    if (this._commonService.isSourcing()) {
      this.isShowupdateButton = this.designCodeList.some(item => item?.grnUpdated != 1);
    }
    this.totalPoQty = this.designCodeList.reduce((a, b) => a + (b.qty ? b.qty : 0), 0);
    this.prepareShipmentColumns();
    this.prepareMetaData();
  }

  private prepareShipmentColumns(): void {
    if (this.shipmentNumber && this.shipmentNumber != 'null') {
      this.columnDefs = [
        {
          field: '',
          headerCheckboxSelection: this.isAllHeaderCheckboxEnabled(),
          checkboxSelection: params => this.isCheckBoxEnabled(params),
          headerCheckboxSelectionFilteredOnly: true,
          maxWidth: 50,
          hide: this.isHideHeaderCheckboxEnabled(),
        },
        {
          headerName: "Shipment Number", field: "shipmentNo", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: "Article", field: "designCode", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: "Booking Qty", field: "shipmentBookingQty", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },

        {
          headerName: "Brand", field: "brand", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: "Category", field: "category", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: "Vendor Name", field: "vendorName", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: "GRN Date", field: "actualGRDate", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: "GRN Qty", field: "receivedQty", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: 'Actions',
          pinned: 'right',
          maxWidth: 100,
          hide: this.hideActionColumn(),
          cellRendererFramework: CellActionComponent,
          cellRendererParams: (params) => {
            return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
          }
        }

      ];
    } else {
      this.columnDefs = [
        {
          field: '',
          headerCheckboxSelection: this.isAllHeaderCheckboxEnabled(),
          checkboxSelection: params => this.isCheckBoxEnabled(params),
          headerCheckboxSelectionFilteredOnly: true,
          maxWidth: 50,
          hide: this.isHideHeaderCheckboxEnabled(),
        },
        {
          headerName: "PO Number", field: "purchaseOrderNo", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },

        {
          headerName: "Vendor Code", field: "vendorCode", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: "Order Qty", field: "qty", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: "Shipped Qty", field: "shippedQty", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: "Shipment Code", field: "shipmentCode", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true, hide: this._commonService.getTenantConfig().tenantUid == 'demo'
        },
        {
          headerName: "Design Code", field: "designCodeCode", filter: 'agSetColumnFilter', sortable: true, unSortIcon: true,
        },
        {
          headerName: 'Actions',
          pinned: 'right',
          maxWidth: 100,
          hide: this.hideActionColumn(),
          cellRendererFramework: CellActionComponent,
          cellRendererParams: (params) => {
            return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
          }
        }

      ];
    }
  }
  public hideActionColumn(): boolean {
    if (this._commonService.getTenantConfig().tenantUid == 'twills') {
      return this.poStatus === POListStatusCodes.PO_CLOSED
    } else {
      return false
    }
  }
  public prepareActionIconButtons(row): any[] {
    let iconsList: any[] = [];
    if (!this.showDetails) {
      iconsList.push({ type: 'size_wise_qty_update', title: "View Size Wise Shipment Quantity/Update GRN Qty", icon: 'edit', data: row })
    }
    else {
      let toleranceQty = row.qty + Math.round((row.tolerance / 100) * row.qty);
      if (this.processType != this._commonService.processType.RAWMATERIAL) {
        if (this._commonService.isVendor()) {
          if (!row.shippedQty || row.shippedQty < toleranceQty) {
            if (this.isShowSaveButton) {
              iconsList.push({ type: 'size_wise_qty', title: "Add/View Size Wise Shipment Quantity", icon: 'plus-circle_blue', data: row });
            }
            else if (row.grnUpdated == 1 || !this.isShowSaveButton) {
              iconsList.push({ type: 'size_wise_qty_view', title: "View Size Wise Shipment Quantity", icon: 'view', data: row });
            } else {
              iconsList.push({ type: 'size_wise_qty_update', title: "View Size Wise Shipment Quantity/Update GRN Qty", icon: 'edit', data: row });
            }
          } else {
            iconsList.push({ type: 'size_wise_qty_view', title: "View Size Wise Shipment Quantity", icon: 'view', data: row });
          }
        }
        else if (this._commonService.isSourcing()) {
          if (row.grnUpdated == 1) {
            iconsList.push({ type: 'size_wise_qty_view', title: "View Size Wise Shipment Quantity", icon: 'view', data: row });
          } else {
            iconsList.push({ type: 'size_wise_qty_update', title: "View Size Wise Shipment Quantity/Update GRN Qty", icon: 'edit', data: row });
          }
        }
        else if (this._commonService.iAdmin) {
          if (!row.shippedQty || row.shippedQty < toleranceQty) {
            if (this.isShowSaveButton) {
              iconsList.push({ type: 'size_wise_qty', title: "Add/View Size Wise Shipment Quantity", icon: 'plus-circle_blue', data: row });
            }
            else {
              iconsList.push({ type: 'size_wise_qty_view', title: "View Size Wise Shipment Quantity", icon: 'view', data: row });
            }
          } else {
            if (row.grnUpdated == 1) {
              iconsList.push({ type: 'size_wise_qty_view', title: "View Size Wise Shipment Quantity", icon: 'view', data: row });
            } else {
              iconsList.push({ type: 'size_wise_qty_update', title: "View Size Wise Shipment Quantity/Update GRN Qty", icon: 'edit', data: row });
            }
          }
        }
      } else {
        if (this._commonService.isVendor()) {
          if (this.vendorList.shipTo == this._commonService.getCurrentUser().code) {
            if (row.grnUpdated == 1) {
              iconsList.push({ type: 'size_wise_qty_view', title: "View Size Wise Shipment Quantity", icon: 'view', data: row });
            } else {
              iconsList.push({ type: 'size_wise_qty_update', title: "View Size Wise Shipment Quantity/Update GRN Qty", icon: 'edit', data: row });
            }
          }
          else if (this.vendorList.vendorCode == this._commonService.getCurrentUser().code) {
            if (!this.isShowSaveButton) {
              iconsList.push({ type: 'size_wise_qty_view', title: "View Size Wise Shipment Quantity", icon: 'view', data: row });
            }
            else if (this.isShowSaveButton && (!row.shippedQty || row.shippedQty < toleranceQty)) {
              iconsList.push({ type: 'size_wise_qty', title: "Add/View Size Wise Shipment Quantity", icon: 'plus-circle_blue', data: row });
            }
          }
        }
      }
    }
    return iconsList;
  }

  public onAction(action: any): void {
    switch (action.type) {
      case 'size_wise_qty':
        if (this.processType == this._commonService.processType.RAWMATERIAL) {
          let activityDetails = this.activityDetails.filter(item => item.designCodeUid == action.data.designCodeUid)[0];

          if (activityDetails?.activityName.toLowerCase() != 'confirm shipping complete' && activityDetails) {
            this._commonService.error(`Shipment not enabled for ${action.data.designCodeCode}`);
            return;
          }

          if (!activityDetails && activityDetails?.activityName.toLowerCase() != 'confirm shipping complete') {
            this._commonService.error(`Shipment closed for ${action.data.designCodeCode} article. Cannot process any further shipments`);
            return;
          }

          else if (!activityDetails) {
            this._commonService.error(`Shipment  completed for ${action.data.designCodeCode}`);
            return;
          }
          if (activityDetails.activityName.toLowerCase() == 'confirm shipping complete' && activityDetails.status == 2) {
            this._commonService.error(`Shipping completed for ${action.data.designCodeCode}`)
          }
          else {
            if (action.data?.qtyWise) {
              this.onClickSizeWiseQty(action.data.qtyWise, 'add');
            }
            else {
              this.getSizeWiseDataByPoNumber(action.data.designCodeUid)
            }
          }
        } else {
          let activityDetails = this.activityDetails.filter(item => item.designCodeUid == action.data.designCodeUid)[0];
          if (activityDetails.activityName.toLowerCase() != 'confirm shipping complete') {
            if (activityDetails.activityCode.toLowerCase() == 'grn completed' || activityDetails.activityCode.toLowerCase() == 'close article') {
              this._commonService.error(`Shipment closed for ${action.data.designCodeCode}`);
              return;
            } else {
              this._commonService.error(`Shipment not enabled for ${action.data.designCodeCode}`);
              return;
            }

          }

          if ((activityDetails.activityName.toLowerCase() == 'confirm shipping complete' && activityDetails.status == 2) || (activityDetails.activityCode.toLowerCase() == 'grn completed' || activityDetails.activityCode.toLowerCase() == 'close article')) {
            this._commonService.error(`Shipping completed for ${action.data.designCodeCode}`)
          }
          else {
            if (action.data?.sizeWiseQuantity) {
              this.onClickSizeWiseQty(action.data.sizeWiseQuantity, 'add');
            }
            else {
              this.getSizeWiseDataByPoNumber(action.data.designCodeUid)
            }

          }
        }
        break;
      case 'size_wise_qty_view':
        this.sizeWiseShipmentData('size_wise_qty_view', action.data);
        break;
      case 'size_wise_qty_update':
        if (!this.showDetails) {
          if (this.shipmentRec.actualGRDate && this.shipmentRec.deliveryDate) {
            if (action.data.sizeWiseQuantity) {
              this.onUpdateSizeWiseQty(action.data);
            } else {
              this.prepareAndOpenSizeWiseDailogForRM(action.data, 'update');
            }
          } else {
            this._commonService.error("Please Select Actual GR Date and Delivery Date To Proceed");
          }

        } else {
          let poStatus = this.allDesignCodesData.filter(item => item.designCodeCode == action.data.designCode)[0].status;
          let activityStatus = this.activityDetails.filter(item => item.designCodeCode == action.data.designCode)[0];
          if (this.processType == this._commonService.processType.FOB || this.processType == this._commonService.processType.JOBWORK) {
            if (activityStatus.activityCode.toLowerCase() == 'grn completed' && activityStatus.status == 2) {
              this._commonService.error("GRN closed for this article. No further GRN update can be done");
            } else if (this.shipmentRec.actualGRDate && poStatus != POListStatusCodes.PO_CLOSED && activityStatus.activityCode.toLowerCase() != 'close article') {
              this.sizeWiseShipmentData('size_wise_qty_update', action.data);
            }
            else if (activityStatus.activityCode.toLowerCase() == 'close article') {
              this._commonService.error("GRN closed for this article. No further GRN update can be done");
            }
            else {
              this._commonService.error("Please Select Actual GR Date To Proceed");
            }
          } else {
            if (this.shipmentRec.actualGRDate && poStatus != POListStatusCodes.PO_CLOSED) {
              this.sizeWiseShipmentData('size_wise_qty_update', action.data);
            } else if (poStatus == POListStatusCodes.PO_CLOSED) {
              this._commonService.error("Article is closed. No further GRN update can be done");
            }
            else {
              this._commonService.error("Please Select Actual GR Date To Proceed");
            }
          }
        }
        break;

    }
  }

  private isCheckBoxEnabled(params): boolean {
    let activityDetails = this.activityDetails?.filter(item => item?.designCodeUid == params?.data?.designCodeUid)?.[0];
    if (activityDetails?.activityName?.toLowerCase() != 'confirm shipping complete') {
      return;
    }
    let toleranceQty = params.data.qty + Math.round((params.data.tolerance / 100) * params.data.qty);
    return params.data.grnUpdated != 1 && (!params.data.shippedQty || params.data.shippedQty < toleranceQty);
  }

  private isAllHeaderCheckboxEnabled(): boolean {
    return this.designCodeList.some(item => item.grnUpdated == 1)
  }
  private isHideHeaderCheckboxEnabled(): boolean {
    return (this.accessStatus == 'update' || this.accessStatus == 'view')
  }

  public onClickSizeWiseQty(row, status): void {
    if (this.processType == this._commonService.processType.RAWMATERIAL) {
      this.prepareAndOpenSizeWiseDailogForRM(row, status);
    } else {
      this.prepareAndOpenSizeWiseDialogForFG(row, status);
    }
  }

  private prepareAndOpenSizeWiseDailogForRM(row, status): void {
    let rowData;
    rowData = (this.showDetails) ? row : [row];
    let sizeWiseQuantity = rowData.map(item => ({
      minQty: 0,
      maxQty: (item.tolerance ? (item.qty + Math.round((item.tolerance / 100) * item.qty)) : item.qty),
      received1MinQty: 0,
      received2MinQty: 0,
      received3MinQty: 0,
      received1MaxQty: item.shippedQ1Qty || 0,
      received2MaxQty: item.shippedQ2Qty || 0,
      received3MaxQty: item.shippedQ3Qty || 0,
      shippedQ1Qty: 0,
      shippedQ2Qty: 0,
      shippedQ3Qty: 0, ...item
    }))
    const dialogRef = this.dialog.open(SizeWiseShipmentComponent, {
      minWidth: '30%',
      data: { data: rowData },
      disableClose: true,
    });
    dialogRef.componentInstance.rowData = rowData;
    dialogRef.componentInstance.metaData = rowData;
    if (status == 'add') {
      dialogRef.componentInstance.disableVendorRelatedFields = false;
      dialogRef.componentInstance.showReceivedQualityField = false;
      dialogRef.componentInstance.disabledReceivedQty = true;
      dialogRef.componentInstance.shipmentData = sizeWiseQuantity;
      dialogRef.componentInstance.rmShiment = {};
      dialogRef.componentInstance.status = "CREATE";
    } else if (status == 'view') {
      dialogRef.componentInstance.disableVendorRelatedFields = true;
      dialogRef.componentInstance.showReceivedQualityField = rowData.some(item => item.grnUpdated == 1) ? true : false;
      dialogRef.componentInstance.disabledReceivedQty = true;
      dialogRef.componentInstance.shipmentData = this.setSizeWizeQtyForViewUpdate(sizeWiseQuantity);
      dialogRef.componentInstance.showSave = false;
      dialogRef.componentInstance.rmShiment = this.setSizeWizeQtyForViewUpdate(sizeWiseQuantity);
      dialogRef.componentInstance.status = "VIEW";
    }
    else if (status == 'update') {
      if (this._commonService.isProductManager() || this._commonService.isSourcing() || this._commonService.iAdmin()) {
        dialogRef.componentInstance.disableVendorRelatedFields = true;
        dialogRef.componentInstance.showReceivedQualityField = true;
        dialogRef.componentInstance.disabledReceivedQty = false;
        dialogRef.componentInstance.shipmentData = this.setSizeWizeQtyForViewUpdate(sizeWiseQuantity);
        dialogRef.componentInstance.rmShiment = this.setSizeWizeQtyForViewUpdate(sizeWiseQuantity);
        dialogRef.componentInstance.status = "UPDATE";
        if (!this.showDetails) {
          dialogRef.componentInstance.actualGRDate = this.shipmentRec.actualGRDate;
          dialogRef.componentInstance.deliveryDate = this.shipmentRec.deliveryDate;
        }
      } else {
        dialogRef.componentInstance.disableVendorRelatedFields = true;
        if (this.processType == this._commonService.processType.RAWMATERIAL) {
          if (this._commonService.isVendor()) {
            if (this.vendorList.vendorCode == this._commonService.getCurrentUser().code) {
              dialogRef.componentInstance.showReceivedQualityField = false;
              dialogRef.componentInstance.disabledReceivedQty = true;
            } else if (this.vendorList.shipTo == this._commonService.getCurrentUser().code) {
              dialogRef.componentInstance.showReceivedQualityField = true;
              dialogRef.componentInstance.disabledReceivedQty = false;
              dialogRef.componentInstance.status = "UPDATE";
            }
          }
        }
        else {
          dialogRef.componentInstance.showReceivedQualityField = true;
          dialogRef.componentInstance.disabledReceivedQty = true;
          dialogRef.componentInstance.status = "UPDATE";
        }
        dialogRef.componentInstance.shipmentData = this.setSizeWizeQtyForViewUpdate(sizeWiseQuantity);
        dialogRef.componentInstance.rmShiment = this.setSizeWizeQtyForViewUpdate(sizeWiseQuantity);
      }

    }

    dialogRef.componentInstance.metaData = rowData;
    dialogRef.componentInstance.hdr = 'Shipment Qty';
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (status == 'update') {
          let findIndex = this.designCodeList.findIndex(item => item.designCodeUid == rowData[0].designCodeUid);
          this.designCodeList[findIndex]['qtyWise'] = result;
          this.selectedDesignCodes = [this.designCodeList[findIndex]];
          this.updateShipment();
        } else {
          let findIndex = this.designCodeList.findIndex(item => item.designCodeUid == rowData[0].designCodeUid);
          this.designCodeList[findIndex]['qtyWise'] = result;
          if (this.selectedDesignCodes.length > 0) {
            let findIndex1 = this.selectedDesignCodes.findIndex(item => item.designCodeUid == rowData[0].designCodeUid);
            this.selectedDesignCodes[findIndex1]['qtyWise'] = result;
          } else {

          }
          this._commonService.success("Shipment details saved temporarily. Details will be lost if page is refreshed.");
        }
      }
    })
  }
  allDialogData: any[] = [];
  sizeWiseShipmentDetails: any[] = [];
  private sizeWiseShipmentData(status, row): void {
    APP_UI_CONFIG.PO.getShipmentSizeWiseData.paramList.shipmentNo = this.shipmentNumber;
    APP_UI_CONFIG.PO.getShipmentSizeWiseData.paramList.designcodeUid = row.designCodeUid;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getShipmentSizeWiseData)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: reponse => {
          if (reponse) {
            this.sizeWiseShipmentDetails = reponse;
            if (status == 'size_wise_qty_view') {
              this.onClickSizeWiseQty(this.sizeWiseShipmentDetails, 'view')
            } else if (status == 'size_wise_qty_update') {
              if (this.processType != this._commonService.processType.RAWMATERIAL) {
                this.onUpdateSizeWiseQty(this.sizeWiseShipmentDetails);
              } else {
                this.prepareAndOpenSizeWiseDailogForRM([row], 'update');
              }
            }
          }

        }, error: error => {

        }
      })
  }
  sizeWiseDataByPoNumber: any;
  private getSizeWiseDataByPoNumber(designCodeUid): void {
    let urlObject = {
      url: `sourcing/po/${designCodeUid}/ByDesigncode?1=1&po=${this.poNumber}&shipment_code=${this.shipmentCodeData}`,
      type: 'GET'
    }

    this.invokeService.serviceInvocation(urlObject)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          this.sizeWiseDataByPoNumber = res;
          this.onClickSizeWiseQty(this.sizeWiseDataByPoNumber, 'add');
        },
        error: error => {
          console.error("Error fetching size codes", error);
        }
      })
  }
  sizeWiseQuantityData: any
  private prepareAndOpenSizeWiseDialogForFG(row, status): void {
    let sizeWiseQuantity;
    let preparePriceMaster: any;
    preparePriceMaster = row.filter(item => {
      return item.sizeCode
        ? row.map(poData => ({ currency: row.currency, ...poData }))
        : this.prepareSizeWiseQtyForNoSize(row);
    });
    let isValidForSizeCode = row.every(item => item.sizeCode)
    if (isValidForSizeCode) {
      sizeWiseQuantity = preparePriceMaster.map(item => {
        return { minQty: 0, maxQty: (item.tolerance ? (item.qty + Math.round((item.tolerance / 100) * item.qty)) : item.qty), shippedQ1Qty: 0, shippedQ2Qty: 0, shippedQ3Qty: 0, ...item }
      })
    } else {
      sizeWiseQuantity = row.sizeWiseOrderQty?.map(item => {
        return { minQty: 0, maxQty: (item.tolerance ? (item.qty + Math.round((item.tolerance / 100) * item.qty)) : item.qty), shippedQ1Qty: 0, shippedQ2Qty: 0, shippedQ3Qty: 0, ...item }
      });
    }
    const dialogRef = this.dialog.open(SizeWiseShipmentComponent, {
      minWidth: '30%',
      data: { data: row },
      disableClose: true,
    });
    dialogRef.componentInstance.rowData = row;
    dialogRef.componentInstance.metaData = row;
    dialogRef.componentInstance.hdr = 'Size Wise Shipment Qty';
    if (status == 'add') {
      this.sizeWiseQuantityData = sizeWiseQuantity;
      dialogRef.componentInstance.disableVendorRelatedFields = false;
      dialogRef.componentInstance.showReceivedQualityField = false;
      dialogRef.componentInstance.disabledReceivedQty = true;
      dialogRef.componentInstance.sizeWiseShipmentData = sizeWiseQuantity;
      dialogRef.componentInstance.status = "CREATE";
    } else if (status == 'view') {
      dialogRef.componentInstance.disableVendorRelatedFields = true;
      dialogRef.componentInstance.showReceivedQualityField = row.some(item => item.grnUpdated == 1) ? true : false;
      dialogRef.componentInstance.disabledReceivedQty = true;
      dialogRef.componentInstance.showSave = false;
      dialogRef.componentInstance.status = "VIEW";
      dialogRef.componentInstance.sizeWiseShipmentData = this.setSizeWizeQtyForViewUpdate(this.sizeWiseShipmentDetails);
    }
    else if (status == 'update') {
      dialogRef.componentInstance.disableVendorRelatedFields = true;
      dialogRef.componentInstance.showReceivedQualityField = true;
      dialogRef.componentInstance.disabledReceivedQty = true;
      dialogRef.componentInstance.status = "UPDATE";
      dialogRef.componentInstance.sizeWiseShipmentData = this.setSizeWizeQtyForViewUpdate(this.sizeWiseShipmentDetails);
    }
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        let findIndex = this.designCodeList.findIndex(item => item.designCodeUid == row[0].designCodeUid);
        this.designCodeList[findIndex]['sizeWiseQuantity'] = result;
        if (this.selectedDesignCodes.length > 0) {
          let findIndex1 = this.selectedDesignCodes.findIndex(item => item.designCodeUid == row[0].designCodeUid);
          this.selectedDesignCodes[findIndex1]['sizeWiseQuantity'] = result;
        } else {

        }
        this._commonService.success("Shipment details saved temporarily. Details will be lost if page is refreshed.");
      }
    })
  }



  private prepareSizeWiseQtyForNoSize(row): any[] {
    return [
      {
        currency: row.currency,
        uuid: row.uuid,
        tenantUid: row.tenantUid,
        parentTenantUid: row.parentTenantUid,
        createdBy: row.createdBy,
        status: 0,
        designCodeUid: row.designCodeUid,
        size: "NA",
        orderQty: row.qty,
        unitPrice: (row.unitPrice / row.qty),
        poUid: row.uuid,
        poNumber: row.purchaseOrderNo,
        poItem: row.purchaseOrderItem,
        soNo: row.salesOrderNo,
        soItem: row.salesOrderItem
      }

    ]
  }

  private setSizeWizeQtyForViewUpdate(row): any[] {
    let sizeWiseQuantity: any[] = [];
    let rowData;
    if (this.showDetails) {
      rowData = row
    } else {
      rowData = [row]
    }
    if (rowData[0].sizeCode) {
      sizeWiseQuantity = this.sizeWiseShipmentDetails.map(item => ({ receivedMinQty: 0, received1MaxQty: item.shippedQ1Qty, received2MaxQty: item.shippedQ2Qty, received3MaxQty: item.shippedQ3Qty, maxQty: item.shippedQ1Qty + item.shippedQ2Qty + item.shippedQ3Qty, ...item }))
    } else {
      sizeWiseQuantity = this.sizeWiseShipmentDetails.map(item => ({ receivedMinQty: 0, received1MaxQty: item.shippedQ1Qty, received2MaxQty: item.shippedQ2Qty, received3MaxQty: item.shippedQ3Qty, maxQty: item.shippedQ1Qty + item.shippedQ2Qty + item.shippedQ3Qty, receivedQ1Qty: item.receivedQ1Qty ?? 0, receivedQ2Qty: item.receivedQ2Qty ?? 0, receivedQ3Qty: item.receivedQ3Qty ?? 0, ...item }));

    }
    return sizeWiseQuantity;
  }

  public onUpdateSizeWiseQty(row): void {
    let sizeWiseQuantity
    sizeWiseQuantity = this.setSizeWizeQtyForViewUpdate(row).map(item => ({ receivedQ1Qty: 0, receivedQ2Qty: 0, receivedQ3Qty: 0, ...item }));
    const dialogRef = this.dialog.open(SizeWiseShipmentComponent, {
      minWidth: '30%',
      data: { data: row },
      disableClose: true,
    });
    dialogRef.componentInstance.rowData = row;
    dialogRef.componentInstance.sizeWiseShipmentData = sizeWiseQuantity.map(item => ({ receivedMinQty: 0, received1MaxQty: item.shippedQ1Qty, received2MaxQty: item.shippedQ2Qty, received3MaxQty: item.shippedQ3Qty, ...item }))
    if (this.processType == this._commonService.processType.RAWMATERIAL) {
      if (this._commonService.isVendor()) {
        if (this.vendorList.vendorCode == this._commonService.getCurrentUser().code) {
          dialogRef.componentInstance.showReceivedQualityField = false;
          dialogRef.componentInstance.disabledReceivedQty = true;
        } else if (this.vendorList.shipTo == this._commonService.getCurrentUser().code) {
          dialogRef.componentInstance.showReceivedQualityField = true;
          dialogRef.componentInstance.disabledReceivedQty = false;
          dialogRef.componentInstance.status = "UPDATE";
        }
      }
      dialogRef.componentInstance.disableVendorRelatedFields = true;
    } else {
      if (this._commonService.isSourcing() || this._commonService.iAdmin()) {
        dialogRef.componentInstance.disableVendorRelatedFields = true;
        dialogRef.componentInstance.showReceivedQualityField = true;
        dialogRef.componentInstance.disabledReceivedQty = false;
        dialogRef.componentInstance.buttonName = 'Update GRN';
        dialogRef.componentInstance.status = "UPDATE";
      }
    }

    dialogRef.componentInstance.metaData = row;
    dialogRef.componentInstance.hdr = 'Size Wise Shipment Qty';

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        let findIndex = this.designCodeList.findIndex(item => item.uuid == row.uuid);
        this.designCodeList[findIndex]['sizeWiseQuantity'] = result;
        this.selectedDesignCodes = [this.designCodeList[findIndex]];
        this.updateShipment();
      }
    })
  }


  public onClose(): void {
    this._commonService.historyBack();
  }

  public onRefreshPage(): void {
    this.getPoGetDetailsByPoNumber();
    if (!!this.shipmentNumber && this.shipmentNumber != 'null') {
      this.getShipmentDetailsByShipmentNumber()
    } else {
      this.getPoDetailsByShipmentCode('', '')
    }
  }

  public onSelectionChanged(data): void {
    this.selectedDesignCodes = data;
  }

  public openResponseDialog(data: any): void {
    const dialogRef = this.dialog.open(TflErrorListComponent, {
      minWidth: '40%',
      disableClose: true,
    });
    dialogRef.componentInstance.columns = [

      { headerName: "PO No.", field: "purchaseOrderNo", sortable: false, maxWidth: 150 },
      {
        headerName: "Design Code ", field: "designCode", sortable: false, maxWidth: 250
      },
      // { headerName: "Dispatch Qty", field: "bookingQty", sortable: false, maxWidth: 150 },
      { headerName: "Error Message", field: "message", sortable: false, minWidth: 300 },
    ];
    dialogRef.componentInstance.rowData = data;
    dialogRef.componentInstance.hdr = "PO Shipment Creation Warning/Errors";

    dialogRef.afterClosed().subscribe(result => {


    });

  }

  public onSaveShipment(): void {
    this._commonService.openDialogForConfirmation(this.shipmentAttachments.length > 0 ? "Are you sure you want save shipment with attachments?" : 'Are you sure you want save shipment without attachments?')
      .pipe(takeUntil(this.$destroyed))
      .subscribe(res => {
        if (res) {
          console.log("selectedDesignCodes:", this.selectedDesignCodes)
          let isValid = this.selectedDesignCodes.every(item => {
            if (item.sizeWiseQuantity) {
              return item?.sizeWiseQuantity.some(item1 => (item1.shippedQ1Qty > 0 || item1.shippedQ2Qty > 0 || item1.shippedQ3Qty > 0))
            } else {
              if (item.qtyWise) {
                return item?.qtyWise.some(item1 => (item1.shippedQ1Qty > 0 || item1.shippedQ2Qty > 0 || item1.shippedQ3Qty > 0))
              }
            }
          })
          if (isValid) {
            let payload = this.preparePayload();
            this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.saveShipment, '', '', payload)
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: res => {
                  let response: any[] = res;
                  if (response.every(item => item.code == 200)) {
                    if (this.shipmentAttachments.length > 0) {
                      this.onSaveShipmentAttachments();
                    } else {
                      this._commonService.success("Shipment has been saved successfully");
                      this.onClose();
                    }
                  } else {
                    this.openResponseDialog(res);
                  }
                },
                error: error => {
                  this._commonService.handleError(error);
                }
              })
          } else {
            this._commonService.error("Please check shipment Qty");
          }
        }
      })

  }

  public updateShipment(): void {
    let isValid = this.selectedDesignCodes.every(item => {
      if (item.sizeWiseQuantity) {
        return item.sizeWiseQuantity?.some(item1 => (item1.receivedQ1Qty > 0 || item1.receivedQ2Qty > 0 || item1.receivedQ3Qty > 0))
      } else {
        if (item.qtyWise) {
          return item.qtyWise?.some(item1 => (item1.receivedQ1Qty > 0 || item1.receivedQ2Qty > 0 || item1.receivedQ3Qty > 0))
        }
      }
    });

    if (isValid) {
      let payload = this.preparePayloadForUpdateGRN(this.selectedDesignCodes);
      this._commonService.openDialogForConfirmation(`Are you sure you want to update GRN?`)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(confirm => {
          if (confirm) {
            this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.updateShipmentGRN, '', '', payload)
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: res => {
                  if (res && res.code == 200) {
                    this._commonService.success("Shipment has been updated successfully");
                    this.getShipmentDetailsByShipmentNumber();
                  } else {
                    this.openResponseDialog(res);
                  }
                },
                error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })


    } else {
      this._commonService.error("Please check Shipment GRN Qty")
    }
  }

  private calculateOrderQty(item): any {
    if (!item.sizeWiseQuantity) {
      return ((item.shippedQ1Qty ? item.shippedQ1Qty : 0) + (item.shippedQ2Qty ? item.shippedQ2Qty : 0) + (item.shippedQ3Qty ? item.shippedQ3Qty : 0))
    } else {
      return item.sizeWiseQuantity.reduce((a, b) => a + ((b.shippedQ1Qty ? b.shippedQ1Qty : 0) + (b.shippedQ2Qty ? b.shippedQ2Qty : 0) + (b.shippedQ3Qty ? b.shippedQ3Qty : 0)), 0)

    }
  }
  private calculateReceivedQty(item): any {
    if (!item.sizeWiseQuantity) {
      return ((item.receivedQ1Qty ? item.receivedQ1Qty : 0) + (item.receivedQ2Qty ? item.receivedQ2Qty : 0) + (item.receivedQ3Qty ? item.receivedQ3Qty : 0))
    } else {
      return item.sizeWiseQuantity.reduce((a, b) => a + ((b.receivedQ1Qty ? b.receivedQ1Qty : 0) + (b.receivedQ2Qty ? b.receivedQ2Qty : 0) + (b.receivedQ3Qty ? b.receivedQ3Qty : 0)), 0)
    }
  }
  private preparePayload(): any {
    const shipmentObjects: ShipmentData[] = [];
    this.selectedDesignCodes.forEach(item1 => {
      if (item1.sizeWiseQuantity) {
        item1.sizeWiseQuantity.forEach(item2 => {
          let eachobject: ShipmentData = { ...this.shipmentRec };
          eachobject['vendorUid'] = item2.vendorUid;
          eachobject['vendorName'] = item2.vendorName;
          eachobject['vendorCode'] = item2.vendorCode;
          eachobject['purchaseOrderNo'] = item2.purchaseOrderNo;
          eachobject['purchaseOrderItem'] = item2.purchaseOrderItem;
          eachobject['sourcingUserUid'] = item2.sourcingUserUid;
          eachobject['salesOrderNo'] = item2.salesOrderNo;

          eachobject['poUid'] = item2.uuid;

          eachobject['designCodeUid'] = item2.designCodeUid;
          eachobject['designCode'] = item2.designCodeCode;
          eachobject['brand'] = item2.brandName;
          eachobject['season'] = item2.season;
          eachobject['warehouseId'] = item2.warehouseId;
          eachobject['category'] = item2.category;
          eachobject['articleType'] = item2.articleType;
          eachobject['code'] = item2.shipmentCode;
          eachobject['channel'] = item2.channel;
          eachobject['deliveryDate'] = moment(this.shipmentRec.deliveryDate).format("YYYY-MM-DD");
          eachobject['shipmentNo'] = this.shipmentRec.shipmentNo;
          eachobject['shipmentCode'] = item2.shipmentCode;
          eachobject['qty'] = item2.qty;
          eachobject['transportPlanningDate'] = this.shipmentRec.transportPlanningDate;
          eachobject['invoiceNumber'] = this.shipmentRec.invoiceNumber;
          eachobject['lrNumber'] = this.shipmentRec.lrNumber;
          eachobject['salesOrderItem'] = item2.salesOrderItem;
          eachobject['shipmentBookingQty'] = this.calculateOrderQty(item2);
          eachobject['shippedQ1Qty'] = item2.shippedQ1Qty;
          eachobject['shippedQ2Qty'] = item2.shippedQ2Qty;
          eachobject['shippedQ3Qty'] = item2.shippedQ3Qty;
          eachobject['sourcingUserName'] = item2.sourcingUserName;
          eachobject['sizeCode'] = item2.sizeCode;
          eachobject['articleTypeUid'] = item2.articleTypeUid;
          eachobject['packingListUid'] = item2.packingListUid;
          shipmentObjects.push(eachobject);
        })
      }
      else {

        if (item1.qtyWise) {
          item1.qtyWise?.forEach(item2 => {
            let eachobject: ShipmentData = { ...this.shipmentRec };
            eachobject['vendorUid'] = item2.vendorUid;
            eachobject['vendorName'] = item2.vendorName;
            eachobject['vendorCode'] = item2.vendorCode;
            eachobject['purchaseOrderNo'] = item2.purchaseOrderNo;
            eachobject['purchaseOrderItem'] = item2.purchaseOrderItem;
            eachobject['sourcingUserUid'] = item2.sourcingUserUid;
            eachobject['salesOrderNo'] = item2.salesOrderNo;

            eachobject['poUid'] = item2.uuid;

            eachobject['designCodeUid'] = item2.designCodeUid;
            eachobject['designCode'] = item2.designCodeCode;
            eachobject['brand'] = item2.brandName;
            eachobject['season'] = item2.season;
            eachobject['warehouseId'] = item2.warehouseId;
            eachobject['category'] = item2.category;
            eachobject['articleType'] = item2.articleType;
            eachobject['code'] = item2.shipmentCode;
            eachobject['channel'] = item2.channel;
            eachobject['deliveryDate'] = moment(this.shipmentRec.deliveryDate).format("YYYY-MM-DD");
            eachobject['shipmentNo'] = this.shipmentRec.shipmentNo;
            eachobject['transportPlanningDate'] = this.shipmentRec.transportPlanningDate;
            eachobject['invoiceNumber'] = this.shipmentRec.invoiceNumber;
            eachobject['lrNumber'] = this.shipmentRec.lrNumber;
            eachobject['salesOrderItem'] = item2.salesOrderItem;
            eachobject['shipmentBookingQty'] = this.calculateOrderQty(item2);
            eachobject['shippedQ1Qty'] = item2.shippedQ1Qty;
            eachobject['shippedQ2Qty'] = item2.shippedQ2Qty;
            eachobject['shippedQ3Qty'] = item2.shippedQ3Qty;
            eachobject['sourcingUserName'] = item2.sourcingUserName;
            eachobject['articleTypeUid'] = item2.articleTypeUid;
            eachobject['packingListUid'] = item2.packingListUid;
            eachobject['shipmentCode'] = item2.shipmentCode;
            eachobject['qty'] = item2.qty;
            shipmentObjects.push(eachobject);
          })

        }
      }
    });
    return shipmentObjects;
  }
  private preparePayloadForUpdateGRN(designCodeList: any[]) {
    const shipmentObjects: ShipmentData[] = [];
    designCodeList.forEach(item => {
      if (item.sizeWiseQuantity) {
        item.sizeWiseQuantity.forEach(item1 => {
          let eachobject: ShipmentData = { ...this.shipmentRec };
          eachobject['shipmentNo'] = this.shipmentRec.shipmentNo;
          eachobject['code'] = item1.code;
          eachobject['vendorUid'] = item1.vendorUid;
          eachobject['vendorName'] = item1.vendorName;
          eachobject['vendorCode'] = item1.vendorCode;
          eachobject['deliveryDate'] = moment(this.shipmentRec.deliveryDate).format("YYYY-MM-DD");
          eachobject['uuid'] = item1.uuid;
          eachobject['actualGRDate'] = moment(this.shipmentRec.actualGRDate).format("YYYY-MM-DD");
          eachobject['transportPlanningDate'] = this.shipmentRec.transportPlanningDate;

          eachobject['salesOrderNo'] = item1.salesOrderNo;
          eachobject['salesOrderItem'] = item1.salesOrderItem;
          eachobject['purchaseOrderNo'] = item1.purchaseOrderNo;
          eachobject['purchaseOrderItem'] = item1.purchaseOrderItem;
          eachobject['shipmentBookingQty'] = this.calculateOrderQty(item1);

          eachobject['receivedQ1Qty'] = item1.receivedQ1Qty;
          eachobject['receivedQ2Qty'] = item1.receivedQ2Qty;
          eachobject['receivedQ3Qty'] = item1.receivedQ3Qty;
          eachobject['receivedQty'] = this.calculateReceivedQty(item1);
          eachobject['shippedQ1Qty'] = item1.shippedQ1Qty;
          eachobject['shippedQ2Qty'] = item1.shippedQ2Qty;
          eachobject['shippedQ3Qty'] = item1.shippedQ3Qty;

          eachobject['designCodeUid'] = item1.designCodeUid;
          eachobject['designCode'] = item1.designCode;
          eachobject['invoiceNumber'] = this.shipmentRec.invoiceNumber;
          eachobject['lrNumber'] = this.shipmentRec.lrNumber;

          eachobject['grnUpdated'] = 1;
          eachobject['brand'] = item1.brand;

          eachobject['category'] = item1.category;
          eachobject['sourcingUserName'] = item1.sourcingUserName;
          eachobject['sourcingUserUid'] = item1.sourcingUserUid;
          eachobject['season'] = item1.season;
          eachobject['articleType'] = item1.articleType;
          eachobject['sizeCode'] = item1.sizeCode;
          eachobject['warehouseId'] = item1.warehouseId;

          eachobject['channel'] = item1.channel;
          eachobject['articleTypeUid'] = item1.articleTypeUid;
          eachobject['packingListUid'] = item1.packingListUid;
          shipmentObjects.push(eachobject);
        });
      } else {
        if (item.qtyWise) {
          item.qtyWise.forEach(item1 => {
            let eachobject: ShipmentData = { ...this.shipmentRec };
            eachobject['shipmentNo'] = this.shipmentRec.shipmentNo;
            eachobject['code'] = item1.code;
            eachobject['vendorUid'] = item1.vendorUid;
            eachobject['vendorName'] = item1.vendorName;
            eachobject['vendorCode'] = item1.vendorCode;
            eachobject['deliveryDate'] = moment(this.shipmentRec.deliveryDate).format("YYYY-MM-DD");
            eachobject['uuid'] = item1.uuid
            eachobject['actualGRDate'] = moment(this.shipmentRec.actualGRDate).format("YYYY-MM-DD");
            eachobject['transportPlanningDate'] = this.shipmentRec.transportPlanningDate;

            eachobject['salesOrderNo'] = item1.salesOrderNo;
            eachobject['salesOrderItem'] = item1.salesOrderItem;
            eachobject['purchaseOrderNo'] = item1.purchaseOrderNo;
            eachobject['purchaseOrderItem'] = item1.purchaseOrderItem;
            eachobject['shipmentBookingQty'] = this.calculateOrderQty(item);

            eachobject['receivedQ1Qty'] = item1.receivedQ1Qty;
            eachobject['receivedQ2Qty'] = item1.receivedQ2Qty;
            eachobject['receivedQ3Qty'] = item1.receivedQ3Qty;
            eachobject['receivedQty'] = this.calculateReceivedQty(item1);
            eachobject['shippedQ1Qty'] = item1.shippedQ1Qty;
            eachobject['shippedQ2Qty'] = item1.shippedQ2Qty;
            eachobject['shippedQ3Qty'] = item1.shippedQ3Qty;

            eachobject['designCodeUid'] = item1.designCodeUid;
            eachobject['designCode'] = item1.designCode;
            eachobject['invoiceNumber'] = this.shipmentRec.invoiceNumber;
            eachobject['lrNumber'] = this.shipmentRec.lrNumber;

            eachobject['grnUpdated'] = 1;
            eachobject['brand'] = item1.brand;

            eachobject['category'] = item1.category;
            eachobject['sourcingUserName'] = item1.sourcingUserName;
            eachobject['sourcingUserUid'] = item1.sourcingUserUid;
            eachobject['season'] = item1.season;
            eachobject['articleType'] = item1.articleType;
            eachobject['warehouseId'] = item1.warehouseId;

            eachobject['channel'] = item1.channel;
            eachobject['articleTypeUid'] = item1.articleTypeUid;
            eachobject['packingListUid'] = item1.packingListUid;
            shipmentObjects.push(eachobject);
          })
        }


      }
    });

    return shipmentObjects;
  }

  public addShipmentAttachmentDialog(): void {
    const dialogRef = this.dialog.open(PoShipmentAttachmentDialogComponent, {
      width: '50%',
      data: { data: this.shipmentNumber },
      disableClose: true,
    });
    dialogRef.componentInstance.isShowAddViewAttachments = true;
    dialogRef.componentInstance.isShowOnlyViewAttachments = false;
    dialogRef.componentInstance.uploadedFiles = this.shipmentAttachments;
    dialogRef.componentInstance.onUploadFiles.subscribe(res => {
      this.shipmentAttachments = res;
    })

  }

  public viewShipmentAttachmentDialog(): void {
    const dialogRef = this.dialog.open(PoShipmentAttachmentDialogComponent, {
      width: '50%',
      data: { data: this.shipmentNumber },
      disableClose: true,
    });
    dialogRef.componentInstance.isShowAddViewAttachments = false;
    dialogRef.componentInstance.isShowOnlyViewAttachments = true;
  }

  private prepareMetaData(): void {
    if (this.designCodeList.length == 0) {
      this.metaDataHtml = `<div class="notificationsMessage">No Article Found For Shipment and Can Ship Once Inspection Done From T&A </div>`
    }

  }

  public onChangeFiles(attachmentCategory, event): void {
    let file: any[] = event.target.files;
    file[0]['docType'] = attachmentCategory;
    file[0]['fileEntry'] = {
      "name": file[0].name,
      "isDirectory": false,
      "isFile": true,
      "isNormalFileInput": true,
    },
      this.shipmentAttachments.push(file[0]);
    if (attachmentCategory == 'PACKINGLIST') {
      this.packinglistDocName = file[0].name;
    } else if (attachmentCategory == 'INVOICE') {
      this.invoiceDocName = file[0].name;
    } else if (attachmentCategory == 'LR DETAILS') {
      this.lrDetailsDocName = file[0].name;
    }
  }


  public deleteSelectedDocTypeFile(attachmentCategory) {
    this._commonService.openDialogForConfirmation(`Are you sure you want delete attachment type:${attachmentCategory} ?`)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(res => {
        if (res) {
          let findIndex = this.shipmentAttachments.findIndex(item => item.docType == attachmentCategory);
          this.shipmentAttachments.splice(findIndex, 1);
          if (attachmentCategory == 'PACKINGLIST') {
            this.packinglistDocName = '';
          } else if (attachmentCategory == 'INVOICE') {
            this.invoiceDocName = '';
          } else if (attachmentCategory == 'LR DETAILS') {
            this.lrDetailsDocName = '';
          }
        }
      })
  }

  public viewFile(attachmentCategory) {
    let file = this.shipmentAttachments.find(item => item.docType == attachmentCategory);
    window.open(file, '_blank');
  }

  public onSaveShipmentAttachments(): void {
    forkJoin([...this.prepareAttachments()])
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          this._commonService.success("Shipment saved with attachments");
          this.onClose();
        },
        error: error => {
          this._commonService.handleError(error);
        }
      })
  }

  private prepareAttachments(): any[] {
    let payload: any[] = [];
    this.shipmentAttachments.forEach((eachFile, index) => {
      const formData = new FormData();
      let droppedFile: any = eachFile;
      if (droppedFile.fileEntry.isFile) {
        if (droppedFile.fileEntry.isNormalFileInput != true) {
          const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
          fileEntry.file((file: File) => {
            formData.append('file', file, droppedFile.relativePath);
            formData.append('docType', droppedFile.docType);
            formData.append('json', JSON.stringify({ name: droppedFile.relativePath }));
          });
        } else {
          formData.append('file', eachFile, eachFile.name);
          formData.append('docType', eachFile.docType);
          formData.append('json', JSON.stringify({ name: eachFile.name }));
        }

        APP_UI_CONFIG.finishGoodsDesignCode.addDocForPoShipment.paramList.shipmentNumber = this.shipmentRec.shipmentNo;
        payload.push(this.invokeService.uploadAttachments(APP_UI_CONFIG.finishGoodsDesignCode.addDocForPoShipment, formData))
      } else {
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
      }
    });
    return payload;
  }

  //Destroy
  public ngOnDestroy(): void {
    this.$destroyed.next(true);
    this.$destroyed.complete();
  }
}