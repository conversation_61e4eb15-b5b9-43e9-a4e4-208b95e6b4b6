import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { User } from 'app/core/user/user.model';
import { ROLE } from 'app/enum-constants';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { environment } from '../../environments/environment';
import { URL_CONSTANTS } from '../core/qinspect/appconfig/appconfig';
import { CommonService } from './common.service';


@Injectable({
  providedIn: 'root'
})
export class AuthService {
  apiURL: string = environment.apiUrl;
  constructor(private _router: Router,
    private http: HttpClient,
    private _commonService: CommonService,
  ) { }

  public setLoggeInUser(user): void {
    localStorage.setItem('user', JSON.stringify(user));
  }

  public getLoggedInUser(): any {
    if (localStorage.hasOwnProperty('user')) {
      return JSON.parse(localStorage.getItem('user'));
    } else {
      return null;
    }
  }

  public setMfaCode() {
    localStorage.setItem('isMfaCodeHasValue', JSON.stringify(true));
  }

  public isMfaCodeHasPinValue(): any {
    if (localStorage.hasOwnProperty('isMfaCodeHasValue')) {
      return true;
    } else {
      return false;
    }
  }


  public logout(): void {
    localStorage.removeItem('user');
    localStorage.removeItem('tenantUid');
    localStorage.removeItem('tenant');
    localStorage.removeItem('tenantUid');
    localStorage.removeItem('currentUser');
    localStorage.removeItem('basicAuth');
    localStorage.removeItem('auditTypeDataObject');
    localStorage.removeItem('sharedLink');
    localStorage.removeItem('isMfaCodeHasValue');
    localStorage.removeItem('techSpecificationGroupData');
    sessionStorage.clear();
    this._router.navigate(['auth/sign-out']);
  }

  public isFactory(user): boolean {
    return user.role.includes("FACTORY")
  }

  public isVendor(user): boolean {
    return user.role.includes("VENDOR")
  }

  public isLab(user): boolean {
    return user.role.includes("LAB")
  }

  public isSupplier(user): boolean {
    return this.isFactory(user) || this.isVendor(user) || this.isLab(user)
  }

  public isLoggedInUserSupplier(): boolean {
    let user = this.getLoggedInUser();
    return this.isFactory(user) || this.isVendor(user) || this.isLab(user)
  }

  //API Calls
  public getTenant(username, password): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'username': username, 'password': password })
    let url = `${environment.apiUrl}${APP_UI_CONFIG.configuration.getTenant.url}`;
    return this.http.get<any>(url, { headers: headers, observe: 'response' }).pipe(map(res => {
      return res;
    }))
  }

  public login(username, password): Observable<User> {
    var token = 'Basic ' + btoa(username + ':' + password);
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", token);
    let url = `${environment.apiUrl}${URL_CONSTANTS.USER}`;
    return this.http.get<User>(url, { headers: headers }).pipe(map(res => {
      // store user details and jwt token in local storage to keep user logged in between page refreshes
      let user: any = res;
      user.token = token
      user.pic = './assets/media/misc/user-profile-icon-20-400x400.png';
      localStorage.setItem('currentUser', JSON.stringify(user));
      return user;
    }))
  }

  public getTenantConfig(username, password, tenantUid): Observable<any> {
    var token = 'Basic ' + btoa(username + ':' + password);
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", token);
    let url = `${environment.apiUrl}config/tenantConfig/${tenantUid}`;
    return this.http.get<any>(url, { headers: headers })
  }

  public validateMFACode(username, password, mfaCode): Observable<any> {
    console.log("tenantUid:", this._commonService.getTenantUid())
    var token = 'Basic ' + btoa(username + ':' + password);
    const headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", token);
    console.log("headers:", headers)
    let url = `${environment.apiUrl}${APP_UI_CONFIG.auth.mfaValidation.url}${mfaCode}`;
    return this.http.post<any>(url, { headers: headers })
  }

  public getQRImage(username, password): Observable<any> {
    var token = 'Basic ' + btoa(username + ':' + password);
    let headers = new HttpHeaders({ 'Client': this._commonService.getTenantUid() })
      .set("Authorization", token);
    let url = `${environment.apiUrl}${APP_UI_CONFIG.auth.mfaRegistration.url}`;
    return this.http.get(url, { headers: headers, responseType: 'text' })
  }

  public getTentantConfig(): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this._commonService.getBasicAuth());
    let url = `${environment.apiUrl}${URL_CONSTANTS.TenantConfig}`;
    return this.http.get(url, { headers: headers, }).pipe(catchError(error => this.handleError(error)))
  }

  public getUserByEmailCode(email: string): Observable<any> {
    var token = 'Basic ' + btoa('admin' + ':' + 'admin');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", token);
    console.log("headers:", headers)
    return this.http.put<any>(environment.apiUrl + 'user/getUserByEmailCode/' + email, {}, { headers: headers })
  }

  public getUserPassword(username: string): Observable<any> {
    var token = 'Basic ' + btoa('admin' + ':' + 'admin');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", token);
    return this.http.get(environment.apiUrl + `user/${username}/forgotpwd`, {
      headers: headers
    });
  }

  public resetUserPassword(payload): Observable<any> {
    var token = 'Basic ' + btoa('admin' + ':' + 'admin');
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", token);
    return this.http.post(environment.apiUrl + `user/update`, payload, {
      headers: headers
    });
  }

  public handleError(errorResponse: HttpErrorResponse) {
    let errorMessage: any = {};
    if (errorResponse.status == 0) {
      let errorMessage1 = 'Could not connect to network. please check and try again later.';
      this._commonService.error(errorMessage1);
    }
    else {
      if (errorResponse.error instanceof ErrorEvent) {
        // client-side error
        errorMessage = `${errorResponse.error}`;
        // this.showNotificationMessage(errorMessage);
      } else {
        // server-side error
        errorMessage = `${errorResponse.error}`;
        // this.showNotificationMessage(errorMessage);

      }
    }
    return throwError(errorMessage);
  }

  public hasLoggedUserPermitted(roleName: ROLE): boolean {
    let userInfo = JSON.parse(localStorage.getItem("currentUser"));
    const found = userInfo.role.split(',').includes(roleName);
    return found;
  }
}
