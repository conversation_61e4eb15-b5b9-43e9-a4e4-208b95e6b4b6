import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { User } from 'app/core/user/user.model';

import { AuthService } from '../../../services/auth.service';

@Injectable({
    providedIn: 'root'
})
export class AuthGuard implements CanActivate {
    constructor(
        private _authService: AuthService,
        private _router: Router
    ) {
    }
    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
        let isCanActivate: boolean = false;
        let user: User = this._authService.getLoggedInUser()
        if (user && !user.mfaEnable) {
            isCanActivate = true;
        } else if (user && user.mfaEnable && this._authService.isMfaCodeHasPinValue()) {
            isCanActivate = true;
        }
        else {
            this._router.navigate(['/auth/sign-in'], { queryParams: { returnUrl: state.url } });
            isCanActivate = false;
        }
        return isCanActivate;
    }
}
