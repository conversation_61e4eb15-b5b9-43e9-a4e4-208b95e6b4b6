<tfl-table-search-grid style="width: 100%; height: 100%" [tableHeadingName]="'CAPA'" [addButtonLabel]="'Add'"
  [showGlobalSearchFilter]="true" [showFilter]="false" [showRefresh]="true" [rowData]="capaScheduleList"
  [fromDate]="defaultfromDate" [toDate]="defaultToDate" [showImport]="false" [showExport]="true" [showAdd]="false"
  [showFilterList]="false" [ShowFromToDateFilters]="true" [fromDate]="defaultfromDate" [toDate]="defaultToDate"
  [tableColumnsDef]="columnDefs" [showCsvExport]="false" [receiveUrlsNotificaiton]="sendUrlsNotification.asObservable()"
  (emitAddEvent)="getAddEvent($event)" (onRefresh)="getRefreshEvent($event)" (emitFilterEvent)="getFilterEvent($event)"
  (emitImportEvent)="getImportEvent($event)" (emitExportEvent)="onExportEvent()"
  (emitRowClickEvent)="getRowClickEvent($event)" (emitOnGetDate)="getOnGetDate($event)"
  (emitActionEvent)="getActions($event)" (emitPostResponseEvent)="getPostReponse($event)"></tfl-table-search-grid>