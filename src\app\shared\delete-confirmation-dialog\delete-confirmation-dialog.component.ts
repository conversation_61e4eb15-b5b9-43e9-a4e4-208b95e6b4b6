import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { environment } from 'environments/environment';
import { ToastrService } from 'ngx-toastr';

export interface DialogData {
  apiData: {
    url: string,
    data:any,
    dynamicMsg?:string
    successMessage?:string
  };
}
@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './delete-confirmation-dialog.component.html',
  styleUrls: ['./delete-confirmation-dialog.component.scss']
})
export class DeleteConfirmationDialogComponent implements OnInit {
  basicAuth = this.globals.basicAuth;
  name:string='';
  
  constructor(
    public dialogRef: MatDialogRef<DeleteConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    private http: HttpClient,
    private globals: Globals,
    private toastr: ToastrService,
  ) { }

  ngOnInit(): void {
    this.name= (this.data.apiData?.data?.code|| this.data.apiData?.data?.data?.fullName ) || ' ';
  }

  public deleteAfterConfirmation(): void {
    const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth).set('Client', tenantUid)
    this.http.delete(environment.apiUrl + this.data.apiData.url, { headers: headers }).subscribe((res: any) => {
      if (res && res.code == 200) {
        if(this.data.apiData.successMessage){
          this.toastr.success(this.data.apiData.successMessage)
        }else
        this.toastr.success(res.message ? res.message : 'Record Deleted')
      } else {
        this.toastr.warning(res.message ? res.message : 'Unable to delete the record')
      }
      this.dialogRef.close(true);
    }, error => {
      this.toastr.error(error.message ? error.message : 'Unable to delete the record')
      this.dialogRef.close(true);
    })
  }

}
