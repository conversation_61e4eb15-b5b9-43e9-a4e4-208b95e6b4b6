import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Status } from 'app/core/qinspect/_model/status.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { environment } from 'environments/environment';
import { FileSystemDirectoryEntry, FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';


export interface DialogData {
  apiData: string;
  dailogHeadig: string;
  message: { success: string, error: string },
  dropFileslabel: string
  formDatakey: any,
  showDownloadTemplateOption: boolean,
  forSourcingRequest:boolean
}

@Component({
  selector: 'app-po-upload-file',
  templateUrl: './po-upload-file.component.html',
  styleUrls: ['./po-upload-file.component.scss']
})
export class PoUploadFileComponent implements OnInit {
  docketNo:string;
  public files: NgxFileDropEntry[] = [];
  status: Status = new Status();
  headingLabel: string;
  apiData: any;
  messages: DialogData['message']
  basicAuth = this.globals.basicAuth;
  dropFileslabel: string;
  formDatakey: string;
  showDownloadTemplateOption: boolean = true;
  selecteddocType:string;
  docTypelist:string[]=['Image','Csv','Pdf','Other']
  constructor(
    public dialogRef: MatDialogRef<PoUploadFileComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    private http: HttpClient,
    private globals: Globals,
    private toastr: ToastrService,
  ) { }

  ngOnInit(): void {
    this.headingLabel = this.data.dailogHeadig;
    this.apiData = this.data.apiData;
    this.messages = this.data.message;
    this.dropFileslabel = this.data.dropFileslabel;
    this.formDatakey = this.data.formDatakey;
    this.showDownloadTemplateOption = this.data.showDownloadTemplateOption;
  }

  public fileOver(event) {
    console.log(event);
  }

  public fileLeave(event) {
    console.log(event);
  }

  deleteFile(i) {
    this.files.splice(i, 1)
    this.status = new Status()
  }

  public setFiles(files: NgxFileDropEntry[]) {
    this.files = files
  }

  public uploadFiles(): void {
    const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
    for (const droppedFile of this.files) {
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          const formData = new FormData()
          formData.append('file', file, droppedFile.relativePath);
          if (this.formDatakey) {
            formData.append(this.formDatakey, JSON.stringify({}))
          }
          let headers = new HttpHeaders()
            .set("Authorization", this.basicAuth).set('Client',tenantUid);
          let baseUrl = environment.apiUrl;
          if (this.apiData.externalUrl) {
            baseUrl = environment.apiUrl;
          }
          if (this.apiData.type == 'POST') {
            this.http.post(baseUrl + this.apiData.url, formData, { headers: headers, responseType: 'json' })
              .subscribe(data => {
                this.status = data as Status
                if (this.status.code == '200') {
                  this.toastr.success(this.status.message);
                  this.files = [];
                  this.dialogRef.close(data);
                } else {
                  this.toastr.warning(this.status.message);
                }
              }, error => {
                if (error) {
                  this.toastr.error(error.error);
                } else {
                  this.toastr.error(this.status.message);
                }
              })
          }
          if (this.apiData.type == 'PUT') {
            this.http.put(baseUrl + this.apiData.url, formData, { headers: headers, responseType: 'json' })
              .subscribe(data => {
                this.status = data as Status
                if (this.status.code != '500') {
                  this.toastr.success(this.messages.success);
                  this.files = [];
                  this.dialogRef.close(data);
                } else {
                  this.status.message = "";
                  this.toastr.warning(this.status.message);
                }
              }, error => {
                if (error) {
                  this.toastr.error(error.error);
                } else {
                  this.toastr.error(this.messages.error);
                }
              })
          }

        });
      } else {
        // It was a directory (empty directories are added, otherwise only files)
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
        console.log(droppedFile.relativePath, fileEntry);
      }
    }
  }

  public sendDocketNo(){
    this.dialogRef.close(this.docketNo);
  }


}
