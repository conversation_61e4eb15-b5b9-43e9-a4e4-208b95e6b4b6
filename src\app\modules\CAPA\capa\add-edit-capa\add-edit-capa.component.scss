// Variables
$primary-color: #517ebc;
$transition-speed: 0.3s;

::ng-deep .text-align-left .ag-cell-wrapper {
    justify-content: flex-start !important;
    text-align: left !important;
}

::ng-deep .CRITICAL {
    justify-content: flex-start !important;
    text-align: left !important;
    border-left: 5px solid #DF6B52 !important;
    border-right-color: white !important;
    min-height: 40px;
    height: 100%;
    //display: inline-table;
}

::ng-deep .MAJOR {
    justify-content: flex-start !important;
    text-align: left !important;
    border-left: 5px solid #D6CA4D !important;
    border-right: 5px solid white !important;
    min-height: 40px;
    height: 100%;
    //display: inline-table;
}

::ng-deep .MINOR {
    justify-content: flex-start !important;
    text-align: left !important;
    border-left: 5px solid #FFC300 !important;
    border-right: 5px solid white !important;
    min-height: 40px;
    height: 100%;
    // display: inline-table;
}

::ng-deep .DEFAULT {
    justify-content: flex-start !important;
    text-align: left !important;
    border-left: 5px solid #D7DBDD !important;
    border-right: 5px solid white !important;
    min-height: 40px;
    height: 100%;
    //display: inline-table;
}

::ng-deep .default-look {
    min-height: 40px;
    height: 100%;
}

.toggle_button_wrapper {
    // background-color: #0d6efd;
    // border-radius: 5px;
    // padding: 0px 8px;
    // color: white;
    cursor: pointer;
    margin-right: 8rem;
    font-size: 32px;
    margin-left: 5px;
}

.full-screen-iocn {
    position: absolute;
    right: -8rem;
    top: -23px;
    color: royalblue;
    font-size: 1.7rem;
}

.i-mr {
    margin-top: 2px;
}

.blueCol {
    color: #517ebc
}

.subBtn {
    position: fixed;
    right: 0px;
}

.capaItemWrapper {
    border-radius: 7px;
    border: 3px solid #e2e8f0;
    box-shadow: -5px -5px 9px rgba(255, 255, 255, 0.45), 5px 5px 9px rgba(94, 104, 121, 0.3);
    margin-bottom: 1rem;
}

.bulletCircular {
    height: 1rem;
    width: 1rem;
    border-radius: 50%;
    background-color: #5888c7;
    border: 4px solid #9ec3f3;
}

.clauseValue {
    color: #6796db;
    /* font-size: 1.1rem; */
    font-weight: 600;
    letter-spacing: 1px;
    padding-left: 1rem;
}

.acceptIcon {
    font-size: 1.3rem;
    color: #22c55e;
    cursor: default;
    font-weight: bold;
    background: white;
    padding: 6px;
    border-radius: 50%;
}

.float-end-btn {
    position: fixed;
    position: fixed;
    width: 45px;
    height: 45px;
    bottom: 44%;
    right: 18px;
    background-color: #517ebc;
    color: #FFF;
    border-radius: 50%;
    text-align: center;
    z-index: 999;
    border: 4px solid #e1e3e5;
}

.float-top-btn {
    position: fixed;
    position: fixed;
    width: 45px;
    height: 45px;
    bottom: 50%;
    right: 18px;
    background-color: #517ebc;
    color: #FFF;
    border-radius: 50%;
    text-align: center;
    z-index: 999;
    border: 4px solid #e1e3e5;
}

.float-top-btn:hover,
.float-end-btn:hover {
    transform: scale(1.1);
}

.departments-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;

    .department-chip {
        background-color: rgba(81, 126, 188, 0.1);
        padding: 0.1rem 0.5rem;
        border-radius: 0.25rem;
        color: #5989cf;
        font-size: 0.8rem;
        font-weight: bold;
        transition: all 0.3s ease;
        letter-spacing: 2px;
        border: 0.0625rem solid rgba(81, 126, 188, 0.2);

        &:hover {
            background-color: rgba($primary-color, 0.2);
            transform: translateY(-0.125rem);
        }

        &.truncate {
            max-width: 11rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
        }
    }
}