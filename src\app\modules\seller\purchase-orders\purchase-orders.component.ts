import { HttpHeaders } from '@angular/common/http';
import { Component, ViewChild } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { FilterList } from 'app/core/qinspect/_model/filterList.model';
import { QiUser } from 'app/core/qinspect/_model/user.model';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import { environment } from 'environments/environment';

import { UploadFileComponent } from '../../../shared/upload-file/upload-file.component';

@Component({
  selector: 'app-purchase-orders',
  templateUrl: './purchase-orders.component.html',
  styleUrls: ['./purchase-orders.component.scss']
})
export class PurchaseOrdersComponent extends BaseTableSharedComponent {
//declarations:
 @ViewChild('gridPo') gridPo: any;

  basicAuth = this.globals.basicAuth;
  user: QiUser;
  poData: any[] = [];
  
  selectedStatusCode = "-2";
  columnDefs: any[] = [
    { headerName: "PO Number",  width: 150, maxWidth: 150, field: "purchaseOrderNo", sortable: true, unSortIcon:true, filter: 'agTextColumnFilter' },
    // { headerName: "Product Type", field: "productTypeName", sortable: true, unSortIcon:true, filter: 'agTextColumnFilter' },
    { headerName: "ASIN",  width: 130, maxWidth: 130, field: "designCodeCode", sortable: true, unSortIcon:true, filter: 'agTextColumnFilter' },
    { headerName: "ASIN Description", field: "description", sortable: true, unSortIcon:true, filter: 'agTextColumnFilter' },
    // { headerName: "GL Name", field: "productGroupName", sortable: true, unSortIcon:true, filter: 'agTextColumnFilter' },
    { headerName: "Quantity", field: "qty",  width: 150, maxWidth: 200, sortable: true, unSortIcon:true, filter: 'agTextColumnFilter' },    
    { headerName: "PO Creation Date",  width: 200, maxWidth: 200, 
      field: "poCreationDate", sortable: true, unSortIcon:true, filter: 'agTextColumnFilter', cellRendererFramework: CustomDateTooltipComponent, },
  ];  

  public init(): void {
    this.user = this._authService.getLoggedInUser();
    this.defaultfromDate.setDate(this.defaultToDate.getMonth() - 1);
    this.showTable = true;
    this.showAddEdit = false;
    APP_UI_CONFIG.purchaseOrders.getByStatus.paramList["id"] = this.selectedStatusCode;
    super.setBasUrl(APP_UI_CONFIG.purchaseOrders.getByStatus);
    super.init();
  }

  public getFilterList(event: any): void {
    let filterData: FilterList[] = event;
    filterData.forEach(item => {
      if (item.id == "fromDate") {
        this.FromToDate.from = item.value;
      }
      else if (item.id == "toDate") {
        this.FromToDate.to = item.value;
      } else if (item.id == "status") {
        this.selectedStatusCode = item.value;
      }
    })
    this.init();
  }

  public getImportEvent(event): void {
    this.openImportDialog();
  }

  public prepareActionIconButtons(row: any): string {
    let icons: string = '';
    if (row.status == 4 && (this.user.role.indexOf("VENDOR") != -1 || this.user.role.indexOf("FACTORY") != -1)) {
      icons = icons + this._commonService.createIcon('accept', 'Accept PO', 'price_check');
      icons = icons + this._commonService.createIcon('reject', 'Reject PO', 'block');
    }
    if (row.status == 0) {
      if (row.deleted == 1) {
        icons = icons + this._commonService.createIcon('activate', 'Activate', 'check_circle');
      }
      if (row.deleted == 0) {
        icons = icons + this._commonService.createIcon('deactivate', 'Deactivate', 'hide_source');
      }
    }
    icons = icons + this._commonService.createIcon('download', 'Download', 'download');
    return icons;

  }

  public getActions(event): void {
    let actionData: any = event;
    this.status = actionData.status;
    this.selectedData = actionData.data;
    if (actionData.status == 'edit') {
      this.showTable = false;
      this.showAddEdit = true;
    }
    else if (actionData.status == 'activate') {

    }
    else if (actionData.status == 'deactivate') {

    }
    else if (actionData.status == 'viewJson') {

    }
    
    else if (actionData.status == 'download') {
      let headers = new HttpHeaders({ 'Content-Type': 'application/csv' })
        .set("Authorization", this.basicAuth).set('Client', this._commonService.getTenantUid());;
      this.http.get(environment.apiUrl + 'saleOrder/po/' + this.selectedData.uuid + '/refdoc', {
        observe: 'response',
        responseType: 'blob',
        headers: headers
      }).subscribe(res => {
        this.fileSaverService.save((<any>res).body, this.selectedData.purchaseOrderNo + ".pdf");
      });
    }
  }

  private openImportDialog(): void {
    const dialogRef = this.dialog.open(UploadFileComponent, {
      width: '70%',
      data: { dailogHeadig: 'Import Purchase orders', apiData: { 'url': 'saleOrder/po/bulkImport', type: "POST", }, message: { success: 'Purchase Order import successfully', error: 'Purchase Order not imported' }, dropFileslabel: 'purchase order' },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.init();
      }
    });
  }
}
