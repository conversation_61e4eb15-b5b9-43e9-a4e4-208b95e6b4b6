import { <PERSON><PERSON><PERSON><PERSON> } from './auditor.model';
import { Qi<PERSON>uditorIncharge } from './auditorincharge.model';
import { AuditType } from './AuditType.model';
import { QiBrand } from './brand.model';
import { QiCategory } from './category.model';
import { QiFactory } from './factory.model';
import { QiCustomer } from './qicustomer.model';
import { QiVendor } from './vendor.model';

export interface iDefect {
  id?: string;
  auditId?: string;
  sampleId?: string;
  stylecodeId?: string;
  stylecode?: string;
  category?: string;
  defectCode?: string,
  severity?: string;
  imageUrl?: string;
  comments?: string;
}

export interface iDefectSample {
  sampleid?: string;
  defects?: iDefect[];
}

export interface iCategroryDefects {
  category?: string;
  samples?: iDefectSample[];
}

export interface iResultsData {
  id?: number;
  formId?: string;
  value?: string;
  tenantId?: number;
  tenantUid?: string;
  auditId?: string;
}

interface ISizecode {
  auditId: string,
  createdTime: number,
  designcode: string,
  id: number,
  modifiedTime: number,
  offerQty: string,
  orderQty: string,
  parentTenantUid: string,
  sizeCode: string,
  tenantId: number,
  tenantUid: string,
  uuid: string
}

export interface iAuditDesignCode {
  auditId?: string,
  brandUid?: string,
  carton?: string,
  channel?: string,
  comments?: string,
  createdBy?: string,
  createdTime?: number,
  deleted?: number,
  description?: string,
  designcode?: string,
  fabricCode?: string,
  goldSeal?: string,
  id?: number,
  modifiedBy?: string,
  modifiedTime?: number,
  offerQty?: number,
  offerQty2?: number,
  offerQty3?: number,
  orderQty?: number,
  parent?: string,
  parentTenantUid?: string,
  poNumber?: string,
  productTypeUid?: string,
  size?: string,
  sizecodes?: ISizecode[],
  status?: number,
  tenantId?: number,
  tenantUid?: string,
  uuid?: string
};

export class QiAuditDesignCode implements iAuditDesignCode {
  constructor(
    public auditId?,
    public brandUid?,
    public carton?,
    public channel?,
    public comments?,
    public createdBy?,
    public createdTime?,
    public deleted?,
    public description?,
    public designcode?,
    public fabricCode?,
    public goldSeal?,
    public id?,
    public modifiedBy?,
    public modifiedTime?,
    public offerQty = 0,
    public offerQty2 = 0,
    public offerQty3 = 0,
    public orderQty = 0,
    public parent?,
    public parentTenantUid?,
    public poNumber?,
    public productTypeUid?,
    public size = "0",
    public sizecodes?,
    public status?,
    public tenantId?,
    public tenantUid?,
    public uuid?
  ) { }
}

export interface iSchedule {
  auditDate?: number;
  submittedDate?: number;

  auditor?: QiAuditor;
  auditorIncharge?: QiAuditorIncharge;
  vendor?: QiVendor;
  factory?: QiFactory;
  actualFactory?: QiFactory;
  auditType?: AuditType;
  brand?: QiBrand;
  customer?: QiCustomer;
  category?: QiCategory;
  _designcode?: QiAuditDesignCode,
  designcodes?: QiAuditDesignCode[];
  results?: iResultsData[];

  goldSealNo?: string;
  productType?: string;
  productTypeUid?: string;
  seasonAndYear?: string;
  status?: number;
  lastModified?: number;
  size?: string;
  result?: string;
  revokedResult?: string;
  revokeApprovedBy?: string;
  revokedComments?: string;
  auditId?: string;
  reportId?: string;
  type?: string;

  auditDateText?: string;
  submittedDateText?: string;
  statusText?: string;

  defects?: iDefect[]; //iCategroryDefects[];
  inspectionAgencyUid?: string,
  labUid?: string
  auditDesignCodes?: iAuditDesignCode[],
  _auditDesignCode?: iAuditDesignCode,
  uuid?: string,
  auditEndTime?: string,
  auditStartTime?: string,
  childSeqId?: number,
  offeredQty?: number,
  orderedQty?: number,
  purchaseOrderNo?: string,
  salesOrderNo?: string,
  salesOrderItem?: string,
  auditFlowReason?: string,
  auditFlowRemarks?: string,
  shipmentNo?: string,
  shipmentDestination?: string,
  comments?: string
  // labUid?: string
  // inspectionAgencyUid?: string
  // type?: string
}

export class QiSchedule implements iSchedule {
  constructor(
    public auditDate?,
    public submittedDate?,
    public auditor?,
    public auditorIncharge?,
    public vendor?,
    public factory?,
    public actualFactory?,
    public auditType?,
    public brand?,
    public customer?,
    public category?,
    public productTypeUid?,
    public productTypeObj?,
    public _designcode?,
    public designcodes?,
    public results?,
    public auditId?,
    public goldSealNo?,
    public productType?,
    public seasonAndYear?,
    public status?,
    public lastModified?,
    public size?,
    public result?,
    public revokedResult?,
    public revokeApprovedBy?,
    public revokedComments?,
    public reportId?,
    public auditDateText?,
    public submittedDateText?,
    public statusText?,
    public defects?,
    public purchaseOrderItem?,
    public purchaseOrderNo?,
    public approver?,
    public createdTime?,
    public type?,
    public auditDesignCodes?,
    public _auditDesignCode?,
    public inspectionAgencyUid?,
    public labUid?,
    public uuid?,
    public auditEndTime?,
    public auditStartTime?,
    public childSeqId?,
    public offeredQty?,
    public orderedQty?,
    public salesOrderNo?,
    public salesOrderItem?,
    public buyerPo?,
    public auditFlowReason?,
    public auditFlowRemarks?,
    public shipmentNo?,
    public shipmentDestination?,
    public inchargeUid?,
    public comments?,
    public contactNo?,
    public altemail?,
    public designation?
  ) { }
};

export const NEWSCH = {
  "auditor": { "uuid": 0 },
  "vendor": { "uuid": 0 },
  "factory": { "uuid": 0 },
  "auditType": { "uuid": 0 },
  "category": { "uuid": 0 },
  "customer": {},
  "designcodes": [
    {
      "goldSeal": "",
      "size": "0",
      "designcode": "NA",
      "poNumber": "",
      "orderQty": 0,
      "offerQty": 0,
      "channel": "0",
      "carton": "0",
      "comments": "",
      "status": 0,
      "productTypeUid": 0,
      "brandUid": 0,
    }
  ],
  "status": 0,
  "auditDate": "",

};

export const AMENDSCH = {
  "auditor": { "uuid": 0 },
  "vendor": { "uuid": 0 },
  "factory": { "uuid": 0 },
  "auditType": { "uuid": 0 },
  "seasonAndYear": "",
  "category": { "uuid": 0 },
  "designcodes": [
    {
      "id": 0,
      "auditId": "",
      "designcode": "",
      "poNumber": "",
      "orderQty": 0,
      "offerQty": 0,
      "offerQty2": 0,
      "offerQty3": 0,
      "goldSeal": "",
      "size": "0",
      "channel": "0",
      "carton": "0",
      "productTypeUid": 0,
      "brandUid": "",
      "comments": "",
      "status": 0,
      "createdOn": 0,
      "modifiedOn": 0
    }
  ],
  "status": 0,
  "auditId": "",
  "auditDate": ""
};
