import { HttpClient, HttpClientModule } from '@angular/common/http';
import { <PERSON>ttpEvent, HttpHandler, HttpHeaders, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';
import { environment } from 'environments/environment';
import { ToastrService } from 'ngx-toastr';
import { Observable } from 'rxjs';

import { CustomDashboard } from '../_model/CustomDashboard.model';
import { Status } from '../_model/Status.model';
import { Appconfig, Globals } from '../appconfig/appconfig.component';


@Injectable({
  providedIn: 'root'
})
export class GlobalActionsService {

  http: HttpClient;
  basicAuth = this.globals.basicAuth;

  constructor(private httpin: HttpClient, private globals:Globals, private toastr: ToastrService) {
    this.http = httpin;
  }

  publishChanges(): Observable<Status> {
    const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': tenantUid})
      .set("Authorization", this.basicAuth);

    return this.http.put<Status>( environment.apiUrl+'delta/commit/', null, {
        headers:headers
      });
  }


}
