import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ThemePalette } from '@angular/material/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { InspectionAgency } from 'app/core/qinspect/_model/InspectionAgency.model';
import { NEWSCH, QiSchedule } from 'app/core/qinspect/_model/schedule.model';
import { ShipmentDto } from 'app/core/qinspect/_model/ShipmentDto.model';
import { QiUser } from 'app/core/qinspect/_model/user.model';
import { Globals } from 'app/core/qinspect/appconfig/appconfig.component';
import { environment } from 'environments/environment';

import { CommonService } from '../../../../services/common.service';
import { InvokeService } from '../../../../services/invoke.service';
import { SaleOrderService } from '../../services/sale-order.service';
import { ScheduleCommonService } from '../../services/schedule-common.service';

@Component({
  selector: 'create-edit-purchase-order',
  templateUrl: './create-edit-purchase-order.component.html',
  styleUrls: ['./create-edit-purchase-order.component.scss']
})
export class CreateEditPurchaseOrderComponent implements OnInit {
  auditDt: any;
  schdMinDt: Date = new Date();
  auditDtText: any = new Date();
  auditTypeList: any[] = [];
  inspectionAgencyList: any[] = [];
  selectedVendor: any;
  rec: QiSchedule;
  user: QiUser;
  inspectionAgency: InspectionAgency;
  shipmentDto: ShipmentDto = new ShipmentDto();
  userRole: string = '';
  schedule: QiSchedule;
  hdr: string;
  submitDisabled: boolean = false;
  @Input() data: QiSchedule;
  @Input() fromCalendar: boolean = false;
  @Input() startDate: any;
  @Input() disableSaveButton: boolean = false;
  @Output() closeEvent: EventEmitter<any> = new EventEmitter();
  enableMeridian: any = true;
  public color: ThemePalette = 'primary';
  @ViewChild('picker') picker: any;
  public startDateControl = new FormControl(new Date());
  factory = 'Factory';

  constructor(
    public common: ScheduleCommonService,
    public globals: Globals,
    private saleOrderService: SaleOrderService,
    public _invokeService: InvokeService,
    public commonService: CommonService
  ) {
    this.user = this.globals.getUser();
    this.userRole = this.user.role;
  }

  ngOnInit(): void {
    let today = new Date(); // new Date();
    if (this.commonService.getTenantUid() == 'lots') {
      this.factory = 'Store';
    }
    today.setMinutes(0, 0, 0);
    this.common.clearData();
    this.common.getData();
    //this.auditDt = new Date(today);
    this.auditDt=(new Date(today));
    this.schedule = this.data;
    if (this.schedule?.auditId) {
      this.rec = this.common.copyForUpdate(this.schedule);
      this.hdr = 'Update Schedule';
      let dt = new Date(this.rec.auditDate);
      // this.auditDt = dt;//new String(dt.getFullYear+"-"+dt.getMonth()+"-"+dt.getDay());
      this.auditDt=(dt);
      this.schdMinDt = new Date(this.rec.auditDate);
      this.auditDtText = dt.getFullYear() + "-" + dt.getMonth() + "-" + dt.getDay();
    } else {
      this.rec = <QiSchedule>Util.clone(NEWSCH);
      this.rec.designcodes[0].designcode = 'NA';
      this.hdr = 'Add Schedule';
      if (this.userRole === 'VENDOR') {
        this.auditDt=(new Date(today.getTime() + 86400 * 1 * 1000));
      }
      if (this.fromCalendar == false) {
        //this.auditDt = new Date(today.getTime() + 86400 * 1 * 1000); // two days from today
        this.schdMinDt = new Date(this.auditDt);
        this.schdMinDt.setDate(this.schdMinDt.getDate() + 2);
        // this.auditDt = this.schdMinDt;
        this.auditDt=(this.schdMinDt);
      } else {
        let startDate = this.startDate as string;
        this.schdMinDt = new Date(startDate);
        this.schdMinDt.setDate(this.schdMinDt.getDate());
        this.auditDt=(this.schdMinDt);
      }

    }
    if (this.userRole === 'VENDOR') {
      let schdMinDt1 = today.getTime() + 86400 * 1 * 1000; // two days from today
      let schdMinDt2 = this.schdMinDt.getTime();
      if (schdMinDt2 < schdMinDt1) this.schdMinDt = new Date(schdMinDt1);
    }
    this.setFormData();
  }
  private setFormData() {
    if (this.rec.auditType.uuid != 0) { this.common.auditTypeList.setValue(this.rec.auditType.uuid); }
    if (this.userRole === 'AUDITOR') this.common.auditorList.setValue(this.user.uuid);
    else this.common.auditorList.setValue(this.rec.auditor.uuid);
    if (this.userRole === 'VENDOR') {
      this.common.vendorList.setValue(this.user.uuid);
      let vendor = this.common.vendorList?.getData()?.find(obj => obj.uuid == this.user.uuid)
      if (vendor) {
        this.selectedVendor = vendor.code + ' - ' + vendor.firstName;
        this.common.vendorList.setValue(vendor.uuid);
      }
    } else {
      this.common.vendorList.setValue(this.rec.vendor.uuid);
      let vendor = this.common.vendorList?.getData()?.find(obj => obj.uuid == this.rec.vendor.uuid)
      if (vendor) {
        this.selectedVendor = vendor.code + ' - ' + vendor.firstName;
        this.common.vendorList.setValue(vendor.uuid);
      }
    }
    this.common.factoryList.setValue(this.rec.factory.uuid);
    if (this.rec.seasonAndYear) this.common.setSeasonAndYear(this.rec.seasonAndYear);

    this.common.categoryList.setValue(this.rec.category.uuid);
    this.common.brandList.setValue(this.rec.designcodes[0].brandUid);
    this.common.customerList.setValue(this.rec.designcodes[0].customerUid);
    this.common.productTypeList.setValue(this.rec.designcodes[0].productTypeUid);

  }

  isEditable(sch: QiSchedule) {
    if (sch == null || sch == undefined) return false;
    if (sch.status == 10) return true;
    if (sch.status == 1 || sch.status == 3) return this.userRole === 'ADMIN';
    // if (this.userRole === 'VENDOR') return false;
    return sch.statusText != 'Deleted';
  }


  public getByPO() {
    this.saleOrderService.getShipmentByPo(this.rec.designcodes[0].poNumber).subscribe(data => {
      this.shipmentDto = data
      if (this.userRole === 'AUDITOR') this.common.auditorList.setValue(this.user.uuid);
      //      else this.common.auditorList.setValue(this.shipmentDto.auditor.uuid);

      if (this.userRole === 'VENDOR') this.common.vendorList.setValue(this.user.uuid);
      else {
        if (this.shipmentDto.vendor && this.shipmentDto.vendor.uuid)
          this.common.vendorList.setValue(this.shipmentDto.vendor.uuid);
      }

      if (this.shipmentDto.factory && this.shipmentDto.factory.uuid)
        this.common.factoryList.setValue(this.shipmentDto.factory.uuid.toString());

      //this.common.categoryList.setValue(this.shipmentDto.category.uuid);
      if (this.shipmentDto.brand && this.shipmentDto.brand.uuid)
        this.common.brandList.setValue(this.shipmentDto.brand.uuid.toString());
      //this.common.productTypeList.setValue(this.rec.designcodes[0].productTypeUid);   
      if (this.shipmentDto.catalogUid)
        this.rec.designcodes[0].designcode = this.shipmentDto.catalogUid;
      if (this.shipmentDto.salesOrder && this.shipmentDto.salesOrder.description)
        this.rec.designcodes[0].description = this.shipmentDto.salesOrder.description;



      //this.rec.designcodes[0].productTypeUid = this.common.productTypeList.selectedItem;   
    });
  }

  public onFactorySelect() {
    let id = this.common.factoryList.selectedItem;
    let selectedFactory = this.common.factoryList.getData().find(obj => obj.uuid.toString() == id)
    let factoryVendor = this.common.vendorList.getData().find(obj => obj.uuid == selectedFactory.vendorUid);//replace 64 with selectedFactory.vendorId
    if (factoryVendor) {
      this.selectedVendor = factoryVendor.uuid + ' - ' + factoryVendor.firstName;
      this.common.vendorList.selectedItem = factoryVendor.uuid
    } else {
      this.selectedVendor = '';
      this.common.vendorList.setValue('');
    }
  }

  public upperCaseStyleCode(event) {
    if (event.inputType == 'deleteContentBackward') return;
    let letters = /^[0-9a-zA-Z,]+$/;
    if (event.inputType == 'insertFromPaste') {
      if (this.rec.designcodes[0].designcode.match(letters)) {
        this.rec.designcodes[0].designcode = this.rec.designcodes[0].designcode.toUpperCase();
        return;
      } else {
        setTimeout(() => {
          this.rec.designcodes[0].designcode = '';
          // this.detmsgs = [];
          // this.detmsgs.push({ severity: 'info', summary: 'Pasted code is invalid', detail: 'Invalid characters present, please check' });
          return;
        }, 1);
      }
    }
    if (event.inputType == 'insertText') {
      if (event.data.match(letters)) {
        this.rec.designcodes[0].designcode = this.rec.designcodes[0].designcode.toUpperCase();
      } else {
        setTimeout(() => {
          let str = this.rec.designcodes[0].designcode;
          this.rec.designcodes[0].designcode = str.substring(0, str.length - 1);
        }, 1);
      }
    }
  }

  getFormData() {
    this.rec.auditType.uuid = this.common.auditTypeList.selectedItem;
    this.rec.seasonAndYear = this.common.getSeasonAndYear();
    this.rec.auditor.uuid = this.common.auditorList.selectedItem;
    this.rec.factory.uuid = this.common.factoryList.selectedItem;
    this.rec.vendor.uuid = this.common.vendorList.selectedItem;
    this.rec.category.uuid = this.common.categoryList.selectedItem;
    if (this.common.brandList.selectedItem != null && this.common.brandList.selectedItem != "0") {
      this.rec.brand.uuid = this.common.brandList.selectedItem;
      this.rec.designcodes[0].brandUid = this.common.brandList.selectedItem;
    } else {
      this.rec.brand = null;
      this.rec.designcodes[0].brandUid = null;
    }
    if (this.common.customerList.selectedItem != null && this.common.customerList.selectedItem != "0") {
      this.rec.customer.uuid = this.common.customerList.selectedItem;
      this.rec.designcodes[0].customerUid = this.common.customerList.selectedItem;
    } else {
      this.rec.customer = null;
      this.rec.designcodes[0].customerUid = null;
    }
    this.rec.designcodes[0].productTypeUid = this.common.productTypeList.selectedItem;
    this.rec.productTypeUid = this.common.productTypeList.selectedItem;
    this.rec.designcodes[0].goldSeal = this.rec.designcodes[0].designcode;
    this.rec.auditDate = this.auditDt.getTime();
    this.rec.goldSealNo = this.rec.designcodes[0].designcode;

  }

  public onSave() {
    this.submitDisabled = true;
    this.getFormData();
    if (this.rec.auditType.uuid == 0) {
      this.commonService.warning('Please select Audit Type');
      this.submitDisabled = false;
      return;
    }
    if (this.rec.auditor.uuid == 0) {
      this.commonService.warning('Please select Auditor');
      this.submitDisabled = false;
      return;
    }
    if (this.rec.factory.uuid == 0) {
      this.commonService.warning('Please select Factory');
      this.submitDisabled = false;
      return;
    }
    // if (this.rec.vendor.uuid == 0) {
    //   this.commonService.warning('Please select Vendor');
    //   this.submitDisabled = false;
    //   return;
    // }
    if (this.rec.designcodes[0].productTypeUid == 0) {
      this.commonService.warning('Please select Product Type');
      this.submitDisabled = false;
      return;
    }

    if (this.rec.auditId) {
      APP_UI_CONFIG.schedules.update.payload = this.common.copyForUpdate(this.rec);
      APP_UI_CONFIG.schedules.update.paramList['id'] = this.rec.auditId;
      this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.update, null, this.rec.auditId, this.common.copyForUpdate(this.rec)).subscribe(res => {
        if (res.code == 200) {
          this.commonService.success(res.message);
          this.close();
        } else {
          this.commonService.error(res.message);
        }
      }, error => {
        this.commonService.handleError(error);
      })
      this.submitDisabled = false;
    } else {
      this._invokeService.serviceInvocation(APP_UI_CONFIG.schedules.create, null, null, this.common.copyForUpdate(this.rec)).subscribe(res => {
        if (res.code == 200) {
          this.commonService.success(res.message);
          this.close();
        } else {
          this.commonService.error(res.message);
        }
      })
      this.submitDisabled = false;
    }
  }

  public close(): void {
    this.closeEvent.emit(true);
  }




}
