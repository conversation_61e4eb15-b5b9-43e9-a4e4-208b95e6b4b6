import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { FilterList } from 'app/core/qinspect/_model/filterList.model';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';
import { ROLE } from 'app/enum-constants';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { CustomDateTimeTooltipComponent } from 'app/shared/custom-date-time-tooltip/custom-date-time-tooltip.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { environment } from 'environments/environment';


export enum scheduleStatus {
  DELETED = -1,
  SCHEDULED = 0,
  SUBMITTED = 1,
  ABORTED = 2,
  REVOKED = 3,
  FAILED = 4,
  DRAFT = 5,
  DRAFT_COMPLETED = 6,
  DRAFT_FROM_SAP = 7,
  EXPIRED = 8,
  SAVE_OR_UPDATE = 9,
  PREVIEW = 10,
  STARTED = 11,
  CONFIRMED = 12,
  REVIEWED = 13,
  PARTIALLY_SUBMITTED = 14,
  EDITED = 15,//for Godrej done post Submit
  DIVERSION = 16, //for Godrej done post Submit
  L1_REVIEW = 17, // for audit review process
  L1_REVIEWED = 18,// for audit review process
  L2_REVIEW = 19, // for audit review process
  L2_REVIEWED = 20, // for audit review process
  AUDITOR_SUBMITTED = 23, // for audit review process
}

@Component({
  selector: 'app-audit-summary',
  templateUrl: './audit-summary.component.html',
  styleUrls: ['./audit-summary.component.scss']
})
export class auditSummaryComponent extends BaseTableSharedComponent {
  //declarations:
  scheduleList: any[] = [];
  columnDefs: any[] = [
    // {
    //   field: '',
    //   // headerCheckboxSelection: true,
    //   checkboxSelection: true,
    //   showDisabledCheckboxes: true,
    //   width: 52,
    //   minWidth: 52,
    // },
    {
      headerName: "Audit Date", field: "auditDateText", sortable: true, unSortIcon: true, minWidth: 130,
      width: 130,
      valueGetter: (data) => {
        return data?.data?.auditDateText?.substring(0, 10);
      },
    },   
    {
      headerName: "Agency", field: "agencyName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth:200, width: 200
    },
    {
      headerName: "Auditor", field: "auditorName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150
    },
    
    {
      headerName: "Vendor", field: "vendorName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 225, width: 225
    },
    {
      headerName: "Factory", field: "factoryName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 225, width: 225
    },
   
    {
      headerName: "Status", field: "statusText", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150
    },
    {
      headerName: "Audit Number", field: "auditNumber", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth:130,
    //   cellRenderer: (data) => {
    //     return this.prepareReferenceAuditLinks(data.value)
    //   },
    //   onCellClicked: (event) =>
    //     this.navigateToAuditPlanning(),
    },    
    {
      headerName: "Submitted Date",
      field: "submittedTimeText",
      sortable: true, unSortIcon: true,
      minWidth: 130,
      width: 130,
      valueGetter: (data) => {
        return data?.data?.submittedTimeText?.substring(0, 10);
      },
    },
    {
      headerName: "Audit ID", field: "auditId", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth:130,
    },
    {
      headerName: "Audit Type", field: "auditTypeName", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150,
    },
   
    // {
    //   headerName: "Design Code", field: "designcodes", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter', minWidth: 150, width: 150,
    //   valueGetter: (data) => {
    //     if (data?.data?.designcodes && data?.data?.designcodes.length > 0)
    //       return data?.data?.designcodes[0]?.designcode;
    //   },
    //   getQuickFilterText: params => {
    //     if (params?.data?.designcodes && params?.data?.designcodes.length > 0)
    //       return params?.data?.designcodes[0]?.designcode;
    //   }
    // },
    {
      headerName: 'Actions',
      pinned: 'right',
      minWidth: 200,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: (params) => {
        return { actions: this.prepareActionIconButtons(params.data) }
      }
    }
  ];
  filtersList: FilterList[] = [
    { id: "fromDate", type: 'date', label: 'From', defaultValue: this.defaultfromDate, value: this.defaultfromDate },
    { id: "toDate", type: 'date', label: 'To', defaultValue: this.defaultToDate, value: this.defaultToDate },
  ];
  title = "Inspection Schedules"
  type: string = "create";
  status: string = 'create';
  showSubmitAudit: boolean = false;
  submitTypeFromPopup: number;
  showAdd: boolean = true;
  public init(): void {
    
    this.defaultfromDate.setDate(this.defaultToDate.getDate() - 100);
    this.setDates(this.defaultfromDate, this.defaultToDate);
    this.selectedData = {};
    this.showTable = true;
    this.showAddEdit = false;
    this.showSubmitAudit = false;
    this.updateView();
    this.route.paramMap.subscribe(
      (routeParam) => {
        this.type = "fqa";
        this.serviceInvocation();
        this.updateView()
        this.changeDetectorRef.detectChanges();
      },
      (err) => { }
    );    
  }

  private updateView() {
    if (this.type == "testing") {
      this.title = "Lab Schedules"
    } else if (this.type == "fqa") {
      this.title = "Audit Summary"
    } else if (this.type == "inspection") {
      this.title = "Inspection Schedules"
    }
  }

  public prepareReferenceAuditLinks(referenceAsin: string) {
      return `<p class="mt-4" style="text-decoration: underline;color: rgb(50, 91, 226);">${referenceAsin}</p>`;
  }

  navigateToAuditPlanning():void{
    //this._router.navigate(['/master/auditPlanning/edit/a19081e7-eec1-438b-8a10-593257d37ff0'])
  }

  private serviceInvocation(): void {
    let params :any[]= this.prepareParameters();
    // params.push({ key: "mode", value: this.type });
    params.push({ key: "type", value: "FQA" })
    APP_UI_CONFIG.schedules.getSchedulesListByType.paramList.from = params[0].value;
    APP_UI_CONFIG.schedules.getSchedulesListByType.paramList.to = params[1].value;
    APP_UI_CONFIG.schedules.getSchedulesListByType.paramList.type = params[2].value;
    this.subscription.add(
      this.invokeService.serviceInvocation(APP_UI_CONFIG.schedules.getSchedulesListByType)
        .subscribe({
          next: response => {
            if (this.user.tenantUid == 'amazon') {
              this.scheduleList = response?.filter((item) => item.type?.toLowerCase() == this.type);
            } else {
              this.scheduleList = Util.clone(response)
            }
          },
          error: error => {
            this._commonService.handleError(error);
          }
        }))
  }

  public getFilterList(event: any): void {
    let filterData: FilterList[] = event;
    filterData.forEach(item => {
      if (item.id == "fromDate") {
        this.FromToDate.from = item.value;
      }
      if (item.id == "toDate") {
        this.FromToDate.to = item.value;
      }
    })
    this.serviceInvocation();
  }

  public getOnGetDate(event: any): void {
    this.FromToDate.from = event.from;
    this.FromToDate.to = event.to;
    this.serviceInvocation();
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];
    //iconsList.push({ type: 'history', title: "History", icon: 'history', data: row });
    // if(row.statusText =='Deleted' || row.statusText =='Aborted'){
    //   icons = icons + this._commonService.createIcon('viewAuditor', 'View', 'visibility');
    // }else{
    if (row.status != scheduleStatus.STARTED && row.status != scheduleStatus.SCHEDULED && row.status != scheduleStatus.DELETED && row.status != scheduleStatus.ABORTED) {
      iconsList.push({ type: 'download', title: "Download Report", icon: 'download_blue', data: row });
      iconsList.push({ type: 'review', title: "View Report", icon: 'done_gray', data: row });
    }
    //For now Commented this functionality for godrej tenenat
    // if (environment.tenantUid == 'godrej' && (row.status == 1 || row.status == 6 || row.status == 14)) {
    //   icons = icons + this._commonService.createIcon('download2nd', 'Download 2nd Report', 'download_for_offline');
    // }

    // if(row.status==1 || row.statusText == 'Draft Completed' && (row.auditType?.type == "FQA" || row.auditType?.type == "INSPECTION") ){
    //   icons = icons + this._commonService.createIcon('submit', 'Submit Audit', 'edit_note');
    // }

    if (ROLE.TEST_USER != this._commonService.getUseRole() && row.auditType?.portalSupport == 1 && (row.status == scheduleStatus.SUBMITTED || row.status == scheduleStatus.EDITED || row.status == scheduleStatus.DIVERSION) && this._commonService.getTenantUid() == 'godrej') {
      iconsList.push({ type: 'submit', title: "Update Audit", icon: 'edit', data: row });
    }
    if (ROLE.TEST_USER != this._commonService.getUseRole() && row.statusText == 'Completed') {
      iconsList.push({ type: 'mail', title: "Email Report", icon: 'email_blue', data: row });
    }
    if (ROLE.TEST_USER != this._commonService.getUseRole() && this.type == 'testing' && row.statusText != 'Scheduled') {
      iconsList.push({ type: 'edit', title: "Testing", icon: 'play', data: row });
    }
    //else if(row.statusText!='Deleted' && row.statusText!='Aborted'){
    //   if(this.mode == 'lab' ){
    //     icons = icons + this._commonService.createIcon('updateLab', 'Edit', 'edit');  
    //   }else{
    //     icons = icons + this._commonService.createIcon('updateAuditor', 'Edit', 'edit');  
    //   }
    // }    
    // if (row.statusText != 'Started' && row.statusText!='Deleted' && row.statusText!='Aborted'){
    // icons = icons + this._commonService.createIcon('viewAuditor', 'View', 'visibility');
    //   if(row.statusText != 'Completed'){
    //   icons = icons + this._commonService.createIcon('assign', 'Assign Auditor', 'assignment_ind');
    //   }
    // }
    //}


    return iconsList;
  }

  public getActions(event): void {
    let actionData: any = event;
    this.selectedData = actionData.data;
    if (actionData.status == 'download') {
      this.downloadReport(actionData.data)
    }

    if (actionData.status == 'download2nd') {
      this.downloadReport2nd(actionData.data)
    }

    if (actionData.status == 'edit') {
      this._commonService.navigate('testing/testingProcessByCategory/' + actionData.data.auditId);
    }

    if (actionData.status == 'mail') {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: 'Confirm sending email ?',
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          APP_UI_CONFIG.schedules.sendReport.paramList['id'] = actionData.data.auditId
          this.invokeService.serviceInvocation(APP_UI_CONFIG.schedules.sendReport).subscribe(res => {
            this._commonService.success('Report sent successfully.');
          });
        }
      });

    }
    if (actionData.status == 'viewAuditor') {
      this.status = 'view';
      this.showTable = false;
      this.showAddEdit = true;
    }
    if (actionData.status == 'updateAuditor') {
      this.status = 'edit';
      this.selectedData = event.data
      this.showTable = false;
      this.showAddEdit = true;
    }
    if (actionData.status == 'updateLab') {
      this.status = 'edit';
      this.selectedData = event.data
      this.showTable = false;
      this.showAddEdit = true;
    }
    if (actionData.status == 'assign') {
      this.status = 'assign';
      this.selectedData = event.data
      this.showTable = false;
      this.showAddEdit = true;
    }

    if (actionData.status == 'submit') {

      APP_UI_CONFIG.schedules.canUpdateReport.paramList['id'] = actionData.data.auditId;
      this.invokeService.serviceInvocation(APP_UI_CONFIG.schedules.canUpdateReport, null, actionData.data.auditId, null).subscribe(res => {
        if (event.data?.auditType?.postSubmitFlow == 1 && res.code == 200) {
          const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            data: 'How do you want to update the audit ? fromInspectionPage',
          });

          dialogRef.afterClosed().subscribe(result => {
            if (result) {
              this.submitTypeFromPopup = result;
              this.showSubmitComp(event);
            }
          });
        } else if (res.code == 400) {
          this._commonService.error(res.message);
        }
      },
        (error) => {
          this._commonService.error('Something went wrong, Please try again later.');
        },
        () => {

        });
    }

    if(actionData.status == 'review'){
      if(actionData.data.status == 17 || actionData.data.status == 6 || actionData.data.status == 19 || actionData.data.status == 23 || actionData.data.status == 1){
        this._router.navigate(['/schedules/reviewProcess/edit/' + this.selectedData.auditId])
      }
      else{
        this._commonService.warning('Report may not generated yet.');
      }
    }
   

    // if (actionData.status == 'download') {
    // 	this.downloadReport(actionData.data)
    // }
    // if (actionData.status == 'download') {
    // 	this.downloadReport(actionData.data)
    // }

  }

  showSubmitComp(event) {
    this.status = 'submit';
    this.selectedData = event.data
    this.showTable = false;
    this.showAddEdit = false;
    this.showSubmitAudit = true;
  }

  downloadReport(row) {
    let reportUrl = URL_CONSTANTS.BASE_URL + URL_CONSTANTS.AUDIT_GETREPORT + '/' + row.auditId + '.pdf';
    window.open(reportUrl, '_blank');
    //return reportUrl;
  }

  downloadReport2nd(row) {
    let reportUrl = URL_CONSTANTS.BASE_URL + 'audit/getsecondreport' + '/' + row.auditId + '.pdf';
    window.open(reportUrl, '_blank');
    //return reportUrl;
  }

  public getcloseEvent(event): void {
    this.showSubmitAudit = false;
    this.serviceInvocation();
    super.closeAddEdit();
  }

}