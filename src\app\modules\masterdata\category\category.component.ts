import { Component } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/qinspect/_common/util';
import { QiCategory } from 'app/core/qinspect/_model/category.model';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { environment } from 'environments/environment';

import {
  DeleteConfirmationDialogComponent,
} from '../../../shared/delete-confirmation-dialog/delete-confirmation-dialog.component';
import { AddEditCategoryComponent } from './add-edit-category/add-edit-category.component';

@Component({
  selector: 'app-category',
  templateUrl: './category.component.html',
  styleUrls: ['./category.component.scss']
})
export class CategoryComponent extends BaseTableSharedComponent {

  //declarations:
  categoryList: any;
  columnDefs: any[] = [
    // {
    //   field: '',
    //   // headerCheckboxSelection: true,
    //   checkboxSelection: true,
    //   showDisabledCheckboxes: true,
    //   width: 25,
    //   minWidth: 25,
    // },
    { headerName: "Code", field: "code", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    { headerName: "Category", field: "category", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    {
      headerName: "Contact No",
      field: "contactNo",
      sortable: true, unSortIcon: true,
      filter: 'agTextColumnFilter'
    },

    { headerName: "Email", field: "email", sortable: true, unSortIcon: true, filter: 'agTextColumnFilter' },
    {
      headerName: 'Actions',
      pinned: 'right',
      maxWidth: 180,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: (params) => {
        return { actions: this.prepareActionIconButtons(params.data), }
      }
    }
  ];


  public init(): void {
    this.getCategoryList();
    // super.setBasUrl(APP_UI_CONFIG.masterdata.category.get);
    super.init();
  }

  public prepareActionIconButtons(row: any): any {
    let iconsList: any[] = [];
    let icons: string = '';
    iconsList.push({ type: 'view', icon: 'view', title: "View", data: row });
    if (row.deleted == 0) {
      iconsList.push({ type: 'update', icon: 'edit', title: "Update", data: row });
    }
    if (row.deleted == 1) {
      iconsList.push({ type: 'activate', icon: 'activate', title: "Activate", data: row });
    }
    else if (row.deleted == 0) {
      iconsList.push({ type: 'deactivate', icon: 'deactivate', title: "Deactivate", data: row });
    }
    // iconsList.push({ type: 'mapping', icon: 'gnatt_chart', title: "Category Mapping", data: row });
    iconsList.push({ type: 'delete', icon: 'delete', title: "Delete", data: row });
    return iconsList;
  }

  private getCategoryList(): void {
    APP_UI_CONFIG.masterdata.category.get.paramList['level'] = 0;
    let categoryListApi = this._commonService.getTenantUid()== 'twills' ? APP_UI_CONFIG.masterdata.category.get : APP_UI_CONFIG.masterdata.category.getWithoutSourcing;
    this.subscription.add(this.invokeService.serviceInvocation(categoryListApi)
      .subscribe({
        next: response => {
          this.categoryList = this._commonService.sortTabularList(response || [])
        },
        error: error => {
          this._commonService.handleError(error);
        }
      }))
  }

  public getAddEvent(event): void {
    // this.selectedData = {};
    this.status = 'new';
    this._commonService.openDialog(AddEditCategoryComponent, null, this.status, '30%').subscribe(res => {
      if (res) {
        this.init();
      }
    });
  }

  public getActions(event): void {
    let actionData: any = event;
    this.selectedData = actionData.data;
    let name = (actionData.data.fullName || actionData.data.code) || ' ';

    if (actionData.status == 'update') {
      this.status = 'update';
      this._commonService.openDialog(AddEditCategoryComponent, this.selectedData, this.status, '30%').subscribe(res => {
        if (res) {
          this.init();
        }
      })
    }
    if (actionData.status == 'view') {
      this.status = 'view';
      this._commonService.openDialog(AddEditCategoryComponent, this.selectedData, this.status, '30%');
    }
    if (actionData.status == 'activate') {
      this.onActivate(this.selectedData);
    }
    if (actionData.status == 'deactivate') {
      this.onDeActivate(this.selectedData);
    }
    if (actionData.status == 'delete') {
      this.onDelete(actionData.data);
    }
    if (actionData.status == 'mapping') {
      this._router.navigate(['master/categoryMapping/' + this.selectedData.code + '/' + this.selectedData.uuid]);

    }
  }

  private onActivate(data): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: 'Are you sure you want to activate category ' + ((data.fullName || data.code) || '') + ' ?',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        APP_UI_CONFIG.masterdata.category.activate.paramList['id'] = data.uuid;
        this.subscription.add(this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.category.activate, null, data.uuid, data)
          .subscribe({
            next: response => {
              if (response.code == 400) {
                this._commonService.error('Unable to activate category');
              } else {
                this._commonService.success('Category activated successfully');
                this.onRefresh();
              }
            }, error: error => {
              this._commonService.handleError(error);
            }
          })
        );
      }
    });
  }

  private onDeActivate(data): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: 'Are you sure you want to deactivate Category ' + ((data.fullName || data.code) || '') + ' ?',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        APP_UI_CONFIG.masterdata.category.deactivate.paramList['id'] = data.uuid;
        this.subscription.add(this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.category.deactivate, null, data.uuid, data)
          .subscribe({
            next: response => {
              if (response.code == 400) {
                this._commonService.error('Unable to deactivate category');
              } else {
                this._commonService.success('Category deactivated successfully');
                this.onRefresh();
              }
            }, error: error => {
              this._commonService.handleError(error);
            }
          })
        );
      }
    });

  }

  public onDelete(row): void {
    let rec = <QiCategory>Util.clone(row);

    let url = URL_CONSTANTS.CATEGORY + '/' + row.uuid;
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '50%',
      data: { apiData: { url: url, data: row } },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        setTimeout(() => {
          this.init();
        }, 1000);
      }
    });
  }

  public onRefresh() {
    this.init();
  }

}
