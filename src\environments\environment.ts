// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

//Stage environment
export const environment = {
  production: false,
  Title: 'Quality360',
  SubTitle: 'Quality & Compliance Automation Platform',
  CopyRights: '3Frames Software Labs Pvt Ltd.',
  //apiUrl: 'https://norlanka.3frameslab.com/api/',//
  //apiUrl: 'http://qa.3frameslab.com:9093/',
  // apiUrl: 'https://ismart.3frameslab.com/nstage/', //Norlanka stage
  // apiUrl: 'http://qa.3frameslab.com:9020/',  // elgi
  // apiUrl: 'https://ismart.3frameslab.com/qasrv/', // ismart
  // apiUrl: 'https://lotssrv.3frameslab.com/api/',
  //apiUrl: 'http://raymond.3frameslab.com:9093/',          // Dev <PERSON>
  // apiUrl: 'http://aspada.3frameslab.com:9090',
  //apiUrl: 'https://pds.3frameslab.com/api/',  
  //apiUrl: 'https://ismart.3frameslab.com/qa4inspect/',            // pds url
  // apiUrl: 'http://localhost/',
  //tenantUid: 'test',                                   // norlanka tenent
  //tenantUid: 'godrej',
  //tenantUid: 'praj',                                // test tenent
  // tenantUid: 'norlanka',                                        // pds tenent
  // tenantUid: 'givaudan',
  // tenantUid: 'demo',
  //tenantUid: 'lots',                                    // elgi tenent
  // tenantUid: 'twills',
  //tenantUid: 'mtr',     
  //tenantUid: 'demo',                                 // elgi tenent
  // tenantUid: 'twills',
  tenantUid: 'madura',                                      // elgi tenent
  //  apiUrl:'http://localhost:9098/',
  // apiUrl: 'http://************:9090',
  // apiUrl: 'https://lotssrv2.3frameslab.com/qa4inspect/',
  //apiUrl: AppConfig.BASE_URL,

  /** Amazon Configuration */
  apiUrl: 'https://ismart.3frameslab.com/demoapi/',
  //apiUrl:'http://localhost:8081/',
  //sourcingApiUrl: 'http://localhost:8383/sourcing/',
  sourcingApiUrl: 'https://ismart.3frameslab.com/demoapi/sourcing/',


  USER: 'user',
  // USER: 'sourcing/auth/details', //enable for licious
  Version: 0.4,
  releaseDate: '12-01-2022',
  sessIdleDefault: 30, // 30 mins
  sessExpireDefault: 2, // 5 mins 

  //lots staging uid - ismart
  horecaTableAuditTypeUid: 'fb305cf6-44a2-4022-b7fa-a7585c695b65', // value to pass in auditTypeUid param for horeca Table payload
  filterAuditTypeUuid: ['fb305cf6-44a2-4022-b7fa-a7585c695b65'] // to filter the Audit types from dashboard screen's dropdown 

  //lots production uid
  //horecaTableAuditTypeUid:'569ec81f-10e1-4c38-92ae-af2b2aca3954', // value to pass in auditTypeUid param for horeca Table payload
  //filterAuditTypeUuid:['569ec81f-10e1-4c38-92ae-af2b2aca3954'] // to filter the Audit types from dashboard screen's dropdown 
};

export const Partners = {
  STAGE: 'stage',
  AMAZON: 'amazon',
  NORLANKA: 'norlanka',
  PRAJ: 'praj',
  GIVAUDAN: 'givaudan',
  TWILLS: 'twills',
  MTR: 'mtr',
  TITAN: 'titan',
  LOTS: 'lots',
  MADURA: 'madura'
}





