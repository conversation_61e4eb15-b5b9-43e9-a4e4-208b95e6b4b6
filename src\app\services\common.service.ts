import { CurrencyPipe, Location } from '@angular/common';
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import {
  designCodeMasterDetailStatus,
  designcodeResponseStatus,
  IndentRequestStatusCodes,
  POStatus,
  samplingDesignCodeStatus,
  SamplingRequestStatusCodes,
} from 'app/core/qinspect/_common/AppConstants';
import { tenantListForGenericDashboard } from 'app/enum-constants';
import { SOURCING_CONFIGURATION } from 'app/modules/sourcing/sourcing.configuraiton';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import { TflErrorListComponent } from 'app/shared/tfl-error-list/tfl-error-list.component';
import { environment } from 'environments/environment';
import * as FileSaver from 'file-saver';
import moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CommonService {
  patterns = {
    alpha: "^[a-zA-Z](?:[a-zA-Z ]*[a-zA-Z])?$",
    // alphaNum: "^[A-Za-z0-9]+$",
    alphaNum: "^[0-9-a-z-A-Z( ) ]+$",
    alphaNumData: "^[0-9-a-z-A-Z]+$",
    alphaNumDataWithSpace: "^[0-9-a-z-A-Z  ]+$",
    num: "^[0-9]+$",
    FLOAT_NUMBER: "^[0-9.]+$",
    email: "/[^\s@]+@[^\s@]+\.[^\s@]+$/",
    alphaSpecialChar: "^[a-zA-Z*']+$",
    alphaSpecialCharComma: "^[a-zA-Z, ]+$",
    alphaSpecialCharHiphen: "^[a-zA-Z0-9- ]+$",
    alphaSpecialCharSlash: "^[a-zA-Z0-9/ ]+$",
    alphaSpecialCharAnd: "^[a-zA-Z&-]+$",
    alphaNumSpecialChar: "^[a-zA-Z0-9*',]+$",
    alphaSpecialCharPercentage: "^[A-Za-z0-9% ]+$",
    regularExpressionForDescriptionBox: "^(?! )(.|\s)*[a-zA-Z]+(.|\n)*$",
  }
  emitData: BehaviorSubject<any> = new BehaviorSubject(null);
  //just for demo purpose
  isIndentViewRaw: string = 'false';
  menuItem: any[];
  home: any;
  navMenu: any;
  tenantListForGenericDashboardPage: string[] = tenantListForGenericDashboard;
  public processType = {
    RAWMATERIAL: 'RAWMATERIAL',
    JOBWORK: 'JOBWORK',
    FOB: 'FOB',
    FOOD: 'FOOD'
  }
  public units = [
    'bytes',
    'KB',
    'MB',
    'GB',
    'TB',
    'PB'
  ];
  constructor(
    public dialog: MatDialog,
    public toaster: ToastrService,
    public _router: Router,
    public _location: Location,
    public route: ActivatedRoute,
    public currencyPipe: CurrencyPipe) {
  }

  public dateFormat(date: any): any {
    let validData = moment(new Date(date));
    if (validData.isValid()) {
      return moment(new Date(date)).format('MMMM Do YYYY');
    } else {
      return "";
    }
  }
  public dateShortFormat(date: any): any {
    let validData = moment(new Date(date));
    if (validData.isValid()) {
      return moment(new Date(date)).format('MMM Do YYYY');
    } else {
      return "";
    }
  }

  public dateTimeFormat(date: any): any {
    let validData = moment(new Date(date));
    if (validData.isValid()) {
      return moment(new Date(date)).format('MMM Do YYYY hh:mm:ss A');
    } else {
      return "";
    }
  }
  setBasicAuth(basic) {
    localStorage.setItem('basicAuth', basic);
  }
  getBasicAuth(): any {
    return localStorage.getItem('basicAuth');
  }

  public getTenantUid(): any {
    return JSON.parse(localStorage.getItem('tenantUid'));
  }

  public setData(data): void {
    this.emitData.next(data);
  }
  public getData(): Observable<any> {
    return this.emitData.asObservable()
  }

  public allowedUploadFileTypes(type): boolean {
    if (type == 'image/jpeg' || type == 'image/png' || type == 'image/jpg' || type == 'application/pdf') {
      return true;
    } else {
      return false;
    }
  }
  public isFileAllowed(fileName: string) {
    let isFileAllowed = false;
    const allowedFiles = ['.pdf', '.jpg', '.jpeg', '.png'];
    const regex = /(?:\.([^.]+))?$/;
    const extension = regex.exec(fileName);
    if (undefined !== extension && null !== extension) {
      for (const ext of allowedFiles) {
        if (ext === extension[0]) {
          isFileAllowed = true;
        }
      }
    }
    return isFileAllowed;
  }


  public saveAsCSVFile(buffer: any, fileName: string): void {
    let EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    let EXCEL_EXTENSION = '.csv';
    const data: Blob = new Blob([buffer], {
      type: EXCEL_TYPE
    });
    FileSaver.saveAs(data, fileName + '_export_' + moment().format("DD-MM-YYYY") + EXCEL_EXTENSION);
  }

  public saveAsPDFFile(buffer: any, fileName: string): void {
    let EXCEL_TYPE = 'application/pdf';
    let EXCEL_EXTENSION = '.pdf';
    const data: Blob = new Blob([buffer], {
      type: EXCEL_TYPE
    });
    FileSaver.saveAs(data, fileName + '_export_' + moment().format("DD-MM-YYYY") + EXCEL_EXTENSION);
  }

  public isFileAllowedForCad(fileName: string) {
    let isFileAllowed = false;
    const allowedFiles = ['.pdf', '.jpg', '.jpeg', '.png', '.dwg'];
    const regex = /(?:\.([^.]+))?$/;
    const extension = regex.exec(fileName);
    if (undefined !== extension && null !== extension) {
      for (const ext of allowedFiles) {
        if (ext === extension[0]) {
          isFileAllowed = true;
        }
      }
    }
    return isFileAllowed;
  }

  public warning(error: any) {
    this.toaster.warning(error);
  }
  public error(error: any) {
    this.toaster.error(error);
  }
  public success(error: any) {
    this.toaster.success(error);
  }

  public historyBack(): void {
    this._location.back();
  }

  public handleError(error): void {
    let message = 'Something Went Wrong; Please Try Again.';
    if (error && error.message) {
      this.error(error.message);
    }
    else {
      this.error(message);
    }
  }

  public navigate(path, queryParams?) {
    this._router.navigate([path], { queryParams: queryParams });
  }

  public createIcon(type: string, title, icon, colorClass?, message?): string {
    if (type.includes('activate')) {
      colorClass = 'text-green-900';
    }
    if (type.includes('deactivate')) {
      colorClass = 'text-warn-900';
    }
    if (type.includes('delete')) {
      colorClass = 'text-red-700';
    }
    if (type.includes('view')) {
      colorClass = 'text-gray-700';
    }
    if (type.includes('edit')) {
      colorClass = 'text-blue-600';
    }
    if (type.includes('update')) {
      colorClass = 'text-blue-600';
    }
    if (!message) {
      return `<span data-action-type="${type}" class="material-icons table_action_icon ${colorClass} mr-2 cursor-pointer" title="${title}">${icon}</span>`
    } else {
      return `<span class="action_icon_text"><span data-action-type="${type}" class="material-icons table_action_icon ${colorClass} mr-2 cursor-pointer" title="${title}">${icon}</span>${message}</span>`
    }
  }
  capaDetails: any;
  public setCAPAItem(data): void {
    this.capaDetails = data;
  }
  public getCAPAItem(): any {
    return this.capaDetails;
  }

  public prepareRoutes(): void {
    this.navMenu = JSON.parse(sessionStorage.getItem('navigation'));
    let currentMenu: any = JSON.parse(sessionStorage.getItem('currentMenu'));
    this.navMenu?.forEach(item => {
      if (item.children) {
        item.children.forEach(child => {
          if (child.children) {
            child.children.forEach(innerChild => {
              if (innerChild.id == currentMenu?.id) {
                this.menuItem = [];
                this.menuItem.push({ type: item.type, id: item.id, label: item.title });
                this.menuItem.push({ type: child.type, id: child.id, label: child.title });
                this.menuItem.push({ type: innerChild.type, id: innerChild.id, label: innerChild.title, routerLink: this._router.url });
                return;
              }
            })
          }
          if (child.id == currentMenu?.id) {
            this.menuItem = [];
            this.menuItem.push({ type: item.type, id: item.id, label: item.title });
            this.menuItem.push({ type: child.type, id: child.id, label: child.title, routerLink: this._router.url });
            return;
          }
        })
      } else {
        this.menuItem = [];
        this.menuItem.push({ type: item.type, id: item.id, label: item.title, routerLink: this._router.url });
        return;
      }
    });

  }

  public openDialog(component, data, status, width?): Observable<any> {
    const dialogRef = this.dialog.open(component, {
      minWidth: width ? width : '50%',
      width: width,
      data: { data: data, status: status },
      disableClose: true,
    });
    return dialogRef.afterClosed();
  }

  public getCurrentUser(): any {
    const currentUser: any = JSON.parse(localStorage.getItem('currentUser'));
    if (currentUser) {
      return currentUser;
    }
  }

  public getTenantConfig(): any {
    const tenantConfig: any = JSON.parse(sessionStorage.getItem('tenantConfig'));
    if (tenantConfig) {
      return tenantConfig;
    }
  }

  public getUseRole(): string {
    const currentUser: any = JSON.parse(localStorage.getItem('currentUser'));
    if (currentUser) {
      return currentUser.role;
    }
  }
  public getUseUid(): string {
    const currentUser: any = JSON.parse(localStorage.getItem('currentUser'));
    if (currentUser) {
      return currentUser.uuid;
    }
  }
  desingCodesWithspecsList: any[] = [];
  public saveSpecsTemporary(data): void {
    const index = this.desingCodesWithspecsList.findIndex(item => item.uuid == data.uuid);
    if (index == -1) {
      this.desingCodesWithspecsList.push(data);
    }

  }
  public getTemporarySpecs(): any[] {
    return this.desingCodesWithspecsList;
  }

  public prepareDesingCodStatus(row): string {
    let status: string = '';
    switch (row.status) {
      case designcodeResponseStatus.CREATED:
        status = this.prepareSourcingStatus("Pending", "#ffa500");
        break;
      case designcodeResponseStatus.ACCEPTED:
        status = this.prepareSourcingStatus("Accepted", "#008000");
        break;
      case designcodeResponseStatus.APPROVED:
        status = this.prepareSourcingStatus("Approved", "#008000");
        break;
      case designcodeResponseStatus.REJECTED:
        status = this.prepareSourcingStatus("Rejected", "#cc3300");
        break;
      case designcodeResponseStatus.ADDITIONALSPECS:
        status = this.prepareSourcingStatus("Specs Submitted", "#0000ff");
        break;
      case designcodeResponseStatus.DROPPED:
        status = this.prepareSourcingStatus("Dropped", "#ff0000");
        break;
      case designcodeResponseStatus.PROTOCOLATTACHED:
        status = this.prepareSourcingStatus("Protocol Attached", "#ff33cc");
        break;
      case designcodeResponseStatus.BIDDINGREREQUESTSTARTED:
        status = this.prepareSourcingStatus("Bidding Requested", "ffa500");
        break;
      case designcodeResponseStatus.BIDDINGREJECTED:
        status = this.prepareSourcingStatus("Bidding Rejected", "#ff0000");
        break;
      case designcodeResponseStatus.BIDDINGRAPPROVED:
        status = this.prepareSourcingStatus("Bidding Approved", "#008000");
        break;
      case designcodeResponseStatus.BIDDINGDROPPED:
        status = this.prepareSourcingStatus("Bidding Dropped", "#ff0000");
        break;
      case designcodeResponseStatus.TESTREPORTRAISED:
        status = this.prepareSourcingStatus("PPT Started", "rgb(22 163 74 / 1)");
        break;
      default:
        status = '';
    }
    return status;
  }

  public prepareDesignCodeMasterStatus(row): string {
    let status: string = '';
    switch (row.status) {
      case designCodeMasterDetailStatus.NEW:
        status = this.prepareSourcingStatus("Not Yet Requested", "#64748b");
        break;
      case designCodeMasterDetailStatus.REQUESTED:
        status = this.prepareSourcingStatus("Collection Requested", "#0ea5e9");
        break;
      case designCodeMasterDetailStatus.QUOTATION_REQUESTED:
        status = this.prepareSourcingStatus("Quotation Requested", "#3B82F6");
        break;
      case designCodeMasterDetailStatus.BIDDING_REQUESTED:
        status = this.prepareSourcingStatus("Bidding Requested", "#a855f7");
        break;
      case designCodeMasterDetailStatus.PPT_REQUESTED:
        status = this.prepareSourcingStatus("PPT Requested", "#ff66ff");
        break;
      case designCodeMasterDetailStatus.FINAL_REPORT_SUBMITTED:
        status = this.prepareSourcingStatus("Final Report Submitted", "#020617");
        break;
      case designCodeMasterDetailStatus.ASIN_MAPPED:
        status = this.prepareSourcingStatus("ASIN Mapped", "#22c55e");
        break;
      default:
        status = '';
    }
    return status;
  }

  public prepareSourcingStatus(statusText, color): string {
    return `<div style="text-align:center;background-color:${color}; border:1px solid ${color};color:white;padding:0.25rem 0.5rem;border-radius: 2rem;">${statusText}</div>`;
  }
  public prepareStatusText(statusText, color): string {
    return `<span style="color:${color}" class="font-weight-600">${statusText}</span>`;
  }

  public calculateFileSize(bytes: number = 0, precision: number = 2): string {
    if (isNaN(parseFloat(String(bytes))) || !isFinite(bytes)) return '?';

    let unit = 0;

    while (bytes >= 1024) {
      bytes /= 1024;
      unit++;
    }
    let fileSize = `${bytes.toFixed(+ precision)} ${this.units[unit]}`;
    return fileSize;
  }

  public fileTypeAndIcon(file): string {
    if (file) {
      let type = file?.split('.').pop();
      let icon: string;
      switch (type) {
        case 'pdf':
          icon = 'pdf';
          break;
        case 'jpg':
        case 'jpeg':
        case 'png':
          icon = 'image';
          break;
        default:
          icon = 'file';
          break;
      }
      return icon;
    } else {
      return 'file';
    }
  }

  public isFileAllowedDynamically(fileName: string, allowedTypes: any) {
    let isFileAllowed = false;
    if (allowedTypes == '*') {
      isFileAllowed = true;
    } else {
      const fliesList: any[] = allowedTypes.split(",");
      const allowedFiles: any = fliesList.map(el => el.trim().toLowerCase());
      const regex = /(?:\.([^.]+))?$/;
      const extension = regex.exec(fileName.toLowerCase());
      if (Array.isArray(allowedFiles)) {
        if (allowedFiles.length == 0) {
          if (extension.some(allow => allowedFiles.includes(allow))) {
            isFileAllowed = true;
          } else {
            isFileAllowed = false;
          }
        } else {
          if (extension.some(allow => allowedFiles.includes(allow))) {
            isFileAllowed = true;
          } else {
            isFileAllowed = false;
          }
        }
      } else {
      }
    }
    return isFileAllowed;
  }


  public prepareReferenceASINsLinks(referenceAsin: string) {
    let referenceLinks = referenceAsin?.split(',');
    if (referenceLinks && referenceLinks.length > 0) {
      let linkWrapper = `<span class="flex flex-row gap-2">`;
      referenceLinks.forEach(asin => {
        linkWrapper = `${linkWrapper}<a target="_blank" href="${SOURCING_CONFIGURATION.referenceAsinBaseLink}${asin}">${asin}</a>`
      });
      linkWrapper = `${linkWrapper}</span>`;
      return linkWrapper;
    }
  }

  public sortByAlphanumeric(array: any[], keyA, keyB): any[] {
    const sortAlphaNum = (a: any, b: any) => a[keyA]?.localeCompare(b[keyB], 'en', { numeric: true });
    return array.sort(sortAlphaNum);
  }

  public iAdmin(): boolean {
    return this.getUseRole()?.toLowerCase() == 'admin';
  }
  public isSourcing(): boolean {
    return this.getUseRole()?.toLowerCase() == 'sourcing';
  }

  public isSeller(): boolean {
    return this.getUseRole()?.toLowerCase() == 'seller';
  }
  public isVendor(): boolean {
    return this.getUseRole()?.toLowerCase() == 'vendor';
  }

  public isProductCompliance(): boolean {
    return this.getUseRole()?.toLowerCase() == 'sme';
  }

  public isProductManager(): boolean {
    return this.getUseRole()?.toLowerCase() == 'product manager';
  }
  public isLab(): boolean {
    return this.getUseRole()?.toLowerCase() == 'lab';
  }

  public prepareArrayListForDropdown(list: any[]): any[] {
    return list?.map(item => ({ name: item, value: item }))
  }

  public preparePoStatus(row): string {
    let status: string = '';
    switch (row.status) {
      case POStatus.PO_UPLOADED:
        status = this.prepareStatusText("PO Uploaded", "#7B8699");
        break;
      case POStatus.PO_SUBMITTED:
        if (this.isVendor()) {
          status = this.prepareStatusText("PO Received", "#6690FF");
        } else {
          status = this.prepareStatusText("PO Submitted", "#6690FF");
        }

        break;
      case POStatus.PO_REJECTED:
        status = this.prepareStatusText("PO Rejected", "#B66935");
        break;
      case POStatus.PO_ACCEPTED:
        status = this.prepareStatusText("In-Production", "#FFB56C");
        break;
      case POStatus.PO_CLOSED:
        status = this.prepareStatusText("PO Closed", "#7DD863");
        break;
      case POStatus.PO_SHIPPED:
        status = this.prepareStatusText("Shipped", "#d97706");
        break;
      case POStatus.PO_GRN_UPDATE:
        status = this.prepareStatusText("GRN Updated", "#FFB56C");
        break;
      default:
        status = '';
    }
    return status
  }

  public prepareIndentStatus(row): string {
    let status: string = '';
    switch (row.status) {
      case IndentRequestStatusCodes.NEW:
        status = this.prepareStatusText("REQUEST CREATED", "#7B8699");
        break;
      case IndentRequestStatusCodes.SUBMITTED:
        status = this.prepareStatusText("SUBMITTED", "#6690FF");
        break;
      case IndentRequestStatusCodes.LIVE:
        status = this.prepareStatusText("PARTIALLY PROCESSED", "#FFB56C");
        break;
      case IndentRequestStatusCodes.CLOSED:
        status = this.prepareStatusText("CLOSED", "#7DD863");
        break;
    }
    return status
  }

  public prepareSampleResponseStatus(row): string {
    let status: string = '';
    switch (row.samplingStatus) {
      case samplingDesignCodeStatus.CREATED:
        status = this.prepareStatusText("CREATED", "#7B8699");
        break;
      case samplingDesignCodeStatus.VENDOR_ACCEPTED:
        status = this.prepareStatusText("VENDOR ACCEPTED", "#FFB56C");
        break;
      case samplingDesignCodeStatus.VENDOR_REJECTED:
        status = this.prepareStatusText("VENDOR REJECTED", "#384353");
        break;
      case samplingDesignCodeStatus.APPROVED:
        status = this.prepareStatusText("VENDOR DISPATCHED", "#6690FF");
        break;
      case samplingDesignCodeStatus.SAMPLE_RECEIVED:
        status = this.prepareStatusText("SAMPLE RECEIVED", "#6690FF)");
        break;
      case samplingDesignCodeStatus.REJECTED:
        status = this.prepareStatusText("REJECTED", "#384353");
        break;
      case samplingDesignCodeStatus.DROPPED:
        status = this.prepareStatusText("DROPPED", "#384353");
        break;
      case samplingDesignCodeStatus.APPROVED:
        status = this.prepareStatusText("APPROVED", "#7DD863");
        break;
      case samplingDesignCodeStatus.SAMPLE_APPROVED:
        status = this.prepareStatusText("SAMPLE APPROVED", "#39AC28");
        break;
      case samplingDesignCodeStatus.SAMPLE_REJECTED:
        status = this.prepareStatusText("SAMPLE REJECTED", "#e73535");
        break;
      case samplingDesignCodeStatus.SAMPLE_DROPPED:
        status = this.prepareStatusText("SAMPLE DROPPED", "#384353");
        break;
      case samplingDesignCodeStatus.SAMPLE_SUBMITTED:
        status = this.prepareStatusText("SAMPLE SUBMITTED", "#6690FF");
        break;
      case samplingDesignCodeStatus.LIVE:
        status = this.prepareStatusText("LIVE", "#FFB56C");
        break;
    }
    return status
  }

  public prepareSampleRequestStatus(status): string {
    switch (status) {
      case SamplingRequestStatusCodes.CREATED:
        status = this.prepareStatusText("CREATED", "#7B8699");
        break;
      case SamplingRequestStatusCodes.SAMPLE_REJECTED:
        status = this.prepareStatusText("SAMPLE REJECTED", "#e73535");
        break;
      case SamplingRequestStatusCodes.VENDOR_DISPATCHED:
        status = this.prepareStatusText("VENDOR DISPATCHED", "#6690FF");
        break;
      case SamplingRequestStatusCodes.SAMPLE_APPROVED:
        status = this.prepareStatusText("SAMPLE APPROVED", "#7DD863");
        break;
      case SamplingRequestStatusCodes.SAMPLE_DROPPED:
        status = this.prepareStatusText("SAMPLE DROPPED", "#384353");
        break;
      case SamplingRequestStatusCodes.VENDOR_REJECTED:
        status = this.prepareStatusText("VENDOR REJECTED", "#FFB56C");
        break;
      case samplingDesignCodeStatus.VENDOR_ACCEPTED:
        status = this.prepareStatusText("VENDOR ACCEPTED", "#22c55e");
        break;
      case samplingDesignCodeStatus.SAMPLE_RECEIVED:
        status = this.prepareStatusText("SAMPLE RECEIVED", "#0000ff");
        break;
      default:
        status = this.prepareStatusText("LIVE", "#FFB56C");
        break;
    }
    return status
  }

  public openDialogForConfirmation(message): Observable<any> {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      minWidth: '30%',
      data: message,
      disableClose: true,
    });
    return dialogRef.afterClosed()
  }

  public currencyFormat(value, currency, isPrefix?, digits?): any {
    if (isPrefix) {
      return `Rs.${value}`
    } else {
      return this.currencyPipe.transform((value || 0), (currency || ''))
    }

  }

  public getListOfErrorObjectsFromArray(responseList: any[]): any[] {
    let list: any[] = [];
    responseList.forEach((item) => {
      if (item.code == '400') {
        list.push(item);
      }
    })

    return list;
  }
  public listOfErrorsDialog(data: any[], columns: any[], hdr: string): Observable<any> {
    const dialogRef = this.dialog.open(TflErrorListComponent, {
      minWidth: '40%',
      disableClose: true,
    });
    dialogRef.componentInstance.columns = columns;
    dialogRef.componentInstance.rowData = data;
    dialogRef.componentInstance.hdr = hdr;

    return dialogRef.afterClosed();
  }
  public sortTabularList(list: any[]): any[] {
    list.sort(function (x, y) {
      return y.createdTime - x.createdTime;
    });
    return list;
  }

  public sortByDateLatest(array: any[], keyA, keyB): any[] {
    return array.sort(function (a, b) {
      var c = new Date(a[keyA]);
      var d = new Date(b[keyB]);
      return Number(d) - Number(c);
    });
  }
  public sortByProperty(array: any[], key): any[] {
    return array.sort((a, b) => (a[key] > b[key]) ? 1 : ((b[key] > a[key]) ? -1 : 0))

  }
  public sortBy(arr, keys, splitKeyChar = '~') {
    return arr.sort((i1, i2) => {
      const sortStr1 = keys.reduce((str, key) => str + splitKeyChar + i1[key], '')
      const sortStr2 = keys.reduce((str, key) => str + splitKeyChar + i2[key], '')
      return sortStr1.localeCompare(sortStr2)
    })
  }

  public sortByCustomFields(data: any[], sortParameters: any[], keyName: string): any[] {
    let sorParams: any[] = sortParameters.map(item => item?.toLowerCase());
    return data?.sort((a, b) => sorParams.indexOf(a[keyName.trim()]?.toLowerCase()) - sorParams.indexOf(b[keyName.trim()]?.toLowerCase()))
  }

  public isEmpty(obj) {
    if (Object.entries(obj).length == 0) {
      return true;
    } else {
      return false;
    }
  }

  public trimEdgeSpaces(input: string): string {
    // Trim spaces from the start and end of the string
    return input.trimStart().trimEnd();
  }

  public isGenericDashBoardVisible(): boolean {
    if (this.tenantListForGenericDashboardPage.length == 0) return false;
    if (this.tenantListForGenericDashboardPage.includes(this.getTenantUid())) {
      return true;
    } else {
      return false;
    }
  }
}



