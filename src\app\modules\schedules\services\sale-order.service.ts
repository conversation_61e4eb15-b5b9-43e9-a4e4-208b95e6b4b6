import { HttpClient, HttpClientModule } from '@angular/common/http';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpHead<PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  QuotationRequest,
  QuotationRequestDesigncode,
  QuotationResponse,
  TestRequisitionForm,
} from 'app/core/qinspect/_model/TestRequisitionForm.model';
import { CommonService } from 'app/services/common.service';
import { environment } from 'environments/environment';
import { FileSaverService } from 'ngx-filesaver';
import { Observable } from 'rxjs';

import { FabricPurchaseOrder } from '../../../core/qinspect/_model/FabricPurchaseOrder.model';
import { Invoice } from '../../../core/qinspect/_model/Invoice.model';
import { PurchaseOrder } from '../../../core/qinspect/_model/PurchaseOrder.model';
import { SaleOrder } from '../../../core/qinspect/_model/SaleOrder.model';
import { ShipmentData } from '../../../core/qinspect/_model/ShipmentData.model';
import { ShipmentDto } from '../../../core/qinspect/_model/ShipmentDto.model';
import { URL_CONSTANTS } from '../../../core/qinspect/appconfig/appconfig';
import { Appconfig, Globals } from '../../../core/qinspect/appconfig/appconfig.component';
import { InvokeService } from '../../../services/invoke.service';


@Injectable({
  providedIn: 'root'
})
export class SaleOrderService {
  http: HttpClient;
  basicAuth = this._commonService.getBasicAuth();

  constructor(private httpin: HttpClient,
    private globals: Globals,
    private fileSaverService: FileSaverService,
    public _invokeService: InvokeService,
    private _commonService: CommonService
  ) {
    this.http = httpin;
  }


  getAllPurchaseOrders(): Observable<PurchaseOrder[]> {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth);

    return this.http.get<PurchaseOrder[]>(environment.apiUrl + 'saleOrder/po/', {
      headers: headers
    });
  }

  updatePurchaseOrder(po: PurchaseOrder): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/po/', po, {
      headers: headers
    });
  }

  activatePurchaseOrder(po: PurchaseOrder): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/po/' + po.code + '/activate', po, {
      headers: headers
    });
  }

  deactivatePurchaseOrder(pouid: string): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.delete<Response>(environment.apiUrl + 'saleOrder/po/' + pouid, {
      headers: headers
    });
  }

  downloadPurchaseOrders() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/csv' })
      .set("Authorization", this.basicAuth);
    this.http.get(environment.apiUrl + 'saleOrder/po/download', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, 'fabrics.csv');
    });
  }


  getAllSaleOrders(): Observable<SaleOrder[]> {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth);

    return this.http.get<SaleOrder[]>(environment.apiUrl + 'saleOrder/', {
      headers: headers
    });
  }

  getSalesOrder(obj: any, callback: Function = null): Observable<any> {
    let url = environment.apiUrl + 'sap/salesOrder';
    return this.http.post<any>(url, obj);
  }

  //   createFabricPurchaseOrder(po: FabricPurchaseOrder): Observable<Response> {
  //   // let basicAuth = localStorage.getItem('baiscAuth');

  //   let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
  //     .set("Authorization", this.basicAuth);

  //   return this.http.post<Response>(environment.apiUrl + 'saleOrder/fabricpo/', po, {
  //     headers: headers
  //   });
  // }

  getPurchaseOrder(obj: any, callback: Function = null) {
    let url = environment.apiUrl + 'sap/purchaseOrder';
    this.http
      .post(url, obj)
      .subscribe(
        data => {
          if (callback != null) { callback(data); }
        });
  }

  getShipmentData(obj: any, callback: Function = null) {
    let url = environment.apiUrl + 'sap/shipment';
    this.http
      .post(url, obj)
      .subscribe(
        data => {
          if (callback != null) { callback(data); }
        });
  }


  updateSaleOrder(po: SaleOrder): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/', po, {
      headers: headers
    });
  }

  activateSaleOrder(po: SaleOrder): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/' + po.code + '/activate', po, {
      headers: headers
    });
  }

  deactivateSaleOrder(pouid: string): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.delete<Response>(environment.apiUrl + 'saleOrder/' + pouid, {
      headers: headers
    });
  }

  downloadSaleOrders() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/csv' })
      .set("Authorization", this.basicAuth);
    this.http.get(environment.apiUrl + 'saleOrder/download', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, 'fabrics.csv');
    });
  }


  getAllShipment(): Observable<ShipmentData[]> {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth);

    return this.http.get<ShipmentData[]>(environment.apiUrl + 'saleOrder/shipment/', {
      headers: headers
    });
  }

  updateShipmentData(po: ShipmentData): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/shipment/', po, {
      headers: headers
    });
  }

  activateShipmentData(po: ShipmentData): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/shipment/' + po.code + '/activate', po, {
      headers: headers
    });
  }

  deactivateShipmentData(pouid: string): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.delete<Response>(environment.apiUrl + 'saleOrder/shipment/' + pouid, {
      headers: headers
    });
  }

  downloadShipmentDatas() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/csv' })
      .set("Authorization", this.basicAuth);
    this.http.get(environment.apiUrl + 'saleOrder/shipment/download', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this.fileSaverService.save((<any>res).body, 'fabrics.csv');
    });
  }

  getShipmentByPo(pouid: string): Observable<ShipmentDto> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let data = {
      url: 'shipment/' + pouid + '/po',
      type: 'GET',
    }
    return this._invokeService.serviceInvocation(data);
    // let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
    //   .set("Authorization", this.basicAuth);

    // return this.http.get<ShipmentDto>(environment.apiUrl + 'shipment/' + pouid + '/po', {
    //   headers: headers
    // });
  }


  getShipment(shipmentNo: string): Observable<ShipmentDto> {
    let data = {
      url: 'shipment/' + shipmentNo,
      type: 'GET',
    }
    return this._invokeService.serviceInvocation(data);
  }


  getAllFabricPurchaseOrders(): Observable<FabricPurchaseOrder[]> {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth);

    return this.http.get<FabricPurchaseOrder[]>(environment.apiUrl + 'saleOrder/fabricpo', {
      headers: headers
    });
  }

  createFabricPurchaseOrder(po: FabricPurchaseOrder): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'saleOrder/fabricpo/', po, {
      headers: headers
    });
  }


  updateFabricPurchaseOrder(po: FabricPurchaseOrder): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/fabricpo/', po, {
      headers: headers
    });
  }

  activateFabricPurchaseOrder(po: FabricPurchaseOrder): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/fabricpo/' + po.uuid + '/activate', po, {
      headers: headers
    });
  }

  deactivateFabricPurchaseOrder(pouid: string): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.delete<Response>(environment.apiUrl + 'saleOrder/fabricpo/' + pouid, {
      headers: headers
    });
  }



  getAllInvoices(): Observable<Invoice[]> {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", basicAuth);

    return this.http.get<Invoice[]>(environment.apiUrl + 'saleOrder/invoice', {
      headers: headers
    });
  }

  getByAuditType() {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'config/byAuditType', {
      headers: headers
    });
  }

  getConfigsByModifiedTimeAndAuditTypeUid(auditTypeUid: string, lastModified: number) {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Client': environment.tenantUid
    }).set("Authorization", basicAuth);

    // Construct the URL with query parameters
    const url = `${environment.apiUrl}config/byAuditTypeUid?auditTypeUid=${auditTypeUid}&last-modified=0`;

    return this.http.get<any>(url, {
      headers: headers
    });
  }

  getByAuditTypeByUid(auditTypeUid: string) {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'config/byAuditType/' + auditTypeUid, {
      headers: headers
    });
  }

  getByAuditTypeByAndProtocol(auditTypeUid: string, testingRequestUid: string) {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'config/byAuditTypeAndProtocol/' + auditTypeUid + '/' + testingRequestUid, {
      headers: headers
    });
  }

  getSchedulesForVendor(vendorUid: string) {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get<any[]>(`${environment.apiUrl}vendor/${vendorUid}/getSchedule`, {
      headers: headers
    });
  }

  getfetchInspectionResponse(auditUid) {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'audit/' + auditUid + '/response', {
      headers: headers
    });
  }

  getAuditImage(auditUid, type, imgName) {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'image/png', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get(environment.apiUrl + 'audit/' + auditUid + '/' + type + '/' + imgName, {
      responseType: 'blob',
      headers: headers
    });
  }

  getProductImage(factoryUid: string, imageName: string) {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'image/png', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get(environment.apiUrl + `vendor/${factoryUid}/productImage/${imageName}`, {
      responseType: 'blob',
      headers: headers
    });
  }

  finalSubmitReport(data: any): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'audit/report', data, {
      headers: headers
    });
  }

  finalSubmitReportWithStatus(data: any, status: any): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'audit/report/updateFinalReport/' + status, data, {
      headers: headers
    });
  }



  submitDraftReport(data: any): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'audit/report/draft', data, {
      headers: headers
    });
  }

  submitForReview(data: any): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'audit/review/SELLER', data, {
      headers: headers
    });
  }

  partialSave(data: any): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'audit/report/partialSubmit', data, {
      headers: headers
    });
  }

  submitForRequestL1(data: any): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'audit/review/AGENCY', data, {
      headers: headers
    });
  }

  submitForRequestL2(data: any): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'audit/review/ADMIN', data, {
      headers: headers
    });
  }

  submitForRequestL2Seller(auditId: any): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + `audit/submitreview/SELLER/${auditId}`, {
      headers: headers
    });
  }

  submitToAdmin(data: any): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'audit/report/auditorSubmit', data, {
      headers: headers
    });
  }

  updateTestRequestStatus(requestUid: string, status: string): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + `testProtocol/testRequests/${requestUid}/${status}`, null, {
      headers: headers
    });
  }

  saveFactorySAQScore(auditUid: string): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'vendor/' + auditUid + '/saveVendorFactoryScores/', {
      headers: headers
    });
  }

  saveAuditResponses(auditReqData: any, auditUid: string): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'audit/' + auditUid + '/response', auditReqData, {
      headers: headers
    });
  }

  createInvoice(po: Invoice): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'saleOrder/invoice', po, {
      headers: headers
    });
  }

  updateInvoice(po: Invoice): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/invoice', po, {
      headers: headers
    });
  }

  activateInvoice(po: Invoice): Observable<Response> {
    // let basicAuth = localStorage.getItem('baiscAuth');

    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'saleOrder/invoice/' + po.code + '/activate', po, {
      headers: headers
    });
  }

  deactivateInvoice(pouid: string): Observable<Response> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json' })
      .set("Authorization", this.basicAuth);

    return this.http.delete<Response>(environment.apiUrl + 'saleOrder/invoice/' + pouid, {
      headers: headers
    });
  }

  getScheduleFieldConfigById(auditId): Observable<any[]> {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'scheduleFieldConfig/' + auditId, {
      headers: headers
    });
  }

  getAsinCode(code: string): Observable<any[]> {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'designCode/byCode/' + code, {
      headers: headers
    });
  }

  getLookUpValues() {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'lookUpValues', {
      headers: headers
    });
  }

  createTestRequests(data: TestRequisitionForm): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + 'testProtocol/testRequests', data, {
      headers: headers
    });
  }

  updateTestRequests(data: TestRequisitionForm): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + 'testProtocol/testRequests', data, {
      headers: headers
    });
  }

  createQuotationRequest(data: QuotationRequest): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.sourcingApiUrl + 'quotation/request', data, {
      headers: headers
    });
  }

  updateDesignCodeMasterStatus(data: QuotationRequestDesigncode[]): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.sourcingApiUrl + 'quotation/designCodeMaster', data, {
      headers: headers
    });
  }

  getQuotationRequest(referenceUid: string): Observable<any> {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<Response>(environment.sourcingApiUrl + 'quotation/request/byReference/' + referenceUid, {
      headers: headers
    });
  }

  saveLabAcknowledge(testingRequest: TestRequisitionForm): Observable<Response> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + `testProtocol/testRequests/labAcknowledge`, testingRequest, {
      headers: headersObj
    });
  }

  updateTRFStatus(requestUid: string): Observable<Response> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + `testProtocol/testRequests/${requestUid}/updateStatus`, null, {
      headers: headersObj
    });
  }

  saveLabBidding(quotationResponse: QuotationResponse): Observable<Response> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.sourcingApiUrl + `quotation/response`, quotationResponse, {
      headers: headersObj
    });
  }

  updateTestingRequestStatus(testingRequest: TestRequisitionForm): Observable<Response> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + `testProtocol/testRequests/status`, testingRequest, {
      headers: headersObj
    });
  }

  startLabTesting(testingRequest: TestRequisitionForm): Observable<Response> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + `testProtocol/testRequests/startTesting`, testingRequest, {
      headers: headersObj
    });
  }

  startSchedule(auditId: string): Observable<Response> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + `audit/start/${auditId}`, null, {
      headers: headersObj
    });
  }

  uploadZip(formData: FormData): Observable<any> {
    let headersObj = new HttpHeaders({ 'Accept-Encoding': 'multipart/form-data', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + `audit/imagesupload`, formData, {
      headers: headersObj
    });
  }

  getPurchaseOrdersDetails(poNumber): Observable<PurchaseOrder[]> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.get<PurchaseOrder[]>(environment.apiUrl + `saleOrder/polist/${poNumber}`, {
      headers: headersObj
    });
  }

  uploadProductImagesZip(factoryUid: string, formData: FormData): Observable<any> {
    let headersObj = new HttpHeaders({ 'Accept-Encoding': 'multipart/form-data', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + `/factory/${factoryUid}/productImagesUpload`, formData, {
      headers: headersObj
    });
  }

  uploadProductImagesZipForCheckpoint(auditUid: string, checkpointUid: string, formData: any): Observable<any> {
    let headersObj = new HttpHeaders({ 'Accept-Encoding': 'multipart/form-data', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + `/audit/${auditUid}/checkpointresponse/uploadImageAttachment/${checkpointUid}`, formData, {
      headers: headersObj
    });
  }

  updateCheckpointResponse(auditUid: string, checkpointUid: string, data: any): Observable<any> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + `audit/${auditUid}/checkpointresponse/update/${checkpointUid}`, data, {
      headers: headersObj
    });
  }
  updateReportDraft(auditUid: string): Observable<any> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + `audit/genreport/${auditUid}/draft`, null, {
      headers: headersObj
    });
  }

  getByAuditTypeByUidAuditId(auditTypeUid: string, auditUid: string) {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.get<any[]>(environment.apiUrl + `config/byAuditType/${auditTypeUid}/AuditId/${auditUid}`, {
      headers: headers
    });
  }

  generateReport(auditUid: string): Observable<any> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + `audit/genreport/${auditUid}`, null, {
      headers: headersObj
    });
  }

  inspectionStatusUpdated(planningUid: string, statusName: string, comments: string): Observable<any> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.put<Response>(environment.apiUrl + `schedulePlanning/updateStatus/${planningUid}/${statusName}/${comments}`, {
      headers: headersObj
    });
  }

  updateReportDraftOnEmail(auditUid: string): Observable<any> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': this._commonService.getTenantUid() })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + `audit/genreport/${auditUid}`, null, {
      headers: headersObj
    });
  }

  addTestProductImages(auditUid: string, imageList: any[]): Observable<any> {
    let headersObj = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", this.basicAuth);

    return this.http.post<Response>(environment.apiUrl + `/audit/${auditUid}/miscellaneous`, imageList, {
      headers: headersObj
    });
  }

  autoExecuteCheckpoints() {
    let basicAuth = this.globals.basicAuth;
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Client': environment.tenantUid })
      .set("Authorization", basicAuth);

    return this.http.get<any[]>(environment.apiUrl + 'testProtocol/autoExecute', {
      headers: headers
    });
  }

}