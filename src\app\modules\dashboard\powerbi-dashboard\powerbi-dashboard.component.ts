import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { DashboardService } from 'app/services/dashboard.service';
import { environment } from 'environments/environment';

@Component({
  selector: 'kt-powerbi-dashboard',
  templateUrl: './powerbi-dashboard.component.html',
  styleUrls: ['./powerbi-dashboard.component.scss']
})
export class PowerbiDashboardComponent implements OnInit {
  tetantsData: any = {};
  powerBIurl: any;
  constructor(public _dashboardService: DashboardService,
    public sanitizer: DomSanitizer,
    private cdRef: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.getTetantsConfig();
    this.powerBIurl = this.sanitizer.bypassSecurityTrustResourceUrl('https://app.powerbi.com/view?r=eyJrIjoiODQ5NTE2ZjgtODM5NC00MTM1LWE0ZDMtNTI0MDc4NjU3ZmJkIiwidCI6IjZjYTljZTA3LWY3YjQtNGVmNC1iZmY0LWE1YWFhMjU4ZTc0OSJ9');
  }

  private getTetantsConfig(): void {
    const tenantUid = JSON.parse(localStorage.getItem("tenantUid"));
    let tetant: string = tenantUid;
    this._dashboardService.getTetantnsConfig(tetant).subscribe(res => {
      if (res) {
        this.tetantsData = res;
        //  this.powerBIurl = this.sanitizer.bypassSecurityTrustResourceUrl(this.tetantsData.powerBIurl);
        this.cdRef.detectChanges();
      }

    })
  }

}
