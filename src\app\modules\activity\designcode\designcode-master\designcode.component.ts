import { Component } from '@angular/core';
import { ColDef, ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { ICON_LIST } from 'app/icon-list';
import { AttachmentTypeComponent } from 'app/shared/attachment-type/attachment-type.component';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { ImgIconComponent } from 'app/shared/tfl-table-search-grid/grid-supporting-components/img-icon/img-icon.component';
import { UploadFileComponent } from 'app/shared/upload-file/upload-file.component';
import { environment, Partners } from 'environments/environment';
import { FileSystemFileEntry, NgxFileDropEntry } from 'ngx-file-drop';
import { ReplaySubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';



@Component({
  selector: 'app-designcode2',
  templateUrl: './designcode.component.html',
  styleUrls: ['./designcode.component.scss']
})
export class DesigncodeComponent extends BaseTableSharedComponent {
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  //declarations:
  hdr: string = "Products/Design codes";
  showDesignCodeTemplate: boolean = true;
  columnDefs: ColDef[] = [];
  designCodeList: any[] = [];
  actionIconList: any[] = [{ type: 'upload_product_image', title: 'Upload Product Image To Multiple Design Codes', icon: ICON_LIST.UPLOAD_BLUE, command: (item) => this.onUploadProductImages() }];
  attachmentList: any[] = [];
  public init(): void {
    this.defaultfromDate.setDate(this.defaultToDate.getDate() - 1);
    this.onRefresh();
    super.init();
  }
  public onRefresh(): void {
    this.serviceInvocation();
    if (this._commonService.getTenantUid() == Partners.TITAN || this._commonService.getTenantUid() == Partners.MADURA) {
      this.prepareTableColumnForTitan();
    } else {
      this.prepareTableColumns();
    }

  }
  private serviceInvocation(): void {
    //Note: Changed to getFullDesignCodes from getAllDesignCodes.
    if (this._commonService.getTenantUid() != Partners.TWILLS) {
      super.setBasUrl(APP_UI_CONFIG.masterdata.designCodeMaster.getFullDesignCodes);
    } else {
      super.setBasUrl(APP_UI_CONFIG.masterdata.designCodeMaster.getAllDesignCodes);
    }
    super.init();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      { headerName: "Design Code", field: "designCodeCode", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "SO Item", field: "salesOrderItem", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "PO No", field: "purchaseOrderNo", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "Shipment No", field: "shipmentNo", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "Shipment Qty", field: "shipmentQty", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "Buyer PO", field: "buyerOrderRefNo", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "Merchandizer", field: "merchandizerName", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "Vendor", field: "vendorName", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "Factory", field: "factoryName", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "Profit Center", field: "profitCenterName", sortable: true, filter: 'agTextColumnFilter' },
      { headerName: "Planned Delivery Date", field: "plannedDeliveryDate", sortable: true },
      { headerName: "Vendor Delivery Date", field: "vendorDeliveryDate", sortable: true },
      { headerName: "SISR DATE", field: "sisrDate", sortable: true, filter: 'agTextColumnFilter' },
    ];
  }
  private prepareTableColumnForTitan(): void {
    this.columnDefs = [
      { headerName: "SKU", field: "code", sortable: true, filter: 'agTextColumnFilter', minWidth: 120 },
      { headerName: "Division", field: "division", sortable: true, filter: 'agTextColumnFilter', minWidth: 130 },
      { headerName: "Brand", field: "brand", sortable: true, filter: 'agTextColumnFilter', minWidth: 120 },
      { headerName: "Category", field: "category", sortable: true, filter: 'agTextColumnFilter', minWidth: 120 },
      { headerName: "Sub Category", field: "subCategory", sortable: true, filter: 'agTextColumnFilter', minWidth: 120 },
      { headerName: "Product Type", field: "productTypeName", sortable: true, filter: 'agTextColumnFilter', minWidth: 120 },
      { headerName: "Season", field: "season", sortable: true, filter: 'agTextColumnFilter', maxWidth: 120 },
      { headerName: "Color", field: "color", sortable: true, filter: 'agTextColumnFilter', maxWidth: 120 },
      { headerName: "Lining", field: "lining", sortable: true, filter: 'agTextColumnFilter', minWidth: 120 },
      { headerName: "Material", field: "material", sortable: true, filter: 'agTextColumnFilter', minWidth: 120 },
      { headerName: "Retail Price", field: "mrp", sortable: true, filter: 'agTextColumnFilter', minWidth: 80 },
      { headerName: "Short Description", field: "description", sortable: true, filter: 'agTextColumnFilter', minWidth: 170 },
      { headerName: "Collection Name", field: "collectionName", sortable: true, filter: 'agTextColumnFilter', minWidth: 150 },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
        }
      }
    ];
  }
  private prepareActionIconButtons(row: any) {
    let iconsList: any[] = []
    iconsList.push({ type: 'add_attachment', title: "Attachments", icon: ICON_LIST.ADD_DOC_BLUE, data: row });
    if (this._commonService.getTenantUid() == Partners.TITAN || this._commonService.getTenantUid() == Partners.MADURA) {
      iconsList.push({ type: 'delete', title: "Delete Products/Design codes", icon: 'delete', data: row });
    }
    // iconsList.push({ type: 'view_attachment', title: "Attachments", icon: ICON_LIST.VIEW_FILE_BLUE, data: row });
    return iconsList;
  }

  public onAction(action): void {
    switch (action.type) {
      case 'add_attachment':
        this.onAddViewAttachments(action.data, true, true);
        break;
      case 'view_attachment':
        this.onAddViewAttachments(action.data, false, true);
        break;
      case 'delete':
        this.onDelete(action.data)
    }
  }

  private onDelete(action): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: 'Are you sure you want to delete the design code?',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        APP_UI_CONFIG.masterdata.designCodeMaster.deleteDesignCode.paramList.designCodeUid = action?.uuid;
        this.invokeService.serviceInvocation(APP_UI_CONFIG.masterdata.designCodeMaster.deleteDesignCode, null, null, null).subscribe({
          next: res => {
            if (res && res.code == 200) {
              this._commonService.success("Design code deleted successfully");
              this.onRefresh();
            } else {
              this._commonService.error(res.message ? res.message : 'Failed to delete the design code');
            }
          },
          error: error => {
            this._commonService.handleError(error);
          }
        })
      }
    })
  }


  public getImportEvent(event): void {
    this.openImportDialog();
  }

  public getActions(event): void {

  }

  public getOnGetDate(event: any): void {
    this.FromToDate.from = event.from;
    this.FromToDate.to = event.to;
    this.onRefresh();
  }



  public onDesignCodeDownloadTemplate(): void {
    APP_UI_CONFIG.PO.downloadPurchaseOrderTemplate.paramList.PurchaseOrder = 'DesignCodeMaster'
    this.invokeService.downloadFile(APP_UI_CONFIG.PO.downloadPurchaseOrderTemplate, '', `DesignCodeMaster_Template.csv`, '');
  }
  private openImportDialog(): void {
    const userCategoryMapEnabled = this._commonService.getTenantConfig()?.featuresSet ? JSON.parse(this._commonService.getTenantConfig()?.featuresSet)?.enableImportExportConfig == 1 : false;
    let url = "activity/designcode/import";
    if (userCategoryMapEnabled) {
      url = "impex/DesignCodeMaster/import";
    }
    const dialogRef = this.dialog.open(UploadFileComponent, {
      width: '50%',
      data: { dailogHeadig: 'Import Products/Design Codes', apiData: { 'url': url, type: "POST" }, message: { success: 'Products imported successfully', error: 'Products not imported' }, dropFileslabel: 'products' },
      disableClose: true,
    });

    dialogRef.afterClosed()
      .pipe(takeUntil(this.$destroyed))
      .subscribe(result => {
        if (result) {
          this.onRefresh();
        }
      });
  }


  //attachments

  onAddViewAttachments(row, isAdd, isView): void {
    this.getAttachments(row, isAdd, isView)
  }

  private getMessage(document): string {
    return `Please use the ${document.extensions} format to upload the attachment.`;
  }

  private onConfirmSaveAttachments(files: NgxFileDropEntry[], row, selectedAttachment, dialogRef): void {
    if (this.attachmentList.findIndex(item => item.docType == selectedAttachment.value) > -1) {
      this._commonService.openDialogForConfirmation(`The Attachment Type:${selectedAttachment.value} already available, Are you sure you want to replace it by new file?`).subscribe({
        next: reponse => {
          if (reponse) {
            this.onSaveAttachments(files, row, selectedAttachment, dialogRef)
          }
        }
      })
    } else {
      this.onSaveAttachments(files, row, selectedAttachment, dialogRef)
    }

  }

  private onSaveAttachments(files: NgxFileDropEntry[], row, selectedAttachment, dialogRef): void {
    files.forEach((droppedFile, index) => {
      const formData = new FormData()
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          formData.append('file', file, droppedFile.relativePath);
          formData.append('json', JSON.stringify({ name: droppedFile.relativePath }));
        });
        APP_UI_CONFIG.administration.designCodeAttachment.uploadAttachment.paramList.designCode = row.code
        APP_UI_CONFIG.administration.designCodeAttachment.uploadAttachment.paramList.attachmentType = selectedAttachment.value;
        this.invokeService.uploadAttachments(APP_UI_CONFIG.administration.designCodeAttachment.uploadAttachment, formData)
          .pipe(takeUntil(this.$destroyed))
          .subscribe({
            next: res => {
              if (res && res.code == 200) {
                this._commonService.success(res.message ? res.message : "File imported successfully");
                dialogRef.close();
                this.onAddViewAttachments(row, true, true)
              } else {
                this._commonService.handleError(res);
              }
            },
            error: error => {
              this._commonService.handleError(error);
            }
          });
      }
    })
  }

  private getAttachments(row, isAdd, isView): void {
    APP_UI_CONFIG.administration.designCodeAttachment.getAttachmentListByDesignCode.paramList.designCode = row.code
    this.invokeService.serviceInvocation(APP_UI_CONFIG.administration.designCodeAttachment.getAttachmentListByDesignCode)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: res => {
          if (res) {
            this.attachmentList = res;
            this.onViewAttachments(row, this.attachmentList, isAdd, isView)
          }
        },
        error: error => {
          this._commonService.handleError(error);
        }
      })
  }

  private onViewAttachments(row, documentsList, isAdd, isView): void {
    let attachmentsColumnDefs: any = [
      {
        headerName: "Type", field: "fileName", width: 70, maxWidth: 70,
        cellRendererFramework: ImgIconComponent,
        cellRendererParams: (params: ICellRendererParams) => params.data
      },
      { headerName: "Name", field: "fileName", sortable: true, maxWidth: 380 },
      { headerName: "Attachment Type", field: "docType", sortable: true, unSortIcon: true },
      {
        headerName: "File Size", field: "size", sortable: true
      },
      {
        headerName: 'Actions',
        maxWidth: 100,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: params => {
          return { actions: this.prepareActionIconButtonsAttachments(params.data), onAction: (action) => this.onAttachmentAction(action) }
        }
      }
    ];

    const dialogRef = this.dialog.open(AttachmentTypeComponent, {
      width: '50%',
      data: { data: '' },
      disableClose: true,
    });
    if (isAdd) {
      dialogRef.componentInstance.hdr = `Attachments for design code :${row.code}`
      dialogRef.componentInstance.isAttachmentUpload = true;
      const supportedDocuments: any[] = JSON.parse(this._commonService.getTenantConfig()?.supportedDocuments)
      dialogRef.componentInstance.documentTypes = supportedDocuments.map(item => {
        return { label: item.name, value: item.name, extensions: item.extensions, required: item.mandatory == 1, message: this.getMessage(item), isPresent: (documentsList.find(doc => doc.docType == item.name) ? true : false) }
      });
    } else {
      dialogRef.componentInstance.isAttachmentUpload = false;
    }
    dialogRef.componentInstance.isViewAttachment = true;
    dialogRef.componentInstance.attachmentList = documentsList.map(item => ({ size: this._commonService.calculateFileSize(item.fileSize), ...item }));
    dialogRef.componentInstance.attachmentColumnsDef = attachmentsColumnDefs;
    dialogRef.componentInstance.onUploadAttachment.subscribe(response => {
      this.onConfirmSaveAttachments(response.files, row, response.selectedAttachment, dialogRef)
    });

  }

  private prepareActionIconButtonsAttachments(row): any[] {
    let iconsList: any[] = []
    iconsList.push({ type: 'view_attachment', title: "Attachments", icon: ICON_LIST.VIEW_BLUE, data: row });
    return iconsList;
  }

  private onAttachmentAction(action): void {
    switch (action.type) {
      case 'view_attachment':
        this.onViewAttachment(action.data);
        break;
    }
  }
  private onViewAttachment(row): void {
    APP_UI_CONFIG.administration.designCodeAttachment.getAttachmentByDesignCodeAndAttachmentType.paramList.designCode = row.code;
    APP_UI_CONFIG.administration.designCodeAttachment.getAttachmentByDesignCodeAndAttachmentType.paramList.attachmentType = row.docType;
    let url = this.invokeService.rePlaceString(APP_UI_CONFIG.administration.designCodeAttachment.getAttachmentByDesignCodeAndAttachmentType);
    window.open(environment.apiUrl + url, '_blank').focus();
  }



  private onUploadProductImages(): void {
    let url = "designCode/picturesUpload";
    let extensions = ".zip";
    const dialogRef = this.dialog.open(UploadFileComponent, {
      width: '50%',
      data: { dailogHeadig: 'Upload Product Images', extensions: extensions, apiData: { 'url': url, type: "POST" }, message: { success: 'Product Images uploaded successfully', error: 'Failed to upload the product images' }, note: `Please use the ${extensions} format to upload the attachment.`, dropFileslabel: 'Product Images' },
      disableClose: true,
    });

    dialogRef.afterClosed()
      .pipe(takeUntil(this.$destroyed))
      .subscribe(result => {
        if (result) {
          this.onRefresh();
        }
      });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(true);
    this.$destroyed.complete();
  }

}
