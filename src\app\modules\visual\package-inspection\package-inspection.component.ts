import { HttpHeaders } from '@angular/common/http';
import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { BaseTableSharedComponent } from 'app/shared/base-table-shared.component';
import { CustomDateTooltipComponent } from 'app/shared/custom-date-tooltip/custom-date-tooltip.component';
import { ImageViewerDialogComponent } from 'app/shared/image-viewer-dialog/image-viewer-dialog.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { environment } from 'environments/environment';

@Component({
  selector: 'app-package-inspection',
  templateUrl: './package-inspection.component.html',
  styleUrls: ['./package-inspection.component.scss']
})
export class PackageInspectionComponent extends BaseTableSharedComponent {
  //table columns
  columnDefs: any[] = [];
  inspectionList: any[] = [];

  public init(): void {
    this.onRefresh();
    super.init();
  }

  private getPackageInspections(): void {
    this.subscription.add(this.invokeService.serviceInvocation(APP_UI_CONFIG.visual.labelCheck.get).subscribe({
      next: response => {
        this.inspectionList = response;
      }, error: error => {

      }
    }))
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Lot No", field: "referenceDataUid", sortable: true,
      },
      {
        headerName: "Manufacture Data", field: "referenceDataName", sortable: true,
        cellRendererFramework: CustomDateTooltipComponent, minWidth: 200,
        cellRendererParams: (params: ICellRendererParams) => params.data
      },
      {
        headerName: "Use By", field: "type", sortable: true,
        cellRendererFramework: CustomDateTooltipComponent, minWidth: 200,
        cellRendererParams: (params: ICellRendererParams) => params.data
      },
      {
        headerName: "MRP", field: "referenceDataUid", sortable: true,
      },
      {
        headerName: "USP", field: "referenceDataName", sortable: true,
      },
      {
        headerName: "Use By", field: "type", sortable: true,
      },
      {
        headerName: "Result", field: "referenceDataName", sortable: true,
      },
      {
        headerName: "Failure Reason", field: "type", sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        minWidth: 200,
        cellRendererFramework: CellActionComponent,
        cellRendererParams: (params) => {
          return { actions: this.prepareActionIconButtons(params.data), onAction: (action) => this.onAction(action) }
        }
      }
    ];
  }
  //Refresh
  public onRefresh(): void {
    this.getPackageInspections();
    this.prepareTableColumns();
  }

  public getAddEvent(event): void {
    this._router.navigate(['visual/addPackage'])
  }

  public onAction(event): void {
    let actionData: any = event;
    if (actionData.status == 'view') {
      this.onViewDocument(actionData.data);
      // this._router.navigate(['visual/viewMtc/' + actionData.data.uuid])
    }
    if (actionData.status == 'viewResponse') {
      // this._router.navigate(['mtc/viewResponse/' + actionData.data.uuid])
    }
    if (actionData.status == 'reProcess') {
      this.reProcess(actionData.data);
    }
    if (actionData.status == 'extract') {
      this.extract(actionData.data);
    }
    if (actionData.status == 'validate') {
      this._router.navigate(['visual/validationPackage/' + actionData.data.uuid])
    }
    if (actionData.status == 'email') {
      // this.getreport(actionData.data)
    }
    if (actionData.status == 'edit') {
      // this.selectedRec = actionData.data;
      // this.showTable = false;
      // this.showEditConfig = true;
    }
  }
  public getOnGetDate(event: any): void {
    this.FromToDate.from = event.from;
    this.FromToDate.to = event.to;
    this.init();
  }

  public prepareActionIconButtons(row): any {
    let iconsList: any[] = [];
    iconsList.push({ type: 'view_po', title: "View PO Details", icon: 'view', data: row });
    // if (!environment.production) {
    //   icons = icons + this._commonService.createIcon('viewResponse', 'View Responses', 'visibility', "", "");
    // }
    // if (!environment.production) {
    //   icons = icons + this._commonService.createIcon('reProcess', 'Reprocess Responses', 'sticky_note_2', "", "");
    // }
    // icons = icons + this._commonService.createIcon('extract', 'Extract Data', 'settings', "", "");
    // icons = icons + this._commonService.createIcon('validate', 'Validate', 'check_circle', "", "");
    // icons = icons + this._commonService.createIcon('email', 'Send Validation Report', 'email', "", "");
    // icons = icons + this._commonService.createIcon('edit', 'Edit Certificate', 'edit', "", "");
    return iconsList;
  }



  private reProcess(row): void {
    APP_UI_CONFIG.visual.packageinspection.reProcessInspection.paramList['id'] = row.uuid;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.visual.packageinspection.reProcessInspection, null, row.uuid).subscribe(data => {
      this._commonService.success('Re-processing data done');
    }, error => {
      this._commonService.handleError(error)
    });
    APP_UI_CONFIG.visual.packageinspection.extractPackageInspectionColors.paramList['id'] = row.uuid;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.visual.packageinspection.extractPackageInspectionColors, null, row.uuid).subscribe(data => {
      this._commonService.success('Re-processing for color extraction done');
    }, error => {
      this._commonService.handleError(error);
    });
    APP_UI_CONFIG.visual.packageinspection.extractPackageInspectionDimensions.paramList['id'] = row.uuid;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.visual.packageinspection.extractPackageInspectionDimensions, null, row.uuid).subscribe(data => {
      this._commonService.success('Extracted dimensions');
    }, error => {
      this._commonService.handleError(error);
    })
  }

  public extract(row): void {
    APP_UI_CONFIG.visual.packageinspection.ocrPackage.paramList['id'] = row.uuid;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.visual.packageinspection.ocrPackage, null, row.uuid).subscribe(data => {
      this._commonService.success('Extracted content');
    }, error => {
      this._commonService.handleError(error);
    });
    APP_UI_CONFIG.visual.packageinspection.extractPackageInspectionColors.paramList['id'] = row.uuid;
    this.invokeService.serviceInvocation(APP_UI_CONFIG.visual.packageinspection.extractPackageInspectionColors, null, row.uuid).subscribe(data => {
      this._commonService.success('Re-processing for color extraction done');
    }, error => {
      this._commonService.handleError(error);
    })
  }


  private onViewDocument(data): void {
    let headers = new HttpHeaders({ 'Content-Type': 'application/csv' })
      .set("Authorization", this._commonService.getBasicAuth()).set('Client', this._commonService.getTenantUid());;
    this.http.get(environment.apiUrl + 'sourcing/activity/' + data.docUid + '/doc', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      this.onViewImage(res.body);
    });

  }
  public onViewImage(imageData): void {
    const dialogRef = this.dialog.open(ImageViewerDialogComponent, {
      data: [imageData],
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {

      }
    });
  }

}
