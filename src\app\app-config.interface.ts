import { AnyTxtRecord } from 'dns';

import { API_TYPES } from './enum-constants';

export interface IAppConfig {
    auth: {
        mfaRegistration: {
            url: string,
            type: string,
        },
        mfaValidation: {
            url: string,
            type: string,
            paramList: { mfaCode: string }
        }
    },
    configuration: {
        getTenant: {
            url: string,
            type: string,
        },
        getTenantConfig: {
            url: string,
            type: string,
            paramList: { tenantUid: string }
        }
    }
    schedules: {
        getSchedules: {
            url: string,
            type: string,
            params: []
        },
        getSchedulesByStatus: {
            url: string,
            type: string,
            paramList?: any
        },
        getSchedule: {
            url: string,
            type: string,
            paramList?: any
        },
        getExceptionSchedules: {
            url: string,
            type: string,
            params: string
        },
        createExceptionAudit: {
            url: string
            type: string,
            payload: any
        },
        updateExceptionAudit: {
            url: string
            type: string,
            id: any,
            payload: any,
            paramList?: any
        }
        getShipmentByPo: {
            url: string
            type: string,
            id: any,
            paramList?: any
        }
        getSalesOrderPolist: {
            url: string
            type: string,
            id: any,
            paramList?: any
        }
        create: {
            url: string,
            type: string,
            payload: any
        },
        createShipment: {
            url: string,
            type: string,
            payload: any
        },
        update: {
            url: string
            type: string,
            id: any,
            payload: any,
            paramList?: any
        },
        deleteSchedules: {
            url: string
            type: string,
            id: any,
            paramList?: any
        },
        getAutiTypeList: {
            url: string
            type: string,
            id: any,
            paramList?: any
        },
        getAuditTypesByType: {
            url: string
            type: string,
            auditType: any,
            paramList?: any
        }
        getAuditTypeById: {
            url: string
            type: string,
            id: any,
            paramList?: any
        }
        sendReport: {
            url: string,
            type: string,
            id: any,
            paramList?: any
        },
        releaseFinalReport: {
            url: string,
            type: string,
            id: any,
            paramList?: any
        },
        getAuditors: {
            url: string
            type: string,
        },
        getVendors: {
            url: string,
            type: string,
        },
        getSellers: {
            url: string,
            type: string,
            paramList?: any
        },
        getMappedSellers: {
            url: string,
            type: string,
            paramList?: any
        },
        getMappings: {
            url: string,
            type: string,
            paramList?: any
        },
        getBrands: {
            url: string,
            type: string,
        },
        getDesignCodes: {
            url: string,
            type: string,
        },
        getCustomers: {
            url: string,
            type: string,
        },
        getFactory: {
            url: string,
            type: string,
        },
        getCategory: {
            url: string,
            type: string,
        },
        getProductTypes: {
            url: string,
            type: string,
        },
        getDefective: {
            url: string,
            type: string,
            params: any
        },
        getScheduleHistory: {
            url: string,
            type: string,
            paramList: {
                auditUid: string
            }
        },
        canUpdateReport: {
            url: string,
            type: string,
            id: any,
            paramList: { id: string }
        },
        byAuditType: {
            url: string,
            type: string,
            paramList: { auditId: string }
        },
        getSchedulesListByType: {
            url: string,
            type: string
            paramList: { from: number, to: number, type: String }
        },
    },
    review: {
        get: {
            url: string,
            type: string,
            paramList: any
        },
        getByAudit: {
            url: string,
            type: string,
            paramList: any
        }
    },
    capa: {
        getCapa: {
            url: string,
            type: string,
            paramList: { type: string, from: number, to: number }
        },
        create: {
            url: string,
            type: string,
            auditUid?: string,
            payload?: string,
            paramList?: any
        },
        updateCapaDetails: {
            url: string,
            type: string,
            paramList: { auditUid: any, capUid: any }
        },
        getCapaDetails: {
            url: string,
            type: string,
            auditUid?: string,
            capUid?: string,
            paramList?: any
        },
        getCapaDetailsByCap: {
            url: string,
            type: string,
            auditUid?: string,
            capUid?: string,
            paramList?: any
        },
        updateCapaStatus: {
            url: String,
            type: String,
            paramList: { auditUid: any, capUid: any, code: any }
        },
        reAuditCapaStatus: {
            url: string,
            type: string,
            auditUid?: string,
            capUid?: string,
            paramList?: any
        },
        updateCapaDetailStatus: {
            url: string,
            type: string,
            auditUid?: string,
            capUid?: string,
            paramList: { auditUid: any, capUid: any, detailUid: any, code: any }
        },
        createReinspection: {
            url: string,
            type: string,
            auditUid?: string,
            paramList?: any
        },
        updateCapaDetailsAttachment: {
            url: string,
            type: string,
            paramList: { auditUid: any, capUid: any },
            payload?: any
        },
        getCapaDetailsAttachment: {
            url: string,
            type: string,
            paramList: { auditUid: any, capUid: any, fileUid: any }
        },
        notifyCapEntries: {
            url: string,
            type: string,
            paramList: { auditUid: any, capDetailUid: any }
        },
        deleteCapAttachment: {
            url: string,
            type: string,
            paramList: { auditUid: any, capUid: any, fileUid: any }
        },
        getCheckpointResponse: {
            url: string,
            type: string,
            paramList: { auditUid: any, checkpointUid: any, }
        },
    },
    inspection: {
        getinspection: {
            url: string,
            type: string,
            params?: string
        },
        getfabricInspection: {
            url: string,
            type: string,
            params?: any
        },
        getPkgInspection: {
            url: string,
            type: string
        },
        getTechparks: {
            url: string,
            type: string,
            params?: any
        },
        createTechparks: {
            url: string,
            type: string,
            payload: any
        },
        UpdateTechparks: {
            url: string,
            type: string,
            id: any,
            payload: any,
            paramList?: any
        },
        getinspectionTemplate: {
            url: string,
            type: string,
        },
        deployinspectionTemplate: {
            url: string,
            type: string,
            paylod?: any,
            id: any,
            paramList?: any
        },
        unDeployinspectionTemplate: {
            url: string,
            type: string,
            paylod?: any,
            id: any,
            paramList?: any
        },
        getVendor: {
            url: string,
            type: string,
        },
        getFactories: {
            url: string,
            type: string,
        },
        getPages: {
            url: string,
            type: string,
            paramList?: any
        },
        getConfigs: {
            url: string,
            type: string,
        },
        validateInspection: {
            url: string,
            type: string,
            paramList?: { ponumber: any, designcode: any, audittype: any }
        }
    },
    cadDesign: {
        getCad: {
            url: string,
            type: string,
        },
        extract: {
            url: string,
            type: string,
            payload: any,
            id: any,
            paramList?: any
        },
        viewBase64: {
            url: string,
            type: string,
            payload: any,
            id: any,
            paramList?: any
        },
        view: {
            url: string,
            type: string,
            payload: any,
            id: any,
            paramList?: any
        },
        dimensions: {
            url: string,
            type: string,
            id: any,
            paramList?: any
        },
        preview: {
            url: string,
            type: string,
            id: any,
            paramList?: any
        },
        uploadCad: {
            url: string,
            type: string,
            payload: any,
        },
        saveBlocks: {
            url: string,
            type: string,
            payload?: any,
            paramList?: any
        },
        exportCad: {
            url: string,
            type: string,
            paramList?: { uuid: any }
        }
    },
    shipment: {
        get: {
            url: string,
            type: string,
            params: any,
        },
        create: {
            url: string,
            type: string,
            payload: any,
        },
        update: {
            url: string,
            type: string,
            id: string,
            payload: string,
            paramList?: any
        },
        delete: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
    },
    activity: {
        getInbox: {
            url: string,
            type: string
        },
        getAllInboxItems: {
            url: string,
            type: string
        },
        getInboxByUser: {
            url: string,
            type: string
        },
        getInboxByStatus: {
            url: string,
            type: string,
            id: string,
            paramList?: { statusCode }
        }
        getInboxItemDetails: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        getInboxItemByUid: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        updateInboxItemStatus: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        updateInboxItemDetails: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        updateInboxItemsDetails: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        getTemplates: {
            url: string,
            type: string,
            params: any
        },
        createOrUpdateTemplate: {
            url: string,
            type: string,
            payload: any,
        },
        getTemplateDetails: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        getTemplateDetailsList: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: { id: any }
        },
        getDesigncodes: {
            url: string,
            type: string,
            params: any
        },
        getDesigncodesByFilter: {
            url: string,
            type: string,
            params: any
        },
        createOrUpdateDesigncode: {
            url: string,
            type: string,
            payload: any,
        },
        getDesigncodeDetails: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        getDesigncodeAuditTrailDetails: {
            url: string,
            type: string,
            uid: string,
            payload: any,
            paramList?: any
        },
        getDesigncodeSummary: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        uploadDesigncodeDocs: {
            url: string,
            type: string,
            paramList?: { designcodeUid: any }
        },
        viewActivityDocument: {
            url: string,
            type: string,
            paramList?: { docUid: any }
        },
        delete: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        getCalenderItems: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        getCalenderSummary: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        updateViewStatus: {
            url: string,
            type: string,
            paramList: { uuid: string, status: any }
        }
    },
    quotation: {
        getRequests: {
            url: string,
            type: string,
            params: any
        },
        getRequestsByStatus: {
            url: string,
            type: string,
            paramList: { statusCode: string }
        },
        createOrUpdateRequest: {
            url: string,
            type: string,
            payload: any,
        },
        sendQuotationByEmail: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        getRequestDetails: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        getResponses: {
            url: string,
            type: string,
            params: any
        },
        getResponsesByStatus: {
            url: string,
            type: string,
            paramList: { statusCode: string }
        },
        createOrUpdateResponse: {
            url: string,
            type: string,
            payload: any,
        },
        acceptResponse: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        shortlistResponse: {
            url: string,
            type: string,
            id: string,
            payload: any
        },
        saveTechSpecifications: {
            url: string,
            type: string
        },
        saveSpecifications: {
            url: string,
            type: string
        },
        negotiateResponse: {
            url: string,
            type: string,
            id: string,
            payload: any
        },
        approveResponse: {
            url: string,
            type: string,
            id: string,
            payload: any
        },
        getResponsesByRequest: {
            url: string,
            type: string,
            paramList: { requestUid: string }
        },
        getResponseDesigncodesByRequest: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        updateResponseStatus: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        getResponsesByRequestAndStatus: {
            url: string,
            type: string,
            id: string,
            status: string,
            payload: any,
            paramList?: any
        },
        updateQuotationResponseDesigncodeDetailsByUid: {
            url: string,
            type: string,
            id: string,
            payload: any,
            paramList?: any
        },
        submitResponseDesigncodeDetailsByUid: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        approveResponseDesigncodeDetailsByUid: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        approveResponseDesigncodeDetails: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        getQuotationResponseDesigncodeDetailsByUid: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        getResponseDetails: {
            url: string,
            type: string,
            paramList: { responseUid: string }
        },
        getRequestDetailsById: {
            url: string,
            type: string,
            paramList: { requestUid: string }
        },
        importPOList: {
            url: string,
            type: string,
            paramList: { responseUid: any }
        },
        getPOList: {
            url: string,
            type: string,
        },
        getPODetails: {
            url: string,
            type: string,
            id: null,
            payload: null,
            paramList: { id: null }
        },
        acceptPO: {
            url: string,
            type: string,
            id: null,
            payload: null,
            paramList?: any
        },
        rejectPO: {
            url: string,
            type: string,
            id: null,
            payload: null,
            paramList?: any
        },
        getFeedbackByUser: {
            url: string,
            type: string,
            paramList?: { responseUid: any }
        }
        shareFeedback: {
            url: string,
            type: string,
            payload: null,
            paramList?: { feedbackCategory: any }
        },
        acceptQuotation: {
            url: string,
            type: string,
            payload: null,
            paramList?: { designcodeUid: any }
        },
        rejectQuotation: {
            url: string,
            type: string,
            payload: null,
            paramList?: { designcodeUid: any }
        },
        approveQuotation: {
            url: string,
            type: string,
            payload: null,
        },
        dropQuotation: {
            url: string,
            type: string,
            payload: null,
            paramList?: { designcodeUid: any }
        },
        createPptUpload: {
            url: string,
            type: string,

        },
        updateFile: {
            url: string,
            type: string,
            paramList: { docUid: any }
        },
        getUploadedFileList: {
            url: string,
            type: string,
            paramList: { uuid: any }
        },
        getAllFilesBasedOnDesignCode: {
            url: string,
            type: string,
            paramList: { designCodeUid: any }
        },

        viewPptAttchments: {
            url: string,
            type: string,
            paramList: { docUid: any }
        },

        getCurrencyList: {
            url: string,
            type: string,
        },
        getFeedbackByResponseUid: {
            url: string,
            type: string,
            paramList?: { responseUid: any }
        },
        updateFeedbackByResponse: {
            url: string,
            type: string,
        },
        getFeedbackByDesignCode: {
            url: string,
            type: string,
            paramList?: { designcodeUid: any }
        },
        sharedFeedbackByDesignCode: {
            url: string,
            type: string,
            payload: any,
            paramList?: { designcodeUid: any }
        },
        attachTestProtocol: {
            url: string,
            type: string,
            payload: any,
        },
        finalSubmitQuotation: {
            url: string,
            type: string,
            payload: any,
        },
        rejectBidding: {
            url: string,
            type: string,
            payload: any,
        },
        dropBidding: {
            url: string,
            type: string,
            payload: any,
        },
        approveBidding: {
            url: string,
            type: string,
            payload: any,
        },
        getTestRequestDetailsByDesignCode: {
            url: string,
            type: string,
            payload: any,
            paramList?: { designcodeUid: any }
        },
        getTestRequestsByVendor: {
            url: string,
            type: string,
            paramList?: { vendorUid: string }
        },
        getTestRequestsByLab: {
            url: string,
            type: string,
            paramList: { labUid: string }
        },
        saveOrUpdateShippingDetails: {
            url: string,
            type: string,
            payload: any,
        },
        updateQuotationResponseViewStatus: {
            url: string,
            type: string,
            payload: any,
        },
        updateQuotationRequestViewStatus: {
            url: string,
            type: string,
            payload: any,
        },
        getAllDataByKeyWord: {
            url: string,
            type: string,
            paramList: { keyWord: any }
        },
        designCodeGroupRequest: {
            url: string,
            type: string,
            payload: any,
        },

    },

    fqaVendorSellerMapping: {
        getRequest: {
            url: string,
            type: string,
            paramList: { type: any }
        },
        getSellerList: {
            url: string,
            type: string,
            paramList: { sellerUid: any }
        },
        assignSeller: {
            url: string,
            type: string,
            payload: any
        },
        assignVendor: {
            url: string,
            type: string,
            payload: AnyTxtRecord
        },
        getByFactory: {
            url: string,
            type: string,
            paramList: { factoryUid: any }
        }
    },

    request: {
        getRequest: {
            url: string,
            type: string,
            params: any
        },
        activate: {
            url: string,
            type: string,
            paramList: { uuid: any }
        },
        deactivate: {
            url: string,
            type: string,
            paramList: { uuid: any }
        },
        setFilters: {
            url: string,
            type: string,
            payload: any
        },
        addSupplierToRequest: {
            url: string,
            type: string,
            payload: any,
            paramList: { requestUid: any }
        },
        submitRequest: {
            url: string,
            type: string,
            paramList?: { requestUid: string }
        },
        getRequestByUid: {
            url: string,
            type: string,
            paramList?: { requestUid: string }
        },
        create: {
            url: string
            type: string,
            payload: any,
        },
        updateIndentRequest: {
            url: string
            type: string,
            paramList: { requestUid: any }
        },
        updateRequestViewStatus: {
            url: string
            type: string,
            payload: any,
        },
        getInspectionProtocolList: {
            url: string,
            type: string,
            paramList: { designCodeUid: any }
        },
        getCollectionListByUid: {
            url: string,
            type: string,
            paramList: { collectionUid: any }
        },
        getDesignCodeListByCollectionUid: {
            url: string,
            type: string,
            paramList: { requestUid: any }
        },
        getTestProtocolList: {
            url: string,
            type: string,
            // paramList: { productTypeUid: any }
        },
        updateDesignCodeConfig: {
            url: string,
            type: string,
            // paramList: { productTypeUid: any }
        },
        getProductTypeList: {
            url: string,
            type: string,
        },
        getProductList: {
            url: string,
            type: string,
        },
        updatePORequestViewStatus: {
            url: string
            type: string,
            payload: any,
        },
        updateSampleRequestViewStatus: {
            url: string
            type: string,
            payload: any,
        },
        updateResponseViewStatus: {
            url: string
            type: string,
        },
        createTempVendorUser: {
            url: string
            type: string,
            payload: any,
        },
        getTempVendorUserByTempCode: {
            url: string
            type: string,
            paramList: { tempCode: any }
        },
        getTempVendors: {
            url: string
            type: string,
        },
        updateIndentRequestBySampling: {
            url: string
            type: string
        }
    },
    sourcingRoadmap: {
        get: {
            url: string,
            type: string,
            paramList: { isALL: any }
        },
        activate: {
            url: string,
            type: string,
            paramList: { uuid: any }
        },
        deactivate: {
            url: string,
            type: string,
            paramList: { uuid: any }
        },
        getByUid: {
            url: string,
            paramList?: { roadMapUid: string }
            type: 'GET',
        },
        getSourcingUserList: {
            url: string,
            type: 'GET',
            params: null
        },
        create: {
            url: string
            type: string,
            payload: any,
        },
        updateDesigncodesByRoadmapUid: {
            url: string,
            type: string,
            payload: any,
            paramList: { roadmpUid }
        },
        updateDesigncodeByDesignCodeUid: {
            url: string,
            type: string,
            payload: any,
            paramList: { designCodeUid }
        },
        copyDesignCode: {
            url: string,
            type: string,
        }
        getDesigncodesByRoadmapUid: {
            url: string,
            type: string,
            paramList: { roadMapUid: any }
        },
        getDocumentsByDesigncodeUid: {
            url: string,
            type: string,
            paramList: { documentUid: any, extension: any }
        },
        getDocumentsByDocUid: {
            url: string,
            type: string,
            paramList: { docUid: any }
        },
        getDocumentList: {
            url: string,
            type: string,
            paramList: { designUid: null, }
        },
        uploadDocumentsByDesigncodeUid: {
            url: string,
            type: string,
            paramList: any
        },
        update: {
            url: string
            type: string,
            payload: string,
            paramList?: { rmid: any }
        },
        updateRoadmapDocumentForImport: {
            url: string,
            type: string,
            payload: any
        },
        uploadAttachmentsByDesignCodeUid: {
            url: string
            type: string,
            payload: string,
            paramList?: { designUid: any }
        },
        designCodeDetailsByUid: {
            url: string
            type: string,
            paramList?: { designCodeUid: any }
        },
        designCodeDetailsByUuid: {
            url: string
            type: string,
            paramList?: { designCodeUid: any }
        },
        getAttachmentsByDesignCodeUid: {
            url: string,
            type: string,
            payload: any,
            paramList: { roadMapUid: null, designCodeUid: null }
        },
        exportRoadmap: {
            url: string
            type: string,
        },
        downloadRoadmapTemplate: {
            url: string
            type: string,
        },
        updateRoadMapViewStatus: {
            url: string
            type: string,
            // payload: any,
        },
        getProductList: {
            url: string
            type: string,
        },
        getUserListBasedOnUserCategoryMapping: {
            url: string
            type: string,
            paramList?: { uuid: any }
        },
        getAllDataByKeyWord: {
            url: string,
            type: string,
            paramList: { keyWord: any }
        }
        getSubCategoryBasedOnCategory: {
            url: string,
            type: string,
            paramList: { isAll: any, parentCategoryUid: any }
        },
    },
    sourcingDesignCode: {
        getRequestDesignCode: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        getRequestDesignCodeDetails: {
            url: string,
            type: string,
            id: string,
            paramList?: any
        },
        importDesignCodes: {
            url: string,
            type: string,
            // paramList: { roadMapUid: null }
        },
        exportDesignCodes: {
            url: string,
            type: string,
            // paramList: { roadMapUid: null }
        },
        downloadDesignCodeTemplate: {
            url: string,
            type: string,
        },
        downloadDocumentByUid: {
            url: string,
            type: string,
            paramList: { docUid: null, extension: any }
        },
    },
    finishGoodsDesignCode: {
        getFinishedGoods: {
            url: string
            type: string
        },
        createFg: {
            url: string
            type: string,
            payload: any,
        },
        createPriceMaster: {
            url: string,
            type: string,
            paramList: { uuid: null | string },
        },
        updatePriceMaster: {
            url: string,
            type: string,
            paramList: { uuid: null | string },
        },
        getUpdatedPriceMaster: {
            url: string,
            type: string,
            paramList: { designCodeUid: null | string },
        },
        getFinishedGoodsByUuid: {
            url: string,
            type: string,
            id: any,
            payload: any,
            paramList?: { uuid: string },
        },
        getArticleTypeList: {
            url: string,
            type: string
        },
        createArticleTypeList: {
            url: string
            type: string,
            payload: any,
        },
        updateArticleTypeList: {
            url: string
            type: string,
            payload: any,
        },
        getSleeveData: {
            url: string,
            type: string
        },
        getNecklineData: {
            url: string,
            type: string
        },
        getFabricCodeData: {
            url: string,
            type: string
        },
        update: {
            url: string
            type: string,
            payload: string,
            paramList?: { uuid: any }
        },
        fabricList: {
            url: string
            type: string,
        },
        createRawMaterialArticle: {
            url: string
            type: string,
            payload: any,
        },
        approveRawMaterialDocument: {
            url: string
            type: string,
            payload: any,
            paramList?: { docUid: any, rmUid: any }
        },
        updateRawMaterialDocument: {
            url: string
            type: string,
            payload: any,
            paramList?: { docUid: any }
        },
        deleteRawMaterialDocument: {
            url: string
            type: string,
            paramList?: { docUid: any }
        },
        updateRawMaterialArticle: {
            url: string
            type: string,
            payload: string,
            paramList?: { uuid: any }
        },
        getRawMaterial: {
            url: string,
            type: string,
        },
        getRawMaterialByFilter: {
            url: string,
            type: string,
            payload: string,
        },
        getRawMaterials: {
            url: string,
            type: string,
            payload: string,
        },
        getFinishedFilterData: {
            url: string,
            type: string,
            payload: string,
        }
        getRawMaterialByUuid: {
            url: string,
            type: string,
            id: any,
            payload: any,
            paramList?: any
        },
        getMeasurements: {
            url: string,
            type: string,
        },
        getMeasurementByUuid: {
            url: string,
            type: string,
            id: any,
            payload: any,
            paramList?: any
        },
        createMeasurement: {
            url: string
            type: string,
            payload: any,
        },
        getGenderList: {
            url: string,
            type: string,
        },
        getFitList: {
            url: string,
            type: string,
        },
        getSourcingLovList: {
            url: string,
            type: string,
            paramList?: { keyword: any }
        },
        getOfferMonthList: {
            url: string,
            type: string,
        },
        getFabricContentGroup: {
            url: string,
            type: string,
        },
        getFabricDesignList: {
            url: string,
            type: string,
        },
        getFabricWaveList: {
            url: string,
            type: string,
        },
        getFabricTypeList: {
            url: string,
            type: string,
        },
        getColorList: {
            url: string,
            type: string,
        },
        getBrands: {
            url: string,
            type: string,
            paramList: { isAll: boolean }
        },
        getCategoryDropdownList: {
            url: string,
            type: string,
        },
        getCategoryDropdownListBasedOnSubBrand: {
            url: string,
            type: string,
            paramList: { subBrandUuid: any, isAll: boolean }
        },
        getCategoryDropdownListBasedOnMultiSubBrands: {
            url: string,
            type: string,
            paramList: { subBrandUuid: any, isAll: boolean }
        },
        getCategoryDropdownListBasedOnMultiSubBrandOrBrandOrCategoryGroup: {
            url: string,
            type: string,
            paramList: { isAll: boolean }
        },
        getSubCategoryDropdownList: {
            url: string,
            type: string,
        },
        getSubCategoryDropdownListBasedOnCategory: {
            url: string,
            type: string,
            paramList: { categoryUuid: any, isAll: boolean }
        },
        getSubBrandsDropdownList: {
            url: string,
            type: string,
        },
        getSubBrandsDropdownListBasedOnBrand: {
            url: string,
            type: string,
            paramList: { brandUuid: any, isAll: boolean }
        },
        getSubBrandsDropdownListBasedOnMultiBrand: {
            url: string,
            type: string,
            paramList: { brandUuid: any, isAll: boolean }
        },
        getSubBrandsDropdownListBasedOnMultiBrands: {
            url: string,
            type: string,
            paramList: { isAll: boolean }
        },
        getCategoryTypeDropdownListBasedOnCategoryGroup: {
            url: string,
            type: string,
            paramList: { group: any }
        },
        getCategoryByCategoryGroupAndSubBrand: {
            url: string,
            type: string,
            paramList: { group: any, subBrand: any, isAll: boolean }
        }
        getSeasonDropdownList: {
            url: string,
            type: string,
        },
        getPriceMaster: {
            url: string,
            type: string,
            id: null,
            payload: any,
            paramList: { categoryUid: any }
        },
        getUomDropdownList: {
            url: string,
            type: string,
        },
        importRawMaterial: {
            url: string,
            type: string,
            paramList: { designCodeUid: null, uuid: null, type: null }
        },
        getRawMaterialDocList: {
            url: string,
            type: string,
            paramList: { designCodeUid: null }
        },
        getRawMaterialDocListByUuid: {
            url: string,
            type: string,
            paramList: { id: null }
        },
        downloadDocumentByUid: {
            url: string,
            type: string,
            paramList: { docUid: null, extension: any }
        },
        createBom: {
            url: string
            type: string,
            payload: any,
        },
        updateBom: {
            url: string
            type: string,
            payload: any,
        },
        getBomList: {
            url: string
            type: string,
        },
        getBomListBybomUid: {
            url: string
            type: string,
            paramList: { bomUid: null | string }
        },
        getBomListByFgId: {
            url: string,
            type: string,
            paramList: { finishedGoodsId: null | string }
        },
        uploadDocumentGeneral: {
            url: string,
            type: string,
            paramList: { type: null | string }
        },
        getDocumentForSourcingByType: {
            url: string,
            type: string,
            paramList: { type: null | string }
        },
        addDocForFG: {
            url: string,
            type: string,
            paramList: { designUid: null | string, type: null | string }
        },
        addDocForFGFromDocList: {
            url: string,
            type: string,
            paramList: { designUid: null | string }
            payload: any,
        },
        getFinishGoodDocList: {
            url: string,
            type: string,
            paramList: { designUid: null | string }
        },
        approveFGDocument: {
            url: string,
            type: string,
            paramList: { designUid: null | string, type: null | string }
        },
        approveFGDocumentAttachedFile: {
            url: string,
            type: string,
            paramList: { designcodeUid: string }
        },
        bomAttachFile: {
            url: string,
            type: string,
            paramList: { bomUid: null | string }
        },
        downloadFGDocumentByUid: {
            url: string,
            type: string,
            paramList: { docUid: null, extension: any }
        },
        downloadRWDocumentByUid: {
            url: string,
            type: string,
            paramList: { docUid: null, extension: any }
        },
        getBrandDepartment: {
            url: string,
            type: string,
        },
        getcategoryGrouping: {
            url: string,
            type: string,
        },
        getcategoryGroupingByArticleType: {
            url: string,
            type: string,
            paramList: { articleType: string }
        },

         getcategoryGroupByArticleType: {
            url: string,
            type: string,
        },
         getcategoryByArticleType: {
            url: string,
            type: string,
            // paramList: { articleType: string }
        },
        getcategoryType: {
            url: string,
            type: string,
        },
        getcategoryGroupingByUuid: {
            url: string,
            type: string,
            paramList: { uuid: null | string }
        },
        createCategoryGrouping: {
            url: string
            type: string,
            payload: any,
        },
        updateCategoryGrouping: {
            url: string
            type: string,
            payload: any,
        },
        getRawMaterialTypes: {
            url: string,
            type: string,
        },
        getRawMaterialTypesBasedOnArticleType: {
            url: string,
            type: string,
            paramList: { articleTypeUuid: null | string }
        },
        getRawMaterialSubTypes: {
            url: string,
            type: string,
            paramList: { rawMaterialType: null | string }
        },
        createRawMaterialTypes: {
            url: string
            type: string,
            payload: any,
        },
        updateRawMaterialTypes: {
            url: string
            type: string,
            payload: any,
        },
        importPriceMaster: {
            url: string,
            type: string
        },
        exportPriceMaster: {
            url: string,
            type: string,
            payload: any
        },
        importBOM: {
            url: string,
            type: string
        },
        exportBOM: {
            url: string,
            type: string
        },
        downloadBOMTemplate: {
            url: string,
            type: string,
        },
        downloadPriceMasterTemplate: {
            url: string,
            type: string,
        },
        importRawMaterialList: {
            url: string,
            type: string
        },
        downloadRawMaterialTemplate: {
            url: string,
            type: string
        },
        exportRawMaterial: {
            url: string,
            type: string,
            payload: any
        },
        addDocForPoShipment: {
            url: string,
            type: string,
            paramList: { shipmentNumber: null | string }
        },
        getPoShipmentDocList: {
            url: string,
            type: string,
            paramList: { po: null | string }
        },
        downloadPoShipmentDocumentByUid: {
            url: string,
            type: string,
            paramList: { docUid: null }
        },
        importFG: {
            url: string,
            type: string,
        },
        downloadFgTemplate: {
            url: string,
            type: string,
        },
        exportFG: {
            url: string,
            type: string,
            payload: any
        },
        addOtherTypeDocForArticle: {
            url: string,
            type: string,
            paramList: { designUid: null | string }
        },
        getAttachments: {
            url: string,
            type: string,
        },
        getArticleTypeListByType: {
            url: string,
            type: string,
            paramList: { type: string }
        },
        getAllArticleTypeList: {
            url: string,
            type: string,
        },
        addDocForRMFromDocList: {
            url: string,
            type: string,
            paramList: { rawMaterialUid: null, designCodeUid: null },
            payload: any
        },
        updateFGOtherTypeDocument: {
            url: string
            type: string,
            payload: any,
            paramList?: { docUid: any }
        },
        deleteFGDocument: {
            url: string
            type: string,
            paramList?: { docUid: any }
        },
        getAllFinishedFilterData: {
            url: string
            type: string,
        }

    },
    reports: {
        bulkFabricInwardReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        fgDispatchForAllFactoriesAndCategoriesReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        fgDispatchForAllFactoriesAndCategoriesQtyWiseReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        wipUpdateForFactoriesReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        wipUpdateForFactoriesQtyWiseReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        leadAvgTimeReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        fobInwardsReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        fgDispatchForAllFactoriesAndCategoriesForRMReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        catWiseRmSamplingReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        catWiseFgSamplingReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        activityStatusReport: {
            url: string,
            type: string,
            paramList: { format: any }
        },
        catWisePoReports: {
            url: string,
            type: string,
            paramList: { format: any }
        },


    }
    sourcingDashboard: {
        TotalSampleRequestCount: {
            url: string,
            type: string
        },
        requestSummary: {
            url: string,
            type: string
        },
        getCategoryWiseSummaryOfFG: {
            url: string
            type: string
        },
        getCategoryWiseSummaryOfRM: {
            url: string
            type: string
        }
        getActivitySummaryByPO: {
            url: string
            type: string
        },
        getActivitySummary: {
            url: string
            type: string
        },
        poQtySummary: {
            url: string
            type: string
        }
    },
    saleOrders: {
        get: {
            url: string,
            type: string,
            params: any,
        },
        create: {
            url: string
            type: string,
            payload: any,
        },
        update: {
            url: string
            type: string,
            id: any,
            payload: string,
            paramList?: any
        },
        delete: {
            url: string
            type: string,
            id: any,
            paramList?: any
        }

    },
    purchaseOrders: {
        get: {
            url: string,
            type: string,
            payload: any,
        },
        getByStatus: {
            url: 'saleOrder/po/{id}/status',
            type: string,
            id: any,
            payload: any,
            paramList?: any
        },
        getByGroup: {
            url: string,
            type: string,
        },
        create: {
            url: string
            type: string,
            payload: any,
        },
        update: {
            url: string
            type: string,
            id: any,
            payload: string,
            paramList?: any
        },
        delete: {
            url: string
            type: string,
            id: any,
            paramList?: any
        },
        getByManufacturer: {
            url: string,
            type: string,
            paramList?: any
        },
        getPoDetailByPoUid: {
            url: string,
            type: string,
            paramList: { uuid: any }
        },
        getPoDetailsByPONumber: {
            url: string,
            type: string,
            paramList: { poNo: any }
        },
        getDesignCodeDetailsByCode: {
            url: string,
            type: string,
            paramList: { code: any }
        }
    },
    mtc: {
        scannedDocuments: {
            url: string,
            type: string,
            externalUrl?: boolean
        },
        reProcess: {
            url: string,
            type: string,
            externalUrl?: boolean,
            id: any,
            paramList?: any
        },
        extract: {
            url: string,
            type: string,
            externalUrl?: boolean,
            id: any,
            paramList?: any
        },
        getProjects: {
            url: string,
            type: string,
        },
        getCertificates: {
            url: string,
            type: string,
        },
        getCertificateTypes: {
            url: string,
            type: string,
        },
        getCertificateType: {
            url: string,
            type: string,
            id: any,
            paramList?: any
        },
        uploadCertificate: {
            url: string,
            type: string,
            payload: any,
        },
        getImage: {
            url: string,
            type: string,
            id: any,
            paramList?: any
        },
        getRefData: {
            url: string,
            type: string,
        },
        saveImage: {
            url: string,
            type: string,
            payload: any
        },
        getImageFile: {
            url: string,
            type: string,
            id: any,
            paramList?: any
        },
        ocrWithUpdate: {
            url: string,
            type: string,
            payload: any,
            id: any,
            paramList?: any
        },
        addImageConfi: {
            url: string,
            type: string,
            payload: any,
            id: any,
            paramList?: any
        },
        autoOcrWithImageConfig: {
            url: string,
            type: string,
            payload: any,
            externalUrl?: boolean,
            id: any,
            paramList?: any
        },
        ocr: {
            url: string,
            type: string,
            payload: any,
            id: any,
            paramList?: any
        },
        autoOcr: {
            url: string,
            type: string,
            payload: any,
            externalUrl?: boolean,
            id: any,
            paramList?: any
        },
        getImageResponse: {
            url: string,
            type: string,
            id: any,
            paramList?: any
        },
        autoOcrReProcess: {
            url: string,
            type: string,
            externalUrl?: boolean,
            payload: any;
            id: any,
            paramList?: any
        },
        validateImage: {
            url: string,
            type: string,
            id: any,
            paramList?: any
        }
        sentEmailReport: {
            url: string,
            type: string,
            id: null,
            paramList?: any
        },
        materials: {
            get: {
                url: string,
                type: string,
            }
        },

    },
    masterdata: {
        auditor: {
            getauditor: {
                url: string,
                type: string,
                param: string
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            getallauditors: {
                url: string,
                type: string,
            },
            getAuditorsByAgency: {
                url: string,
                type: string,
                paramList: { agencyUid: string }
            },
            getAuditorsByLab: {
                url: string,
                type: string,
                paramList: { labUid: string }
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        auditorincharge: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getall: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: { id: any }
            }
        },
        customers: {
            get: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            createcustomers: {
                url: string,
                type: string,
                payload: string
            },
            updatecustomers: {
                url: string,
                type: string,
                payload: string,
                id: any,
                paramList?: any
            }
        },
        brands: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string,
            },
            updatebrands: {
                url: string,
                type: string,
                param: string,
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any,
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
        },
        articleType: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        brandDepartment: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        userCategory: {
            get: {
                url: string,
                type: string,
                paramList?: any
            },
            create: {
                url: string,
                type: string,
                param: string
            },
            update: {
                url: string,
                type: string,
                param: string
            }
        },
        categoryScreen: {
            get: {
                url: string,
                type: string,
                paramList: string
            },
            create: {
                url: string,
                type: string,
                param: string
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            }
        },
        uom: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        }
        fit: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        subCategory: {
            get: {
                url: string,
                type: string,
                paramList: { level: any }
            },
            create: {
                url: string,
                type: string,
                payload: any,
            }
        },
        subBrand: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
                param: string
            },
            update: {
                url: string,
                type: string,
                payload: any,
            },
        },
        offerMonth: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        fabricType: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        fabricWeave: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        season: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        fabricContentGroup: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        fabricDesign: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        sleeve: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        categoryType: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        categoryGroup: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        rawMaterialType: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        neckline: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        color: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                param: string
            }
        },
        productType: {
            get: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any,
            },
            update: {
                url: string,
                type: string,
                payload: any,
            }
        },
        vendor: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getvendor: {
                url: string,
                type: string,
                param: string
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            allVendors: {
                url: string,
                type: string,
                param: string
            },
            mappedVendors: {
                url: string,
                type: string,
                paramList?: any
            },
            getFactoryProductTypes: {
                url: string,
                type: string,
                paramList: any
            },
            getFactoryProducts: {
                url: string,
                type: string,
                paramList: any
            }
        },
        testingLab: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getByRole: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            getInternalLab: {
                url: string,
                type: string,
                param: string
            }
        },
        seller: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getByUid: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            addOrUpdate: {
                url: string,
                type: string,
                payload: any
            }
        },
        inspectionAgency: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getByType: {
                url: string,
                type: string,
                paramList: { type: string }
            },
            getByUid: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            addFiles: {
                url: string,
                type: string,
                payload: any,
                paramList?: any
            },
            getSupportingFiles: {
                url: string,
                type: string,
                payload: any,
                paramList?: any
            },
        },
        factory: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getByUid: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            addFactory: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            getByVendor: {
                url: string,
                type: string,
                id: any,
                paramList: { id: any }
            }
        },
        designCodeMaster: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getSellerListByDesignCodes: {
                url: string,
                type: string,
                paramList: { category: string }
            },
            getVendorListByCategories: {
                url: string,
                type: string,
                paramList: { category: string }
            },
            getSellerListByVendorUId: {
                url: string,
                type: string,
                paramList: { vendorUid: string }
            },
            getVendorListBySeller: {
                url: string,
                type: string,
                paramList: { sellerUid: string }
            },
            updateTestRequests: {
                url: string,
                type: string,
                payload: any
            },
            getSmeOrLabListByDesignCodes: {
                url: string,
                type: string,
                paramList: { category: string, type: string }
            },
            getByCode: {
                url: string,
                type: string,
                paramList: { code: string }
            },
            getByBulkDesignCode: {
                url: string,
                type: string,
                paramList: { codes: string }
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            getAllDesignCodes: {
                url: string,
                type: string
            },
            getFullDesignCodes: {
                url: string,
                type: string
            },
            deleteDesignCode: {
                url: string,
                type: string,
                paramList: { designCodeUid: any }
            },
        },
        auditorfactory: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getfactory: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any,
            },
            update: {
                url: string,
                type: string,
                payload: any,
            }
        },
        category: {
            get: {
                url: string,
                type: string,
                paramList: { level: any }
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            add: {
                url: string,
                type: string,
                payload: any,
            },
            update: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            getWithoutSourcing: {
                url: string,
                type: string,
                paramList: { level: any }
            },
            addWithoutSourcing: {
                url: string,
                type: string,
                payload: any,
            },
        },
        facilityCameraInspectionConfig: {
            get: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
            },
            create: {
                url: string,
                type: string,
                payload: any
            },
            download: {
                url: string,
                type: string
            }
        },
        fabrics: {
            getfabrics: {
                url: string,
                type: string,
                param: string
            }
        },
        timelineactivity: {
            get: {
                url: string,
                type: string,
                param: string
            }
        },
        customdashboard: {
            getcustomdashboard: {
                url: string,
                type: string,
                param: string
            }
        },
        schedulePlanning: {
            getSchedulePlanning: {
                url: string,
                type: string
                paramList: { type: String, from: number, to: number }
            },
            getSchedulesPlanningList: {
                url: string,
                type: string
                paramList: { from: number, to: number, type: String }
            },
            saveSchedulePlanning: {
                url: string,
                type: string,
                payload: any
            },
            updateSchedulePlanning: {
                url: string,
                type: string,
                payload: any
            },
            getSchedulePlanningById: {
                url: string,
                type: string,
                paramList: { uuid: null }
            },
            getSchedulePlanningByAudiId: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
        },
        userRole: {
            get: {
                url: string,
                type: string
            },
            add: {
                url: string,
                type: string
            }
        },
        users: {
            getusers: {
                url: string,
                type: string,
                paramList: { role: string }
            },
            add: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            getByUid: {
                url: string,
                type: string,
                paramList: { uuid: any }
            },
        },
        userCategoryMapping: {
            getByCategory: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
        },
        scheduleFiledConfig:{
            getAll:{
                url: string,
                type: string,
            },
            getScheduleConfigByAuditTypeUid: {
                url: string,
                type: string,
                paramList: { auditTypeUid: string }
            },
            updateScheduleConfigByAuditTypeUid: {
                url: string,
                type: string,
                paramList: { auditTypeUid: string }
                payload:string
            }
        },
        masterDynamics:{
            getAllApi:{
                url:string,
                type:string,
            }
        }
    },
    administration: {
        homePageCount: {
            get: {
                url: string,
                type: string,
                paramList: { page: string }
            }
        },
        getRolesList: {
            url: string,
            type: string,
        },
        getUserByRole: {
            url: string,
            type: string,
            paramList: { role: string }
        },
        getAllUsersData: {
            url: string,
            type: string,
        },
        getAllUsersByRole: {
            url: string,
            type: string,
            paramList: { role: string }
        },
        organization: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                payload: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            update: {
                url: string,
                type: string,
                payload: string
            },
            deactivate: {
                url: string,
                type: string,
                id: null,
                paramList?: any
            }
        },
        state: {
            get: {
                url: string,
                type: string,
            },
            activate: {
                url: string,
                type: string,
                id: any,
                payload: any,
                paramList?: any
            },
            deActivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            addstate: {
                url: string,
                type: string,
                payload: any
            },
            updatestate: {
                url: string,
                type: string,
                payload: any,
            },
            addstatetransition: {
                url: string,
                type: string,
                payload: any
            },
            updatestatetransition: {
                url: string,
                type: string,
                payload: any,
            },
            getTransition: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        auditcategories: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                payload: string
            }
            update: {
                url: string,
                type: string,
                payload: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: null,
                paramList?: any
            }
        },
        audittypes: {
            get: {
                url: string,
                type: string,
            },
            getUnmappedCheckpointCategories: {
                url: string,
                type: string,
            },
            getaudittype: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                payload: string
            },
            update: {
                url: string,
                type: string,
                payload: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            getAllCategoryTypes: {
                url: string,
                type: string,
            },
            getAllAuditCategories: {
                url: string,
                type: string,
            }
        },
        products: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                payload: string
            },
            update: {
                url: string,
                type: string,
                payload: string
            },
            activate: {
                url: string,
                type: string,
                payload: string,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        aqlType: {
            get: {
                url: string,
                type: string,
                param: string
            }
        }
        aql: {
            get: {
                url: string,
                type: string,
                param: string
            },
            getByType: {
                url: string,
                type: string,
                paramList: any
            },
            create: {
                url: string,
                type: string,
                payload: string
            },
            update: {
                url: string,
                type: string,
                payload: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        producttypeaql: {
            get: {
                url: string;
                type: string;
                param: string
            },
            create: {
                url: string;
                type: string;
                payload: string
            },
            update: {
                url: string;
                type: string;
                payload: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        optiontype: {
            get: {
                url: string,
                type: string,
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            create: {
                url: string,
                type: string,
                payload: any
            },
            update: {
                url: string,
                type: string,
                payload: any
            },
            downloadOptionTypes: {
                url: string,
                type: string,
            }
        },
        attributes: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                payload: string
            },
            update: {
                url: string,
                type: string,
                payload: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        panelties: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                payload: string
            },
            update: {
                url: string,
                type: string,
                payload: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        checkpoints: {
            get: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            getOptionTypes: {
                url: string,
                type: string,
            },
            getAllOptionTypes: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                payload: any,
            },
            create: {
                url: string,
                type: string,
                payload: any,
            },
            export: {
                url: string,
                type: string,
            }
        },
        checkpointscategory: {
            get: {
                url: string,
                type: string,
            },
            getcheckpointscategory: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            getunmappedCheckPoints: {
                url: string,
                type: string
            }
            getCheckpointsByCategory: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            getAllCheckpoints: {
                url: string,
                type: string
            },
            updateCheckpointCategory: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            }
            createCheckpointCategory: {
                url: string,
                type: string,
                payload: any
            },
            activateCheckpointCategory: {
                url: string,
                type: string,
                payload: any
            },
            deactivateCheckpointCategory: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            productCheckpointCategory: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        defecttypes: {
            get: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
            },
            create: {
                url: string,
                type: string,
                payload: any,
            },
            downloadDefects: {
                url: string,
                type: string,
            }
        },
        defecttypecategory: {
            get: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            create: {
                url: string,
                type: string,
                payload: any,
            },
            update: {
                url: string,
                type: string,
                payload: any,
            },
        }
        configurations: {
            get: {
                url: string,
                type: string,
                param: string
            },
            create: {
                url: string,
                type: string,
                payload: any,
            },
            update: {
                url: string,
                type: string,
                payload: any,
            }
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            getAuditCategories: {
                url: string,
                type: string,
            },
            getProductAllType: {
                url: string,
                type: string,
            },
            getAllAuditTypes: {
                url: string,
                type: string
            },
            getAllAttributes: {
                url: string,
                type: string
            },
            getAllCheckpointCategories: {
                url: string,
                type: string
            },
            getAllDefectTypeCategories: {
                url: string,
                type: string
            },
            getAllPenalties: {
                url: string,
                type: string
            },
            getConfigurationDetails: {
                url: string,
                type: string,
                paramList?: any
            },
            viewJson: {
                url: string,
                type: string,
                id: string,
                paramList?: any
            },
            viewReportTemplate: {
                url: string,
                type: string,
                id: string,
                responseType: string,
                paramList?: any
            },
            getCategories: {
                url: string,
                type: string
            },
            getCategoriesByUser: {
                url: string,
                type: string
            },
            getCategoryMappedUser: {
                url: string,
                type: string,
                paramList?: any
            }
        },
        referenceData: {
            get: {
                url: string,
                type: string,
            },
            activate: {
                url: string,
                type: string,
                id: any,
                payload: any,
                paramList?: any
            },
            deActivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            getMtcRefenceDataById: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            saveOrUpdateMtcRefenceData: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            getMtcCertTypeByUid: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            saveOrUpdateMtcCertificateType: {
                url: string,
                type: string,
                payload: any
            },
            export: {
                url: string,
                type: string,
            }
        },
        visualReferenceData: {
            get: {
                url: string,
                type: string,
            },
            activate: {
                url: string,
                type: string,
                id: any,
                payload: any,
                paramList?: any
            },
            deActivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },

            getMtcRefenceDataById: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            saveOrUpdateMtcCertificateType: {
                url: string,
                type: string,
                payload: any
            },
        },
        facilities: {
            get: {
                url: string,
                type: string,
            },
            saveOrUpdate: {
                url: string,
                type: string,
                payload: any
            },
            viewCams: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        facilityEdge: {
            get: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            updateFacilityEdge: {
                url: string,
                type: string,
                payload: any,
            },
            create: {
                url: string,
                type: string,
                payload: any,
            },
            downloadEdges: {
                url: string,
                type: string,
            }
        },
        facilityNotifier: {
            get: {
                url: string,
                type: string,
                param: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            update: {
                url: string,
                type: string,
                payload: any,
            },
            create: {
                url: string,
                type: string,
                payload: any,
            },
            downloadNotifiers: {
                url: string,
                type: string,
            }
        },
        facilityCamera: {
            get: {
                url: string,
                type: string,
                param: string
            },
            activateFacilityCamera: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivateFacilityCamera: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            updateFacilityCamera: {
                url: string,
                type: string,
                payload: any,
            },
            updateFacilityCameraConfig: {
                url: string,
                type: string,
                payload: any,
            },
            create: {
                url: string,
                type: string,
                payload: any
            },
            downloadCameras: {
                url: string,
                type: string
            }
        },
        cameraZone: {
            getCames: {
                url: string,
                type: string,
            },
            getCamByUid: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            saveOrUpdateFacilityCam: {
                url: string,
                type: string,
                payload: any
            },
            uploadCamImage: {
                url: string,
                type: string,
                id: any,
                payload: any,
                paramList?: any
            },
            getSampleFile: {
                url: string,
                type: string,
                id: any,
                paramList?: any,
            }
        },
        cadApp: {
            getCadApp: {
                url: string,
                type: string,
            },
        },
        equipment: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
                payload: string
            },
            activate: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            deactivate: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }
        },
        schedulePermission: {
            get: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
            },
            getAuditTypes: {
                url: string,
                type: string,
            },
            getAuditTypesWithoutSapFlag: {
                url: string,
                type: string,
            }
        },
        designCodeAttachment: {
            getAttachements: {
                url: string,
                type: string,
            },
            uploadAttachment: {
                url: string,
                type: string,
                paramList: { designCode: string, attachmentType: string }
            },
            designCodePictureUpload: {
                url: string,
                type: string,
            },
            getAttachmentListByDesignCode: {
                url: string,
                type: string,
                paramList: { designCode: string }
            },
            getAttachmentByDesignCodeAndAttachmentType: {
                url: string,
                type: string,
                paramList: { designCode: string, attachmentType: string }
            },
        },

    },
    visual: {
        facilityNotifications: {
            getNotifications: {
                url: string,
                type: string,
                param: string
            },
            downloadNotifiers: {
                url: string,
                type: string,
            }
        },
        packageinspection: {
            get: {
                url: string,
                type: string,
                param: any,
                externalUrl: boolean
            },
            getVisualRefData: {
                url: string,
                type: string,
                externalUrl: boolean
            },
            addVisualPackage: {
                url: string,
                type: string,
                payload: any,
                externalUrl: boolean,
            },
            uploadVisualPackageDimensions: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            },
            reProcessInspection: {
                url: string,
                type: string,
                payload: any,
                id: any,
                externalUrl: boolean,
                paramList?: any
            },
            extractPackageInspectionColors: {
                url: string,
                type: string,
                payload: any,
                id: any,
                externalUrl: boolean,
                paramList?: any
            },
            extractPackageInspectionDimensions: {
                url: string,
                type: string,
                payload: any,
                id: any,
                externalUrl: boolean,
                paramList?: any
            },
            ocrPackage: {
                url: string,
                type: string,
                payload: any,
                id: any,
                externalUrl: boolean,
                paramList?: any
            },
            getPackageInspectionResponses: {
                url: string,
                type: string,
                id: any,
                externalUrl: boolean,
                paramList?: any
            },
            validatePackageInspection: {
                url: string,
                type: string,
                id: any,
                externalUrl: boolean,
                paramList?: any
            },
            getPackageInspectionColors: {
                url: string,
                type: string,
                id: any,
                externalUrl: boolean,
                paramList?: any
            },
            getDimensions: {
                url: string,
                type: string,
                id: any,
                externalUrl: boolean,
                paramList?: any
            }

        },
        surfaceInspection: {
            get: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            },
            create: {
                url: string,
                type: string,
                payload: any,
                id: any,
                paramList?: any
            }
        },
        labelCheck: {
            get: {
                url: string,
                type: string,
            }
        },
    }
    videoAnalytics: {
        licensePlate: {
            get: {
                url: string,
                type: string,
                externalUrl: boolean
            },
            extractLicensePlate: {
                url: string,
                type: string,
                externalUrl: boolean,
                paylod: any,
                id: any,
                paramList?: any
            },
            reProcesstLicensePlate: {
                url: string,
                type: string,
                externalUrl: boolean,
                paylod: any,
                id: any,
                paramList?: any
            },
            getValidationMessages: {
                url: string,
                type: string,
                externalUrl: boolean,
                paylod: any,
                id: any,
                paramList?: any
            }


        },
    },
    documentManagement: {
        docManagement: {
            get: {
                url: string,
                type: string,
                externalUrl: boolean
            },
            getTree: {
                url: string,
                type: string,
                id: any,
                paramList?: any
            }

        }
    },
    testingModule: {
        testRequests: {
            get: {
                url: string,
                type: string,
            },
            getTestRequestsById: {
                url: string,
                type: string,
                paramList: any
            },
            getTestRequestsByAuditId: {
                url: string,
                type: string,
                paramList: any
            },
            exportTRF: {
                url: string,
                type: string,
            },
            cancelTRF: {
                url: string,
                type: string,
                paramList: any
            },
        }
    },
    testRequests: {
        protocol: {
            create: {
                url: string,
                type: string,
            },
            get: {
                url: string,
                type: string,
                externalUrl: boolean
            },
            getProtocol: {
                paramList?: any,
                url: string,
                type: string,
                externalUrl: boolean
            },
            getProtocolRequirement: {
                url: string,
                type: string,
                externalUrl: boolean
            },
            getProtocolRequirementByProductType: {
                paramList?: any,
                url: string,
                type: string,
                externalUrl: boolean
            },
            getIncidents: {
                url: string,
                type: string,
            },
            getPolicyDetailsByUid: {
                url: string,
                type: string,
                paramList: { policyUid: any }
            },
            updateCheckpointByUid: {
                url: string,
                type: string
            },
            executeCheckpoint: {
                url: string,
                type: string
            },
        }
    },
    sampling: {
        getCollectionRequests: {
            url: string
            type: string,
            paramList: { collectionRequestUid: any }
        },
        createSampleRequest: {
            url: string
            type: string,
            payload: any,
        },
        getRequests: {
            url: string,
            type: string,
        },
        getResponses: {
            url: string,
            type: string,
        },
        getRequestDetailsById: {
            url: string,
            type: string,
            paramList: { sampleRequestUid: any }
        },
        getResponseByRequesById: {
            url: string,
            type: string,
            paramList: { sampleRequestUid: any }
        },
        getResponseDetailsById: {
            url: string,
            type: string,
            paramList: { sampleRequestUid: any }
        },
        acceptSample: {
            url: string,
            type: string,
        },
        rejectSample: {
            url: string,
            type: string,
        },
        dispatchSample: {
            url: string,
            type: string,
        },
        receivedSample: {
            url: string,
            type: string,
        },
        feedbackSample: {
            url: string,
            type: string,
        },
        getdesignCodefeedbackSample: {
            url: string,
            type: string,
            paramList: { designCodeUid: null }
        },
        getRawMaterialfeedbackSample: {
            url: string,
            type: string,
            paramList: { rawMaterialUid: null }
        },
        sampleSubmitForApproval: {
            url: string,
            type: string,
        },
        approveIfApproveRequired: {
            url: string,
            type: string,
        },
        rejectIfApproveRequired: {
            url: string,
            type: string,
        },
        dropIfApproveRequired: {
            url: string,
            type: string,
        },
        saveOrUpdateSampleFeedback: {
            url: string,
            type: string,
        },
        getRejectedFeedback: {
            url: string,
            type: string,
            paramList: { poNUmber: null }
        }
    }
    userManagement: {
        getUsersByRole: {
            url: string,
            type: string,
            paramList: { role: string }
        }
    },
    PO: {
        getPOList: {
            url: string,
            type: string,
        },
        getPODetailsByUid: {
            url: string,
            type: string,
            paramList: { uid: any }
        },
        getPODetailsByPoNumber: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        getDesignCodeActivityByPoNumber: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        importPOList: {
            url: string,
            type: string,
            paramList: { responseUid: any }
        },
        importPOShipmentList: {
            url: string,
            type: string,
        },
        getShipmentListByPONumber: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        closeShipment: {
            url: string,
            type: string,
        },
        closeGRN: {
            url: string,
            type: string,
        },
        closeArticle: {
            url: string,
            type: string,
        },
        closePo: {
            url: string,
            type: string,
        },
        saveShipment: {
            url: string,
            type: string,
        },
        updateShipmentGRN: {
            url: string,
            type: string,
        },
        submitPOByNumber: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        getSizeCodesDetailsForFg: {
            url: string,
            type: string,
            paramList: { designcodeUid: any }
        },
        acceptPOByNumber: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        rejectPOByNumber: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        closeArticlesByPoUid: {
            url: string,
            type: string,
            paramList: { poUid: any }
        },
        getShipmentDetailsByPonumber: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        getShipmentDetailsByShipmentnumber: {
            url: string,
            type: string,
            paramList: { shipmentNumber: any }
        },
        getPoDetailsByShipmentCode: {
            url: string,
            type: string,
            paramList: { poNumber: any, shipmentCode: any }
        },
        deletePOByNumber: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        getShipmentList: {
            url: string,
            type: string,
        },
        // getPoDetailsByShipmentNumber:{
        //     url:string,
        //     type:string,
        //     paramList:{shipmentNo : any}
        // }
        getShipmentDetailsByShipmentNo: {
            url: string,
            type: string,
            paramList: { shipmentNo: any }
        },
        getShipmentSizeWiseData: {
            url: string,
            type: string,
            paramList: { shipmentNo: any, designcodeUid: any }
        },
        getActivitiesByPo: {
            url: string,
            type: string,
            paramList: { processType: any }
        },
        downloadPOTemplate: {
            url: string,
            type: string,
        },
        downloadPurchaseOrderTemplate: {
            url: string,
            paramList: { PurchaseOrder: any },
            type: string,
        },
        deletePo: {
            url: string,
            type: string,
            paramList: { purchaseOrderNo: any }
        },
        downloadPOShipmentTemplate: {
            url: string,
            type: string,
        },
        exportPO: {
            url: string,
            type: string,
            payload: any
        },
        exportShipment: {
            url: string,
            type: string,
            payload: any
        },
        getDailyProductionTillDateDataByPo: {
            url: string,
            type: string,
            paramList: { poNumber: any, date: any }
        },
        getDailyProductionByDateDataByPo: {
            url: string,
            type: string,
            paramList: { poNumber: any, date: any }
        },
        getDailyProductionDataByPo: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        updateDailyProductionDataByPo: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        updatePoDetails: {
            url: string,
            type: string,
            payload: any,
            paramList: { poNumber: any }
        },
        getTASummaryByDesignCode: {
            url: string,
            type: string,
            paramList: { poNumber: any, designCodeUid: any }
        },
        downloadPOReport: {
            url: string,
            type: string,
            paramList: { poNumber: any }
        },
        getShipmentDetailsByArticleNumber: {
            url: string,
            type: string,
            paramList: { poNumber: any, productCode: any }
        },
        updatePORejectFeedback: {
            url: string,
            type: string,
        },
        startPOActivity: {
            url: string,
            type: string,
        },
        getInvoiceList: {
            url: string,
            type: string,
        },
        getPendingInvoiceList: {
            url: string,
            type: string
        },
        vieBomDetails: {
            url: string,
            type: string,
            paramList: { finishedGoodsId: null }
        }

    }
    TechSpecification: {
        getList: {
            url: string,
            type: string,
            paramList: { productTypeUid: string }
        },
        getListByGroup: {
            url: string,
            type: string,
        },
        getListDataByGroupUid: {
            url: string,
            type: string,
        },
        addConfiguration: {
            url: string,
            type: string,
        },
        getConfigurationByUid: {
            url: string,
            paramList: { uuid: any },
            type: string,
        },
        updateConfiguration: {
            url: string,
            type: string,
        },
        deleteConfiguration: {
            url: string,
            type: string,
            paramList: { configUuid: any }
        },
        getProductTypeByDesignCodeUid: {
            url: string,
            type: string,
            paramList: { designCodeUid: any }
        },
        getSpecificationsByUid: {
            url: string,
            type: string,
            paramList: { responseUid: string, designCodeUid: string }
        },
        getSpecGroupList: {
            url: string,
            type: string,
        },
        downloadSpecGroupTemplate: {
            url: string,
            type: string,
        },
        downloadSpecConfigTemplate: {
            url: string,
            type: string,
        },
        getSpecGroupListByCategory: {
            url: string,
            type: string,
            paramList: { categoryUid: any }
        },
        addSpecGroup: {
            url: string,
            type: string,
        },
        updateSpecGroup: {
            url: string,
            type: string,
        },
        deleteSpecGroup: {
            url: string,
            type: string,
            paramList: { uuid: any }
        },
        exportGroup: {
            url: string
            type: string,
        },
        exportGroupTechSpecification: {
            url: string
            type: string,
        },
        mapCategoeyToGroup: {
            url: string,
            type: string,
            paramList: { roadmapuuid: any }
        },
        getMasterSpecsUOM: {
            url: string,
            type: string,
        },
        getExistingGroupList: {
            url: string,
            type: string,
        },
        getExistingSpecificationList: {
            url: string,
            type: string,
        }
    }

    qualityDashBoard: {
        getStatsByAllAuditType: {
            url: string,
            type: string,
        },
        getDesignCodeWiseDataForTable: {
            url: string,
            type: string,
        },
        getStatsByAuditType: {
            url: string,
            type: string,
        }
        getDefectCount: {
            url: string,
            type: string,
        }
        getCheckpointSummary: {
            url: string,
            type: string,
        }
        getDashBoardFilterList: {
            url: string,
            type: string,
        }
        getOptionListForFilters: {
            url: string,
            type: string,
        }
        getTopNDefects: {
            url: string,
            type: string,
        }
        getNotificationList: {
            url: string,
            type: string,
            paramList: { userId: any }
        }
    },
    warranty: {
        getAllWarranties: {
            url: string,
            type: string,
        },
        acceptWarranty: {
            url: string,
            type: string,
            paramList: { warrantyNumber: any }
        },
        rejectWarranty: {
            url: string,
            type: string,
            paramList: { warrantyNumber: any }
        }
    },
    policyDashboard: {
        getOption: {
            url: string,
            type: string,
        },
        classificationDocumentUpload: {
            url: string,
            type: string,
        },
        getDocumentClassificationList: {
            url: string,
            type: string,
        }
    },
    artwork:{
        getDocRefereAll:{
            url: string,
            type: string,
        },
        getDocRefereByUid:{
            url: string,
            type: string,
            paramList: { uuid: any }
        }
    }
}