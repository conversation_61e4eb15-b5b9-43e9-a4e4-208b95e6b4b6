<form #f="ngForm" novalidate class="w-full flex flex-col p-2 pb-0">
    <div [innerHTML]="capTitle" class="w-full questionBackgrd mb-2"></div>
    <div class="flex flex-row flex-wrap items-baseline p-2">
        <div class="col-md-4 flex flex-col gap-1 mb-2">
            <b class="flex flex-row items-center gap-2 ">Cause
                <span *ngIf=" formData.status == 'edit' && !editableFields.isCauseDisabled"
                    class="requiredField">*</span>:
                <i class="pi pi-question-circle"
                    pTooltip="Cause refers to the underlying reason or source of a problem or issue"
                    tooltipPosition="right" style="font-size:1rem;color:#517EBC;cursor:pointer"></i>
            </b>
            <div *ngIf=" formData.status != 'edit'" class="pr-8 colorAqs mb-2">{{rec.cause?rec.cause:'N/A'}}</div>
            <ng-container *ngIf="formData.status == 'edit'">
                <textarea class="width90" [rows]="2" [cols]="30" [(ngModel)]="rec.cause" name="cause" id="cause"
                    placeholder="Cause" pInputTextarea [disabled]="editableFields.isCauseDisabled" maxlength="200"
                    required></textarea>
                <small class="warning-message" *ngIf="rec.cause?.length==200">
                    Character limit reached, max 200 characters allowed.
                </small>
            </ng-container>
        </div>

        <div class="col-md-4 flex flex-col gap-1 mb-2">
            <b class="flex flex-row items-center gap-2 ">Corrective Action
                <span *ngIf=" formData.status == 'edit'" class="requiredField">*</span>:
                <i class="pi pi-question-circle"
                    pTooltip="Corrective action refers to the systematic process of identifying, addressing, and eliminating the root cause(s) of a non-conformance, deviation, or problem to prevent its recurrence"
                    tooltipPosition="right" style="font-size:1rem;color:#517EBC;cursor:pointer"></i>
            </b>
            <div *ngIf="formData.status != 'edit'" class="pr-8 colorAqs mb-2">
                {{rec.correctiveAction?rec.correctiveAction:'N/A'}}
            </div>
            <ng-container *ngIf="formData.status == 'edit'">
                <textarea class="width90" [rows]="2" [cols]="30" [(ngModel)]="rec.correctiveAction"
                    name="correctiveAction" placeholder="Corrective Action" pInputTextarea
                    [disabled]="editableFields.isCauseDisabled" required maxlength="255"></textarea>
                <small class="warning-message" *ngIf="rec.correctiveAction?.length==255">
                    Character limit reached, max 255 characters allowed.
                </small>
            </ng-container>
        </div>

        <div class="col-md-4 flex flex-col gap-1 mb-2">
            <b class="flex flex-row items-center gap-2 ">Preventive Action
                <span *ngIf=" formData.status == 'edit' && !editableFields.isPreventiveActionDisabled"
                    class="requiredField">*</span>:
                <i class="pi pi-question-circle"
                    pTooltip="Preventive action refers to the proactive process of identifying, addressing, and eliminating potential sources of non-conformances, deviations, or problems to prevent their occurrence in the future. "
                    tooltipPosition="right" style="font-size:1rem;color:#517EBC;cursor:pointer"></i>
            </b>
            <div *ngIf=" formData.status != 'edit'" class="pr-8 colorAqs mb-2">
                {{rec.preventiveAction?rec.preventiveAction:'N/A'}}
            </div>
            <ng-container *ngIf="formData.status == 'edit'">
                <textarea class="width90" [rows]="2" [cols]="30" [(ngModel)]="rec.preventiveAction"
                    name="preventiveAction" placeholder="Preventive Action" pInputTextarea
                    [disabled]="editableFields.isPreventiveActionDisabled" required maxlength="255"></textarea>
                <small class="warning-message" *ngIf="rec.preventiveAction?.length==255">
                    Character limit reached, max 255 characters allowed.
                </small>
            </ng-container>
        </div>

        <div class="col-md-4 flex flex-col gap-1 mb-2">
            <b class="flex flex-row items-center gap-2 ">Estimated Completion Date
                <span *ngIf="formData.status == 'edit'" class="requiredField">*</span>:
            </b>
            <div *ngIf=" formData.status != 'edit'" class="pr-8 colorAqs mb-2">
                {{rec.plannedCompletionTime?rec.plannedCompletionTimeText:'N/A'}}
            </div>
            <p-calendar class="width90" *ngIf="formData.status == 'edit'" inputId="calendar-12h" [showTime]="false"
                [(ngModel)]="plannedCompletionDate" name="plannedCompletionTime" [minDate]="today"
                [disabled]="editableFields.isPlannedCompletionTimeDisabled" required
                placeholder="Estimated Completion Date"></p-calendar>
        </div>

        <div class="col-md-4 flex flex-col gap-1 mb-2">
            <b class="flex flex-row items-center gap-2 ">Cap Closure Comments
                <span *ngIf="formData.status == 'edit' && !editableFields.isCapClosureCommentsDisabled"
                    class="requiredField">*</span>:
            </b>
            <div *ngIf=" formData.status != 'edit'" class="pr-8 colorAqs mb-2">
                {{rec.capClosureComments?rec.capClosureComments:'N/A'}}
            </div>
            <p-dropdown class="width90" *ngIf="formData.status == 'edit'" [options]="agencyCloserComments"
                [(ngModel)]="rec.capClosureComments" name="capClosureComments" optionLabel="name" optionValue="uuid"
                [disabled]="editableFields.isCapClosureCommentsDisabled" placeholder="Cap Closure Comments"
                (onChange)="onCapClosureCommentsChange()"
                [required]="!editableFields.isCapClosureCommentsDisabled"></p-dropdown>
        </div>

        <div class="col-md-4 flex flex-col gap-1 mb-2">
            <b class="flex flex-row items-center gap-2 ">CAP Closure Feedback
                <span *ngIf="formData.status == 'edit' && rec.capClosureComments == 'CAPA Satisfactory'"
                    class="requiredField">*</span>:
            </b>
            <div *ngIf="formData.status != 'edit'" class="pr-8 colorAqs mb-2">
                {{rec.capClosureFeedback?rec.capClosureFeedback:'N/A'}}
            </div>
            <p-dropdown class="width90" *ngIf=" formData.status == 'edit'" [options]="agencyCloserRequired"
                [(ngModel)]="rec.capClosureFeedback" name="capClosureFeedback" optionLabel="name" optionValue="uuid"
                [disabled]="rec.capClosureComments != 'CAPA Satisfactory'" placeholder="Choose an option"
                [required]="rec.capClosureComments == 'CAPA Satisfactory'"></p-dropdown>
        </div>

        <div class="col-md-4 flex flex-col gap-1 mb-2">
            <b class="flex flex-row items-center gap-2 ">FollowUp Remarks
                <span *ngIf="formData.status == 'edit' && isVendor &&  rec.capClosureComments && rec.capClosureComments != 'CAPA Satisfactory'"
                    class="requiredField">*</span>:
            </b>
            <div *ngIf="formData.status != 'edit'" class="pr-8 colorAqs">
                {{rec.followupRemarks?rec.followupRemarks:'N/A'}}
            </div>
            <ng-container *ngIf="formData.status == 'edit'">
                <textarea class="width90" [rows]="2" [cols]="30" [(ngModel)]="rec.followupRemarks"
                    name="followupRemarks" placeholder="FollowUp Remarks" pInputTextarea required
                    [disabled]="isSeller || (rec.capClosureComments == 'CAPA Satisfactory') || !rec.capClosureComments" maxlength="255"></textarea>
                <small class="warning-message" *ngIf="rec.followupRemarks?.length==255">
                    Character limit reached, max 255 characters allowed.
                </small>
            </ng-container>
        </div>

        <div class="col-md-4 flex flex-col gap-1 mb-2">
            <b class="flex flex-row items-center gap-2 ">{{l1l2LabelText}} Remarks
                <span *ngIf="formData.status == 'edit' && !editableFields.isRemarksL1Disabled"
                    class="requiredField">*</span>:
            </b>
            <div *ngIf="formData.status != 'edit'" class="pr-8 colorAqs">
                {{rec.remarksL1?rec.remarksL1:'N/A'}}
            </div>
            <ng-container *ngIf="formData.status == 'edit'">
                <textarea class="width90" [rows]="2" [cols]="30" [(ngModel)]="rec.remarksL1" name="remarksL1"
                    pInputTextarea [required]="!editableFields.isRemarksL1Disabled"
                    [disabled]="editableFields.isRemarksL1Disabled" maxlength="255"></textarea>
                <small class="warning-message" *ngIf="rec.remarksL1?.length==255">
                    Character limit reached, max 255 characters allowed.
                </small>
            </ng-container>
        </div>
    </div>
    <div class="row pl-2 pr-2">
        <div class="col-md-6" *ngIf="!isSeller && formData.status == 'edit' && data.capaAttachmentList?.length<=2"
            [ngClass]="data.capaAttachment?.length>0 ? 'col-md-6' : 'col-md-12'">
            <div *ngIf="!isTestUser" class="w-full flex flex-col gap-2">
                <file-upload class="w-full" (uploadFile)="onFileChanged($event)" [multiple]="true"
                    [columnDefs]="columnDefs" [message]="'Supporting Files can be uploaded (max 3)'"></file-upload>
                <!-- <div *ngIf="files?.length>3">
                    <span class="label-error2" style="color: red;">You can't upload more than 3 files.
                        Please delete at least {{files?.length - 3}} files to proceed. </span>
                </div> -->
            </div>
        </div>
        <div class="col-md-6">
            <div class="w-full felx flex-col mb-2" *ngIf="data.capaAttachment">
                <div class="capaAttachmentWrapper">
                    <div class="mb-2 te">Attachments</div>
                    <div class="flex flex-row items-center gap-4 tfl-flex-justify-space-between mb-2"
                        *ngFor="let item of data.capaAttachmentList; let i=index">
                        <div (click)="getCapAttachment(item)" class="flex flex-row items-center">
                            <i class="pi pi-file mr-1" [pTooltip]="item" tooltipPosition="bottom"
                                style="font-size: 1rem;font-weight:bold;color:#94a3b8;cursor:pointer"></i>
                            <span class="fileName"> {{ item }}</span>
                        </div>
                        <div>
                            <i class="pi pi-download mr-4" pTooltip="Download/Open" tooltipPosition="bottom"
                                style="font-size: 1rem;font-weight:bold;color:#2196f3;cursor:pointer"
                                (click)="getCapAttachment(item)(item)"></i>
                            <i class="pi pi-trash" pTooltip="Delete" tooltipPosition="bottom"
                                style="font-size: 1rem;font-weight:bold;color:rgb(243 94 94);cursor:pointer"
                                (click)="deleteCapAttachment(item)"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="formData.status == 'edit'" class="w-full border-t flex flex-row items-center justify-end gap-4 p-2 pb-0">

        <tfl-button *ngIf="formData.status =='edit' && (isSeller)" [icon]="'content_copy'" [buttonName]="'Copy'"
            [isDisabled]="!f.form.valid" (submit)="onCopy(formData.data)"></tfl-button>

        <tfl-button *ngIf="formData.status =='edit' && !(isAgency || isOps || isAdmin || isTestUser)" [icon]="'save'"
            [buttonName]="'Save'" [isDisabled]="!f.form.valid" (submit)="onSaveOrSubmit(true)"></tfl-button>

        <tfl-button *ngIf="formData.status == 'edit' && !isTestUser" [icon]="'done'" [buttonName]="'Submit'"
            [isDisabled]="!f.form.valid" (submit)="onSaveOrSubmit(false)"></tfl-button>
    </div>
</form>

<ng-container *ngIf="showCopyTo">
    <p-dialog [header]="'Copy Details'" [(visible)]="showCopyTo" [style]="{ width: '65%' }" [modal]="true"
        [closeOnEscape]="false" [draggable]="false" [dismissableMask]="false" [autoZIndex]="false" appendTo="body"
        [transitionOptions]="'300ms'" (visibleChange)="showCopyTo=false">
        <div class="w-full flex flex-col gap-1">
            <div class="modal-content pb-4">
                <div class="mb-2">Note: Copying only <b><i>Cap Closure Comments,CAP Closure Feedback, {{l1l2LabelText}}
                            Remarks</i></b>
                </div>
                <div class="row justify-between mt-4">
                    <div class="col-md-5">
                        <input type="text" [(ngModel)]="copyToDetails.from" pInputText placeholder="From"
                            [disabled]="true" />
                    </div>
                    <div class="col-md-5">
                        <p-multiSelect [options]="toCapaItemList" [(ngModel)]="copyToDetails.toList" optionLabel="label"
                            appendTo="body" optionValue="value" placeholder="Select Copy To"></p-multiSelect>
                    </div>
                </div>
            </div>
            <p-divider></p-divider>
            <div class="w-full flex flex-row justify-end gap-6 mt-2">
                <tfl-secondary-button [buttonName]="'Cancel'" (submit)="showCopyTo=false"></tfl-secondary-button>
                <tfl-button [buttonName]="'Copy & Save'" [isDisabled]="copyToDetails.toList.length==0"
                    (submit)="saveCopiedCapaItems()"></tfl-button>
            </div>
        </div>
    </p-dialog>
</ng-container>