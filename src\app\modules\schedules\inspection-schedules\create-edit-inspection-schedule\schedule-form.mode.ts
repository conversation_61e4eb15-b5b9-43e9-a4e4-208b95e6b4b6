import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES } from 'app/enum-constants';


export const SCHEDULE_CREATE_FORM_MODEL = {
    auditDate: {
        type: FORM_CONTROL_TYPES.DATE,
        fieldStatus: FORM_CONTROL_TYPES.TEXT_SEARCH,
        value: "",
        label: "Audit Date",
        placeholder: "DD/MM/YYYY",
        show: true,
        showTime: true,
        rules: {
            minDate: new Date(),
            required: true,
        },
    },
    auditor: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Auditor",
        placeholder: "Auditor",
        show: true,
        rules: {
            required: true,
        },
    },
    auditType: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Audit Type",
        placeholder: "Audit Type",
        show: true,
        rules: {
            required: true,
        },
    },
    shipmentDestination: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Shipment Destination",
        placeholder: "Shipment Destination",
        show: true,
        rules: {
            required: true,
        },
    },
    purchaseOrderNo: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Purchase Order No",
        placeholder: "Purchase Order No",
        show: true,
        rules: {
            required: true,
        },
        tooltip: 'Purchase Order Number',
    },
    _designcode: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "SKU",
        placeholder: "SKU",
        show: false,
        // rules: {
        //     required: false,
        // },
        tooltip: 'Design Code/Product Code/Article Number',
    },
    designcodes: {
        type: FORM_CONTROL_TYPES.MULTI_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.MULTI_SELECT,
        options: [],
        value: "",
        label: "Design Codes",
        placeholder: "Design Codes",
        show: false,
        // rules: {
        //     required: false,
        // },

    }


    // orderedQty: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     options: [],
    //     value: "",
    //     label: "PO Qty",
    //     placeholder: "PO Qty", 
    //     show: false,
    //     tooltip: 'Purchase Order Quantity',
    // },
    // brand: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Brand",
    //     placeholder: "Brand",
    //     show: false,
    //     tooltip: 'Brand',
    // },
    // productType: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Product Type",
    //     placeholder: "Product Type",
    //     show: false,
    // },
    // category: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Category",
    //     placeholder: "Category",
    //     show: false,
    // },
    // description: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Product Description",
    //     placeholder: "Product Description",
    //     show: false,
    // },
    // division: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Division",
    //     placeholder: "Division",
    //     show: false,
    // },
    // color: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Color",
    //     placeholder: "Color",
    //     show: false,
    // },
    // material: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Material",
    //     placeholder: "Material",
    //     show: false
    // },
    // mrp: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Retail Price",
    //     placeholder: "Retail Price",
    //     show: false
    // },
    // season: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Season",
    //     placeholder: "Season",
    //     show: false
    // },
    // lining: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     value: "",
    //     label: "Lining",
    //     placeholder: "Lining",
    //     show: false
    // },
    // vendor: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     options: [],
    //     value: "",
    //     label: "Supplier",
    //     placeholder: "Supplier",
    //     show: false,
    // },
    // vendorName: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     options: [],
    //     value: "",
    //     label: "Supplier",
    //     placeholder: "Supplier",
    //     show: false,
    // },
    // factory: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     options: [],
    //     value: "",
    //     label: "Factory",
    //     placeholder: "Factory",
    //     show: false
    // },
    // factoryName: {
    //     type: FORM_CONTROL_TYPES.TEXT,
    //     fieldStatus: FORM_CONTROL_TYPES.VIEW,
    //     options: [],
    //     value: "",
    //     label: "Factory",
    //     placeholder: "Factory",
    //     show: false
    // },


}


export const MADURA_SCHEDULE_CREATE_FORM_MODEL = {
    auditDate: {
        type: FORM_CONTROL_TYPES.DATE,
        fieldStatus: FORM_CONTROL_TYPES.TEXT_SEARCH,
        value: new Date(),
        label: "Audit Date",
        placeholder: "DD/MM/YYYY HH:MM",
        show: true,
        showTime: true,
        topGroupTitle: true,
        topGroupTitleLabel: 'Audit Details',
        rules: {
            minDate: new Date(),
            required: true,
        },
    },

    auditType: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (item) => { },
        options: [],
        value: "",
        label: "Audit Type",
        placeholder: "Audit Type",
        show: true,
        rules: {
            required: true,
        },
    },
    auditor: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (item) => { },
        options: [],
        value: "",
        label: "Auditor",
        placeholder: "Auditor",
        show: true,
        rules: {
            required: true,
        },
    },
    factory: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (item) => { },
        options: [],
        value: "",
        label: "Factory",
        placeholder: "Select Factory",
        show: true,
        topGroupTitle: true,
        topGroupTitleLabel: 'Factory & Vendor Details',
        rules: {
            required: true,
        }
    },

    vendor: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Select Vendor",
        placeholder: "Vendor",
        show: true,
    },
    goldSeal: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Gold Seal",
        placeholder: "Gold Seal",
        topGroupTitle: true,
        topGroupTitleLabel: 'Product Details',
        show: true,
    },
    designcodes: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        onChange: (item) => { },
        options: [],
        value: "",
        label: "Style Code(s)",
        placeholder: "Style Code(s)",
        show: true,
    },
    brand: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (item) => { },
        options: [],
        value: "",
        label: "Brand",
        placeholder: "Select Brand",
        show: true,
        tooltip: 'Brand',
    },
    productType: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (item) => { },
        options: [],
        value: "",
        label: "Product Type",
        placeholder: "Select Product Type",
        show: true,
    },
    category: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        fieldStatus: FORM_CONTROL_TYPES.SINGLE_SELECT,
        options: [],
        value: "",
        label: "Product Category",
        placeholder: "Select Product Category",
        show: true,
    },
    size: {
        type: FORM_CONTROL_TYPES.NUMBER,
        fieldStatus: FORM_CONTROL_TYPES.NUMBER,
        value: 0,
        label: "@Sizes",
        placeholder: "Sizes",
        topGroupTitle: true,
        topGroupTitleLabel: 'Other Details',
        show: true,
        min: 0,
        rules: {

        }
    },
    carton: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: 0,
        label: "#Carton(s)",
        placeholder: "#Carton(s)",
        min: 0,
        show: true,
    },
    poNumber: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "PO Number",
        placeholder: "PO Number",
        show: true,
    },
    channel: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Channel",
        placeholder: "Channel",
        show: true,
    },
    orderQty: {
        type: FORM_CONTROL_TYPES.NUMBER,
        fieldStatus: FORM_CONTROL_TYPES.NUMBER,
        value: "",
        label: "Ordered Quantity",
        placeholder: "Ordered Quantity",
        show: true,
    },
    fabricCode: {
        type: FORM_CONTROL_TYPES.TEXT,
        fieldStatus: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Fabric/Material Code",
        placeholder: "Fabric/Material Code",
        show: true,
    },
    comments: {
        type: FORM_CONTROL_TYPES.TEXTAREA,
        fieldStatus: FORM_CONTROL_TYPES.TEXTAREA,
        value: "",
        label: "Comments",
        placeholder: "Comments",
        show: true,
    },

    offerQty: {
        type: FORM_CONTROL_TYPES.NUMBER,
        fieldStatus: FORM_CONTROL_TYPES.NUMBER,
        value: "",
        label: "Q1",
        placeholder: "Q1",
        topGroupTitle: true,
        topGroupTitleLabel: 'Offered Quantity',
        show: true,
    },
    offerQty2: {
        type: FORM_CONTROL_TYPES.NUMBER,
        fieldStatus: FORM_CONTROL_TYPES.NUMBER,
        value: "",
        label: "Q2",
        placeholder: "Q2",
        show: true,
    },
    offerQty3: {
        type: FORM_CONTROL_TYPES.NUMBER,
        fieldStatus: FORM_CONTROL_TYPES.NUMBER,
        value: "",
        label: "Q3",
        placeholder: "Q3",
        show: true,
    },


}
