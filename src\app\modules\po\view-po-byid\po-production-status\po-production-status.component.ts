import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { ICellRendererParams } from 'ag-grid-community';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { PO_PROCESS_TYPES, TA_ACTIVITY_STATUS, TA_INBOX_ITEMS_STATUS } from 'app/core/qinspect/_common/AppConstants';
import { URL_CONSTANTS } from 'app/core/qinspect/appconfig/appconfig';
import { FORM_CONTROL_TYPES } from 'app/enum-constants';
import { CommonService } from 'app/services/common.service';
import { InvokeService } from 'app/services/invoke.service';
import { ImageViewerDialogComponent } from 'app/shared/image-viewer-dialog/image-viewer-dialog.component';
import {
  CellActionComponent,
} from 'app/shared/tfl-table-search-grid/grid-supporting-components/cell-action/cell-action.component';
import { ImgIconComponent } from 'app/shared/tfl-table-search-grid/grid-supporting-components/img-icon/img-icon.component';
import { environment } from 'environments/environment';
import { FileSaverService } from 'ngx-filesaver';
import { ReplaySubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'po-production-status',
  templateUrl: './po-production-status.component.html',
  styleUrls: ['./po-production-status.component.scss']
})
export class PoProductionStatusComponent implements OnInit, OnDestroy {
  @Input() designCodeData;
  hdr: string = "T&A History";
  historySummaryData: any[] = [];
  designCodeUid: string = '';
  poNumber: string = '';
  fileIcon: string = "assets/svg/attach_blue.svg";
  doneIcon: string = "assets/svg/check_done.svg";
  inboxItem: any;
  auditId: any;
  showDetails: boolean = false;
  requestCodeList: any;
  commentsList: any[] = [];
  documentList: any[] = [];
  invoiceNo: string;;
  isAuditIdValid: boolean = false;
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  PO_PROCESS_TYPES = PO_PROCESS_TYPES;
  activityInfoMessage = [];
  public TA_ACTIVITY_STATUS = TA_ACTIVITY_STATUS;
  FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  configurationList: any[] = [];
  attributesList: any[] = [];
  attachmentsColumnDefs2: any = [
    {
      headerName: "Type", field: "fileName", width: 70, maxWidth: 70,
      cellRendererFramework: ImgIconComponent,
      cellRendererParams: (params: ICellRendererParams) => params.data
    },
    { headerName: "Name", field: "fileName", sortable: true, unSortIcon: true, width: 380, maxWidth: 380 },
    { headerName: "Attachment Type", field: "docType", sortable: true, width: 300, maxWidth: 300, },
    {
      headerName: 'Actions',
      minWidth: 150, maxWidth: 200,
      cellRendererFramework: CellActionComponent,
      cellRendererParams: params => {
        return { actions: this.prepareActionIconButtons2(params.data), onAction: action => this.onActions(action) }
      }
    }
  ]
  constructor(
    private _activatedRoute: ActivatedRoute,
    public _commonService: CommonService,
    public invokeService: InvokeService,
    public _router: Router,
    private sanitizer: DomSanitizer,
    public dialog: MatDialog,
    public fileSaverService: FileSaverService,
    private http: HttpClient,) { }


  ngOnInit(): void {
    this.showDetails = this._commonService.getTenantConfig().tenantUid != 'demo' ? true : false;
    this._activatedRoute.queryParams.subscribe(params => {
      this.invoiceNo = params['invoiceNo'];
      this.auditId = params['auditId'];
      if (this.invoiceNo) {
        this.getActivitySummaryByInvoiceNumber();
      }
    });

    this._activatedRoute.paramMap.subscribe(paramMap => {
      this.poNumber = paramMap.get('poNumber');
      this.designCodeUid = paramMap.get('designCodeUid');
      if (!!this.poNumber && !!this.designCodeUid && this.showDetails) {
        this.getProductionStatusSummary();
      }
    });
  }

  private getProductionStatusSummary(): void {
    if (!this.invoiceNo) {
      APP_UI_CONFIG.PO.getTASummaryByDesignCode.paramList.poNumber = this.poNumber;
      APP_UI_CONFIG.PO.getTASummaryByDesignCode.paramList.designCodeUid = this.designCodeUid;
      this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getTASummaryByDesignCode).subscribe({
        next: response => {
          if (response) {
            let summaryData = response.sort(function (a, b) {
              let completedTime_order = +new Date(a.startedTime) - +new Date(b.startedTime);
              let activity_master_order = parseInt(b.activityMasterOrdinal) - parseInt(b.activityMasterOrdinal);
              let activity_order = parseInt(b.ordinal) - parseInt(b.ordinal);
              return completedTime_order || activity_master_order || activity_order;
            });

            this.historySummaryData = summaryData.map(item => ({ statusText: this.prepareStatusText(item.status), ...item }));
            if (summaryData.length > 0) {
              this.hdr = `T&A History For Article Number:${this.historySummaryData[0].designCodeCode}, PO Number : ${this.historySummaryData[0].purchaseOrderNo}`;
              this.inboxItem = this.findRecentActivity(this.historySummaryData);
              this.attributesList = this.inboxItem?.attributes?.filter(item => (item.type != 'APPROVE' && item.type != 'SUBMIT' && item.type != 'FAIL'))
            }
            if (this.historySummaryData.length == 0) {
              this.activityInfoMessage = [{ severity: 'info', detail: 'T&A Not Started yet' }];
            }
          }

        }, error: error => {

        }
      })
    }
  }

  private findRecentActivity(historySummaryData: any[]): any {
    let filteredCompletedActivities: any[] = historySummaryData.filter(item => item.completedTime);
    return filteredCompletedActivities.sort(function (a, b) {
      return b.activityOrdinal - a.activityOrdinal;
    })[0] || historySummaryData[0];
  }
  public onDownloadInspectionSummary(inboxItem): void {
    let reportUrl = URL_CONSTANTS.BASE_URL + URL_CONSTANTS.AUDIT_GETREPORT + '/' + inboxItem.auditId + '.pdf';
    window.open(reportUrl, '_blank');
  }

  public onViewInspectionSummary(inboxItem): void {
    // if (inboxItem.activity.inspectionStatus == 17 || inboxItem.activity.inspectionStatus == 6 || inboxItem.activity.inspectionStatus == 19 || inboxItem.activity.inspectionStatus == 23 || inboxItem.activity.inspectionStatus == 1) {
    this._router.navigate(['/schedules/reviewProcess/edit/' + inboxItem.auditId])
    // }
    // else {
    // this._commonService.warning('Report may not generated yet.');
    // }
  }
  invoiceList:any
  private getActivitySummaryByInvoiceNumber(): void {
    const queryParams = [{ key: 'invoice_no', value: this.invoiceNo }]
    this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getInvoiceList, queryParams)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {

          if (response) {
            this.invoiceList = response;
            console.log("this.invoiceList",this.invoiceList)
            this.getPendingInvoiceList(response);
          }
        },
        error: error => {
          this._commonService.error(error);
        }
      })
  }

  private getPendingInvoiceList(response): void {
    let maxOrdinal = response[0].activityMasterOrdinal
    for (let i = 1; i < response.length; i++) {
      if (response[i].activityMasterOrdinal > maxOrdinal) {
        maxOrdinal = response[i].activityMasterOrdinal;
      }
    }
    const queryParams = [{ key: 'processType', value: PO_PROCESS_TYPES.FOOD }, { key: 'ordinal', value: maxOrdinal }]
    this.invokeService.serviceInvocation(APP_UI_CONFIG.PO.getPendingInvoiceList, queryParams)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: data => {
          if (data) {
            data.map(item => {
              response.push(item)
            })
            let summaryData = response.sort(function (a, b) {
              let completedTime_order = +new Date(a.completedTime) - +new Date(b.completedTime);
              let activity_master_order = parseInt(b.activityMasterOrdinal) - parseInt(b.activityMasterOrdinal);
              let activity_order = parseInt(b.ordinal) - parseInt(b.ordinal);
              return  activity_master_order || activity_order;
            });

            // sort((a, b) => +new Date(b.completedTime) - +new Date(a.completedTime))

            // let summaryData = response?.sort((a, b) => (a.activityMasterOrdinal || a.ordinal > b.activityMasterOrdinal || b.ordinal) ? 1 : ((b.activityMasterOrdinal || b.ordinal > a.activityMasterOrdinal || a.ordinal) ? -1 : 0));
            this.historySummaryData = summaryData.map(item => ({ statusText: this.prepareStatusText(item.status), ...item }));
            this.isAuditIdValid = this.historySummaryData.some(item => {
              (!!item.auditId)
            })
            if (summaryData.length > 0) {
              this.hdr = `T&A History For Invoice Number:${this.invoiceNo}`;
              this.inboxItem = this.findRecentActivity(this.historySummaryData);
              this.attributesList = this.inboxItem?.attributes?.filter(item => (item.type != 'APPROVE' && item.type != 'SUBMIT' && item.type != 'FAIL'))
              this.requestCodeList = JSON.parse(this.inboxItem?.requestMap);
            }
          }
        },
        error: error => {
          this._commonService.error(error);
        }
      })
  }


  public InspectionStatus(inboxItem): any {
    let inspectionStatus: any;
    switch (inboxItem.inspectionStatus) {
      case TA_ACTIVITY_STATUS.DELETED:
        inspectionStatus = 'Deleted'
        break;
      case TA_ACTIVITY_STATUS.SCHEDULED:
        inspectionStatus = 'Scheduled'
        break;
      case TA_ACTIVITY_STATUS.SUBMITTED:
        inspectionStatus = 'Submitted'
        break;
      case TA_ACTIVITY_STATUS.ABORTED:
        inspectionStatus = 'Aborted'
        break;
      case TA_ACTIVITY_STATUS.REVOKED:
        inspectionStatus = 'Revoked'
        break;
      case TA_ACTIVITY_STATUS.FAILED:
        inspectionStatus = 'Failed'
        break;
      case TA_ACTIVITY_STATUS.DRAFT:
        inspectionStatus = 'Draft'
        break;
      case TA_ACTIVITY_STATUS.DRAFT_COMPLETED:
        inspectionStatus = 'Draft Completed'
        break;
      case TA_ACTIVITY_STATUS.EXPIRED:
        inspectionStatus = 'Expired'
        break;
      case TA_ACTIVITY_STATUS.SAVE_OR_UPDATE:
        inspectionStatus = 'Save Or Update'
        break;
      case TA_ACTIVITY_STATUS.PREVIEW:
        inspectionStatus = 'Preview'
        break;
      case TA_ACTIVITY_STATUS.STARTED:
        inspectionStatus = 'Started'
        break;
      case TA_ACTIVITY_STATUS.CONFIRMED:
        inspectionStatus = 'Confirmed'
        break;
      case TA_ACTIVITY_STATUS.REVIEWED:
        inspectionStatus = 'Reviewed'
        break;
      case TA_ACTIVITY_STATUS.PARTIALLY_SUBMITTED:
        inspectionStatus = 'Partially Submitted'
        break;
      case TA_ACTIVITY_STATUS.EDITED:
        inspectionStatus = 'Edited'
        break;
      case TA_ACTIVITY_STATUS.DIVERSION:
        inspectionStatus = 'Diversion'
        break;
      case TA_ACTIVITY_STATUS.L1_REVIEW:
        inspectionStatus = 'L1 Review'
        break;
      case TA_ACTIVITY_STATUS.L1_REVIEWED:
        inspectionStatus = 'L1 Reviewed'
        break;
      case TA_ACTIVITY_STATUS.L2_REVIEW:
        inspectionStatus = 'L2 Review'
        break;
      case TA_ACTIVITY_STATUS.L2_REVIEWED:
        inspectionStatus = 'L2 Reviewed'
        break;
      case TA_ACTIVITY_STATUS.AUDITOR_SUBMITTED:
        inspectionStatus = 'Auditor Submitted'
        break;
      case TA_ACTIVITY_STATUS.PARTIALLY_SAVED:
        inspectionStatus = 'Partially Saved'
        break;
      default:
        break
    }
    return inspectionStatus;
  }



  private prepareStatusText(status) {
    let statusText: string = '';
    switch (status) {
      case TA_INBOX_ITEMS_STATUS.PENDING:
        statusText = `<span class="font-weight-600 text-warning-600">Pending</span>`;
        break;
      case TA_INBOX_ITEMS_STATUS.COMPLETED:
        statusText = `<span class="font-weight-600 text-success-800">Completed</span>`;
        break;
      case TA_INBOX_ITEMS_STATUS.REJECTED:
        statusText = `<span class="font-weight-600 text-danger-800">Rejected</span>`;
        break;
    }
    return statusText
  }


  public onRefresh(): void {

  }

  public onViewItem(item): void {
    this.inboxItem = item;
    this.commentsList = item.comments || [];
    this.documentList = item.documents || [];
    this.attributesList = this.inboxItem?.attributes?.filter(item => (item.type != 'APPROVE' && item.type != 'SUBMIT' && item.type != 'FAIL'))

  }

  public close(): void {
    this._commonService.historyBack();
  }

  public prepareActionIconButtons2(row: any): any {
    let icons: any[] = [];
    icons.push({ type: 'view', icon: 'view', title: 'View', data: row });
    return icons;
  }

  private onActions(action) {
    switch (action.type) {
      case 'view':
        this.onViewDocument(action.data)
        break;

    }
  }

  private onViewDocument(data): void {
    let headers = new HttpHeaders({ 'Content-Type': 'application/csv' })
      .set("Authorization", this._commonService.getBasicAuth()).set('Client', this._commonService.getTenantUid());;
    this.http.get(environment.apiUrl + 'sourcing/activity/' + data.docUid + '/doc', {
      observe: 'response',
      responseType: 'blob',
      headers: headers
    }).subscribe(res => {
      if (data.fileType && this.isImage(data.fileType)) {
        this.viewShipmentImage(res.body, data.docType);
      } else
        this.fileSaverService.save((<any>res).body, data.fileType + data.fileName);
    });

  }

  public viewShipmentImage(imageData: Blob, docType: string) {
    if (imageData) {
      const urlCreator = window.URL;
      const image = {};
      image['name'] = docType + ' IMAGE';
      image['pic'] = this.sanitizer.bypassSecurityTrustUrl(
        urlCreator.createObjectURL(imageData));
      const dialogRef = this.dialog.open(ImageViewerDialogComponent, {
        data: [image],
      });
    }
  }

  public isImage(name: string): boolean {
    let isImage: boolean = false;
    let extension = name.toLowerCase();
    switch (extension) {
      case '.png':
        isImage = true;
        break;
      case '.jpg':
        isImage = true;
        break;
      case '.jpeg':
        isImage = true;
        break;
      case '.svg':
        isImage = true;
        break;
      case '.webp':
        isImage = true;
        break;
      default:
        isImage = false;
        break;
    }
    return isImage;
  }

  public isDueDateOver(plannedEndDate): boolean {
    return new Date() > new Date(plannedEndDate)
  }

  public navigateToPoDetails(poNumber): void {
    this._commonService.navigate(`po/poById/${this.requestCodeList[0].requestCode}/${this.inboxItem?.invoiceNo}`);
  }

  //destroy
  public ngOnDestroy(): void {
    this.$destroyed.next(true);
    this.$destroyed.complete();
  }

}
